const isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ message: 'Requires admin role' });
  }
};

const isTeacher = (req, res, next) => {
  if (req.user && (req.user.role === 'platform_teacher' || req.user.role === 'new_teacher')) {
    next();
  } else {
    res.status(403).json({ message: 'Requires teacher role' });
  }
};

const isStudent = (req, res, next) => {
  if (req.user && req.user.role === 'student') {
    next();
  } else {
    res.status(403).json({ message: 'Requires student role' });
  }
};

const isPlatformTeacher = (req, res, next) => {
  if (req.user && req.user.role === 'platform_teacher') {
    next();
  } else {
    res.status(403).json({ message: 'Requires platform teacher role' });
  }
};

module.exports = {
  isAdmin,
  isTeacher,
  isStudent,
  isPlatformTeacher
};
