{"name": "@paypal/paypalhttp", "version": "1.0.1", "description": "A library for integrating with PayPalHttp.", "keywords": ["paypalhttp", "payments"], "homepage": "https://github.com/paypal/paypalhttp_node", "author": "PayPal", "main": "index", "directories": {"lib": "./lib"}, "repository": {"type": "git", "url": "https://github.com/paypal/paypalhttp_node.git"}, "engines": {"node": ">=4"}, "dependencies": {}, "devDependencies": {"busboy": "^0.2.14", "chai": "1.5.0", "eslint": "^2.7.0", "eslint-config-braintree": "^1.0.2", "mocha": "3.2.0", "nock": "^9.0.13", "sinon": "^2.3.2"}, "license": "MIT", "scripts": {"lint": "eslint lib/ spec/", "pretest": "npm run lint", "test:unit": "mocha spec/unit --recursive -r spec/spec_helper", "test": "npm run test:unit"}}