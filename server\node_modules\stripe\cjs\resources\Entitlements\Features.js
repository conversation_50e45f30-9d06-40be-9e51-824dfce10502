"use strict";
// File generated from our OpenAPI spec
Object.defineProperty(exports, "__esModule", { value: true });
exports.Features = void 0;
const StripeResource_js_1 = require("../../StripeResource.js");
const stripeMethod = StripeResource_js_1.StripeResource.method;
exports.Features = StripeResource_js_1.StripeResource.extend({
    create: stripeMethod({ method: 'POST', fullPath: '/v1/entitlements/features' }),
    retrieve: stripeMethod({
        method: 'GET',
        fullPath: '/v1/entitlements/features/{id}',
    }),
    update: stripeMethod({
        method: 'POST',
        fullPath: '/v1/entitlements/features/{id}',
    }),
    list: stripeMethod({
        method: 'GET',
        fullPath: '/v1/entitlements/features',
        methodType: 'list',
    }),
});
