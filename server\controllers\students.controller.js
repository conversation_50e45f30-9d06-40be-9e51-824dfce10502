const db = require('../db');

const completeProfile = async (req, res) => {
  const userId = req.user.id;
  const {
    native_language,
    preferred_islamic_language,
    preferred_arabic_language,
    age,
    country,
    timezone,
    arabic_level,
    private_tutoring
  } = req.body;

  try {
    // Validate required fields
    if (!native_language || !preferred_islamic_language || !preferred_arabic_language || 
        !age || !country || !timezone || !arabic_level) {
      return res.status(400).json({ message: 'All fields are required' });
    }

    // Validate age
    if (age < 5 || age > 120) {
      return res.status(400).json({ message: 'Invalid age' });
    }

    // Update student profile
    const [result] = await db.execute(
      `UPDATE users 
       SET native_language = ?, 
           preferred_islamic_language = ?,
           preferred_arabic_language = ?,
           age = ?,
           country = ?,
           timezone = ?,
           arabic_level = ?,
           private_tutoring = ?,
           profile_completed = true
       WHERE id = ?`,
      [
        native_language,
        preferred_islamic_language,
        preferred_arabic_language,
        age,
        country,
        timezone,
        arabic_level,
        private_tutoring ? 1 : 0,
        userId
      ]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get updated user data
    const [users] = await db.execute(
      'SELECT * FROM users WHERE id = ?',
      [userId]
    );

    if (!users || users.length === 0) {
      return res.status(404).json({ message: 'User not found' });
    }

    const user = users[0];
    delete user.password; // Remove password from response

    res.json({
      message: 'Profile completed successfully',
      user
    });
  } catch (error) {
    console.error('Error completing profile:', error);
    res.status(500).json({ message: 'Error completing profile' });
  }
};

module.exports = {
  completeProfile
};
