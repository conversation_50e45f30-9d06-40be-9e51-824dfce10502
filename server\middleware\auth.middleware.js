const jwt = require('jsonwebtoken');
const config = require('../config/auth.config');
const db = require('../config/db');

const verifyToken = async (req, res, next) => {
  try {
    // Log all headers for debugging
    console.log('Request headers:', {
      ...req.headers,
      path: req.path,
      method: req.method
    });

    // Check for token in Authorization header
    let token = req.headers.authorization;

    // If no Authorization header, check for token in cookies
    if (!token && req.cookies && req.cookies.token) {
      token = req.cookies.token;
      console.log('Using token from cookies');
    }

    // If still no token, check query parameters (not recommended for production)
    if (!token && req.query && req.query.token) {
      token = req.query.token;
      console.log('Using token from query parameters');
    }

    if (!token) {
      console.log('No token provided in request');
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    // Remove Bearer prefix if present
    const tokenValue = token.startsWith('Bearer ') ? token.slice(7) : token;

    console.log('Token value:', tokenValue.substring(0, 10) + '...');
    console.log('Secret key:', config.secret.substring(0, 3) + '...');

    let decoded;
    try {
      // Use a more permissive approach for debugging
      decoded = jwt.verify(tokenValue, config.secret, {
        ignoreExpiration: false, // Set to true to ignore token expiration
        algorithms: ['HS256']
      });

      console.log('Token decoded successfully:', {
        id: decoded.id,
        role: decoded.role,
        exp: decoded.exp ? new Date(decoded.exp * 1000).toISOString() : 'none'
      });

      if (!decoded || !decoded.id) {
        console.log('Invalid token payload:', decoded);
        return res.status(401).json({
          success: false,
          message: 'Invalid token: missing required fields'
        });
      }
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError);

      // Provide more detailed error message
      let errorMessage = 'Invalid token';
      if (jwtError.name === 'TokenExpiredError') {
        errorMessage = 'Token expired';
      } else if (jwtError.name === 'JsonWebTokenError') {
        errorMessage = 'Invalid token format or signature';
      }

      return res.status(401).json({
        success: false,
        message: errorMessage,
        error: jwtError.message
      });
    }

    console.log('Getting user from database with id:', decoded.id);

    // Get user from database with profile picture and deletion status
    const [rows] = await db.pool.execute(
      'SELECT id, full_name, email, role, gender, profile_picture_url, status, delete_scheduled_at, deleted_at, deletion_reason FROM users WHERE id = ?',
      [decoded.id]
    );

    if (!rows || rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = rows[0];

    // Check if user account is soft deleted
    if (user.deleted_at) {
      return res.status(401).json({
        success: false,
        message: 'تم حذف هذا الحساب',
        message_en: 'Account has been deleted',
        accountStatus: 'deleted',
        requiresLogout: true,
        deletedAt: user.deleted_at,
        deletionReason: user.deletion_reason
      });
    }

    // Check if user account is deleted (old system compatibility)
    if (user.status === 'deleted') {
      return res.status(401).json({
        success: false,
        message: 'تم حذف هذا الحساب',
        message_en: 'Account has been deleted',
        accountStatus: 'deleted',
        requiresLogout: true
      });
    }

    if (user.status === 'pending_deletion') {
      return res.status(401).json({
        success: false,
        message: 'هذا الحساب مجدول للحذف',
        message_en: 'Account is scheduled for deletion',
        accountStatus: 'pending_deletion',
        deleteScheduledAt: user.delete_scheduled_at,
        requiresLogout: true
      });
    }

    // Attach user to request object
    req.user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(401).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

const isAdmin = async (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Admin role required.'
    });
  }
  next();
};

const isTeacher = async (req, res, next) => {
  if (req.user.role !== 'platform_teacher' && req.user.role !== 'new_teacher') {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Teacher role required.'
    });
  }
  next();
};

const isStudent = async (req, res, next) => {
  console.log('🔍 isStudent middleware check:', {
    userId: req.user?.id,
    userRole: req.user?.role,
    isStudent: req.user?.role === 'student',
    method: req.method,
    url: req.originalUrl
  });

  if (req.user.role !== 'student') {
    console.log('❌ Access denied - not a student:', {
      userId: req.user?.id,
      userRole: req.user?.role
    });
    return res.status(403).json({
      success: false,
      message: 'Access denied. Student role required.'
    });
  }
  next();
};

/**
 * Middleware للتحقق من التوكن فقط بدون التحقق من حالة المستخدم
 * يستخدم في طريق /auth/verify للحصول على معلومات المستخدم مع حالته
 */
const verifyTokenOnly = async (req, res, next) => {
  try {
    // Log all headers for debugging
    console.log('Request headers:', {
      ...req.headers,
      path: req.path,
      method: req.method
    });

    // Check for token in Authorization header
    let token = req.headers.authorization;

    // If no Authorization header, check for token in cookies
    if (!token && req.cookies && req.cookies.token) {
      token = req.cookies.token;
      console.log('Using token from cookies');
    }

    // If still no token, check query parameters (not recommended for production)
    if (!token && req.query && req.query.token) {
      token = req.query.token;
      console.log('Using token from query parameters');
    }

    if (!token) {
      console.log('No token provided');
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }

    // Extract token value (remove 'Bearer ' prefix if present)
    const tokenValue = token.startsWith('Bearer ') ? token.slice(7) : token;

    let decoded;
    try {
      // Use a more permissive approach for debugging
      decoded = jwt.verify(tokenValue, config.secret, {
        ignoreExpiration: false, // Set to true to ignore token expiration
        algorithms: ['HS256']
      });

      console.log('Token decoded successfully:', {
        id: decoded.id,
        role: decoded.role,
        exp: decoded.exp ? new Date(decoded.exp * 1000).toISOString() : 'none'
      });

      if (!decoded || !decoded.id) {
        console.log('Invalid token payload:', decoded);
        return res.status(401).json({
          success: false,
          message: 'Invalid token: missing required fields'
        });
      }
    } catch (jwtError) {
      console.error('JWT verification error:', jwtError);

      // Provide more detailed error message
      let errorMessage = 'Invalid token';
      if (jwtError.name === 'TokenExpiredError') {
        errorMessage = 'Token expired';
      } else if (jwtError.name === 'JsonWebTokenError') {
        errorMessage = 'Invalid token format or signature';
      }

      return res.status(401).json({
        success: false,
        message: errorMessage,
        error: jwtError.message
      });
    }

    console.log('Getting user from database with id:', decoded.id);

    // Get user from database with ALL fields including deletion status
    const [rows] = await db.pool.execute(
      'SELECT id, full_name, email, role, gender, profile_picture_url, status, delete_scheduled_at, deleted_at, deletion_reason FROM users WHERE id = ?',
      [decoded.id]
    );

    if (!rows || rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = rows[0];

    // DON'T check status here - just attach user to request
    // This allows /auth/verify to return user info with status
    req.user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(401).json({
      success: false,
      message: 'Authentication failed'
    });
  }
};

module.exports = {
  verifyToken,
  verifyTokenOnly,
  isAdmin,
  isTeacher,
  isStudent
};
