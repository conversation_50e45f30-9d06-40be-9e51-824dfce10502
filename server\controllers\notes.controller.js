const mysql = require('mysql2/promise');
const config = require('../config/db.config');
const db = require('../config/database');

/**
 * Helper to ensure the requester is either the teacher or the student
 */
function _hasAccess(reqUser, teacherId, studentId) {
  const uid = reqUser.id;
  if (uid === teacherId || uid === studentId) return true;
  return false;
}

// GET /api/notes?teacherId=&studentId=&meetingId=
const getNotes = async (req, res) => {
  let connection;
  try {
    let teacherId = parseInt(req.query.teacherId, 10);
    const studentId = parseInt(req.query.studentId, 10);
    const meetingId = req.query.meetingId ? parseInt(req.query.meetingId, 10) : null;
    // role الحالي
    const writtenBy = req.user.role === 'teacher' || req.user.role === 'platform_teacher' || req.user.role === 'new_teacher' ? 'teacher' : 'student';

    if (!teacherId || !studentId) {
      return res.status(400).json({ success: false, message: 'teacherId and studentId are required' });
    }

    connection = await mysql.createConnection(config);
    // تحقق إذا كان teacherId هو user_id فعلاً، وإلا حوله من profile_id إلى user_id
    const [userRows] = await connection.execute('SELECT id FROM users WHERE id = ?', [teacherId]);
    if (userRows.length === 0) {
      // teacherId هو رقم بروفايل وليس user_id
      const [profileRows] = await connection.execute('SELECT user_id FROM teacher_profiles WHERE id = ?', [teacherId]);
      if (profileRows.length > 0) {
        teacherId = profileRows[0].user_id;
      }
    }

    if (!_hasAccess(req.user, teacherId, studentId)) {
      return res.status(403).json({ success: false, message: 'Unauthorized access to notes' });
    }

    const [rows] = await connection.execute(
      `SELECT content FROM notes WHERE teacher_id = ? AND student_id = ? AND written_by = ? LIMIT 1`,
      [teacherId, studentId, writtenBy]
    );

    res.json({ success: true, content: rows.length ? rows[0].content : '' });
  } catch (err) {
    console.error('getNotes error', err);
    res.status(500).json({ success: false, message: 'Error fetching notes' });
  } finally {
    if (connection) connection.end();
  }
};

// POST /api/notes  { teacherId, studentId, meetingId?, content, writtenBy }
const saveNotes = async (req, res) => {
  let connection;
  try {
    let { teacherId, studentId, meetingId = null, content = '', writtenBy } = req.body;

    if (!teacherId || !studentId) {
      return res.status(400).json({ success: false, message: 'teacherId and studentId are required' });
    }

    // تحقق إذا كان teacherId هو user_id فعلاً، وإلا حوله من profile_id إلى user_id
    connection = await mysql.createConnection(config);
    const [userRows] = await connection.execute('SELECT id FROM users WHERE id = ?', [teacherId]);
    if (userRows.length === 0) {
      // teacherId هو رقم بروفايل وليس user_id
      const [profileRows] = await connection.execute('SELECT user_id FROM teacher_profiles WHERE id = ?', [teacherId]);
      if (profileRows.length > 0) {
        teacherId = profileRows[0].user_id;
      }
    }

    if (!_hasAccess(req.user, teacherId, studentId)) {
      return res.status(403).json({ success: false, message: 'Unauthorized' });
    }

    // Check if note exists
    const [rows] = await connection.execute(
      `SELECT id FROM notes WHERE teacher_id = ? AND student_id = ? AND written_by = ? ${meetingId ? 'AND meeting_id = ?' : 'AND meeting_id IS NULL'} LIMIT 1`,
      meetingId ? [teacherId, studentId, writtenBy, meetingId] : [teacherId, studentId, writtenBy]
    );

    if (rows.length) {
      // update
      await connection.execute(
        'UPDATE notes SET content = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [content, rows[0].id]
      );
    } else {
      // insert
      await connection.execute(
        'INSERT INTO notes (teacher_id, student_id, meeting_id, content, written_by) VALUES (?,?,?,?,?)',
        [teacherId, studentId, meetingId, content, writtenBy]
      );
    }

    res.json({ success: true });
  } catch (err) {
    console.error('saveNotes error', err);
    res.status(500).json({ success: false, message: 'Error saving notes' });
  } finally {
    if (connection) connection.end();
  }
};

module.exports = { getNotes, saveNotes };
