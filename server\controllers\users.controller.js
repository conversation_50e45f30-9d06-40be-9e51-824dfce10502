const mysql = require('mysql2/promise');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../config/db.config');

const updateProfile = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);
    const userId = req.user.id;
    const { full_name, email } = req.body;

    // Log request data
    console.log('Update profile request:', {
      userId,
      full_name,
      email
    });

    // Get current user data
    const [currentUser] = await connection.execute(
      'SELECT * FROM users WHERE id = ?',
      [userId]
    );

    if (currentUser.length === 0) {
      return res.status(404).json({ 
        success: false,
        message: 'User not found' 
      });
    }

    const user = currentUser[0];

    // Validate email format if provided
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.status(400).json({ 
        success: false,
        message: 'Invalid email format' 
      });
    }

    // Check if email is taken by another user
    if (email && email !== user.email) {
      const [existingUser] = await connection.execute(
        'SELECT id FROM users WHERE email = ? AND id != ?',
        [email, userId]
      );

      if (existingUser.length > 0) {
        return res.status(400).json({ 
          success: false,
          message: 'Email already exists' 
        });
      }
    }

    // Build update query dynamically based on provided fields
    const updates = [];
    const values = [];

    if (full_name) {
      updates.push('full_name = ?');
      values.push(full_name);
    }

    if (email) {
      updates.push('email = ?');
      values.push(email);
    }

    // Only update if there are changes
    if (updates.length > 0) {
      values.push(userId);
      const updateQuery = `
        UPDATE users 
        SET ${updates.join(', ')} 
        WHERE id = ?
      `;

      await connection.execute(updateQuery, values);

      // Get updated user data
      const [updatedUser] = await connection.execute(
        'SELECT id, full_name, email, role, profile_picture_url FROM users WHERE id = ?',
        [userId]
      );

      if (updatedUser.length === 0) {
        throw new Error('Failed to fetch updated user data');
      }

      // Log success
      console.log('Profile updated successfully:', updatedUser[0]);

      return res.json({
        success: true,
        message: 'Profile updated successfully',
        user: updatedUser[0]
      });
    } else {
      // No changes to update
      return res.json({
        success: true,
        message: 'No changes to update',
        user: user
      });
    }
  } catch (error) {
    console.error('Error updating profile:', error);
    return res.status(500).json({
      success: false,
      message: 'Error updating profile'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

const changePassword = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);
    const userId = req.user.id;
    const { current_password, new_password } = req.body;

    if (!current_password || !new_password) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
    }

    // Get current user data
    const [currentUser] = await connection.execute(
      'SELECT password FROM users WHERE id = ?',
      [userId]
    );

    if (currentUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Verify current password
    const validPassword = await bcrypt.compare(current_password, currentUser[0].password);
    if (!validPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password is incorrect'
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(new_password, salt);

    // Update password
    await connection.execute(
      'UPDATE users SET password = ? WHERE id = ?',
      [hashedPassword, userId]
    );

    return res.json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    console.error('Error changing password:', error);
    return res.status(500).json({
      success: false,
      message: 'Error changing password'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

const uploadProfilePicture = async (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: 'No file uploaded'
    });
  }

  let connection;
  try {
    connection = await mysql.createConnection(config);
    const userId = req.user.id;
    const profilePictureUrl = `/uploads/profile-pictures/${req.file.filename}`;

    await connection.execute(
      'UPDATE users SET profile_picture_url = ? WHERE id = ?',
      [profilePictureUrl, userId]
    );

    return res.json({
      success: true,
      message: 'Profile picture uploaded successfully',
      url: profilePictureUrl
    });
  } catch (error) {
    console.error('Error uploading profile picture:', error);
    return res.status(500).json({
      success: false,
      message: 'Error uploading profile picture'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

module.exports = {
  updateProfile,
  changePassword,
  uploadProfilePicture
};
