import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Alert,
  Box
} from '@mui/material';
import Layout from '../../components/Layout';
import { useAuth } from '../../contexts/AuthContext';
import { timezones } from '../../utils/constants';
import axios from 'axios';

const CompleteProfile = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [formData, setFormData] = useState({
    native_language: '',
    preferred_islamic_language: '',
    age: '',
    country: '',
    timezone: '',
    arabic_level: 'beginner',
    private_tutoring: false
  });

  // Load student profile data when component mounts
  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const response = await axios.get('/api/students/profile', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.data.success && response.data.profile) {
          const profileData = response.data.profile;
          
          // If profile is already completed, redirect to dashboard
          if (profileData.is_completed) {
            navigate('/student/dashboard');
            return;
          }

          setFormData(prevData => ({
            ...prevData,
            native_language: profileData.native_language || '',
            preferred_islamic_language: profileData.islam_learning_language || '',
            age: profileData.age || '',
            country: profileData.country || '',
            timezone: profileData.timezone || '',
            arabic_level: profileData.arabic_proficiency_level || 'beginner',
            private_tutoring: profileData.private_tutoring_preference || false
          }));
        }
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError(err.response?.data?.message || t('common.error'));
      }
    };

    fetchProfileData();
  }, [t, navigate]);



  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSwitchChange = (e) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setLoading(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      await axios.post('/api/students/profile', {
        nativeLanguage: formData.native_language,
        islamLearningLanguage: formData.preferred_islamic_language,
        arabicLearningLanguage: formData.preferred_islamic_language, // استخدام نفس اللغة للعربية والإسلام
        age: formData.age,
        country: formData.country,
        timezone: formData.timezone,
        arabicProficiencyLevel: formData.arabic_level,
        privateTutoring: formData.private_tutoring
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      setSuccess(t('student.profile.success'));
      // Navigate back to dashboard after successful update
      setTimeout(() => {
        navigate('/student/dashboard');
      }, 2000);
    } catch (err) {
      console.error('Error completing profile:', err);
      setError(err.response?.data?.message || t('common.error'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            {t('student.profile.complete')}
          </Typography>

          {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
          {success && <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>}

          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('student.profile.nativeLanguage')}
                  name="native_language"
                  value={formData.native_language}
                  onChange={handleChange}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('student.profile.preferredIslamicLanguage')}
                  name="preferred_islamic_language"
                  value={formData.preferred_islamic_language}
                  onChange={handleChange}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  type="number"
                  label={t('student.profile.age')}
                  name="age"
                  value={formData.age}
                  onChange={handleChange}
                  required
                  inputProps={{ min: 5, max: 120 }}
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label={t('student.profile.country')}
                  name="country"
                  value={formData.country}
                  onChange={handleChange}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>{t('student.profile.timezone')}</InputLabel>
                  <Select
                    name="timezone"
                    value={formData.timezone}
                    onChange={handleChange}
                    required
                    label={t('student.profile.timezone')}
                  >
                    {timezones.map((timezone) => (
                      <MenuItem key={timezone.value} value={timezone.value}>
                        {timezone.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>{t('student.profile.arabicLevel')}</InputLabel>
                  <Select
                    name="arabic_level"
                    value={formData.arabic_level}
                    onChange={handleChange}
                    required
                    label={t('student.profile.arabicLevel')}
                  >
                    <MenuItem value="beginner">{t('student.profile.levels.beginner')}</MenuItem>
                    <MenuItem value="intermediate">{t('student.profile.levels.intermediate')}</MenuItem>
                    <MenuItem value="advanced">{t('student.profile.levels.advanced')}</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={formData.private_tutoring}
                      onChange={handleSwitchChange}
                      name="private_tutoring"
                    />
                  }
                  label={t('student.profile.privateTutoring')}
                />
              </Grid>

              <Grid item xs={12}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={loading}
                  fullWidth
                >
                  {loading ? t('common.saving') : t('common.save')}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Paper>
      </Container>
    </Layout>
  );
};

export default CompleteProfile;
