const emailService = require('../utils/emailService');
const emailTemplates = require('../templates/bookingCancellationEmails');

/**
 * Parse timezone offset from string format (e.g., "UTC+03:00")
 * @param {string} timezone - Timezone string
 * @returns {number} Offset in minutes
 */
const parseTimezoneOffset = (timezone) => {
  if (!timezone || timezone === 'UTC') return 0;

  const match = timezone.match(/UTC([+-])(\d{2}):(\d{2})/);
  if (!match) return 0;

  const sign = match[1] === '+' ? 1 : -1;
  const hours = parseInt(match[2]);
  const minutes = parseInt(match[3]);

  return sign * (hours * 60 + minutes);
};

/**
 * Convert UTC datetime to user's timezone
 * @param {string} utcDatetime - UTC datetime string
 * @param {string} userTimezone - User's timezone (e.g., "UTC+03:00")
 * @returns {Date} Date object in user's timezone
 */
const convertToUserTimezone = (utcDatetime, userTimezone) => {
  const utcDate = new Date(utcDatetime);
  const offsetMinutes = parseTimezoneOffset(userTimezone);

  // Add the offset to UTC time to get user's local time
  return new Date(utcDate.getTime() + (offsetMinutes * 60 * 1000));
};

/**
 * Format date for email display in user's timezone
 * @param {string} datetime - UTC datetime string
 * @param {string} userTimezone - User's timezone (e.g., "UTC+03:00")
 * @returns {string} Formatted date
 */
const formatBookingDate = (datetime, userTimezone = 'UTC') => {
  const userDate = convertToUserTimezone(datetime, userTimezone);

  const options = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    timeZone: 'UTC' // We already converted to user timezone, so display as UTC
  };

  const arabicDate = userDate.toLocaleDateString('ar-EG', options);
  const englishDate = userDate.toLocaleDateString('en-US', options);

  return `${arabicDate} - ${englishDate}`;
};

/**
 * Format time for email display in user's timezone
 * @param {string} datetime - UTC datetime string
 * @param {string} userTimezone - User's timezone (e.g., "UTC+03:00")
 * @returns {string} Formatted time with timezone
 */
const formatBookingTime = (datetime, userTimezone = 'UTC') => {
  const userDate = convertToUserTimezone(datetime, userTimezone);

  const options = {
    hour: '2-digit',
    minute: '2-digit',
    timeZone: 'UTC' // We already converted to user timezone, so display as UTC
  };

  const arabicTime = userDate.toLocaleTimeString('ar-EG', options);
  const englishTime = userDate.toLocaleTimeString('en-US', options);

  // Add timezone info
  const timezoneDisplay = userTimezone === 'UTC' ? 'UTC' : userTimezone;

  return `${arabicTime} - ${englishTime} (${timezoneDisplay})`;
};

/**
 * Send cancellation email to teacher when they cancel a booking
 * @param {Object} bookingData - Booking information
 * @param {Object} teacherData - Teacher information (includes timezone)
 * @param {Object} studentData - Student information
 * @param {string} cancellationReason - Reason for cancellation (optional)
 * @returns {Promise} Email send result
 */
const sendTeacherCancellationEmail = async (bookingData, teacherData, studentData, cancellationReason = null) => {
  try {
    console.log('📧 Sending teacher cancellation email...');

    const templateData = {
      teacherName: teacherData.full_name,
      studentName: studentData.full_name,
      bookingDate: formatBookingDate(bookingData.datetime, teacherData.timezone),
      bookingTime: formatBookingTime(bookingData.datetime, teacherData.timezone),
      duration: bookingData.duration || '50',
      cancellationReason: cancellationReason,
      refundAmount: bookingData.amount || '0'
    };

    const htmlContent = emailTemplates.getTeacherCancellationTemplate(templateData);

    const result = await emailService.sendEmail({
      to: teacherData.email,
      subject: 'تأكيد إلغاء الدرس - Lesson Cancellation Confirmed - Allemni online',
      html: htmlContent
    });

    console.log('✅ Teacher cancellation email sent successfully');
    return result;
  } catch (error) {
    console.error('❌ Error sending teacher cancellation email:', error);
    throw error;
  }
};

/**
 * Send notification email to student when teacher cancels their booking
 * @param {Object} bookingData - Booking information
 * @param {Object} teacherData - Teacher information
 * @param {Object} studentData - Student information (includes timezone)
 * @param {string} cancellationReason - Reason for cancellation (optional)
 * @returns {Promise} Email send result
 */
const sendStudentNotificationEmail = async (bookingData, teacherData, studentData, cancellationReason = null) => {
  try {
    console.log('📧 Sending student notification email...');

    const templateData = {
      studentName: studentData.full_name,
      teacherName: teacherData.full_name,
      bookingDate: formatBookingDate(bookingData.datetime, studentData.timezone),
      bookingTime: formatBookingTime(bookingData.datetime, studentData.timezone),
      duration: bookingData.duration || '50',
      cancellationReason: cancellationReason,
      refundAmount: bookingData.amount || '0'
    };

    const htmlContent = emailTemplates.getStudentNotificationTemplate(templateData);

    const result = await emailService.sendEmail({
      to: studentData.email,
      subject: 'تم إلغاء درسك - Your Lesson Has Been Cancelled - Allemni online',
      html: htmlContent
    });

    console.log('✅ Student notification email sent successfully');
    return result;
  } catch (error) {
    console.error('❌ Error sending student notification email:', error);
    throw error;
  }
};

/**
 * Send confirmation email to student when they cancel their own booking
 * @param {Object} bookingData - Booking information
 * @param {Object} teacherData - Teacher information
 * @param {Object} studentData - Student information (includes timezone)
 * @param {string} cancellationReason - Reason for cancellation (optional)
 * @returns {Promise} Email send result
 */
const sendStudentCancellationEmail = async (bookingData, teacherData, studentData, cancellationReason = null) => {
  try {
    console.log('📧 Sending student cancellation confirmation email...');

    const templateData = {
      studentName: studentData.full_name,
      teacherName: teacherData.full_name,
      bookingDate: formatBookingDate(bookingData.datetime, studentData.timezone),
      bookingTime: formatBookingTime(bookingData.datetime, studentData.timezone),
      duration: bookingData.duration || '50',
      cancellationReason: cancellationReason,
      refundAmount: bookingData.amount || '0'
    };

    const htmlContent = emailTemplates.getStudentCancellationTemplate(templateData);

    const result = await emailService.sendEmail({
      to: studentData.email,
      subject: 'تأكيد إلغاء الدرس - Lesson Cancellation Confirmed - Allemni online',
      html: htmlContent
    });

    console.log('✅ Student cancellation confirmation email sent successfully');
    return result;
  } catch (error) {
    console.error('❌ Error sending student cancellation confirmation email:', error);
    throw error;
  }
};

/**
 * Send notification email to teacher when student cancels their booking
 * @param {Object} bookingData - Booking information
 * @param {Object} teacherData - Teacher information (includes timezone)
 * @param {Object} studentData - Student information
 * @param {string} cancellationReason - Reason for cancellation (optional)
 * @returns {Promise} Email send result
 */
const sendTeacherNotificationEmail = async (bookingData, teacherData, studentData, cancellationReason = null) => {
  try {
    console.log('📧 Sending teacher notification email...');

    const templateData = {
      teacherName: teacherData.full_name,
      studentName: studentData.full_name,
      bookingDate: formatBookingDate(bookingData.datetime, teacherData.timezone),
      bookingTime: formatBookingTime(bookingData.datetime, teacherData.timezone),
      duration: bookingData.duration || '50',
      cancellationReason: cancellationReason,
      refundAmount: bookingData.amount || '0'
    };

    // Use teacher cancellation template but modify the content for notification
    let htmlContent = emailTemplates.getTeacherCancellationTemplate(templateData);

    // Replace the header and some content to indicate it's a notification
    htmlContent = htmlContent.replace(
      'درس تم إلغاؤه - Lesson Cancelled',
      'الطالب ألغى الدرس - Student Cancelled Lesson'
    );
    htmlContent = htmlContent.replace(
      'تم إلغاء الدرس التالي بنجاح:',
      'قام الطالب بإلغاء الدرس التالي:'
    );
    htmlContent = htmlContent.replace(
      'The following lesson has been successfully cancelled:',
      'The student has cancelled the following lesson:'
    );

    const result = await emailService.sendEmail({
      to: teacherData.email,
      subject: 'الطالب ألغى الدرس - Student Cancelled Lesson - Allemni online',
      html: htmlContent
    });

    console.log('✅ Teacher notification email sent successfully');
    return result;
  } catch (error) {
    console.error('❌ Error sending teacher notification email:', error);
    throw error;
  }
};

/**
 * Send all appropriate cancellation emails based on who cancelled
 * @param {Object} bookingData - Booking information
 * @param {Object} teacherData - Teacher information
 * @param {Object} studentData - Student information
 * @param {string} cancelledBy - Who cancelled ('teacher' or 'student')
 * @param {string} cancellationReason - Reason for cancellation (optional)
 * @returns {Promise} Array of email send results
 */
const sendCancellationEmails = async (bookingData, teacherData, studentData, cancelledBy, cancellationReason = null) => {
  try {
    console.log(`📧 Sending cancellation emails for booking ${bookingData.id}, cancelled by: ${cancelledBy}`);
    
    const emailResults = [];

    if (cancelledBy === 'teacher') {
      // Teacher cancelled: send email to both teacher (confirmation) and student (notification)
      const teacherEmailPromise = sendTeacherCancellationEmail(bookingData, teacherData, studentData, cancellationReason);
      const studentEmailPromise = sendStudentNotificationEmail(bookingData, teacherData, studentData, cancellationReason);
      
      const results = await Promise.allSettled([teacherEmailPromise, studentEmailPromise]);
      emailResults.push(...results);
      
    } else if (cancelledBy === 'student') {
      // Student cancelled: send email to both student (confirmation) and teacher (notification)
      const studentEmailPromise = sendStudentCancellationEmail(bookingData, teacherData, studentData, cancellationReason);
      const teacherEmailPromise = sendTeacherNotificationEmail(bookingData, teacherData, studentData, cancellationReason);
      
      const results = await Promise.allSettled([studentEmailPromise, teacherEmailPromise]);
      emailResults.push(...results);
    }

    // Log results
    emailResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        console.log(`✅ Email ${index + 1} sent successfully`);
      } else {
        console.error(`❌ Email ${index + 1} failed:`, result.reason);
      }
    });

    return emailResults;
  } catch (error) {
    console.error('❌ Error in sendCancellationEmails:', error);
    throw error;
  }
};

module.exports = {
  sendTeacherCancellationEmail,
  sendStudentNotificationEmail,
  sendStudentCancellationEmail,
  sendTeacherNotificationEmail,
  sendCancellationEmails,
  formatBookingDate,
  formatBookingTime,
  parseTimezoneOffset,
  convertToUserTimezone
};
