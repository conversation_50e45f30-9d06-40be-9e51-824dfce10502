import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  Chip,
  TablePagination,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  IconButton,
  Snackbar
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';

const Teachers = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [teachers, setTeachers] = useState([]);
  const [selectedTeacher, setSelectedTeacher] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchText, setSearchText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [processing, setProcessing] = useState(false);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Function to fetch teachers
  const fetchTeachers = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/admin/teachers', {
        params: {
          page: page + 1,
          limit: rowsPerPage,
          search: searchQuery
        }
      });

      setTeachers(response.data.teachers);
      setTotalCount(response.data.total);
      setError('');
    } catch (err) {
      console.error('Error fetching teachers:', err);
      setError(t('admin.teachers.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch and when pagination changes
  useEffect(() => {
    fetchTeachers();
  }, [page, rowsPerPage, searchQuery, t]);

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
  };

  const handleSearch = () => {
    setSearchQuery(searchText);
    setPage(0);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleViewDetails = (teacher) => {
    setSelectedTeacher(teacher);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedTeacher(null);
  };

  // We don't need status change functionality for this simplified view

  const handleDeleteClick = (teacher) => {
    setSelectedTeacher(teacher);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      setProcessing(true);
      await axios.delete(`/admin/teachers/${selectedTeacher.id}`);
      setSnackbar({
        open: true,
        message: 'Teacher deleted successfully',
        severity: 'success'
      });
      const updatedTeachers = teachers.filter(teacher => teacher.id !== selectedTeacher.id);
      setTeachers(updatedTeachers);
      setDeleteDialogOpen(false);
      setSelectedTeacher(null);
    } catch (error) {
      console.error('Error deleting teacher:', error);
      setSnackbar({
        open: true,
        message: error.response?.data?.message || 'Error deleting teacher',
        severity: 'error'
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSelectedTeacher(null);
  };

  const handleCloseSnackbar = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  if (loading && page === 0) {
    return (
      <Layout title={t('admin.teachers.title')}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Layout>
    );
  }

  return (
    <Layout title={t('admin.teachers.title')}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                fullWidth
                label={t('admin.teachers.searchPlaceholder')}
                value={searchText}
                onChange={handleSearchChange}
                onKeyPress={handleKeyPress}
              />
              <Button
                variant="contained"
                onClick={handleSearch}
                sx={{ minWidth: '100px' }}
              >
                {t('common.search')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Teachers Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('admin.teachers.name')}</TableCell>
              <TableCell>{t('admin.teachers.email')}</TableCell>
              <TableCell>{t('admin.teachers.gender')}</TableCell>
              <TableCell>{t('admin.teachers.role')}</TableCell>
              <TableCell>{t('admin.teachers.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {teachers.map((teacher) => (
              <TableRow key={teacher.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Avatar src={teacher.profile_picture_url} alt={teacher.full_name}>
                      {teacher.full_name.charAt(0)}
                    </Avatar>
                    {teacher.full_name}
                  </Box>
                </TableCell>
                <TableCell>{teacher.email}</TableCell>
                <TableCell>{t(`gender.${teacher.gender}`)}</TableCell>
                <TableCell>
                  <Chip
                    label={t(`admin.teacherRole.${teacher.role}`)}
                    color={
                      teacher.role === 'platform_teacher' ? 'success' : 'primary'
                    }
                  />
                </TableCell>
                <TableCell>
                  <Button
                    variant="outlined"
                    onClick={() => handleViewDetails(teacher)}
                  >
                    {t('admin.teachers.viewDetails')}
                  </Button>
                  <IconButton
                    color="error"
                    onClick={() => handleDeleteClick(teacher)}
                    title="Delete teacher"
                    sx={{ ml: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handlePageChange}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleRowsPerPageChange}
          rowsPerPageOptions={[10, 25, 50]}
        />
      </TableContainer>

      {/* Teacher Details Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        {selectedTeacher && (
          <>
            <DialogTitle>
              {t('admin.teachers.teacherDetails')}
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Avatar
                          src={selectedTeacher.profile_picture_url}
                          alt={selectedTeacher.full_name}
                          sx={{ width: 80, height: 80, mr: 2 }}
                        >
                          {selectedTeacher.full_name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="h5">{selectedTeacher.full_name}</Typography>
                          <Typography color="text.secondary">{selectedTeacher.email}</Typography>
                        </Box>
                      </Box>

                      <Typography variant="h6" gutterBottom>
                        {t('admin.teachers.personalInfo')}
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Typography><strong>{t('admin.teachers.name')}:</strong> {selectedTeacher.full_name}</Typography>
                          <Typography><strong>{t('admin.teachers.email')}:</strong> {selectedTeacher.email}</Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography><strong>{t('admin.teachers.gender')}:</strong> {t(`gender.${selectedTeacher.gender}`)}</Typography>
                          <Typography><strong>{t('admin.teachers.role')}:</strong> {t(`admin.teacherRole.${selectedTeacher.role}`)}</Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>
                {t('common.close')}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={processing ? undefined : handleCloseDeleteDialog}>
        <DialogTitle>Delete Teacher</DialogTitle>
        <DialogContent>
          Are you sure you want to delete {selectedTeacher?.full_name}? This action cannot be undone.
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} disabled={processing}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            disabled={processing}
            startIcon={processing ? <CircularProgress size={20} /> : null}
          >
            {processing ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default Teachers;
