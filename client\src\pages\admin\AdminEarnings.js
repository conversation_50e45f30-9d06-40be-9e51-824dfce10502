import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  School as SchoolIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';

const AdminEarnings = () => {
  const { t } = useTranslation();
  const { token } = useAuth();
  const [earnings, setEarnings] = useState([]);
  const [summary, setSummary] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  useEffect(() => {
    fetchEarnings();
  }, [page, rowsPerPage]);

  const fetchEarnings = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: rowsPerPage
      };

      if (startDate && endDate) {
        params.startDate = startDate;
        params.endDate = endDate;
      }

      const { data } = await axios.get('/admin/earnings', {
        params,
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setEarnings(data.data.earnings);
        setSummary(data.data.summary);
        setTotalCount(data.data.pagination.total);
      }
    } catch (error) {
      console.error('Error fetching earnings:', error);
      setError(t('admin.earnings.errorFetching'));
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilter = () => {
    setPage(0);
    fetchEarnings();
  };

  const handleClearFilter = () => {
    setStartDate('');
    setEndDate('');
    setPage(0);
    fetchEarnings();
  };

  const formatCurrency = (amount) => {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
  };

  const formatPercentage = (rate) => {
    return `${(parseFloat(rate || 0) * 100).toFixed(1)}%`;
  };

  return (
    <Layout>
      <Container maxWidth="xl">
        <Typography variant="h4" gutterBottom>
          {t('admin.earnings.title')}
        </Typography>

        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <MoneyIcon color="primary" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      {t('admin.earnings.totalCommission')}
                    </Typography>
                    <Typography variant="h5">
                      {formatCurrency(summary.total_commission)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <SchoolIcon color="success" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      {t('admin.earnings.totalLessons')}
                    </Typography>
                    <Typography variant="h5">
                      {summary.total_lessons || 0}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUpIcon color="info" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      {t('admin.earnings.totalRevenue')}
                    </Typography>
                    <Typography variant="h5">
                      {formatCurrency(summary.total_lesson_amount)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <AssessmentIcon color="warning" sx={{ mr: 2 }} />
                  <Box>
                    <Typography color="textSecondary" gutterBottom>
                      {t('admin.earnings.avgCommissionRate')}
                    </Typography>
                    <Typography variant="h5">
                      {formatPercentage(summary.avg_commission_rate)}
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                type="date"
                label={t('admin.earnings.startDate')}
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                type="date"
                label={t('admin.earnings.endDate')}
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button variant="contained" onClick={handleFilter}>
                  {t('admin.earnings.filter')}
                </Button>
                <Button variant="outlined" onClick={handleClearFilter}>
                  {t('admin.earnings.clearFilter')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Earnings Table */}
        <Paper elevation={3}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ m: 2 }}>
              {error}
            </Alert>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('admin.earnings.date')}</TableCell>
                      <TableCell>{t('admin.earnings.meeting')}</TableCell>
                      <TableCell>{t('admin.earnings.teacher')}</TableCell>
                      <TableCell>{t('admin.earnings.student')}</TableCell>
                      <TableCell align="right">{t('admin.earnings.lessonAmount')}</TableCell>
                      <TableCell align="center">{t('admin.earnings.commissionRate')}</TableCell>
                      <TableCell align="right">{t('admin.earnings.commissionAmount')}</TableCell>
                      <TableCell align="right">{t('admin.earnings.teacherEarnings')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {earnings.map((earning) => (
                      <TableRow key={earning.id}>
                        <TableCell>
                          {format(new Date(earning.created_at), 'yyyy-MM-dd HH:mm')}
                        </TableCell>
                        <TableCell>{earning.meeting_name}</TableCell>
                        <TableCell>{earning.teacher_name}</TableCell>
                        <TableCell>{earning.student_name}</TableCell>
                        <TableCell align="right">
                          <Typography fontWeight="bold">
                            {formatCurrency(earning.lesson_amount)}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Chip 
                            label={formatPercentage(earning.commission_rate)}
                            color="primary"
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Typography fontWeight="bold" color="success.main">
                            {formatCurrency(earning.commission_amount)}
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Typography color="text.secondary">
                            {formatCurrency(earning.teacher_earnings)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25]}
                labelRowsPerPage={t('common.rowsPerPage')}
              />
            </>
          )}
        </Paper>
      </Container>
    </Layout>
  );
};

export default AdminEarnings;
