import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  List,
  ListItem,
  ListItemAvatar,
  Avatar,
  Typography,
  Divider,
  Badge,
  CircularProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import { formatDistanceToNow } from 'date-fns';
import { ar } from 'date-fns/locale';

const formatMessageTime = (timestamp) => {
  if (!timestamp) return 'غير معروف';

  try {
    let date;

    // Convert timestamp to Date object based on its type
    if (typeof timestamp === 'number') {
      // Unix timestamp (seconds)
      date = new Date(timestamp * 1000);
    } else if (typeof timestamp === 'string') {
      if (timestamp.includes('T') || timestamp.includes('Z')) {
        // ISO string or UTC string
        date = new Date(timestamp);
      } else {
        // MySQL datetime string - assume it's in UTC
        date = new Date(timestamp + 'Z');
      }
    } else {
      throw new Error('Invalid timestamp format');
    }

    // Validate date object
    if (isNaN(date.getTime())) {
      console.error('Invalid date object:', date, 'from timestamp:', timestamp);
      return 'غير معروف';
    }

    // Get local time components
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const timeStr = `${hours}:${minutes}`;

    // Get today's date at midnight in local timezone
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get yesterday's date at midnight in local timezone
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Format date based on when it was sent
    if (date >= today) {
      return `اليوم ${timeStr}`; // Today at HH:MM
    } else if (date >= yesterday) {
      return `أمس ${timeStr}`; // Yesterday at HH:MM
    } else {
      // Format date as DD/MM/YYYY HH:MM
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year} ${timeStr}`;
    }
  } catch (error) {
    console.error('Error formatting date:', error, 'for timestamp:', timestamp);
    return 'غير معروف';
  }
};

const ChatList = ({ chats, selectedChat, onChatSelect, loading, currentUser, onDeleteChat }) => {
  const { t } = useTranslation();
  const [, forceUpdate] = useState();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [chatToDelete, setChatToDelete] = useState(null);

  // Update times every minute
  useEffect(() => {
    const timer = setInterval(() => {
      forceUpdate({});
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const handleDeleteClick = (e, chat) => {
    e.stopPropagation(); // Prevent chat selection when clicking delete
    setChatToDelete(chat);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (chatToDelete && onDeleteChat) {
      onDeleteChat(chatToDelete.id);
    }
    setDeleteDialogOpen(false);
    setChatToDelete(null);
  };

  const handleCancelDelete = () => {
    setDeleteDialogOpen(false);
    setChatToDelete(null);
  };

  if (loading) {
    return (
      <Box component="div" sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!chats.length) {
    return (
      <Box component="div" sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 3 }}>
        <Typography component="div" color="textSecondary">{t('chat.noConversations')}</Typography>
      </Box>
    );
  }

  return (
    <Box component="div" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Typography component="div" variant="h6" sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        {t('chat.conversations')}
      </Typography>
      <List sx={{
        flexGrow: 1,
        overflow: 'auto',
        '& .MuiListItem-root': {
          '&:hover': {
            backgroundColor: 'action.hover'
          }
        }
      }}>
        {chats.map((chat) => (
          <React.Fragment key={chat.id}>
            <ListItem
              component="div"
              selected={selectedChat?.id === chat.id}
              onClick={() => onChatSelect(chat)}
              sx={{
                py: 2,
                cursor: 'pointer',
                backgroundColor: selectedChat?.id === chat.id ? 'action.selected' : 'transparent'
              }}
            >
              <ListItemAvatar>
                <Avatar src={chat.teacher_picture || chat.student_picture}>
                  {(chat.teacher_name?.[0] || chat.student_name?.[0] || 'U')}
                </Avatar>
              </ListItemAvatar>
              <Box component="div" sx={{ flex: 1, minWidth: 0 }}>
                <Box component="div" sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                  <Typography component="div" variant="body1" noWrap sx={{ flex: 1 }}>
                    {chat.teacher_name || chat.student_name || t('chat.unknownUser')}
                  </Typography>
                  <Typography component="div" variant="caption" color="textSecondary" sx={{ ml: 2, whiteSpace: 'nowrap' }}>
                    {formatMessageTime(chat.last_message_time || chat.created_at)}
                  </Typography>
                </Box>
                <Box component="div" sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography
                    component="div"
                    variant="body2"
                    color="textSecondary"
                    noWrap
                    sx={{ flex: 1, opacity: chat.unread_count > 0 ? 1 : 0.7 }}
                  >
                    {chat.last_message || t('chat.noMessages')}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {chat.unread_count > 0 && (
                      <Badge
                        badgeContent={chat.unread_count}
                        color="primary"
                        sx={{ mr: 1 }}
                      />
                    )}
                    <IconButton
                      size="small"
                      color="error"
                      onClick={(e) => handleDeleteClick(e, chat)}
                      sx={{
                        opacity: 0.7,
                        '&:hover': { opacity: 1 }
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              </Box>
            </ListItem>
            <Divider component="div" />
          </React.Fragment>
        ))}
      </List>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCancelDelete}
        aria-labelledby="delete-dialog-title"
      >
        <DialogTitle id="delete-dialog-title">
          {t('chat.deleteConfirmTitle')}
        </DialogTitle>
        <DialogContent>
          <Typography>
            {t('chat.deleteConfirmMessage', {
              name: chatToDelete?.teacher_name || chatToDelete?.student_name || t('chat.unknownUser')
            })}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete} color="primary">
            {t('common.cancel')}
          </Button>
          <Button onClick={handleConfirmDelete} color="error" autoFocus>
            {t('common.delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ChatList;
