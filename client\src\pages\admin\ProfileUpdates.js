import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Avatar,
  Card,
  CardContent,
  Tabs,
  Tab,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Check as ApproveIcon,
  Close as RejectIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout';

const ProfileUpdates = () => {
  const { t } = useTranslation();
  const [updates, setUpdates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedUpdate, setSelectedUpdate] = useState(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [adminNotes, setAdminNotes] = useState('');
  const [videoModalOpen, setVideoModalOpen] = useState(false);
  const [currentVideoUrl, setCurrentVideoUrl] = useState('');

  // تحميل طلبات التعديل
  const fetchUpdates = async () => {
    try {
      setLoading(true);
      setError(''); // مسح الأخطاء السابقة

      console.log('Fetching updates with filter:', statusFilter, 'page:', page + 1);

      const response = await axios.get('/api/admin/profile-updates', {
        params: {
          status: statusFilter,
          page: page + 1,
          limit: rowsPerPage
        }
      });

      if (response.data.success) {
        setUpdates(response.data.updates || []);
        setTotalItems(response.data.pagination?.totalItems || 0);
      } else {
        setError(response.data.message || 'حدث خطأ أثناء جلب طلبات التعديل');
      }
    } catch (error) {
      console.error('Error fetching profile updates:', error);
      setError('حدث خطأ أثناء جلب طلبات التعديل');
      setUpdates([]);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUpdates();
  }, [page, rowsPerPage, statusFilter]);

  // تحميل تفاصيل طلب التعديل
  const fetchUpdateDetails = async (id) => {
    try {
      const response = await axios.get(`/api/admin/profile-updates/${id}`);
      if (response.data.success) {
        setSelectedUpdate(response.data.update);
        setDetailsOpen(true);
      }
    } catch (error) {
      console.error('Error fetching update details:', error);
      setError('حدث خطأ أثناء جلب تفاصيل طلب التعديل');
    }
  };

  // فتح الفيديو في modal
  const handleOpenVideo = (videoUrl) => {
    setCurrentVideoUrl(videoUrl);
    setVideoModalOpen(true);
  };

  // الموافقة على طلب التعديل
  const handleApprove = async () => {
    try {
      setActionLoading(true);
      const response = await axios.post(`/api/admin/profile-updates/${selectedUpdate.id}/approve`, {
        adminNotes
      });

      if (response.data.success) {
        setDetailsOpen(false);
        setAdminNotes('');
        fetchUpdates();
        alert('تم الموافقة على طلب التعديل بنجاح');
      }
    } catch (error) {
      console.error('Error approving update:', error);
      setError('حدث خطأ أثناء الموافقة على طلب التعديل');
    } finally {
      setActionLoading(false);
    }
  };

  // رفض طلب التعديل
  const handleReject = async () => {
    try {
      setActionLoading(true);
      const response = await axios.post(`/api/admin/profile-updates/${selectedUpdate.id}/reject`, {
        adminNotes
      });

      if (response.data.success) {
        setDetailsOpen(false);
        setAdminNotes('');
        fetchUpdates();
        alert('تم رفض طلب التعديل بنجاح');
      }
    } catch (error) {
      console.error('Error rejecting update:', error);
      setError('حدث خطأ أثناء رفض طلب التعديل');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'approved': return 'success';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'pending': return 'قيد الانتظار';
      case 'approved': return 'موافق عليه';
      case 'rejected': return 'مرفوض';
      default: return status;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Layout>
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          إدارة تعديلات بيانات المعلمين
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* فلاتر الحالة */}
        <Paper sx={{ mb: 3 }}>
          <Tabs
            value={statusFilter}
            onChange={(e, newValue) => {
              console.log('Filter changed to:', newValue);
              setStatusFilter(newValue);
              setPage(0);
              setError(''); // مسح الأخطاء عند تغيير الفلتر
            }}
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="الكل" value="all" />
            <Tab label="قيد الانتظار" value="pending" />
            <Tab label="موافق عليها" value="approved" />
            <Tab label="مرفوضة" value="rejected" />
          </Tabs>
        </Paper>

        {/* جدول طلبات التعديل */}
        <Paper>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>اسم المعلم</TableCell>
                  <TableCell>البريد الإلكتروني</TableCell>
                  <TableCell>تاريخ الطلب</TableCell>
                  <TableCell>الحالة</TableCell>
                  <TableCell>الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : updates.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      لا توجد طلبات تعديل
                    </TableCell>
                  </TableRow>
                ) : (
                  updates.map((update) => (
                    <TableRow key={update.id}>
                      <TableCell>{update.full_name}</TableCell>
                      <TableCell>{update.email}</TableCell>
                      <TableCell>{formatDate(update.created_at)}</TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusText(update.status)}
                          color={getStatusColor(update.status)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Button
                          startIcon={<ViewIcon />}
                          onClick={() => fetchUpdateDetails(update.id)}
                          size="small"
                        >
                          عرض التفاصيل
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            component="div"
            count={totalItems}
            page={page}
            onPageChange={(e, newPage) => setPage(newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(e) => {
              setRowsPerPage(parseInt(e.target.value, 10));
              setPage(0);
            }}
            labelRowsPerPage="عدد الصفوف في الصفحة:"
            labelDisplayedRows={({ from, to, count }) =>
              `${from}-${to} من ${count !== -1 ? count : `أكثر من ${to}`}`
            }
          />
        </Paper>

        {/* نافذة تفاصيل طلب التعديل */}
        <Dialog
          open={detailsOpen}
          onClose={() => setDetailsOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            تفاصيل طلب تعديل البيانات
          </DialogTitle>
          <DialogContent>
            {selectedUpdate && (
              <Box sx={{ mt: 2 }}>
                {/* معلومات الطلب */}
                <Card sx={{ mb: 3 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      معلومات الطلب
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Typography><strong>تاريخ الطلب:</strong> {formatDate(selectedUpdate.created_at)}</Typography>
                      </Grid>
                      <Grid item xs={12} sm={6}>
                        <Typography><strong>حالة الطلب:</strong>
                          <Chip
                            label={
                              selectedUpdate.status === 'pending' ? 'قيد الانتظار' :
                              selectedUpdate.status === 'approved' ? 'موافق عليه' : 'مرفوض'
                            }
                            color={
                              selectedUpdate.status === 'pending' ? 'warning' :
                              selectedUpdate.status === 'approved' ? 'success' : 'error'
                            }
                            size="small"
                            sx={{ ml: 1 }}
                          />
                        </Typography>
                      </Grid>
                      {selectedUpdate.reviewed_at && (
                        <Grid item xs={12} sm={6}>
                          <Typography><strong>تاريخ المراجعة:</strong> {formatDate(selectedUpdate.reviewed_at)}</Typography>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </Card>

                <Grid container spacing={3}>
                  {/* البيانات الحالية */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom color="primary">
                          البيانات الحالية
                        </Typography>
                        <Typography><strong>الاسم:</strong> {selectedUpdate.current_full_name || 'غير متوفر'}</Typography>
                        <Typography><strong>البريد الإلكتروني:</strong> {selectedUpdate.current_email || 'غير متوفر'}</Typography>
                        <Typography><strong>الدولة:</strong> {selectedUpdate.current_country || 'غير متوفر'}</Typography>
                        <Typography><strong>المنطقة:</strong> {selectedUpdate.current_residence || 'غير متوفر'}</Typography>
                        <Typography><strong>المؤهلات:</strong> {selectedUpdate.current_qualifications || 'غير متوفر'}</Typography>
                        <Typography><strong>سنوات الخبرة:</strong> {selectedUpdate.current_teaching_experience || 'غير متوفر'}</Typography>
                        <Typography><strong>السيرة الذاتية:</strong> {selectedUpdate.current_cv ? 'متوفرة' : 'غير متوفرة'}</Typography>
                        {selectedUpdate.current_profile_picture_url && (
                          <Box sx={{ mt: 1 }}>
                            <Typography><strong>الصورة الشخصية الحالية:</strong></Typography>
                            <Avatar
                              src={`https://allemnionline.com${selectedUpdate.current_profile_picture_url}`}
                              sx={{ width: 60, height: 60, mt: 1 }}
                            />
                          </Box>
                        )}
                        {selectedUpdate.current_intro_video_url && (
                          <Box sx={{ mt: 1 }}>
                            <Typography><strong>فيديو التعريف الحالي:</strong></Typography>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => handleOpenVideo(selectedUpdate.current_intro_video_url)}
                              sx={{ mt: 1 }}
                            >
                              مشاهدة الفيديو
                            </Button>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* البيانات المطلوب تعديلها */}
                  <Grid item xs={12} md={6}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom color="secondary">
                          البيانات الجديدة المطلوبة
                        </Typography>
                        <Typography><strong>الاسم:</strong> {selectedUpdate.full_name || 'غير متوفر'}</Typography>
                        <Typography><strong>البريد الإلكتروني:</strong> {selectedUpdate.email || 'غير متوفر'}</Typography>
                        <Typography><strong>الدولة:</strong> {selectedUpdate.country || 'غير متوفر'}</Typography>
                        <Typography><strong>المنطقة:</strong> {selectedUpdate.residence || 'غير متوفر'}</Typography>
                        <Typography><strong>المؤهلات:</strong> {selectedUpdate.qualifications || 'غير متوفر'}</Typography>
                        <Typography><strong>سنوات الخبرة:</strong> {selectedUpdate.teaching_experience || 'غير متوفر'}</Typography>
                        <Typography><strong>السيرة الذاتية:</strong> {selectedUpdate.cv ? 'متوفرة' : 'غير متوفرة'}</Typography>
                        {selectedUpdate.profile_picture_url && (
                          <Box sx={{ mt: 1 }}>
                            <Typography><strong>الصورة الشخصية الجديدة:</strong></Typography>
                            <Avatar
                              src={`https://allemnionline.com${selectedUpdate.profile_picture_url}`}
                              sx={{ width: 60, height: 60, mt: 1 }}
                            />
                          </Box>
                        )}
                        {selectedUpdate.intro_video_url && (
                          <Box sx={{ mt: 1 }}>
                            <Typography><strong>فيديو التعريف الجديد:</strong></Typography>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => handleOpenVideo(selectedUpdate.intro_video_url)}
                              sx={{ mt: 1 }}
                            >
                              مشاهدة الفيديو
                            </Button>
                          </Box>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>

                  {/* ملاحظات الإدارة */}
                  {selectedUpdate.status === 'pending' && (
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        label="ملاحظات الإدارة"
                        value={adminNotes}
                        onChange={(e) => setAdminNotes(e.target.value)}
                        placeholder="اكتب ملاحظاتك هنا..."
                      />
                    </Grid>
                  )}

                  {/* عرض ملاحظات الإدارة السابقة */}
                  {selectedUpdate.admin_notes && (
                    <Grid item xs={12}>
                      <Alert severity="info">
                        <Typography variant="subtitle2">ملاحظات الإدارة:</Typography>
                        <Typography>{selectedUpdate.admin_notes}</Typography>
                      </Alert>
                    </Grid>
                  )}

                  {/* قسم السيرة الذاتية والمؤهلات */}
                  <Grid item xs={12}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          السيرة الذاتية والمؤهلات
                        </Typography>
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" color="primary">السيرة الذاتية الحالية:</Typography>
                            <Typography variant="body2" sx={{ mt: 1, p: 1, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                              {selectedUpdate.current_cv || 'غير متوفرة'}
                            </Typography>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" color="secondary">السيرة الذاتية الجديدة:</Typography>
                            <Typography variant="body2" sx={{ mt: 1, p: 1, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                              {selectedUpdate.cv || 'غير متوفرة'}
                            </Typography>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" color="primary">المؤهلات الحالية:</Typography>
                            <Typography variant="body2" sx={{ mt: 1, p: 1, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                              {selectedUpdate.current_qualifications || 'غير متوفرة'}
                            </Typography>
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" color="secondary">المؤهلات الجديدة:</Typography>
                            <Typography variant="body2" sx={{ mt: 1, p: 1, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
                              {selectedUpdate.qualifications || 'غير متوفرة'}
                            </Typography>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            {selectedUpdate && selectedUpdate.status === 'pending' && (
              <>
                <Button
                  onClick={handleApprove}
                  color="success"
                  variant="contained"
                  startIcon={<ApproveIcon />}
                  disabled={actionLoading}
                >
                  {actionLoading ? <CircularProgress size={20} /> : 'موافقة'}
                </Button>
                <Button
                  onClick={handleReject}
                  color="error"
                  variant="contained"
                  startIcon={<RejectIcon />}
                  disabled={actionLoading}
                >
                  {actionLoading ? <CircularProgress size={20} /> : 'رفض'}
                </Button>
              </>
            )}
            <Button onClick={() => setDetailsOpen(false)}>
              إغلاق
            </Button>
          </DialogActions>
        </Dialog>

        {/* نافذة عرض الفيديو */}
        <Dialog
          open={videoModalOpen}
          onClose={() => setVideoModalOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            عرض الفيديو
          </DialogTitle>
          <DialogContent>
            {currentVideoUrl && (
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <video
                  controls
                  style={{
                    width: '100%',
                    maxHeight: '400px',
                    borderRadius: '8px'
                  }}
                >
                  <source src={currentVideoUrl} type="video/mp4" />
                  <source src={currentVideoUrl} type="video/webm" />
                  <source src={currentVideoUrl} type="video/ogg" />
                  متصفحك لا يدعم عرض الفيديو
                </video>
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setVideoModalOpen(false)}
              variant="outlined"
              sx={{ mr: 1 }}
            >
              رجوع للتفاصيل
            </Button>
            <Button
              onClick={() => {
                setVideoModalOpen(false);
                setDetailsOpen(false);
              }}
              color="primary"
            >
              إغلاق الكل
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Layout>
  );
};

export default ProfileUpdates;
