import React, { useEffect, useState, useRef } from "react";
import {
  MicrophoneIcon as MicOnIcon,
  VideoCameraIcon as CamOnIcon,
  VideoCameraSlashIcon as CamOffIcon,
  ArrowUpOnSquareIcon as ScreenShareIcon,
  HandRaisedIcon as RaiseHandIcon,
  ChatBubbleLeftRightIcon as ChatIcon,
  UserGroupIcon as ParticipantsIcon,
  ClipboardDocumentIcon as NotesIcon,
} from "@heroicons/react/24/solid";
import MicOffIcon from "../icons/Bottombar/MicOffIcon";
import PipIcon from "../icons/Bottombar/PipIcon";
import { useMeeting, usePubSub } from "@videosdk.live/react-sdk";
import { useMeetingAppContext } from "../MeetingAppContext";
import classNames from "classnames";
import { useAuth } from "../../../contexts/AuthContext";
import {
  convertFromDatabaseTime,
  getCurrentTimeInTimezone,
} from "../../../utils/timezone";

// A lightweight, no-tooltip button used inside this bar
const BarButton = ({ onClick, active = false, badge, children, title }) => (
  <button
    type="button"
    title={title}
    onClick={onClick}
    className={classNames(
      "relative flex items-center justify-center w-10 h-10 mx-1 rounded-md transition-colors focus:outline-none",
      active ? "bg-white text-black" : "bg-gray-700 text-white hover:bg-gray-600"
    )}
  >
    {children}
    {badge && (
      <span className="absolute -top-1 -right-1 bg-red-500 text-xs text-white rounded-full px-1">
        {badge}
      </span>
    )}
  </button>
);



export default function SimpleBottomBar({ onClose, setIsMeetingLeft, meetingData }) {
  const {
    toggleMic,
    toggleWebcam,
    toggleScreenShare,
    changeMic,
    changeWebcam,
    localMicOn,
    localWebcamOn,
    localScreenShareOn,
    leave,
    participants,
  } = useMeeting();
  const { sideBarMode, setSideBarMode, pipMode, setPipMode } = useMeetingAppContext();
  const { publish: publishRaiseHand } = usePubSub("RAISE_HAND");
  const [unread, setUnread] = useState(0);

  // PiP handling
  const pipWindowRef = useRef(null);
  const handleTogglePip = async () => {
    // If already in PiP, exit it
    if (pipWindowRef.current) {
      await document.exitPictureInPicture();
      pipWindowRef.current = null;
      return;
    }

    if (!("pictureInPictureEnabled" in document)) {
      alert("Picture-in-Picture not supported in this browser.");
      return;
    }

    // Create canvas to draw participants' videos
    const source = document.createElement("canvas");
    source.width = 1280;
    source.height = 720;
    const ctx = source.getContext("2d");

    // Create hidden video element that will be popped out
    const pipVideo = document.createElement("video");
    pipVideo.autoplay = true;
    pipWindowRef.current = pipVideo;

    // Feed canvas stream to this video
    const stream = source.captureStream();
    pipVideo.srcObject = stream;

    // Helper to continuously draw all <video> tags on canvas
    const drawCanvas = () => {
      const videos = document.querySelectorAll("video");
      try {
        ctx.fillStyle = "black";
        ctx.fillRect(0, 0, source.width, source.height);

        const length = videos.length;
        const rows = length > 2 ? 2 : length > 0 ? 1 : 0;
        const cols = length < 2 ? 1 : length < 5 ? 2 : 3;
        const w = source.width / cols;
        const h = source.height / rows;

        videos.forEach((vid, idx) => {
          const r = Math.floor(idx / cols);
          const c = idx % cols;
          ctx.drawImage(vid, c * w, r * h, w, h);
        });

        if (pipWindowRef.current) requestAnimationFrame(drawCanvas);
      } catch (_) {
        // ignore errors while drawing
      }
    };

    // Start drawing and request PiP when ready
    drawCanvas();
    pipVideo.onloadedmetadata = () => {
      pipVideo.requestPictureInPicture();
    };
    await pipVideo.play();

    pipVideo.addEventListener("enterpictureinpicture", () => {
      setPipMode && setPipMode(true);
      drawCanvas();
    });

    pipVideo.addEventListener("leavepictureinpicture", () => {
      pipWindowRef.current = null;
      setPipMode && setPipMode(false);
      pipVideo.srcObject.getTracks().forEach((t) => t.stop());
    });
  };

  // Listen to chat messages for unread count
  usePubSub("CHAT", {
    onMessageReceived: () => {
      if (sideBarMode !== "CHAT") setUnread((c) => c + 1);
    },
  });
    const { currentUser } = useAuth();

  const [remainingMs, setRemainingMs] = useState(null);
  const [participantTz, setParticipantTz] = useState(null);

  // Determine participant timezone when meetingData available
  useEffect(() => {
    if (!meetingData) return;
    const role = currentUser?.role;
    const tz =
      role === "teacher" || role === "platform_teacher" || role === "new_teacher"
        ? meetingData.teacher_timezone || meetingData.timezone || null
        : meetingData.student_timezone || meetingData.timezone || null;
    setParticipantTz(tz);
  }, [meetingData, currentUser]);

  // Setup countdown when meetingData ready (timezone optional)
  useEffect(() => {
    if (!meetingData || !meetingData.duration) return;

    const dbDateTime = meetingData.meeting_date || meetingData.datetime;
    if (!dbDateTime) return;

    // Convert DB datetime to participant timezone if available, else fallback to UTC
    let meetingStart;
    try {
      if (participantTz) {
        meetingStart = convertFromDatabaseTime(dbDateTime, participantTz);
      } else {
        // Assume DB datetime stored as UTC
        if (dbDateTime.includes("T") || dbDateTime.includes("Z")) {
          meetingStart = new Date(dbDateTime);
        } else {
          meetingStart = new Date(dbDateTime.replace(" ", "T") + "Z");
        }
      }
    } catch (e) {
      meetingStart = new Date(dbDateTime);
    }

    const endTs = meetingStart.getTime() + meetingData.duration * 60 * 1000;

    const compute = () => {
      const now = participantTz ? getCurrentTimeInTimezone(participantTz).getTime() : Date.now();
      const diff = endTs - now;
      return diff;
    };

    // initial value
    const initial = compute();
    if (initial <= 0) {
      setRemainingMs(0);
      // immediately leave if already expired
      (async () => {
        try {
          await leave();
        } catch (err) {
          console.error("leave() error", err);
        }
        if (setIsMeetingLeft) setIsMeetingLeft(true);
        if (onClose) onClose();
      })();
      return;
    }
    setRemainingMs(initial);

    const handleEndMeeting = async () => {
      try {
        await leave();
      } catch (err) {
        console.error("leave() error", err);
      }
      if (setIsMeetingLeft) setIsMeetingLeft(true);
      if (onClose) onClose();
    };

    const id = setInterval(() => {
      const diff = compute();
      if (diff <= 0) {
        clearInterval(id);
        setRemainingMs(0);
        handleEndMeeting();
      } else {
        setRemainingMs(diff);
      }
    }, 1000);

    return () => clearInterval(id);
  }, [meetingData, participantTz]);

  const formatTime = (ms) => {
    if (ms === null) return "--:--";
    const total = Math.floor(ms / 1000);
    const m = String(Math.floor(total / 60)).padStart(2, "0");
    const s = String(total % 60).padStart(2, "0");
    return `${m}:${s}`;
  };

  return (
    <div className="flex items-center justify-center bg-gray-800 py-2 px-3 select-none text-sm">
      {/* Timer */}
      <span className="text-white font-mono mr-3">{formatTime(remainingMs)}</span>

      {/* Mic */}
      <BarButton
        onClick={async () => {
          try {
            await toggleMic();
          } catch (err) {
            console.error("toggleMic error", err);
            try {
              const devices = await navigator.mediaDevices.enumerateDevices();
              const mic = devices.find((d) => d.kind === "audioinput");
              if (mic && changeMic) {
                await changeMic(mic.deviceId);
                await toggleMic();
              }
            } catch (e) {
              console.error("toggleMic fallback error", e);
            }
          }
        }}
        active={localMicOn}
        title={localMicOn ? "Mute Mic" : "Unmute Mic"}
      >
        {localMicOn ? (
          <MicOnIcon className="h-5 w-5" />
        ) : (
          <MicOffIcon className="h-5 w-5" />
        )}
      </BarButton>

      {/* Cam */}
      <BarButton
        onClick={async () => {
          try {
            await toggleWebcam();
          } catch (err) {
            console.error("toggleWebcam error", err);
            try {
              const devices = await navigator.mediaDevices.enumerateDevices();
              const cam = devices.find((d) => d.kind === "videoinput");
              if (cam && changeWebcam) {
                await changeWebcam(cam.deviceId);
                await toggleWebcam();
              }
            } catch (e) {
              console.error("toggleWebcam fallback error", e);
            }
          }
        }}
        active={localWebcamOn}
        title={localWebcamOn ? "Turn off Camera" : "Turn on Camera"}
      >
        {localWebcamOn ? <CamOnIcon className="h-5 w-5" /> : <CamOffIcon className="h-5 w-5" />}
      </BarButton>

      {/* Screen Share */}
      <BarButton
        onClick={async () => {
          try {
            await toggleScreenShare();
          } catch (err) {
            console.error("toggleScreenShare error", err);
          }
        }}
        active={localScreenShareOn}
        title={localScreenShareOn ? "Stop Share" : "Share Screen"}
      >
        <ScreenShareIcon className="h-5 w-5" />
      </BarButton>

      {/* PiP */}
      <BarButton
        onClick={handleTogglePip}
        active={pipMode}
        title={pipMode ? "Exit PiP" : "Picture-in-Picture"}
      >
        <PipIcon className="h-5 w-5" />
      </BarButton>

      {/* Raise Hand */}
      <BarButton onClick={() => publishRaiseHand("raise") } title="Raise Hand">
        <RaiseHandIcon className="h-5 w-5" />
      </BarButton>

      {/* Chat */}
      <BarButton
        onClick={() => {
          setSideBarMode((m) => (m === "CHAT" ? null : "CHAT"));
          setUnread(0);
        }}
        active={sideBarMode === "CHAT"}
        badge={unread > 0 ? unread : undefined}
        title="Chat"
      >
        <ChatIcon className="h-5 w-5" />
      </BarButton>

      {/* Notes */}
      <BarButton
        onClick={() => setSideBarMode((m) => (m === "NOTES" ? null : "NOTES"))}
        active={sideBarMode === "NOTES"}
        title="Notes"
      >
        <NotesIcon className="h-5 w-5" />
      </BarButton>

      {/* Participants */}
      <BarButton
        onClick={() => setSideBarMode((m) => (m === "PARTICIPANTS" ? null : "PARTICIPANTS"))}
        active={sideBarMode === "PARTICIPANTS"}
        badge={participants ? new Map(participants).size : 0}
        title="Participants"
      >
        <ParticipantsIcon className="h-5 w-5" />
      </BarButton>

      {/* Leave */}
      <button
        type="button"
        onClick={() => {
          leave();
          setIsMeetingLeft && setIsMeetingLeft(true);
          onClose && onClose();
        }}
        className="ml-3 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-500 transition-colors"
      >
        Leave
      </button>
    </div>
  );
}
