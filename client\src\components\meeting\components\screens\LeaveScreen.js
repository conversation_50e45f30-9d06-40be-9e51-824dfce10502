export function LeaveScreen({ setIsMeetingLeft, onClose }) {
  return (
    <div className="bg-gray-800 h-screen flex flex-col flex-1 items-center justify-center">
      <h1 className="text-white text-4xl">تم ترك الميتنج!</h1>
      <div className="mt-12 flex gap-4">
        <button
          className="bg-purple-350 text-white px-16 py-3 rounded-lg text-sm"
          onClick={() => {
            setIsMeetingLeft(false);
          }}
        >
          الانضمام مرة أخرى
        </button>
        {onClose && (
          <button
            className="bg-gray-600 text-white px-16 py-3 rounded-lg text-sm"
            onClick={onClose}
          >
            إغلاق الميتنج
          </button>
        )}
      </div>
    </div>
  );
}

export default LeaveScreen;
