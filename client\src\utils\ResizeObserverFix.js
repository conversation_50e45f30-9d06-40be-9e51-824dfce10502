import { useEffect } from 'react';

/**
 * Componente para manejar y suprimir el error de ResizeObserver
 * "ResizeObserver loop completed with undelivered notifications"
 */
const ResizeObserverFix = () => {
  useEffect(() => {
    // Guardar la implementación original de console.error
    const originalConsoleError = console.error;
    
    // Reemplazar console.error con una versión que filtra el error específico
    console.error = (...args) => {
      // No mostrar el error de ResizeObserver loop
      if (
        args.length > 0 &&
        typeof args[0] === 'string' &&
        args[0].includes('ResizeObserver loop completed with undelivered notifications')
      ) {
        return;
      }
      
      // Para todos los demás errores, usar la implementación original
      originalConsoleError.apply(console, args);
    };
    
    // Limpiar al desmontar el componente
    return () => {
      console.error = originalConsoleError;
    };
  }, []);
  
  return null;
};

export default ResizeObserverFix;
