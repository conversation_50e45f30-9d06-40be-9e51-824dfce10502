import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { useAuth } from '../../contexts/AuthContext';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  useTheme,
  alpha,
  Fade,
  Alert,
  CircularProgress,
  Stack,
  Divider
} from '@mui/material';
import {
  Email as EmailIcon,
  Verified as VerifiedIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';

const VerifyEmail = () => {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [resendLoading, setResendLoading] = useState(false);
  const [resendMessage, setResendMessage] = useState('');
  const [countdown, setCountdown] = useState(0); // remaining seconds

  const navigate = useNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const { login } = useAuth();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';

  const email = location.state?.email || '';

  useEffect(() => {
    if (!email) {
      navigate('/login');
    }
  }, [email, navigate]);

  // Initialize countdown based on stored expiry or start new 3-minute window
  useEffect(() => {
    if (!email) return;
    const key = `verificationExpiry_${email}`;
    const storedExpiry = localStorage.getItem(key);
    let diff = 0;
    if (storedExpiry) {
      diff = Math.floor((parseInt(storedExpiry, 10) - Date.now()) / 1000);
    }
    if (diff > 0) {
      setCountdown(diff);
    } else {
      const newExpiry = Date.now() + 10 * 60 * 1000;
      localStorage.setItem(key, newExpiry.toString());
      setCountdown(600);
    }
  }, [email]);

  // Handle countdown timer
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (!code || code.length !== 6) {
      setError(t('auth.invalidCode'));
      setLoading(false);
      return;
    }

    try {
      const response = await axios.post('/api/auth/verify-email', { 
        email, 
        code 
      });
      
      if (response.data.success) {
        // Login user with the token
        await login(response.data.token, response.data.user);
        
        // Navigate based on user role
        if (response.data.user.role === 'student') {
          navigate('/student/complete-profile');
        } else if (response.data.user.role === 'new_teacher') {
          navigate('/teacher/application');
        } else {
          navigate('/dashboard');
        }
      }
    } catch (error) {
      setError(error.response?.data?.message || t('auth.verificationFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    setResendLoading(true);
    setResendMessage('');
    setError('');

    try {
      const response = await axios.post('/api/auth/resend-verification', { 
        email 
      });
      
      if (response.data.success) {
        setResendMessage(t('auth.verificationCodeSent'));
        const key = `verificationExpiry_${email}`;
        const newExpiry = Date.now() + 10 * 60 * 1000;
        localStorage.setItem(key, newExpiry.toString());
        setCountdown(180); // reset to 3-minute countdown
      }
    } catch (error) {
      setError(error.response?.data?.message || t('auth.resendFailed'));
    } finally {
      setResendLoading(false);
    }
  };

  const handleCodeChange = (e) => {
    const value = e.target.value.replace(/\D/g, ''); // Only digits
    if (value.length <= 6) {
      setCode(value);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        py: 8,
        background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.light, 0.1)})`,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: 'url("https://png.pngtree.com/background/20230216/original/pngtree-islamic-background-picture-image_2027687.jpg")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          opacity: 0.1,
          zIndex: -1
        }
      }}
    >
      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 1 }}>
        <Fade in timeout={800}>
          <Paper
            elevation={24}
            sx={{
              p: { xs: 3, sm: 6 },
              background: alpha('#fff', 0.98),
              backdropFilter: 'blur(20px)',
              borderRadius: 4,
              position: 'relative',
              overflow: 'hidden',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              boxShadow: `0 8px 32px ${alpha(theme.palette.primary.dark, 0.2)}`
            }}
          >
            {/* Header */}
            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <Box
                sx={{
                  mx: 'auto',
                  mb: 3,
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`
                }}
              >
                <EmailIcon sx={{ fontSize: 40, color: 'white' }} />
              </Box>

              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  mb: 2,
                  background: `linear-gradient(120deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                {t('auth.verifyEmail')}
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  color: 'text.secondary',
                  mb: 1
                }}
              >
                {t('auth.verificationCodeSentTo')}
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  color: theme.palette.primary.main,
                  fontWeight: 600,
                  fontSize: '1.1rem'
                }}
              >
                {email}
              </Typography>
            </Box>
            {/* Form */}
            <Box component="form" onSubmit={handleSubmit}>
              <Stack spacing={3}>
                {/* Verification Code Input */}
                <Box>
                  <Typography
                    variant="body2"
                    sx={{
                      mb: 2,
                      color: 'text.secondary',
                      fontWeight: 500,
                      textAlign: 'center'
                    }}
                  >
                    {t('auth.verificationCode')}
                  </Typography>
                  <TextField
                    fullWidth
                    value={code}
                    onChange={handleCodeChange}
                    placeholder="000000"
                    inputProps={{
                      maxLength: 6,
                      style: {
                        textAlign: 'center',
                        fontSize: '2rem',
                        fontFamily: 'monospace',
                        letterSpacing: '0.5rem',
                        fontWeight: 'bold'
                      }
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 3,
                        bgcolor: alpha(theme.palette.background.paper, 0.8),
                        '& fieldset': {
                          borderColor: alpha(theme.palette.primary.main, 0.3),
                          borderWidth: 2
                        },
                        '&:hover fieldset': {
                          borderColor: alpha(theme.palette.primary.main, 0.5)
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: theme.palette.primary.main,
                          borderWidth: 2
                        }
                      }
                    }}
                  />
                </Box>

                {/* Error Message */}
                {error && (
                  <Alert
                    severity="error"
                    sx={{
                      borderRadius: 2,
                      boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.15)}`
                    }}
                  >
                    {error}
                  </Alert>
                )}

                {/* Success Message */}
                {resendMessage && (
                  <Alert
                    severity="success"
                    sx={{
                      borderRadius: 2,
                      boxShadow: `0 4px 12px ${alpha(theme.palette.success.main, 0.15)}`
                    }}
                  >
                    {resendMessage}
                  </Alert>
                )}

                {/* Verify Button */}
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading || code.length !== 6}
                  sx={{
                    py: 1.8,
                    fontSize: '1.1rem',
                    fontWeight: 600,
                    borderRadius: 3,
                    textTransform: 'none',
                    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                    boxShadow: `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`,
                    '&:hover': {
                      background: `linear-gradient(90deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
                      boxShadow: `0 12px 32px ${alpha(theme.palette.primary.main, 0.4)}`,
                      transform: 'translateY(-2px)'
                    },
                    '&:disabled': {
                      opacity: 0.6,
                      transform: 'none'
                    },
                    transition: 'all 0.3s ease-in-out'
                  }}
                >
                  {loading ? (
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <CircularProgress size={20} color="inherit" />
                      <Typography>{t('auth.verifying')}</Typography>
                    </Stack>
                  ) : (
                    t('auth.verify')
                  )}
                </Button>

                {/* Divider */}
                <Divider sx={{ my: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    {t('auth.or')}
                  </Typography>
                </Divider>

                {/* Resend Code Button */}
                <Button
                  variant="outlined"
                  fullWidth
                  onClick={handleResendCode}
                  disabled={resendLoading || countdown > 0}
                  sx={{
                    py: 1.5,
                    borderRadius: 3,
                    textTransform: 'none',
                    fontWeight: 600,
                    borderColor: alpha(theme.palette.primary.main, 0.3),
                    color: theme.palette.primary.main,
                    '&:hover': {
                      borderColor: theme.palette.primary.main,
                      bgcolor: alpha(theme.palette.primary.main, 0.05)
                    }
                  }}
                >
                  {countdown > 0 ? (
                    `${t('auth.resendCodeIn')} ${countdown}s`
                  ) : resendLoading ? (
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <CircularProgress size={16} color="inherit" />
                      <Typography>{t('auth.sending')}</Typography>
                    </Stack>
                  ) : (
                    t('auth.resendCode')
                  )}
                </Button>

                {/* Back to Login */}
                <Box sx={{ textAlign: 'center', mt: 3 }}>
                  <Button
                    component={Link}
                    to="/login"
                    startIcon={<ArrowBackIcon />}
                    sx={{
                      color: 'text.secondary',
                      textTransform: 'none',
                      fontWeight: 500,
                      '&:hover': {
                        color: theme.palette.primary.main,
                        bgcolor: 'transparent'
                      }
                    }}
                  >
                    {t('auth.backToLogin')}
                  </Button>
                </Box>
              </Stack>
            </Box>
          </Paper>
        </Fade>
      </Container>
    </Box>
  );
};

export default VerifyEmail;
