import moment from 'moment-timezone';

/**
 * Parse timezone string (e.g., "UTC+03:00") to get offset in minutes
 * @param {string} timezoneString - Timezone string like "UTC+03:00"
 * @returns {number} - Offset in minutes
 */
export const parseTimezoneOffset = (timezoneString) => {
  if (!timezoneString || typeof timezoneString !== 'string') {
    return 0;
  }

  // Handle UTC format (e.g., "UTC+03:00", "UTC-05:00")
  const match = timezoneString.match(/UTC([+-])(\d{1,2}):?(\d{2})?/);
  if (match) {
    const sign = match[1] === '+' ? 1 : -1;
    const hours = parseInt(match[2], 10);
    const minutes = parseInt(match[3] || '0', 10);
    return sign * (hours * 60 + minutes);
  }

  return 0;
};

/**
 * Convert a date from local time to a specific timezone
 * @param {Date|string} date - Date to convert
 * @param {string} timezone - Target timezone (e.g., "UTC+03:00")
 * @returns {Date} - Converted date
 */
export const convertToTimezone = (date, timezone) => {
  if (!date || !timezone) {
    return new Date(date);
  }

  const offsetMinutes = parseTimezoneOffset(timezone);
  const dateObj = new Date(date);
  
  // Get the current timezone offset
  const localOffset = dateObj.getTimezoneOffset();
  
  // Calculate the difference and apply it
  const targetOffset = -offsetMinutes; // Negative because getTimezoneOffset returns negative for positive timezones
  const diff = targetOffset - localOffset;
  
  return new Date(dateObj.getTime() + (diff * 60 * 1000));
};

/**
 * Convert a date from a specific timezone to local time
 * @param {Date|string} date - Date to convert
 * @param {string} timezone - Source timezone (e.g., "UTC+03:00")
 * @returns {Date} - Converted date
 */
export const convertFromTimezone = (date, timezone) => {
  if (!date || !timezone) {
    return new Date(date);
  }

  const offsetMinutes = parseTimezoneOffset(timezone);
  const dateObj = new Date(date);
  
  // Get the current timezone offset
  const localOffset = dateObj.getTimezoneOffset();
  
  // Calculate the difference and apply it (reverse of convertToTimezone)
  const sourceOffset = -offsetMinutes;
  const diff = localOffset - sourceOffset;
  
  return new Date(dateObj.getTime() + (diff * 60 * 1000));
};

/**
 * Format date in a specific timezone
 * @param {Date|string} date - Date to format
 * @param {string} timezone - Timezone (e.g., "UTC+03:00")
 * @param {string} format - Format string (default: 'YYYY-MM-DD HH:mm:ss')
 * @returns {string} - Formatted date string
 */
export const formatInTimezone = (date, timezone, format = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date) {
    return '';
  }

  const convertedDate = convertFromTimezone(date, timezone);
  return moment(convertedDate).format(format);
};

/**
 * Get current date/time in a specific timezone
 * @param {string} timezone - Timezone (e.g., "UTC+03:00")
 * @returns {Date} - Current date in the specified timezone
 */
export const getCurrentTimeInTimezone = (timezone) => {
  if (!timezone) {
    return new Date();
  }

  // Get current UTC time
  const utcMoment = moment.utc();

  // Get student's timezone offset in minutes
  const offsetMinutes = parseTimezoneOffset(timezone);

  // Add the offset to UTC time to get student's local time
  const studentMoment = utcMoment.clone().add(offsetMinutes, 'minutes');

  // Create a Date object that when displayed will show the student's time
  // We need to adjust for the browser's timezone to make it display correctly
  const browserOffsetMinutes = new Date().getTimezoneOffset();
  const adjustedMoment = studentMoment.clone().subtract(browserOffsetMinutes, 'minutes');

  return adjustedMoment.toDate();
};

/**
 * Convert booking datetime to UTC for database storage
 * @param {string} dateStr - Date string (YYYY-MM-DD)
 * @param {string} timeStr - Time string (HH:mm)
 * @param {string} studentTimezone - Student's timezone (e.g., "UTC+03:00")
 * @returns {string} - Formatted datetime string for server (in UTC)
 */
export const convertBookingDateTime = (dateStr, timeStr, studentTimezone) => {
  if (!dateStr || !timeStr || !studentTimezone) {
    return `${dateStr} ${timeStr}:00`;
  }

  // Create datetime string
  const dateTimeStr = `${dateStr} ${timeStr}:00`;

  // Convert UTC offset string to moment timezone format
  const offsetMinutes = parseTimezoneOffset(studentTimezone);
  const offsetHours = offsetMinutes / 60;
  const sign = offsetHours >= 0 ? '+' : '-';
  const absHours = Math.abs(offsetHours);
  const hours = Math.floor(absHours);
  const minutes = Math.round((absHours - hours) * 60);
  const momentTimezone = `${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;

  // Parse the datetime in student's timezone and convert to UTC
  const studentMoment = moment(dateTimeStr, 'YYYY-MM-DD HH:mm:ss').utcOffset(momentTimezone);
  const utcMoment = studentMoment.utc();

  console.log('Converting booking datetime:', {
    original: dateTimeStr,
    timezone: studentTimezone,
    offsetMinutes,
    momentTimezone,
    studentMoment: studentMoment.format(),
    utcMoment: utcMoment.format(),
    formatted: utcMoment.format('YYYY-MM-DD HH:mm:ss')
  });

  // Format for database (UTC)
  return utcMoment.format('YYYY-MM-DD HH:mm:ss');
};

/**
 * Convert database datetime to student's timezone for display
 * @param {string} dbDateTime - Database datetime string
 * @param {string} studentTimezone - Student's timezone (e.g., "UTC+03:00")
 * @returns {Date} - Date object that represents the time in student's timezone
 */
export const convertFromDatabaseTime = (dbDateTime, studentTimezone) => {
  if (!dbDateTime || !studentTimezone) {
    return new Date(dbDateTime);
  }

  // Parse the database datetime as UTC
  let utcMoment;
  if (dbDateTime.includes('T') || dbDateTime.includes('Z')) {
    utcMoment = moment.utc(dbDateTime);
  } else {
    // MySQL datetime format - parse as UTC
    utcMoment = moment.utc(dbDateTime, 'YYYY-MM-DD HH:mm:ss');
  }

  // Get student's timezone offset in minutes
  const offsetMinutes = parseTimezoneOffset(studentTimezone);

  // Add the offset to UTC time to get student's local time
  const studentMoment = utcMoment.clone().add(offsetMinutes, 'minutes');

  // Create a Date object that when displayed will show the student's time
  // We need to adjust for the browser's timezone to make it display correctly
  const browserOffsetMinutes = new Date().getTimezoneOffset();
  const adjustedMoment = studentMoment.clone().subtract(browserOffsetMinutes, 'minutes');

  return adjustedMoment.toDate();
};

/**
 * Format a date in student's timezone without browser interference
 * @param {Date|string} date - Date to format
 * @param {string} studentTimezone - Student's timezone (e.g., "UTC+03:00")
 * @param {string} formatString - Format string for moment.js (default: 'YYYY-MM-DD HH:mm:ss')
 * @returns {string} - Formatted date string in student's timezone
 */
export const formatDateInStudentTimezone = (date, studentTimezone, formatString = 'YYYY-MM-DD HH:mm:ss') => {
  if (!date || !studentTimezone) {
    return moment(date).format(formatString);
  }

  // Parse the date as UTC first
  let utcMoment;
  if (typeof date === 'string') {
    if (date.includes('T') || date.includes('Z')) {
      utcMoment = moment.utc(date);
    } else {
      utcMoment = moment.utc(date, 'YYYY-MM-DD HH:mm:ss');
    }
  } else {
    // If it's a Date object, convert to UTC moment
    utcMoment = moment.utc(date);
  }

  // Get student's timezone offset in minutes
  const offsetMinutes = parseTimezoneOffset(studentTimezone);

  // Add the offset to get student's local time
  const studentMoment = utcMoment.clone().add(offsetMinutes, 'minutes');

  return studentMoment.format(formatString);
};
