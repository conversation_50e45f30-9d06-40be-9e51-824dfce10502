const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');
const config = require('../config/db.config');

const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      throw new Error();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const connection = await mysql.createConnection(config);
    
    const [rows] = await connection.execute(
      'SELECT id, full_name, email, role, status, delete_scheduled_at FROM users WHERE id = ?',
      [decoded.id]
    );

    await connection.end();

    if (!rows.length) {
      throw new Error();
    }

    const user = rows[0];

    // Check if user account is deleted or pending deletion
    if (user.status === 'deleted') {
      return res.status(401).json({
        message: 'تم حذف هذا الحساب',
        message_en: 'Account has been deleted',
        accountStatus: 'deleted',
        requiresLogout: true
      });
    }

    if (user.status === 'pending_deletion') {
      return res.status(401).json({
        message: 'هذا الحساب مجدول للحذف',
        message_en: 'Account is scheduled for deletion',
        accountStatus: 'pending_deletion',
        deleteScheduledAt: user.delete_scheduled_at,
        requiresLogout: true
      });
    }

    req.user = user;
    req.token = token;
    next();
  } catch (error) {
    res.status(401).json({ message: 'Please authenticate' });
  }
};

const authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ 
        message: 'You do not have permission to perform this action' 
      });
    }
    next();
  };
};

module.exports = { auth, authorize };
