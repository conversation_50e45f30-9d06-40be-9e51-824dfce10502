import axios from 'axios';

/**
 * معالج حالة المستخدم - للتعامل مع الحسابات المحذوفة أو المجدولة للحذف
 */
class UserStatusHandler {
  static handleStatusError(error, navigate, logout) {
    if (error.response && error.response.data) {
      const { accountStatus, requiresLogout, message, deleteScheduledAt } = error.response.data;

      // إذا كان الحساب محذوف أو مجدول للحذف
      if (accountStatus === 'deleted' || accountStatus === 'pending_deletion') {
        if (requiresLogout) {
          // حفظ رسالة الحالة في localStorage
          localStorage.setItem('accountStatusMessage', JSON.stringify({
            message: message,
            message_en: error.response.data.message_en || message,
            accountStatus: accountStatus,
            deleteScheduledAt: deleteScheduledAt
          }));

          // تسجيل الخروج وإعادة التوجيه لصفحة تسجيل الدخول
          logout();
          navigate('/login');
          return true; // تم التعامل مع الخطأ
        }
      }
    }
    return false; // لم يتم التعامل مع الخطأ
  }

  /**
   * إضافة interceptor لـ axios للتعامل مع أخطاء حالة المستخدم تلقائياً
   */
  static setupAxiosInterceptor(navigate, logout) {
    axios.interceptors.response.use(
      (response) => response,
      (error) => {
        // التحقق من أخطاء حالة المستخدم
        if (this.handleStatusError(error, navigate, logout)) {
          return Promise.reject(error);
        }
        
        // إذا لم يكن خطأ حالة مستخدم، تمرير الخطأ كما هو
        return Promise.reject(error);
      }
    );
  }

  /**
   * التحقق من حالة المستخدم قبل تحميل الصفحة
   */
  static async checkUserStatus() {
    try {
      const token = localStorage.getItem('token');
      if (!token) return { valid: false, reason: 'no_token' };

      // استخدام /auth/verify بدلاً من /users/profile للحصول على معلومات المستخدم مع الحالة
      const response = await axios.get('/api/auth/verify', {
        headers: { Authorization: `Bearer ${token}` }
      });

      const user = response.data.user;
      if (!user) return { valid: false, reason: 'no_user' };

      // التحقق من حالة الحساب
      if (user.status === 'deleted') {
        // حذف التوكن من localStorage
        localStorage.removeItem('token');

        // حفظ رسالة الحالة لعرضها في صفحة تسجيل الدخول
        localStorage.setItem('accountStatusMessage', JSON.stringify({
          message: 'تم حذف هذا الحساب',
          message_en: 'Account has been deleted',
          accountStatus: 'deleted'
        }));

        return {
          valid: false,
          reason: 'deleted',
          message: 'تم حذف هذا الحساب'
        };
      }

      if (user.status === 'pending_deletion') {


        return {
          valid: true, // السماح بالوصول مع تحذير
          reason: 'pending_deletion',
          message: 'هذا الحساب مجدول للحذف',
          deleteScheduledAt: user.delete_scheduled_at,
          user
        };
      }

      return { valid: true, user };
    } catch (error) {
      console.error('Error checking user status:', error);

      // إذا كان الخطأ 401، قد يكون التوكن منتهي الصلاحية أو المستخدم محذوف
      if (error.response && error.response.status === 401) {
        localStorage.removeItem('token');
        return {
          valid: false,
          reason: 'unauthorized',
          message: 'انتهت صلاحية جلسة تسجيل الدخول'
        };
      }

      return {
        valid: false,
        reason: 'error',
        message: 'خطأ في التحقق من حالة الحساب'
      };
    }
  }

  /**
   * عرض رسالة تحذيرية للمستخدمين المجدولين للحذف
   */
  static showPendingDeletionWarning(deleteScheduledAt) {
    const deleteDate = new Date(deleteScheduledAt);
    const now = new Date();
    const daysLeft = Math.ceil((deleteDate - now) / (1000 * 60 * 60 * 24));
    
    return {
      show: true,
      message: `تحذير: حسابك مجدول للحذف خلال ${daysLeft} أيام. يمكنك إلغاء عملية الحذف من صفحة الملف الشخصي.`,
      daysLeft,
      deleteDate: deleteDate.toLocaleDateString('ar-EG')
    };
  }
}

export default UserStatusHandler;
