import React, { useState, useEffect } from 'react';
import Layout from '../../components/Layout';
import {
  Box,
  Button,
  Dialog,
  TextField,
  Typography,
  Card,
  CardContent,
  Grid,
  IconButton,
  Chip,
  Container,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  useTheme,
  alpha
} from '@mui/material';
import {
  VideoCall as VideoCallIcon,
  ContentCopy as ContentCopyIcon,
  Cancel as CancelIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';
import VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import toast from 'react-hot-toast';
import { convertFromDatabaseTime, getCurrentTimeInTimezone, formatDateInStudentTimezone } from '../../utils/timezone';
import moment from 'moment-timezone';

const Meetings = () => {
  const { t, i18n } = useTranslation();
  const { currentUser } = useAuth();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';
  const [meetings, setMeetings] = useState([]);
  const [openMeeting, setOpenMeeting] = useState(false);
  const [currentMeeting, setCurrentMeeting] = useState(null);
  const [teacherProfile, setTeacherProfile] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    fetchMeetings();
    fetchTeacherProfile();

    // Update current time every second
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // Fetch meetings every minute to update statuses
    const meetingsInterval = setInterval(fetchMeetings, 60000);

    return () => {
      clearInterval(timeInterval);
      clearInterval(meetingsInterval);
    };
  }, []);

  const fetchTeacherProfile = async () => {
    try {
      const response = await axios.get('/teacher/profile');
      if (response.data.success) {
        const { profile } = response.data;
        setTeacherProfile(profile);
      }
    } catch (error) {
      console.error('Error fetching teacher profile:', error);
    }
  };

  const fetchMeetings = async () => {
    try {
      const response = await axios.get('/meetings');
      // Check if response.data is an array or has a meetings property
      if (Array.isArray(response.data)) {
        setMeetings(response.data);
      } else if (response.data && Array.isArray(response.data.meetings)) {
        setMeetings(response.data.meetings);
      } else {
        console.error('Invalid meetings data format:', response.data);
        setMeetings([]);
      }
    } catch (error) {
      console.error('Error fetching meetings:', error);
      setMeetings([]);
    }
  };

  // handleCreateMeeting removed - meetings are now created by students during booking

  const calculatePrice = (duration) => {
    if (!teacherProfile || !teacherProfile.price_per_lesson) return '0.00';
    const price = duration === '50' ?
      parseFloat(teacherProfile.price_per_lesson) :
      parseFloat(teacherProfile.price_per_lesson) / 2;
    return price.toFixed(2);
  };

  const handleStartMeeting = async (meeting) => {
    try {
      await axios.put(`/meetings/${meeting.id}/status`, { status: 'ongoing' });
      setCurrentMeeting(meeting);
      setOpenMeeting(true);
    } catch (error) {
      console.error('Error updating meeting status:', error);
    }
  };

  const handleCloseMeeting = async () => {
    try {
      // Update meeting status to completed
      await axios.put(`/meetings/${currentMeeting.id}/status`, {
        status: 'completed'
      });

      // Close the meeting dialog
      setOpenMeeting(false);
      setCurrentMeeting(null);

      // Refresh meetings list
      fetchMeetings();

      // Show success message
      toast.success(t('meetings.meetingCompleted'));
    } catch (error) {
      console.error('Error completing meeting:', error);
      toast.error(t('meetings.errorCompletingMeeting'));
    }
  };

  const handleJitsiClose = () => {
    handleCloseMeeting();
  };

  const handleCopyLink = (roomName) => {
    try {
      navigator.clipboard.writeText(roomName);
      alert(t('meetings.linkCopied'));
    } catch (error) {
      console.error('Error copying link:', error);
    }
  };

  const handleCancelMeeting = async (meeting) => {
    try {
      // Update meeting status to cancelled
      await axios.put(`/meetings/${meeting.id}/status`, {
        status: 'cancelled'
      });

      // Refresh meetings list
      fetchMeetings();

      // Show success message
      toast.success(t('meetings.cancelSuccess'));
    } catch (error) {
      console.error('Error cancelling meeting:', error);
      toast.error(t('meetings.cancelError'));
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ongoing':
        return 'success';
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'error';
      default:
        return 'primary';
    }
  };

  const getMeetingStatus = (meeting) => {
    const meetingStartTime = new Date(meeting.meeting_date);
    const meetingEndTime = new Date(meeting.meeting_date);
    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + meeting.duration);
    const now = new Date();

    if (meeting.status === 'cancelled') {
      return 'cancelled';
    }

    if (now >= meetingStartTime && now < meetingEndTime) {
      return 'ongoing';
    }

    if (now >= meetingEndTime) {
      return 'completed';
    }

    return 'scheduled';
  };

  const getMeetingActions = (meeting) => {
    const currentStatus = getMeetingStatus(meeting);

    if (currentStatus !== meeting.status) {
      // Update meeting status if it's different
      updateMeetingStatus(meeting.id, currentStatus);
    }

    if (currentStatus === 'cancelled' || currentStatus === 'completed') {
      return null;
    }

    if (currentStatus === 'ongoing') {
      return (
        <Tooltip title={t('meetings.join')}>
          <IconButton
            color="success"
            onClick={() => handleStartMeeting(meeting)}
          >
            <VideoCallIcon />
          </IconButton>
        </Tooltip>
      );
    }

    if (currentStatus === 'scheduled') {
      return (
        <>
          <Tooltip title={t('meetings.start')}>
            <IconButton
              color="primary"
              onClick={() => handleStartMeeting(meeting)}
            >
              <VideoCallIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title={t('meetings.cancel')}>
            <IconButton
              color="error"
              onClick={() => handleCancelMeeting(meeting)}
            >
              <CancelIcon />
            </IconButton>
          </Tooltip>
        </>
      );
    }

    return null;
  };

  const updateMeetingStatus = async (meetingId, newStatus) => {
    try {
      await axios.put(`/meetings/${meetingId}/status`, {
        status: newStatus
      });
      fetchMeetings(); // Refresh meetings list
    } catch (error) {
      console.error('Error updating meeting status:', error);
    }
  };

  // Format meeting date for display
  const formatMeetingDate = (meetingDate) => {
    if (!teacherProfile?.timezone) {
      return format(new Date(meetingDate), 'PPP', {
        locale: isRtl ? ar : enUS
      });
    }
    const formattedDate = formatDateInStudentTimezone(meetingDate, teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
    const momentDate = moment(formattedDate, 'YYYY-MM-DD HH:mm:ss');
    return momentDate.format('dddd, MMMM D, YYYY');
  };

  // Format meeting time for display
  const formatMeetingTime = (meetingDate) => {
    if (!teacherProfile?.timezone) {
      return format(new Date(meetingDate), 'p', {
        locale: isRtl ? ar : enUS
      });
    }
    const formattedDate = formatDateInStudentTimezone(meetingDate, teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
    const momentDate = moment(formattedDate, 'YYYY-MM-DD HH:mm:ss');
    return momentDate.format('h:mm A');
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header Section */}
        <Paper elevation={3} sx={{ p: 3, mb: 4, bgcolor: 'primary.main', color: 'white' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
            <Box>
              <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                {t('meetings.myMeetings')}
              </Typography>
              <Typography variant="body1" sx={{ opacity: 0.9 }}>
                {t('meetings.description')}
              </Typography>
            </Box>
            <Box sx={{ textAlign: 'right' }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                📅 {teacherProfile?.timezone ? (
                  moment(formatDateInStudentTimezone(new Date().toISOString(), teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('dddd, MMMM D, YYYY')
                ) : (
                  format(new Date(), 'PPP', {
                    locale: isRtl ? ar : enUS
                  })
                )}
              </Typography>
              {teacherProfile?.timezone && (
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  {teacherProfile.timezone}
                </Typography>
              )}
            </Box>
          </Box>
        </Paper>

        {/* Meetings Table */}
        <TableContainer component={Paper} elevation={2} sx={{ borderRadius: 2, overflow: 'hidden' }}>
          <Table sx={{ minWidth: 650 }}>
            <TableHead>
              <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PersonIcon color="primary" />
                    {t('meetings.student')}
                  </Box>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CalendarIcon color="primary" />
                    {t('meetings.date')}
                  </Box>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TimeIcon color="primary" />
                    {t('meetings.time')}
                  </Box>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TimeIcon color="primary" />
                    {t('meetings.duration')}
                  </Box>
                </TableCell>
                <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                  {t('bookings.status')}
                </TableCell>
              </TableRow>
            </TableHead>
                <TableBody>
                  {meetings.map((meeting, index) => (
                    <TableRow
                      key={meeting.id}
                      sx={{
                        '&:nth-of-type(odd)': {
                          bgcolor: alpha(theme.palette.grey[100], 0.5)
                        },
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.05),
                          transform: 'scale(1.001)',
                          transition: 'all 0.2s ease-in-out'
                        },
                        cursor: 'pointer'
                      }}
                    >
                      {/* Student Column */}
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            src={meeting.student_picture}
                            alt={meeting.student_name}
                            sx={{ width: 48, height: 48 }}
                          />
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                              {meeting.student_name}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>

                      {/* Date Column */}
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {formatMeetingDate(meeting.meeting_date)}
                        </Typography>
                      </TableCell>

                      {/* Time Column */}
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {formatMeetingTime(meeting.meeting_date)}
                        </Typography>
                      </TableCell>

                      {/* Duration Column */}
                      <TableCell>
                        <Typography variant="body1">
                          {meeting.duration} {t('meetings.minutes')}
                        </Typography>
                      </TableCell>

                      {/* Status Column */}
                      <TableCell>
                        <Chip
                          label={t(`meetings.status.${getMeetingStatus(meeting)}`)}
                          color={getStatusColor(getMeetingStatus(meeting))}
                          variant="filled"
                          sx={{ fontWeight: 'medium' }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}

              {/* Empty State */}
              {meetings.length === 0 && (
                <TableRow>
                  <TableCell colSpan={5}>
                    <Box sx={{ textAlign: 'center', py: 6 }}>
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        📅 {t('meetings.noMeetings')}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {t('meetings.noMeetingsDescription')}
                      </Typography>
                    </Box>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Create Meeting Dialog removed - meetings are now created by students during booking */}

        {/* VideoSDK Meeting Dialog */}
        <Dialog
          fullScreen
          open={openMeeting}
          onClose={handleCloseMeeting}
        >
          {currentMeeting && (
            <VideoSDKMeeting
              roomId={currentMeeting.room_name}
              meetingId={currentMeeting.id}
              meetingData={currentMeeting}
              onClose={handleJitsiClose}
            />
          )}
        </Dialog>
      </Container>
    </Layout>
  );
};

export default Meetings;
