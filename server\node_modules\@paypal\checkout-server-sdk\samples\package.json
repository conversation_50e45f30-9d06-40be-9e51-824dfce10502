{"name": "paypal-checkout-sdk-samples", "version": "1.0.3", "description": "NodeJS SDK for PayPal Checkout APIs", "keywords": [], "homepage": "", "author": "", "main": "index", "directories": {"lib": "lib"}, "repository": {"type": "git", "url": ""}, "engines": {"node": ">=8"}, "dependencies": {"@paypal/checkout-server-sdk": "^1.0.2"}, "license": "MIT", "scripts": {"test:integration": "mocha spec --recursive", "test": "npm run test:integration", "test:orders": "mocha spec/orders --recursive --timeout 60000"}}