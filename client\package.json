{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^6.4.8", "@mui/lab": "^6.0.0-beta.31", "@mui/material": "^6.4.8", "@paypal/react-paypal-js": "^8.8.3", "@popperjs/core": "^2.11.8", "@react-oauth/google": "^0.12.1", "@stripe/react-stripe-js": "^2.9.0", "@stripe/stripe-js": "^4.10.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@videosdk.live/react-sdk": "^0.2.1", "axios": "^1.8.4", "classnames": "^2.5.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.5.0", "i18next": "^24.2.3", "lottie-react": "^2.4.1", "lucide-react": "^0.483.0", "moment-timezone": "^0.5.47", "react": "^19.0.0", "react-dom": "^19.0.0", "react-easy-crop": "^5.4.2", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "react-player": "^2.16.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.4.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}