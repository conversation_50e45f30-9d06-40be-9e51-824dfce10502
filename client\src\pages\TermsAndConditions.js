import React from 'react';
import { Container, Typography, Box, Paper, useTheme, alpha, Fade, Stack, Card, CardContent } from '@mui/material';
import { useTranslation } from 'react-i18next';
import termsConditions from '../i18n/translations/termsConditions';
import i18n from '../i18n/i18n';

// Merge the Terms & Conditions keys into existing translation bundles
i18n.addResourceBundle('en', 'translation', termsConditions.en, true, true);
i18n.addResourceBundle('ar', 'translation', termsConditions.ar, true, true);

const TermsAndConditions = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRTL = i18n.language === 'ar';

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,
        pt: 4,
        pb: 8,
      }}
    >
      <Container maxWidth="lg">
        <Fade in timeout={800}>
          <Paper
            elevation={0}
            sx={{
              p: { xs: 3, md: 6 },
              borderRadius: 3,
              background: 'transparent',
              direction: isRTL ? 'rtl' : 'ltr',
            }}
          >
            <Typography
              variant="h3"
              align="center"
              sx={{
                mb: 4,
                fontWeight: 800,
                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                color: theme.palette.primary.main,
              }}
            >
              {t('termsConditions.title')}
            </Typography>
            {/* Render paragraphs in individual cards */}
            <Stack spacing={4}>
              {t('termsConditions.content')
                .split(/\n\s*\n/)
                .filter((section) => section.trim() !== '')
                .map((section, idx) => {
                  const lines = section.split('\n');
                  const title = lines[0];
                  const body = lines.slice(1).join('\n');
                  return (
                    <Card
                      key={idx}
                      elevation={3}
                      sx={{
                        borderRadius: 3,
                        borderLeft: `6px solid ${theme.palette.primary.main}`,
                        backgroundColor:
                          idx % 2 === 0
                            ? alpha(theme.palette.primary.main, 0.02)
                            : alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02),
                      }}
                    >
                      <CardContent>
                        <Typography
                          variant="h6"
                          sx={{
                            mb: 1.5,
                            fontWeight: 700,
                            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                            color: theme.palette.primary.main,
                          }}
                        >
                          {title}
                        </Typography>
                        <Typography
                          variant="body1"
                          sx={{
                            whiteSpace: 'pre-line',
                            lineHeight: 2,
                            fontSize: '1.05rem',
                            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                            color: theme.palette.text.primary,
                          }}
                        >
                          {body}
                        </Typography>
                      </CardContent>
                    </Card>
                  );
                })}
            </Stack>
          </Paper>
        </Fade>
      </Container>
    </Box>
  );
};

export default TermsAndConditions;
