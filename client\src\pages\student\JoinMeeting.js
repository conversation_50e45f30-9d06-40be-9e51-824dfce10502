import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Box, Typography, CircularProgress, Container } from '@mui/material';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';
import Layout from '../../components/Layout';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';

const JoinMeeting = () => {
  const { roomName } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showMeeting, setShowMeeting] = useState(false);
  const [meetingData, setMeetingData] = useState(null);

  useEffect(() => {
    const validateMeeting = async () => {
      try {
        const response = await axios.get(`/meetings/${roomName}/validate`);
        if (response.data.success && response.data.meeting) {
          setMeetingData(response.data.meeting);
          setShowMeeting(true);
        }
        setLoading(false);
      } catch (error) {
        setError(t('meetings.errors.invalid'));
        setLoading(false);
      }
    };

    validateMeeting();
  }, [roomName, t]);

  if (loading) {
    return (
      <Layout>
        <Container maxWidth="lg">
          <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '60vh'
            }}
          >
            <CircularProgress />
          </Box>
          </ProfileCompletionAlert>
        </Container>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <Container maxWidth="lg">
          <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '60vh',
              gap: 2
            }}
          >
            <Typography variant="h5" color="error">
              {error}
            </Typography>
          </Box>
          </ProfileCompletionAlert>
        </Container>
      </Layout>
    );
  }

  if (showMeeting) {
    return (
      <VideoSDKMeeting
        roomId={roomName}
        meetingId={meetingData?.id}
        meetingData={meetingData}
        onClose={() => navigate(-1)}
      />
    );
  }

  return null;
};

export default JoinMeeting;
