import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import UserStatusHandler from '../utils/userStatusHandler';
import { Alert, AlertTitle, Box, Button } from '@mui/material';
import { Warning as WarningIcon, Info as InfoIcon } from '@mui/icons-material';

/**
 * مكون للتحقق من حالة المستخدم وعرض التحذيرات المناسبة
 */
const UserStatusChecker = ({ children }) => {
  const navigate = useNavigate();
  const { handleLogout, currentUser } = useAuth();
  const [statusCheck, setStatusCheck] = useState({ loading: true });
  const [showWarning, setShowWarning] = useState(false);

  useEffect(() => {
    const checkStatus = async () => {
      if (!currentUser) {
        setStatusCheck({ loading: false, valid: true });
        return;
      }

      const result = await UserStatusHandler.checkUserStatus();
      setStatusCheck({ loading: false, ...result });

      if (!result.valid) {
        if (result.reason === 'deleted' || result.reason === 'unauthorized') {
          // حفظ رسالة الحالة في localStorage
          localStorage.setItem('accountStatusMessage', JSON.stringify({
            message: result.message,
            message_en: result.message_en || result.message,
            accountStatus: result.reason,
            deleteScheduledAt: result.deleteScheduledAt
          }));

          // تسجيل الخروج وإعادة التوجيه للحسابات المحذوفة أو التوكن المنتهي
          handleLogout();
          navigate('/login');
        } else if (result.reason === 'error') {
          // خطأ في الشبكة - عدم إجراء أي شيء، السماح بالمتابعة
          console.warn('Network error checking user status:', result.message);
        }
      } else if (result.reason === 'pending_deletion' || (result.user && result.user.status === 'pending_deletion')) {
        // عرض تحذير للمستخدمين المجدولين للحذف
        setShowWarning(true);
      }
    };

    checkStatus();
  }, [currentUser, handleLogout, navigate]);

  // إعداد axios interceptor
  useEffect(() => {
    UserStatusHandler.setupAxiosInterceptor(navigate, handleLogout);
  }, [navigate, handleLogout]);

  if (statusCheck.loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <div>جاري التحقق من حالة الحساب...</div>
      </Box>
    );
  }

  if (!statusCheck.valid) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Alert severity="error" icon={<WarningIcon />}>
          <AlertTitle>خطأ في الحساب</AlertTitle>
          {statusCheck.message || 'حدث خطأ في التحقق من حالة الحساب'}
        </Alert>
      </Box>
    );
  }

  return (
    <>
      {/* تحذير للمستخدمين المجدولين للحذف */}
      {showWarning && currentUser && currentUser.status === 'pending_deletion' && (
        <Alert 
          severity="warning" 
          icon={<InfoIcon />}
          sx={{ mb: 2 }}
          action={
            <Button 
              color="inherit" 
              size="small" 
              onClick={() => navigate('/student/profile')}
            >
              إلغاء الحذف
            </Button>
          }
        >
          <AlertTitle>تحذير: حساب مجدول للحذف</AlertTitle>
          {UserStatusHandler.showPendingDeletionWarning(currentUser.delete_scheduled_at).message}
        </Alert>
      )}
      
      {children}
    </>
  );
};

export default UserStatusChecker;
