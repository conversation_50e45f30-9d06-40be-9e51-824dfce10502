import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Grid,
  Card,
  CardContent,
  TablePagination,
  TextField,
  Avatar,
  Stack,
  Snackbar,
  IconButton,
  Tooltip,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import VisibilityIcon from '@mui/icons-material/Visibility';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';

const Students = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [students, setStudents] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [studentToDelete, setStudentToDelete] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchText, setSearchText] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success'
  });

  // Fetch students with pagination and search
  const fetchStudents = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/admin/students', {
        params: {
          page: page + 1,
          limit: rowsPerPage,
          search: searchQuery
        }
      });

      setStudents(response.data.students);
      setTotalCount(response.data.total);
      setError('');
    } catch (err) {
      console.error('Error fetching students:', err);
      setError(t('admin.students.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStudents();
  }, [page, rowsPerPage, searchQuery, t]);

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
  };

  const handleSearch = () => {
    setSearchQuery(searchText);
    setPage(0);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleViewDetails = async (studentId) => {
    try {
      setLoading(true);
      const response = await axios.get(`/admin/students/${studentId}`);
      setSelectedStudent(response.data);
      setOpenDialog(true);
      setError('');
    } catch (err) {
      console.error('Error fetching student details:', err);
      setError(t('admin.students.detailsError'));
    } finally {
      setLoading(false);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedStudent(null);
  };

  const handleDeleteClick = (student) => {
    setStudentToDelete(student);
    setOpenDeleteDialog(true);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    setStudentToDelete(null);
  };

  const handleConfirmDelete = async () => {
    if (!studentToDelete) return;

    try {
      setLoading(true);
      await axios.delete(`/admin/students/${studentToDelete.id}`);
      setSnackbar({
        open: true,
        message: t('admin.students.deleteSuccess'),
        severity: 'success'
      });
      fetchStudents();
    } catch (err) {
      console.error('Error deleting student:', err);
      setSnackbar({
        open: true,
        message: t('admin.students.deleteError'),
        severity: 'error'
      });
    } finally {
      setLoading(false);
      handleCloseDeleteDialog();
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  if (loading && page === 0) {
    return (
      <Layout title={t('admin.students.title')}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Layout>
    );
  }

  return (
    <Layout title={t('admin.students.title')}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
      )}

      {/* Search Filter */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                fullWidth
                label={t('admin.students.searchPlaceholder')}
                value={searchText}
                onChange={handleSearchChange}
                onKeyPress={handleKeyPress}
              />
              <Button
                variant="contained"
                onClick={handleSearch}
                sx={{ minWidth: '100px' }}
              >
                {t('common.search')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Students Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('admin.students.name')}</TableCell>
              <TableCell>{t('admin.students.email')}</TableCell>
              <TableCell>{t('admin.students.gender')}</TableCell>
              <TableCell>{t('admin.students.country')}</TableCell>
              <TableCell>{t('admin.students.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {students.map((student) => (
              <TableRow key={student.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Avatar src={student.profile_picture_url} alt={student.full_name}>
                      {student.full_name.charAt(0)}
                    </Avatar>
                    {student.full_name}
                  </Box>
                </TableCell>
                <TableCell>{student.email}</TableCell>
                <TableCell>{t(`gender.${student.gender}`)}</TableCell>
                <TableCell>{student.country || '-'}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title={t('admin.students.viewDetails')}>
                      <IconButton
                        color="primary"
                        onClick={() => handleViewDetails(student.id)}
                      >
                        <VisibilityIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={t('admin.students.delete')}>
                      <IconButton
                        color="error"
                        onClick={() => handleDeleteClick(student)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
            {students.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  {t('admin.students.noStudents')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handlePageChange}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleRowsPerPageChange}
          rowsPerPageOptions={[10, 25, 50]}
        />
      </TableContainer>

      {/* Student Details Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        {selectedStudent && (
          <>
            <DialogTitle>
              {t('admin.students.studentDetails')}
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                        <Avatar
                          src={selectedStudent.profile_picture_url}
                          alt={selectedStudent.full_name}
                          sx={{ width: 80, height: 80, mr: 2 }}
                        >
                          {selectedStudent.full_name.charAt(0)}
                        </Avatar>
                        <Box>
                          <Typography variant="h5">{selectedStudent.full_name}</Typography>
                          <Typography color="text.secondary">{selectedStudent.email}</Typography>
                        </Box>
                      </Box>

                      <Typography variant="h6" gutterBottom sx={{ mt: 2, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        {t('admin.students.personalInfo')}
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.name')}:</strong> {selectedStudent.full_name}</Typography>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.email')}:</strong> {selectedStudent.email}</Typography>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.gender')}:</strong> {t(`gender.${selectedStudent.gender}`)}</Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.country')}:</strong> {selectedStudent.country || t('common.notProvided')}</Typography>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.timezone')}:</strong> {selectedStudent.timezone || t('common.notProvided')}</Typography>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.age')}:</strong> {selectedStudent.age || t('common.notProvided')}</Typography>
                        </Grid>
                      </Grid>

                      <Typography variant="h6" gutterBottom sx={{ mt: 3, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        {t('admin.students.learningPreferences')}
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.nativeLanguage')}:</strong> {selectedStudent.native_language || t('common.notProvided')}</Typography>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.islamLearningLanguage')}:</strong> {selectedStudent.islam_learning_language || t('common.notProvided')}</Typography>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.arabicLearningLanguage')}:</strong> {selectedStudent.arabic_learning_language || t('common.notProvided')}</Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.arabicProficiencyLevel')}:</strong> {selectedStudent.arabic_proficiency_level ? t(`admin.students.proficiencyLevels.${selectedStudent.arabic_proficiency_level}`) : t('common.notProvided')}</Typography>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.privateTutoring')}:</strong> {selectedStudent.private_tutoring_preference ? t('common.yes') : t('common.no')}</Typography>
                          <Typography sx={{ mb: 1 }}><strong>{t('admin.students.profileCompleted')}:</strong> {selectedStudent.is_completed ? t('common.yes') : t('common.no')}</Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialog}>
                {t('common.close')}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={openDeleteDialog}
        onClose={handleCloseDeleteDialog}
      >
        <DialogTitle>
          {t('admin.students.deleteConfirmTitle')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {t('admin.students.deleteConfirmMessage', { name: studentToDelete?.full_name })}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog}>
            {t('common.cancel')}
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
          >
            {t('common.delete')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Layout>
  );
};

export default Students;
