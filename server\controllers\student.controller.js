const mysql = require('mysql2/promise');
const config = require('../config/db.config');

const createProfile = async (req, res) => {
  const connection = await mysql.createConnection(config);
  
  try {
    const {
      nativeLanguage,
      islamLearningLanguage,
      arabicLearningLanguage,
      age,
      country,
      timezone,
      arabicProficiencyLevel,
      privateTutoring
    } = req.body;

    // Check if profile already exists
    const [existingProfiles] = await connection.execute(
      'SELECT id FROM student_completion_data WHERE user_id = ?',
      [req.user.id]
    );

    if (existingProfiles.length) {
      // Update existing profile
      await connection.execute(
        `UPDATE student_completion_data SET
          native_language = ?,
          islam_learning_language = ?,
          arabic_learning_language = ?,
          age = ?,
          country = ?,
          timezone = ?,
          arabic_proficiency_level = ?,
          private_tutoring_preference = ?,
          is_completed = TRUE
        WHERE user_id = ?`,
        [
          nativeLanguage,
          islamLearningLanguage,
          arabicLearningLanguage,
          age,
          country,
          timezone,
          arabicProficiencyLevel,
          privateTutoring,
          req.user.id
        ]
      );
    } else {
      // Create new profile
      await connection.execute(
        `INSERT INTO student_completion_data (
          user_id,
          native_language,
          islam_learning_language,
          arabic_learning_language,
          age,
          country,
          timezone,
          arabic_proficiency_level,
          private_tutoring_preference,
          is_completed
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE)`,
        [
          req.user.id,
          nativeLanguage,
          islamLearningLanguage,
          arabicLearningLanguage,
          age,
          country,
          timezone,
          arabicProficiencyLevel,
          privateTutoring
        ]
      );
    }

    res.json({ message: 'Profile updated successfully' });
  } catch (err) {
    console.error('Error in createProfile:', err);
    res.status(500).json({ message: 'Error updating profile', error: err.message });
  } finally {
    connection.end();
  }
};

const getProfile = async (req, res) => {
  const connection = await mysql.createConnection(config);
  
  try {
    const [profiles] = await connection.execute(
      'SELECT * FROM student_completion_data WHERE user_id = ?',
      [req.user.id]
    );

    if (profiles.length === 0) {
      return res.status(404).json({ message: 'Profile not found' });
    }

    res.json(profiles[0]);
  } catch (err) {
    console.error('Error in getProfile:', err);
    res.status(500).json({ message: 'Error retrieving profile', error: err.message });
  } finally {
    connection.end();
  }
};

const getAllStudents = async (req, res) => {
  const connection = await mysql.createConnection(config);
  
  try {
    const [students] = await connection.execute(
      `SELECT u.*, scd.*
       FROM users u
       LEFT JOIN student_completion_data scd ON u.id = scd.user_id
       WHERE u.role = 'student' AND u.deleted_at IS NULL`
    );

    res.json(students);
  } catch (err) {
    console.error('Error in getAllStudents:', err);
    res.status(500).json({ message: 'Error retrieving students', error: err.message });
  } finally {
    connection.end();
  }
};

module.exports = {
  createProfile,
  getProfile,
  getAllStudents
};
