import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import {
  Box,
  Button,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Card,
  CardMedia,
  CardContent,
  CardActions
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import VideoFileIcon from '@mui/icons-material/VideoFile';
import Layout from '../../components/Layout';
import { useAuth } from '../../contexts/AuthContext';

const VideoUpload = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [videoFile, setVideoFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);

  // Check if user is already logged in
  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
    } else {
      // Check if there's a temporarily saved video URL in localStorage
      const savedVideoUrl = localStorage.getItem('teacherVideoUrl');
      if (savedVideoUrl) {
        setVideoUrl(savedVideoUrl);
        setSuccess(true);
      } else {
        // Check if there's already an uploaded video in the database
        const checkExistingVideo = async () => {
          try {
            const response = await axios.get('/api/teacher/profile');
            if (response.data && response.data.profile && response.data.profile.intro_video_url) {
              // Obtener la ruta del video
              const videoPath = response.data.profile.intro_video_url;

              
              const fullVideoUrl = videoPath;

              setVideoUrl(fullVideoUrl);
              setSuccess(true);

              // Guardar en localStorage para uso futuro
              localStorage.setItem('teacherVideoUrl', fullVideoUrl);
              localStorage.setItem('teacherVideoRelativePath', videoPath);
            }
          } catch (error) {
            console.error('Error checking existing video:', error);
          }
        };

        checkExistingVideo();
      }
    }
  }, [currentUser, navigate]);

  const handleVideoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Check file type
      const validTypes = ['video/mp4', 'video/webm', 'video/ogg'];
      if (!validTypes.includes(file.type)) {
        setError(t('teacher.invalidVideoFormat'));
        return;
      }

      // Check file size (min 1MB, max 100MB)
      if (file.size < 1 * 1024 * 1024) {
        setError(t('teacher.videoTooSmall') || 'Video size must be at least 1MB');
        return;
      }
      if (file.size > 100 * 1024 * 1024) {
        setError(t('teacher.videoTooLarge'));
        return;
      }

      setVideoFile(file);
      setError('');
    }
  };

  const handleUpload = async () => {
    if (!videoFile) {
      setError(t('teacher.noVideoSelected'));
      return;
    }

    setLoading(true);
    setError('');
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('introVideo', videoFile);

    try {
      const response = await axios.post('/api/teacher/upload-video', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      if (response.data && response.data.videoUrl) {
        // Obtener la ruta del video
        const videoPath = response.data.videoUrl;

        // Usar la ruta relativa para la URL completa
        const fullVideoUrl = videoPath;

        setVideoUrl(fullVideoUrl);
        setSuccess(true);

        // Guardar en localStorage
        localStorage.setItem('teacherVideoUrl', fullVideoUrl);

        // También guardar la ruta relativa para el envío del formulario
        localStorage.setItem('teacherVideoRelativePath', videoPath);
      }
    } catch (err) {
      console.error('Video upload error:', err);
      setError(err.response?.data?.message || t('teacher.videoUploadError'));
    } finally {
      setLoading(false);
    }
  };

  const handleContinue = () => {
    navigate('/teacher/application');
  };

  const handleDeleteVideo = async () => {
    try {
      setLoading(true);

      // Get the relative path for deletion
      const relativePath = localStorage.getItem('teacherVideoRelativePath');

      // Only delete the file from the server if it's a temporary upload
      // (not yet saved in the database with the application form)
      if (relativePath) {
        await axios.delete(`/api/teacher/delete-video?videoUrl=${encodeURIComponent(relativePath)}`);
      }

      // Update UI
      setVideoUrl('');
      setSuccess(false);
      setVideoFile(null);

      // Remove from localStorage
      localStorage.removeItem('teacherVideoUrl');
      localStorage.removeItem('teacherVideoRelativePath');
    } catch (err) {
      console.error('Error deleting video:', err);
      setError(err.response?.data?.message || t('teacher.videoDeleteError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout title={t('teacher.videoUpload.title')}>
      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
        <Paper elevation={3} sx={{ p: 4 }}>
          <Typography variant="h5" component="h1" gutterBottom>
            {t('teacher.videoUpload.title')}
          </Typography>

          <Typography variant="body1" paragraph>
            {t('teacher.videoUpload.description')}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && videoUrl ? (
            <Box sx={{ mb: 4 }}>
              <Alert severity="success" sx={{ mb: 2 }}>
                {t('teacher.videoUpload.success')}
              </Alert>

              <Card>
                <CardMedia
                  component="video"
                  controls
                  src={videoUrl.startsWith('/') ? videoUrl : `/${videoUrl}`}
                  sx={{ height: 300 }}
                />
                <CardContent>
                  <Typography variant="body2" color="text.secondary">
                    {t('teacher.videoUpload.videoReady')}
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button
                    size="small"
                    color="primary"
                    onClick={handleContinue}
                  >
                    {t('teacher.videoUpload.continue')}
                  </Button>
                  <Button
                    size="small"
                    color="error"
                    onClick={handleDeleteVideo}
                    disabled={loading}
                  >
                    {t('teacher.videoUpload.delete')}
                  </Button>
                </CardActions>
              </Card>
            </Box>
          ) : (
            <Box>
              <Box sx={{ mb: 3, p: 2, border: '1px dashed grey', borderRadius: 1 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {t('teacher.videoUpload.requirements')}:
                </Typography>
                <Typography variant="body2">
                  • {t('teacher.videoUpload.formatRequirement')}: MP4, WebM, OGG
                </Typography>
                <Typography variant="body2">
                  • {t('teacher.videoUpload.sizeRequirement')}: 1MB - 100MB
                </Typography>
                <Typography variant="body2">
                  • {t('teacher.videoUpload.lengthRequirement')}: 2-5 {t('teacher.videoUpload.minutes')}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                <input
                  type="file"
                  accept="video/mp4,video/webm,video/ogg"
                  style={{ display: 'none' }}
                  id="video-upload-input"
                  onChange={handleVideoChange}
                  disabled={loading}
                />
                <label htmlFor="video-upload-input">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<CloudUploadIcon />}
                    disabled={loading}
                    sx={{ mb: 2 }}
                  >
                    {t('teacher.videoUpload.selectVideo')}
                  </Button>
                </label>

                {videoFile && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <VideoFileIcon sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      {videoFile.name} ({Math.round(videoFile.size / (1024 * 1024))} MB)
                    </Typography>
                  </Box>
                )}

                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleUpload}
                  disabled={!videoFile || loading}
                  startIcon={loading ? <CircularProgress size={20} /> : null}
                >
                  {loading ? t('teacher.videoUpload.uploading') : t('teacher.videoUpload.upload')}
                </Button>

                {loading && (
                  <Box sx={{ width: '100%', mt: 2 }}>
                    <Typography variant="body2" align="center">
                      {uploadProgress}%
                    </Typography>
                    <Box
                      sx={{
                        height: 10,
                        bgcolor: '#e0e0e0',
                        borderRadius: 5,
                        mt: 1
                      }}
                    >
                      <Box
                        sx={{
                          height: '100%',
                          width: `${uploadProgress}%`,
                          bgcolor: 'primary.main',
                          borderRadius: 5
                        }}
                      />
                    </Box>
                  </Box>
                )}
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/teacher/application')}
                  disabled={loading}
                >
                  {t('teacher.videoUpload.skipForNow')}
                </Button>
              </Box>
            </Box>
          )}
        </Paper>
      </Box>
    </Layout>
  );
};

export default VideoUpload;
