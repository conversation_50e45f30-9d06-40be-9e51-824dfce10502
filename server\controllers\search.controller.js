const mysql = require('mysql2/promise');
const config = require('../config/db.config');

const searchTeachers = async (req, res) => {
  let connection;
  try {
    // Log the entire request for debugging
    console.log('Search request received:');
    console.log('- URL:', req.url);
    console.log('- Query params:', req.query);

    // Simple pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    // Create a simple database connection
    connection = await mysql.createConnection(config);

    // Convert pagination parameters to integers explicitly
    const numericLimit = Number(limit);
    const numericOffset = Number(offset);

    console.log('Pagination parameters:', { numericLimit, numericOffset });

    // Extract filter parameters
    const subjects = req.query.subjects ? JSON.parse(req.query.subjects) : [];
    const languages = req.query.languages ? JSON.parse(req.query.languages) : [];
    const priceRange = req.query.priceRange ? JSON.parse(req.query.priceRange) : [0, 1000];
    const rating = req.query.rating ? parseFloat(req.query.rating) : 0;

    console.log('Filter parameters:', { subjects, languages, priceRange, rating });

    // Build the query with filters
    let query = `
      SELECT
        SQL_CALC_FOUND_ROWS
        u.id,
        u.full_name,
        u.profile_picture_url as user_profile_picture,
        tp.profile_picture_url,
        tp.price_per_lesson,
        tp.trial_lesson_price,
        tp.teaching_experience,
        tp.qualifications,
        tp.intro_video_url,
        tp.teaching_languages,
        COALESCE(AVG(r.rating), 0) as average_rating,
        COUNT(DISTINCT r.id) as review_count,
        GROUP_CONCAT(DISTINCT c.name) as subjects
      FROM users u
      JOIN teacher_profiles tp ON u.id = tp.user_id
      LEFT JOIN reviews r ON tp.id = r.teacher_profile_id
      LEFT JOIN teacher_categories tc ON tp.id = tc.teacher_profile_id
      LEFT JOIN categories c ON tc.category_id = c.id
      WHERE u.role = 'platform_teacher'
      AND tp.status = 'approved'
      AND u.deleted_at IS NULL
      AND u.status != 'pending_deletion'
    `;

    // Add filter conditions
    const conditions = [];
    const params = [];

    // Filter by subjects
    if (subjects && subjects.length > 0) {
      conditions.push(`c.id IN (?)`);
      params.push(subjects);
    }

    // Filter by price range
    if (priceRange && priceRange.length === 2) {
      conditions.push(`tp.price_per_lesson BETWEEN ? AND ?`);
      params.push(priceRange[0], priceRange[1]);
    }

    // Filter by rating - we'll handle this in memory after fetching results
    // We can't use AVG() in WHERE clause

    // Filter by languages
    if (languages && languages.length > 0) {
      // This is more complex as languages are stored as JSON
      // We'll handle this in memory after fetching the results
    }

    // Add conditions to query
    if (conditions.length > 0) {
      query += ` AND ${conditions.join(' AND ')}`;
    }

    // Complete the query
    query += `
      GROUP BY u.id
      ORDER BY average_rating DESC
      LIMIT ${numericLimit} OFFSET ${numericOffset}
    `;

    console.log('Final SQL query:', query);
    console.log('Query parameters:', params);

    // Execute the query with proper error handling
    let teachers, countResult;
    try {
      console.log('Executing query...');
      // Use query() with parameters
      [teachers] = await connection.query(query, params);
      console.log('Query executed successfully, fetching row count...');
      [countResult] = await connection.query('SELECT FOUND_ROWS() as total');
      console.log('Row count fetched successfully');
    } catch (queryError) {
      console.error('Error executing query:', queryError);
      console.error('Query error details:', {
        code: queryError.code,
        errno: queryError.errno,
        sqlMessage: queryError.sqlMessage,
        sqlState: queryError.sqlState,
        sql: queryError.sql
      });
      throw new Error(`Query execution failed: ${queryError.message}`);
    }

    let total = countResult[0].total;
    console.log(`Found ${total} teachers matching the criteria`);

    // Collect all language IDs from all teachers
    const allLanguageIds = new Set();
    teachers.forEach(teacher => {
      try {
        if (teacher.teaching_languages) {
          const ids = JSON.parse(teacher.teaching_languages);
          ids.forEach(id => allLanguageIds.add(id));
        }
      } catch (e) {
        console.error('Error parsing teaching languages:', e);
      }
    });

    // Fetch language names for all language IDs
    let languageMap = {};
    if (allLanguageIds.size > 0) {
      const languageIds = Array.from(allLanguageIds);
      const placeholders = languageIds.map(() => '?').join(',');
      const [languages] = await connection.query(
        `SELECT id, name FROM languages WHERE id IN (${placeholders})`,
        languageIds
      );

      // Create a map of id to name
      languageMap = languages.reduce((map, lang) => {
        map[lang.id] = lang.name;
        return map;
      }, {});
    }

    // Process teacher data to ensure proper format
    let processedTeachers = teachers.map(teacher => {
      // Parse teaching languages from JSON string and convert IDs to names
      let teachingLanguageIds = [];
      let teachingLanguageNames = [];
      try {
        if (teacher.teaching_languages) {
          teachingLanguageIds = JSON.parse(teacher.teaching_languages);
          // Map IDs to names using the language map
          teachingLanguageNames = teachingLanguageIds.map(id =>
            languageMap[id] || `Language ID: ${id}`
          );
        }
      } catch (e) {
        console.error('Error parsing teaching languages:', e);
      }

      // Parse subjects from GROUP_CONCAT result
      let subjects = [];
      if (teacher.subjects) {
        subjects = teacher.subjects.split(',');
      }

      // Make sure all fields have proper values
      return {
        ...teacher,
        id: teacher.id || 0,
        full_name: teacher.full_name || '',
        // Use teacher profile picture if available, otherwise use user profile picture
        profile_picture_url: teacher.profile_picture_url || teacher.user_profile_picture || null,
        price_per_lesson: teacher.price_per_lesson || 0,
         trial_lesson_price: teacher.trial_lesson_price || 0,
        teaching_experience: teacher.teaching_experience || 0,
        average_rating: parseFloat(teacher.average_rating) || 0,
        review_count: parseInt(teacher.review_count) || 0,
        // Add teaching languages as names instead of IDs
        teaching_languages: teachingLanguageNames,
        // Keep IDs for reference
        teaching_languages_ids: teachingLanguageIds,
        // Add subjects from GROUP_CONCAT result
        subjects: subjects
      };
    });

    // Filter by languages (in memory)
    if (languages && languages.length > 0) {
      console.log('Filtering by languages:', languages);
      processedTeachers = processedTeachers.filter(teacher => {
        // Check if any of the teacher's languages match the selected languages
        return teacher.teaching_languages.some(lang => languages.includes(lang));
      });

      // Update total count
      total = processedTeachers.length;
      console.log(`After language filtering: ${total} teachers`);
    }

    // Filter by rating (in memory)
    if (rating > 0) {
      console.log('Filtering by rating:', rating);
      processedTeachers = processedTeachers.filter(teacher => {
        return teacher.average_rating >= rating;
      });

      // Update total count
      total = processedTeachers.length;
      console.log(`After rating filtering: ${total} teachers`);
    }

    // Calculate pagination after all filtering
    const pages = Math.ceil(total / limit);
    const currentPage = Number(page);

    // If current page is greater than total pages, return the last page
    const adjustedPage = currentPage > pages && pages > 0 ? pages : currentPage;

    // Get the slice of teachers for the current page
    const startIndex = (adjustedPage - 1) * limit;
    const endIndex = Math.min(startIndex + limit, processedTeachers.length);
    const paginatedTeachers = processedTeachers.slice(startIndex, endIndex);

    // Send the response
    res.json({
      success: true,
      data: {
        teachers: paginatedTeachers,
        pagination: {
          total,
          pages,
          current: adjustedPage,
          limit: Number(limit)
        }
      }
    });
  } catch (error) {
    console.error('Error searching teachers:', error);
    console.error('Error stack:', error.stack);

    // Log more details about the error
    if (error.code) console.error('Error code:', error.code);
    if (error.errno) console.error('Error number:', error.errno);
    if (error.sqlMessage) console.error('SQL message:', error.sqlMessage);
    if (error.sqlState) console.error('SQL state:', error.sqlState);
    if (error.sql) console.error('SQL query:', error.sql);

    // Send a more detailed error message for debugging
    res.status(500).json({
      success: false,
      message: 'Error searching teachers',
      error: error.message,
      errorCode: error.code,
      sqlMessage: error.sqlMessage
    });
  } finally {
    try {
      if (connection) await connection.end();
    } catch (err) {
      console.error('Error closing database connection:', err);
    }
  }
};

const getSearchFilters = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Get all categories - use query instead of execute
    const [categories] = await connection.query(
      'SELECT id, name, description FROM categories'
    );

    // Get languages from languages table - use query instead of execute
    const [languages] = await connection.query(
      'SELECT id, name FROM languages ORDER BY name'
    );

    // Get price range - use query instead of execute
    const [priceRange] = await connection.query(`
      SELECT
        MIN(price_per_lesson) as min_price,
        MAX(price_per_lesson) as max_price
      FROM teacher_profiles
      WHERE status = 'approved'
    `);

    res.json({
      success: true,
      data: {
        categories,
        languages: languages.map(l => l.name),
        priceRange: {
          min: Math.floor(priceRange[0].min_price || 0),
          max: Math.ceil(priceRange[0].max_price || 100)
        }
      }
    });
  } catch (error) {
    console.error('Error getting search filters:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting search filters'
    });
  } finally {
    if (connection) connection.end();
  }
};

module.exports = {
  searchTeachers,
  getSearchFilters
};
