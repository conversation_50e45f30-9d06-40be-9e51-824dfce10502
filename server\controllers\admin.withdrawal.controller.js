const pool = require('../config/database');
const axios = require('axios');

const adminWithdrawalController = {
  // Get all withdrawal requests for admin
  getAllWithdrawals: async (req, res) => {
    try {
      const page = parseInt(req.query.page) || 0;
      const limit = parseInt(req.query.limit) || 10;
      const offset = page * limit;
      const status = req.query.status || 'all';

      const connection = await pool.getConnection();

      let whereClause = '';
      let queryParams = [];

      if (status !== 'all') {
        whereClause = 'WHERE wr.status = ?';
        queryParams.push(status);
      }

      // Get withdrawals with pagination
      const [withdrawals] = await connection.query(
        `SELECT 
          wr.*,
          t.full_name as teacher_name,
          t.email as teacher_email,
          p.full_name as processed_by_name
        FROM withdrawal_requests wr
        JOIN users t ON wr.teacher_id = t.id
        LEFT JOIN users p ON wr.processed_by = p.id
        ${whereClause}
        ORDER BY wr.created_at DESC
        LIMIT ? OFFSET ?`,
        [...queryParams, limit, offset]
      );

      // Get total count
      const [countResult] = await connection.query(
        `SELECT COUNT(*) as total FROM withdrawal_requests wr ${whereClause}`,
        queryParams
      );

      connection.release();

      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        data: {
          withdrawals,
          pagination: {
            page,
            limit,
            total,
            totalPages
          }
        }
      });
    } catch (error) {
      console.error('Error fetching withdrawals:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  },

  // Process withdrawal (approve and send via PayPal)
  processWithdrawal: async (req, res) => {
    const { withdrawalId, action, notes } = req.body; // action: 'approve' or 'reject'
    const adminId = req.user.id;

    let connection;
    try {
      connection = await pool.getConnection();
      await connection.beginTransaction();

      // Get withdrawal request
      const [withdrawalResult] = await connection.query(
        `SELECT wr.*, u.full_name as teacher_name, u.email as teacher_email 
         FROM withdrawal_requests wr 
         JOIN users u ON wr.teacher_id = u.id 
         WHERE wr.id = ?`,
        [withdrawalId]
      );

      if (!withdrawalResult.length) {
        await connection.rollback();
        return res.status(404).json({
          success: false,
          error: 'Withdrawal request not found'
        });
      }

      const withdrawal = withdrawalResult[0];

      if (withdrawal.status !== 'pending') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'Withdrawal request already processed'
        });
      }

      if (action === 'approve') {
        // For testing, we'll simulate PayPal payout
        // In production, you would call PayPal Payouts API here
        
        const payoutResult = await simulatePayPalPayout(withdrawal);
        
        if (payoutResult.success) {
          // Update withdrawal status to completed
          await connection.query(
            `UPDATE withdrawal_requests 
             SET status = 'completed', 
                 payout_batch_id = ?, 
                 payout_item_id = ?,
                 admin_notes = ?, 
                 processed_by = ?, 
                 processed_at = NOW() 
             WHERE id = ?`,
            [payoutResult.batch_id, payoutResult.item_id, notes || 'Approved and processed', adminId, withdrawalId]
          );

          await connection.commit();

          res.json({
            success: true,
            message: 'Withdrawal approved and processed successfully'
          });
        } else {
          // Update withdrawal status to failed
          await connection.query(
            `UPDATE withdrawal_requests 
             SET status = 'failed', 
                 admin_notes = ?, 
                 processed_by = ?, 
                 processed_at = NOW() 
             WHERE id = ?`,
            [notes || payoutResult.error, adminId, withdrawalId]
          );

          // Return money to teacher's balance
          await connection.query(
            'UPDATE users SET balance = balance + ? WHERE id = ?',
            [withdrawal.amount, withdrawal.teacher_id]
          );

          await connection.commit();

          res.status(400).json({
            success: false,
            error: 'Payout failed: ' + payoutResult.error
          });
        }
      } else if (action === 'reject') {
        // Update withdrawal status to cancelled
        await connection.query(
          `UPDATE withdrawal_requests 
           SET status = 'cancelled', 
               admin_notes = ?, 
               processed_by = ?, 
               processed_at = NOW() 
           WHERE id = ?`,
          [notes || 'Rejected by admin', adminId, withdrawalId]
        );

        // Return money to teacher's balance
        await connection.query(
          'UPDATE users SET balance = balance + ? WHERE id = ?',
          [withdrawal.amount, withdrawal.teacher_id]
        );

        await connection.commit();

        res.json({
          success: true,
          message: 'Withdrawal request rejected'
        });
      } else {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'Invalid action'
        });
      }
    } catch (error) {
      if (connection) {
        await connection.rollback();
      }
      console.error('Error processing withdrawal:', error);
      res.status(500).json({
        success: false,
        error: 'Error processing withdrawal request'
      });
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }
};

// Simulate PayPal Payout for testing
async function simulatePayPalPayout(withdrawal) {
  try {
    // In a real implementation, you would call PayPal Payouts API here
    // For testing, we'll simulate success/failure randomly
    
    console.log(`Simulating PayPal payout for ${withdrawal.teacher_name}:`);
    console.log(`Amount: $${withdrawal.amount}`);
    console.log(`PayPal Email: ${withdrawal.paypal_email}`);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate 90% success rate for testing
    const isSuccess = Math.random() > 0.1;
    
    if (isSuccess) {
      return {
        success: true,
        batch_id: `BATCH_${Date.now()}`,
        item_id: `ITEM_${Date.now()}_${withdrawal.id}`
      };
    } else {
      return {
        success: false,
        error: 'Simulated PayPal API error - Invalid PayPal email'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = adminWithdrawalController;
