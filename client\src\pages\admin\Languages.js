import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  IconButton,
  Tooltip,
  Container
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import Layout from '../../components/Layout';
import { useAuth } from '../../contexts/AuthContext';

const Languages = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const [languages, setLanguages] = useState([]);
  const [open, setOpen] = useState(false);
  const [editingLanguage, setEditingLanguage] = useState(null);
  const [formData, setFormData] = useState({
    name: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    fetchLanguages();
  }, []);

  const fetchLanguages = async () => {
    try {
      const response = await axios.get('/admin/languages');
      setLanguages(response.data.languages);
    } catch (err) {
      console.error('Error fetching languages:', err);
      setError(t('admin.languages.fetchError'));
    }
  };

  const handleOpen = (language = null) => {
    if (language) {
      setEditingLanguage(language);
      setFormData({
        name: language.name
      });
    } else {
      setEditingLanguage(null);
      setFormData({
        name: ''
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingLanguage(null);
    setFormData({
      name: ''
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (!currentUser) {
        setError(t('admin.languages.unauthorized'));
        return;
      }

      if (!formData.name.trim()) {
        setError(t('admin.languages.nameRequired'));
        return;
      }

      const data = {
        ...formData,
        created_by: currentUser.id
      };

      if (editingLanguage) {
        const response = await axios.put(`/admin/languages/${editingLanguage.id}`, data);
        if (response.data.success) {
          setSuccess(t('admin.languages.updateSuccess'));
          handleClose();
          fetchLanguages();
        } else {
          setError(response.data.message || t('admin.languages.updateError'));
        }
      } else {
        const response = await axios.post('/admin/languages', data);
        if (response.data.success) {
          setSuccess(t('admin.languages.createSuccess'));
          handleClose();
          fetchLanguages();
        } else {
          setError(response.data.message || t('admin.languages.createError'));
        }
      }
    } catch (err) {
      console.error('Error saving language:', err);
      setError(err.response?.data?.message || t('admin.languages.saveError'));
    }
  };

  const handleDelete = async (id) => {
    if (!id) {
      setError(t('admin.languages.invalidId'));
      return;
    }

    if (window.confirm(t('admin.languages.deleteConfirm'))) {
      try {
        const response = await axios.delete(`/admin/languages/${id}`);
        if (response.data.success) {
          setSuccess(t('admin.languages.deleteSuccess'));
          fetchLanguages();
        } else {
          setError(response.data.message || t('admin.languages.deleteError'));
        }
      } catch (err) {
        console.error('Error deleting language:', err);
        setError(err.response?.data?.message || t('admin.languages.deleteError'));
      }
    }
  };

  return (
    <Layout title={t('admin.languages.title')}>
      <Container maxWidth="md">
        {(success || error) && (
          <Box mb={2}>
            {success && <Alert severity="success">{success}</Alert>}
            {error && <Alert severity="error">{error}</Alert>}
          </Box>
        )}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="h4" component="h1">
            {t('admin.languages.title')}
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpen()}
          >
            {t('admin.languages.addNew')}
          </Button>
        </Box>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('admin.languages.name')}</TableCell>
                <TableCell>{t('common.actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {languages.map((language) => (
                <TableRow key={language.id}>
                  <TableCell>{language.name}</TableCell>
                  <TableCell>
                    <Tooltip title={t('common.edit')}>
                      <IconButton onClick={() => handleOpen(language)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title={t('common.delete')}>
                      <IconButton onClick={() => handleDelete(language.id)}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
          <DialogTitle>
            {editingLanguage ? t('admin.languages.editTitle') : t('admin.languages.addTitle')}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ display: 'grid', gap: 2, pt: 2 }}>
              <TextField
                name="name"
                label={t('admin.languages.name')}
                value={formData.name}
                onChange={handleInputChange}
                fullWidth
                required
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleClose}>{t('common.cancel')}</Button>
            <Button onClick={handleSubmit} variant="contained" color="primary">
              {editingLanguage ? t('common.update') : t('common.save')}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
};

export default Languages;
