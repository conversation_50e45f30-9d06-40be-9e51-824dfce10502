/*!
 * paypal-js v8.2.0 (2025-01-23T17:26:53.747Z)
 * Copyright 2020-present, PayPal, Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var paypalLoadScript=function(t){"use strict";function r(t){var r=t.sdkBaseUrl,n=t.environment,o=function(t,r){var e={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&r.indexOf(n)<0&&(e[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)r.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(e[n[o]]=t[n[o]])}return e}(t,["sdkBaseUrl","environment"]),a=r||function(t){return"sandbox"===t?"https://www.sandbox.paypal.com/sdk/js":"https://www.paypal.com/sdk/js"}(n),i=o,c=Object.keys(i).filter((function(t){return void 0!==i[t]&&null!==i[t]&&""!==i[t]})).reduce((function(t,r){var e,n=i[r].toString();return e=function(t,r){return(r?"-":"")+t.toLowerCase()},"data"===(r=r.replace(/[A-Z]+(?![a-z])|[A-Z]/g,e)).substring(0,4)||"crossorigin"===r?t.attributes[r]=n:t.queryParams[r]=n,t}),{queryParams:{},attributes:{}}),u=c.queryParams,s=c.attributes;return u["merchant-id"]&&-1!==u["merchant-id"].indexOf(",")&&(s["data-merchant-id"]=u["merchant-id"],u["merchant-id"]="*"),{url:"".concat(a,"?").concat(e(u)),attributes:s}}function e(t){var r="";return Object.keys(t).forEach((function(e){0!==r.length&&(r+="&"),r+=e+"="+t[e]})),r}function n(t,r){void 0===r&&(r={});var e=document.createElement("script");return e.src=t,Object.keys(r).forEach((function(t){e.setAttribute(t,r[t]),"data-csp-nonce"===t&&e.setAttribute("nonce",r["data-csp-nonce"])})),e}function o(t,r){void 0===r&&(r=Promise),i(t,r);var e=t.url,o=t.attributes;if("string"!=typeof e||0===e.length)throw new Error("Invalid url.");if(void 0!==o&&"object"!=typeof o)throw new Error("Expected attributes to be an object.");return new r((function(t,r){if("undefined"==typeof document)return t();!function(t){var r=t.url,e=t.attributes,o=t.onSuccess,a=t.onError,i=n(r,e);i.onerror=a,i.onload=o,document.head.insertBefore(i,document.head.firstElementChild)}({url:e,attributes:o,onSuccess:function(){return t()},onError:function(){var t=new Error('The script "'.concat(e,'" failed to load. Check the HTTP status code and response body in DevTools to learn more.'));return r(t)}})}))}function a(t){return window[t]}function i(t,r){if("object"!=typeof t||null===t)throw new Error("Expected an options object.");var e=t.environment;if(e&&"production"!==e&&"sandbox"!==e)throw new Error('The `environment` option must be either "production" or "sandbox".');if(void 0!==r&&"function"!=typeof r)throw new Error("Expected PromisePonyfill to be a function.")}"function"==typeof SuppressedError&&SuppressedError;return t.loadCustomScript=o,t.loadScript=function(t,e){if(void 0===e&&(e=Promise),i(t,e),"undefined"==typeof document)return e.resolve(null);var c=r(t),u=c.url,s=c.attributes,d=s["data-namespace"]||"paypal",l=a(d);return s["data-js-sdk-library"]||(s["data-js-sdk-library"]="paypal-js"),function(t,r){var e=document.querySelector('script[src="'.concat(t,'"]'));if(null===e)return null;var o=n(t,r),a=e.cloneNode();if(delete a.dataset.uidAuto,Object.keys(a.dataset).length!==Object.keys(o.dataset).length)return null;var i=!0;return Object.keys(a.dataset).forEach((function(t){a.dataset[t]!==o.dataset[t]&&(i=!1)})),i?e:null}(u,s)&&l?e.resolve(l):o({url:u,attributes:s},e).then((function(){var t=a(d);if(t)return t;throw new Error("The window.".concat(d," global variable is not available."))}))},t.version="8.2.0",t}({});window.paypalLoadCustomScript=paypalLoadScript.loadCustomScript,window.paypalLoadScript=paypalLoadScript.loadScript;
