const db = require('../db');
const bcrypt = require('bcryptjs');

exports.getDashboardStats = async (req, res) => {
  const connection = await db.pool.getConnection();
  try {
    const [totalTeachersResult] = await connection.execute(
      `SELECT COUNT(*) as count FROM users WHERE role IN ('platform_teacher', 'new_teacher')`
    );

    const [pendingApplicationsResult] = await connection.execute(
      `SELECT COUNT(*) as count FROM teacher_profiles WHERE status = 'pending'`
    );

    const [totalStudentsResult] = await connection.execute(
      `SELECT COUNT(*) as count FROM users WHERE role = 'student'`
    );

    const [totalCourseCategoriesResult] = await connection.execute(
      `SELECT COUNT(*) as count FROM course_categories`
    );

    res.json({
      totalTeachers: totalTeachersResult[0].count,
      pendingApplications: pendingApplicationsResult[0].count,
      totalStudents: totalStudentsResult[0].count,
      totalCourseCategories: totalCourseCategoriesResult[0].count
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ message: 'Error fetching dashboard statistics' });
  } finally {
    connection.release();
  }
};

exports.getRecentApplications = async (req, res) => {
  const connection = await db.pool.getConnection();
  try {
    const [applications] = await connection.execute(
      `SELECT 
        tp.id,
        u.full_name,
        tp.country,
        tp.teaching_languages,
        tp.course_types,
        tp.status,
        tp.created_at
      FROM teacher_profiles tp
      INNER JOIN users u ON tp.user_id = u.id
      ORDER BY tp.created_at DESC
      LIMIT 10`
    );

    res.json(applications);
  } catch (error) {
    console.error('Error fetching recent applications:', error);
    res.status(500).json({ message: 'Error fetching recent applications' });
  } finally {
    connection.release();
  }
};

exports.updateProfile = async (req, res) => {
  try {
    const { id } = req.user;
    const { fullName, email } = req.body;

    // Validate required fields
    if (!fullName || !email) {
      return res.status(400).json({ 
        success: false, 
        error: 'All fields are required' 
      });
    }

    // Check if email is taken by another user
    const [existingUsers] = await db.pool.execute(
      'SELECT id FROM users WHERE email = ? AND id != ?',
      [email, id]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Email already exists',
        type: 'email'
      });
    }

    // Update user profile
    await db.pool.execute(
      'UPDATE users SET full_name = ?, email = ? WHERE id = ?',
      [fullName, email, id]
    );

    // Get updated user data
    const [users] = await db.pool.execute(
      'SELECT id, full_name, email, role, gender, profile_picture_url FROM users WHERE id = ?',
      [id]
    );

    if (!users.length) {
      throw new Error('User not found after update');
    }

    res.json({
      success: true,
      user: users[0]
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      success: false,
      error: 'Error updating profile'
    });
  }
};

exports.updatePassword = async (req, res) => {
  try {
    const { id } = req.user;
    const { currentPassword, newPassword } = req.body;

    // Validate required fields
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'All fields are required'
      });
    }

    // Get current user data
    const [users] = await db.pool.execute(
      'SELECT password FROM users WHERE id = ?',
      [id]
    );

    if (!users.length) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, users[0].password);
    if (!isValidPassword) {
      return res.status(400).json({
        success: false,
        error: 'Current password is incorrect',
        type: 'current_password'
      });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password
    await db.pool.execute(
      'UPDATE users SET password = ? WHERE id = ?',
      [hashedPassword, id]
    );

    res.json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    console.error('Update password error:', error);
    res.status(500).json({
      success: false,
      error: 'Error updating password'
    });
  }
};
