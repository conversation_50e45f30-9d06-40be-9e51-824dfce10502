import React from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Typography, Box, Card, CardContent, Grid, Link } from '@mui/material';
import { Email, Phone, LocationOn, Language } from '@mui/icons-material';
import contactUsTranslations from '../i18n/translations/contactUs';
import i18n from '../i18n/i18n';

// merge contact translations
i18n.addResourceBundle('en', 'translation', contactUsTranslations.en, true, true);
i18n.addResourceBundle('ar', 'translation', contactUsTranslations.ar, true, true);

const ContactUs = () => {
  const { t } = useTranslation();

  const contactInfo = [
    {
      icon: <LocationOn sx={{ fontSize: 40, color: '#1976d2' }} />,
      title: t('contact.mailingAddress.title'),
      company: t('contact.mailingAddress.company'),
      content: t('contact.mailingAddress.address')
    },
    {
      icon: <Email sx={{ fontSize: 40, color: '#1976d2' }} />,
      title: t('contact.email.title'),
      content: t('contact.email.address'),
      note: t('contact.email.note'),
      link: `mailto:${t('contact.email.address')}`
    },
    {
      icon: <Phone sx={{ fontSize: 40, color: '#1976d2' }} />,
      title: t('contact.phone.title'),
      content: t('contact.phone.number'),
      note: t('contact.phone.note'),
      link: `tel:${t('contact.phone.number')}`
    },
    {
      icon: <Language sx={{ fontSize: 40, color: '#1976d2' }} />,
      title: t('contact.website.title'),
      content: t('contact.website.url'),
      link: `https://${t('contact.website.url')}`
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4, pt: { xs: 10, sm: 12, md: 14 } }}>
      <Box textAlign="center" mb={6}>
        <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
          {t('contact.title')}
        </Typography>
        <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 800, mx: 'auto', lineHeight: 1.6 }}>
          {t('contact.subtitle')}
        </Typography>
      </Box>

      <Grid container spacing={4}>
        {contactInfo.map((info, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card 
              sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column',
                transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1, textAlign: 'center', p: 3 }}>
                <Box mb={2}>
                  {info.icon}
                </Box>
                
                <Typography variant="h6" component="h3" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
                  {info.title}
                </Typography>
                
                {info.company && (
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                    {info.company}
                  </Typography>
                )}
                
                {info.link ? (
                  <Link 
                    href={info.link} 
                    target={info.link.startsWith('mailto:') || info.link.startsWith('tel:') ? '_self' : '_blank'}
                    rel="noopener noreferrer"
                    sx={{ 
                      textDecoration: 'none',
                      color: '#1976d2',
                      '&:hover': {
                        textDecoration: 'underline'
                      }
                    }}
                  >
                    <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                      {info.content}
                    </Typography>
                  </Link>
                ) : (
                  <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                    {info.content}
                  </Typography>
                )}
                
                {info.note && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2, fontStyle: 'italic' }}>
                    {info.note}
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box mt={8} textAlign="center">
        <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
          {t('contact.title')}
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
          {t('contact.subtitle')}
        </Typography>
      </Box>
    </Container>
  );
};

export default ContactUs; 