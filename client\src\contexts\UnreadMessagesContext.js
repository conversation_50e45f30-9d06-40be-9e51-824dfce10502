import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSocket } from './SocketContext';
import { useAuth } from './AuthContext';

const UnreadMessagesContext = createContext();

export const useUnreadMessages = () => {
  const context = useContext(UnreadMessagesContext);
  if (!context) {
    throw new Error('useUnreadMessages must be used within UnreadMessagesProvider');
  }
  return context;
};

export const UnreadMessagesProvider = ({ children }) => {
  const [unreadCount, setUnreadCount] = useState(0);
  const { socket, isConnected } = useSocket();
  const { currentUser } = useAuth();

  // Function to fetch unread count from server
  const fetchUnreadCount = () => {
    if (!socket || !isConnected || !currentUser) {
      setUnreadCount(0);
      return;
    }

    if (currentUser.role === 'platform_teacher') {
      socket.emit('get_teacher_chats', { teacherId: currentUser.id }, (response) => {
        if (response.success && response.chats) {
          const totalUnread = response.chats.reduce((total, chat) => {
            return total + (chat.unread_count || 0);
          }, 0);
          setUnreadCount(totalUnread);
        }
      });
    } else if (currentUser.role === 'student') {
      socket.emit('get_student_chats', { studentId: currentUser.id }, (response) => {
        if (response.success && response.chats) {
          const totalUnread = response.chats.reduce((total, chat) => {
            return total + (chat.unread_count || 0);
          }, 0);
          setUnreadCount(totalUnread);
        }
      });
    }
  };

  // Function to manually decrease count (for immediate updates)
  const decreaseUnreadCount = (amount = 1) => {
    setUnreadCount(prev => Math.max(0, prev - amount));
  };

  // Function to manually increase count
  const increaseUnreadCount = (amount = 1) => {
    setUnreadCount(prev => prev + amount);
  };

  // Function to reset count
  const resetUnreadCount = () => {
    setUnreadCount(0);
  };

  useEffect(() => {
    if (!socket || !isConnected || !currentUser) {
      setUnreadCount(0);
      return;
    }

    // Initial fetch
    fetchUnreadCount();

    // Listen for new messages
    const handleNewMessage = (message) => {
      // Only count if the message is not from the current user
      if (message.sender_id !== currentUser.id) {
        increaseUnreadCount(1);
      }
    };

    // Listen for message read events
    const handleMessagesRead = ({ chatId }) => {
      // Refresh the count when messages are read
      setTimeout(() => {
        fetchUnreadCount();
      }, 100);
    };

    // Set up socket listeners
    socket.on('new_message', handleNewMessage);
    socket.on('messages_read', handleMessagesRead);

    // Refresh count every 30 seconds
    const interval = setInterval(fetchUnreadCount, 30000);

    // Cleanup
    return () => {
      socket.off('new_message', handleNewMessage);
      socket.off('messages_read', handleMessagesRead);
      clearInterval(interval);
    };
  }, [socket, isConnected, currentUser]);

  const value = {
    unreadCount,
    fetchUnreadCount,
    decreaseUnreadCount,
    increaseUnreadCount,
    resetUnreadCount
  };

  return (
    <UnreadMessagesContext.Provider value={value}>
      {children}
    </UnreadMessagesContext.Provider>
  );
};
