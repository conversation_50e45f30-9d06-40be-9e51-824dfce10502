const pool = require('../config/database');
const { calculateCommission } = require('../utils/commission');

const lessonController = {
  // Complete a lesson and apply commission
  completeLesson: async (req, res) => {
    const { meetingId } = req.params;
    const connection = await pool.getConnection();
    
    try {
      await connection.beginTransaction();

      // Get meeting details
      const [meetings] = await connection.query(
        `SELECT m.*, tp.user_id as teacher_user_id 
         FROM meetings m
         JOIN teacher_profiles tp ON m.teacher_id = tp.id
         WHERE m.id = ? AND m.status = 'scheduled'`,
        [meetingId]
      );

      if (meetings.length === 0) {
        await connection.rollback();
        return res.status(404).json({
          success: false,
          error: 'Meeting not found or already completed'
        });
      }

      const meeting = meetings[0];
      const lessonAmount = parseFloat(meeting.amount);

      // Calculate commission
      const commission = calculateCommission(lessonAmount);

      // Update meeting status to completed
      await connection.query(
        'UPDATE meetings SET status = "completed" WHERE id = ?',
        [meetingId]
      );

      // Add teacher earnings to their balance (after commission)
      await connection.query(
        'UPDATE users SET balance = balance + ? WHERE id = ?',
        [commission.teacherEarnings, meeting.teacher_user_id]
      );

      // Record admin earnings
      await connection.query(
        `INSERT INTO admin_earnings 
         (meeting_id, teacher_id, student_id, lesson_amount, commission_rate, commission_amount, teacher_earnings)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          meetingId,
          meeting.teacher_user_id,
          meeting.student_id,
          lessonAmount,
          commission.commissionRate,
          commission.commissionAmount,
          commission.teacherEarnings
        ]
      );

      await connection.commit();

      res.json({
        success: true,
        message: 'Lesson completed successfully',
        commission: commission
      });

    } catch (error) {
      await connection.rollback();
      console.error('Error completing lesson:', error);
      res.status(500).json({
        success: false,
        error: 'Error completing lesson'
      });
    } finally {
      connection.release();
    }
  },

  // Get admin earnings summary
  getAdminEarnings: async (req, res) => {
    console.log('[LESSON CONTROLLER] getAdminEarnings called');
    console.log('[LESSON CONTROLLER] Query params:', req.query);
    try {
      const { startDate, endDate, page = 0, limit = 10 } = req.query;
      const offset = page * limit;

      let whereClause = '';
      let queryParams = [];

      if (startDate && endDate) {
        whereClause = 'WHERE ae.created_at BETWEEN ? AND ?';
        queryParams.push(startDate, endDate);
      }

      // Get total earnings
      const [totalResult] = await pool.query(
        `SELECT 
           COUNT(*) as total_lessons,
           SUM(commission_amount) as total_commission,
           SUM(lesson_amount) as total_lesson_amount,
           SUM(teacher_earnings) as total_teacher_earnings,
           AVG(commission_rate) as avg_commission_rate
         FROM admin_earnings ae ${whereClause}`,
        queryParams
      );

      // Get detailed earnings with pagination
      const [earnings] = await pool.query(
        `SELECT 
           ae.*,
           t.full_name as teacher_name,
           s.full_name as student_name,
           m.meeting_name
         FROM admin_earnings ae
         JOIN users t ON ae.teacher_id = t.id
         JOIN users s ON ae.student_id = s.id
         JOIN meetings m ON ae.meeting_id = m.id
         ${whereClause}
         ORDER BY ae.created_at DESC
         LIMIT ? OFFSET ?`,
        [...queryParams, parseInt(limit), parseInt(offset)]
      );

      // Get total count for pagination
      const [countResult] = await pool.query(
        `SELECT COUNT(*) as count FROM admin_earnings ae ${whereClause}`,
        queryParams
      );

      res.json({
        success: true,
        data: {
          summary: totalResult[0] || {
            total_lessons: 0,
            total_commission: 0,
            total_lesson_amount: 0,
            total_teacher_earnings: 0,
            avg_commission_rate: 0
          },
          earnings: earnings || [],
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: countResult[0].count,
            totalPages: Math.ceil(countResult[0].count / limit)
          }
        }
      });

    } catch (error) {
      console.error('[LESSON CONTROLLER] Error fetching admin earnings:', error);
      console.error('[LESSON CONTROLLER] Error stack:', error.stack);
      res.status(500).json({
        success: false,
        error: 'Error fetching admin earnings',
        details: error.message
      });
    }
  }
};

module.exports = lessonController;
