{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useNavigate,useLocation}from'react-router-dom';import axios from'../utils/axios';import{Box,CssBaseline,Drawer,AppBar,Toolbar,List,Typography,Divider,IconButton,ListItem,ListItemIcon,ListItemText,ListItemButton,Container,useTheme,useMediaQuery,Tooltip,Menu,MenuItem,Avatar,Button,alpha,Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle,Badge,Link}from'@mui/material';import{Menu as MenuIcon,Dashboard as DashboardIcon,Person as PersonIcon,School as SchoolIcon,Chat as ChatIcon,Search as SearchIcon,Category as CategoryIcon,Assignment as AssignmentIcon,Translate as TranslateIcon,KeyboardArrowDown as KeyboardArrowDownIcon,Language as LanguageIcon,Logout as LogoutIcon,AccountCircle as AccountCircleIcon,VideoCall as VideoCallIcon,ContactSupport as ContactSupportIcon,AccountBalanceWallet as AccountBalanceWalletIcon,Payment as PaymentIcon,TrendingUp as TrendingUpIcon,Email as EmailIcon,CalendarMonth as CalendarMonthIcon,Star as StarIcon,RateReview as RateReviewIcon,Edit as EditIcon,Delete as DeleteIcon,AccessTime as AccessTimeIcon,Policy as PolicyIcon,MenuBook as MenuBookIcon,ReportProblem as ReportProblemIcon}from'@mui/icons-material';import dayjs from'dayjs';import utc from'dayjs/plugin/utc';import{useAuth}from'../contexts/AuthContext';import MeetingFeedbackDialog from'./MeetingFeedbackDialog';import{useUnreadMessages}from'../contexts/UnreadMessagesContext';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";dayjs.extend(utc);const drawerWidth=240;const Layout=_ref=>{var _currentUser$full_nam;let{children}=_ref;const[pendingDialogOpen,setPendingDialogOpen]=useState(false);const[pendingIssue,setPendingIssue]=useState(null);const{t,i18n}=useTranslation();const{currentUser,handleLogout}=useAuth();const navigate=useNavigate();const location=useLocation();const theme=useTheme();const isRtl=i18n.language==='ar';const isMobile=useMediaQuery(theme.breakpoints.down('sm'));const isTablet=useMediaQuery(theme.breakpoints.between('sm','md'));const[mobileOpen,setMobileOpen]=useState(false);const[anchorEl,setAnchorEl]=useState(null);const[langMenuAnchor,setLangMenuAnchor]=useState(null);const[logoutDialogOpen,setLogoutDialogOpen]=useState(false);const[balance,setBalance]=useState(null);const{unreadCount}=useUnreadMessages();// Helper to fetch pending feedback\nconst fetchPending=async()=>{if((currentUser===null||currentUser===void 0?void 0:currentUser.role)!=='student')return;try{const{data}=await axios.get('/meeting-issues/pending');if(data.success&&data.data){console.log('Found pending feedback issue:',data.data);setPendingIssue(data.data);setPendingDialogOpen(true);}else{console.log('No pending feedback issues found');setPendingIssue(null);setPendingDialogOpen(false);}}catch(err){console.error('Error fetching pending feedback',err);}};// Fetch on mount / route change\nuseEffect(()=>{fetchPending();},[currentUser,location.pathname]);// Try to attach booking_id to pending issue if missing\nuseEffect(()=>{if(!pendingIssue||pendingIssue.booking_id)return;// Fetch bookings to find matching one\nconst findMatchingBooking=async()=>{try{const{data}=await axios.get('/bookings');if(data.success&&data.data){const bookings=data.data;const targetTime=dayjs.utc(pendingIssue.datetime);// Find booking with same teacher and date within 2 hours window\nconst candidates=bookings.filter(b=>{if(b.teacher_name!==pendingIssue.teacher_name)return false;const bTime=dayjs.utc(b.datetime);const timeDiff=Math.abs(bTime.diff(targetTime,'minutes'));return timeDiff<=120;});// Sort by closest time match\ncandidates.sort((a,b)=>{const aDiff=Math.abs(dayjs.utc(a.datetime).diff(targetTime,'minutes'));const bDiff=Math.abs(dayjs.utc(b.datetime).diff(targetTime,'minutes'));return aDiff-bDiff;});if(candidates.length>0){const bestMatch=candidates[0];console.log(`Global feedback: Linking meeting ${pendingIssue.meeting_id} -> booking ${bestMatch.id}`);setPendingIssue(prev=>({...prev,booking_id:bestMatch.id}));}else{console.warn(`Global feedback: No matching booking found for meeting ${pendingIssue.meeting_id}`);}}}catch(err){console.error('Error fetching bookings for global feedback:',err);}};findMatchingBooking();},[pendingIssue]);useEffect(()=>{document.dir=isRtl?'rtl':'ltr';},[isRtl]);useEffect(()=>{if(currentUser){fetchBalance();}},[currentUser]);const fetchBalance=async()=>{try{const response=await axios.get('/api/wallet/balance');if(response.data.success){setBalance(response.data.balance);}}catch(error){console.error('Error fetching balance:',error);}};const handleDrawerToggle=()=>{const newState=!mobileOpen;setMobileOpen(newState);if(isMobile){localStorage.setItem('drawerOpen',newState?'true':'false');}// Check for pending feedback when drawer opens\nif(newState){fetchPending();}};const handleLangMenuOpen=event=>{setLangMenuAnchor(event.currentTarget);};const handleLangMenuClose=()=>{setLangMenuAnchor(null);};const handleLanguageChange=lang=>{i18n.changeLanguage(lang);localStorage.setItem('language',lang);handleLangMenuClose();};const getMenuItems=()=>{if((currentUser===null||currentUser===void 0?void 0:currentUser.role)==='platform_teacher'){return[{text:t('menu.dashboard'),icon:/*#__PURE__*/_jsx(DashboardIcon,{}),path:'/teacher/dashboard'},{text:t('bookings.title'),icon:/*#__PURE__*/_jsx(CalendarMonthIcon,{}),path:'/teacher/bookings'},{text:t('menu.meetings'),icon:/*#__PURE__*/_jsx(VideoCallIcon,{}),path:'/teacher/meetings'},{text:t('teacher.myLessons'),icon:/*#__PURE__*/_jsx(MenuBookIcon,{}),path:'/teacher/my-lessons'},{text:t('menu.profile'),icon:/*#__PURE__*/_jsx(PersonIcon,{}),path:'/teacher/profile'},{text:t('menu.chat'),icon:/*#__PURE__*/_jsx(ChatIcon,{}),path:'/teacher/chat',badge:unreadCount},{text:t('reviews.teacherReviews'),icon:/*#__PURE__*/_jsx(StarIcon,{}),path:'/teacher/reviews'},{text:t('wallet.title'),icon:/*#__PURE__*/_jsx(AccountBalanceWalletIcon,{}),path:'/teacher/wallet'},{text:t('withdrawal.title'),icon:/*#__PURE__*/_jsx(PaymentIcon,{}),path:'/teacher/withdrawal'},{text:t('contactUs.title'),icon:/*#__PURE__*/_jsx(ContactSupportIcon,{}),path:'/teacher/contact-us'},{text:t('myMessages.title'),icon:/*#__PURE__*/_jsx(EmailIcon,{}),path:'/teacher/my-messages'},{text:t('menu.platformPolicy'),icon:/*#__PURE__*/_jsx(PolicyIcon,{}),path:'/platform-policy'}];}const adminItems=[{text:t('nav.dashboard'),path:'/admin/dashboard',icon:/*#__PURE__*/_jsx(DashboardIcon,{})},{text:t('nav.applications'),path:'/admin/applications',icon:/*#__PURE__*/_jsx(AssignmentIcon,{})},{text:t('nav.teachers'),path:'/admin/teachers',icon:/*#__PURE__*/_jsx(SchoolIcon,{})},{text:t('nav.students'),path:'/admin/students',icon:/*#__PURE__*/_jsx(PersonIcon,{})},{text:t('nav.deletedUsers','المستخدمون المحذوفون'),path:'/admin/deleted-users',icon:/*#__PURE__*/_jsx(DeleteIcon,{})},{text:t('nav.profileUpdates'),path:'/admin/profile-updates',icon:/*#__PURE__*/_jsx(EditIcon,{})},{text:t('admin.meetingSessions.title'),path:'/admin/meeting-sessions',icon:/*#__PURE__*/_jsx(AccessTimeIcon,{})},{text:t('admin.meetingIssues.title','Meeting Issues'),path:'/admin/meeting-issues',icon:/*#__PURE__*/_jsx(ReportProblemIcon,{})},{text:t('nav.categories'),path:'/admin/categories',icon:/*#__PURE__*/_jsx(CategoryIcon,{})},{text:t('nav.languages'),path:'/admin/languages',icon:/*#__PURE__*/_jsx(LanguageIcon,{})},{text:t('nav.withdrawalManagement'),icon:/*#__PURE__*/_jsx(PaymentIcon,{}),path:'/admin/withdrawals'},{text:t('admin.earnings.title'),icon:/*#__PURE__*/_jsx(TrendingUpIcon,{}),path:'/admin/earnings'},{text:t('common.profile'),path:'/admin/profile',icon:/*#__PURE__*/_jsx(PersonIcon,{})},{text:t('wallet.title'),path:'/admin/wallet',icon:/*#__PURE__*/_jsx(AccountBalanceWalletIcon,{})},{text:t('admin.messages.title'),path:'/admin/messages',icon:/*#__PURE__*/_jsx(ContactSupportIcon,{})},{text:t('menu.platformPolicy'),path:'/platform-policy',icon:/*#__PURE__*/_jsx(PolicyIcon,{})}];const newTeacherItems=[{text:t('nav.applications'),path:'/teacher/application',icon:/*#__PURE__*/_jsx(AssignmentIcon,{})},{text:t('common.profile'),path:'/teacher/profile',icon:/*#__PURE__*/_jsx(PersonIcon,{})},{text:t('menu.platformPolicy'),path:'/platform-policy',icon:/*#__PURE__*/_jsx(PolicyIcon,{})}];const studentItems=[{text:t('nav.dashboard'),path:'/student/dashboard',icon:/*#__PURE__*/_jsx(DashboardIcon,{})},{text:t('nav.findTeacher'),path:'/student/find-teacher',icon:/*#__PURE__*/_jsx(SearchIcon,{})},{text:t('bookings.title'),path:'/student/bookings',icon:/*#__PURE__*/_jsx(CalendarMonthIcon,{})},{text:t('nav.myTeachers'),path:'/student/my-teachers',icon:/*#__PURE__*/_jsx(SchoolIcon,{})},{text:t('nav.meetings'),path:'/student/meetings',icon:/*#__PURE__*/_jsx(VideoCallIcon,{})},{text:t('nav.chat'),path:'/student/chat',icon:/*#__PURE__*/_jsx(ChatIcon,{}),badge:unreadCount},{text:t('reviews.writeReview'),path:'/student/write-review',icon:/*#__PURE__*/_jsx(RateReviewIcon,{})},{text:t('common.profile'),path:'/student/profile',icon:/*#__PURE__*/_jsx(PersonIcon,{})},{text:t('wallet.title'),path:'/student/wallet',icon:/*#__PURE__*/_jsx(AccountBalanceWalletIcon,{})},{text:t('contactUs.title'),path:'/student/contact-us',icon:/*#__PURE__*/_jsx(ContactSupportIcon,{})},{text:t('myMessages.title'),path:'/student/my-messages',icon:/*#__PURE__*/_jsx(EmailIcon,{})},{text:t('menu.platformPolicy'),path:'/platform-policy',icon:/*#__PURE__*/_jsx(PolicyIcon,{})}];switch(currentUser===null||currentUser===void 0?void 0:currentUser.role){case'admin':return adminItems;case'new_teacher':return newTeacherItems;case'student':return studentItems;default:return[];}};const handleProfileClick=()=>{switch(currentUser===null||currentUser===void 0?void 0:currentUser.role){case'admin':navigate('/admin/profile');break;case'platform_teacher':case'new_teacher':navigate('/teacher/profile');break;case'student':navigate('/student/profile');break;default:navigate('/login');}};const handleLogoutClick=()=>{setAnchorEl(null);// Close menu immediately for better UX\nsetLogoutDialogOpen(true);};const handleConfirmLogout=async()=>{setLogoutDialogOpen(false);try{await handleLogout();}catch(error){console.error('Logout failed:',error);}};const handleCancelLogout=()=>{setLogoutDialogOpen(false);};const drawer=/*#__PURE__*/_jsxs(Box,{children:[currentUser&&balance!==null?/*#__PURE__*/_jsxs(Box,{sx:{mx:2,my:2,p:2,bgcolor:'primary.light',borderRadius:2,display:'flex',alignItems:'center',justifyContent:'center',gap:1.5,minHeight:64},children:[/*#__PURE__*/_jsx(AccountBalanceWalletIcon,{sx:{color:'primary.contrastText',fontSize:'1.5rem'}}),/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:'primary.contrastText',fontSize:'0.75rem',opacity:0.9,display:'block',lineHeight:1.2},children:t('wallet.balance')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{color:'primary.contrastText',fontSize:'1.1rem',fontWeight:700,lineHeight:1.2,mt:0.5},children:[\"$\",balance]})]})]}):/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'center',p:2,minHeight:64},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontFamily:'Tajawal, sans-serif',fontWeight:600,color:'primary.main'},children:t('brand.name')})}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsx(List,{children:getMenuItems().map(item=>/*#__PURE__*/_jsxs(ListItem,{component:\"div\",onClick:()=>{if(location.pathname===item.path){navigate(0);// Reload current page\n}else{navigate(item.path);}},selected:location.pathname===item.path,sx:{minHeight:48,px:2.5,cursor:'pointer',borderRadius:1,mx:1,mb:0.5,'&:hover':{backgroundColor:'action.hover'},'&.Mui-selected':{backgroundColor:'primary.main',color:'primary.contrastText','&:hover':{backgroundColor:'primary.dark'},'& .MuiListItemIcon-root':{color:'primary.contrastText'}}},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:0,mr:isRtl?0:2,ml:isRtl?2:0,justifyContent:'center'},children:item.badge&&item.badge>0?/*#__PURE__*/_jsx(Badge,{badgeContent:item.badge,color:\"error\",max:99,children:item.icon}):item.icon}),/*#__PURE__*/_jsx(ListItemText,{primary:item.text,sx:{'& .MuiListItemText-primary':{fontSize:'0.9rem',fontWeight:500}}})]},item.text))})]});const handleGlobalFeedbackSubmit=async(meetingId,values)=>{try{console.log('Submitting global feedback:',{meetingId,values});await axios.post('/meeting-issues',{meeting_id:meetingId,booking_id:values.booking_id,// Include booking_id\nissue_type:values.issue_type,description:values.description});console.log('Global feedback submitted successfully');}catch(err){console.error('Failed to submit global feedback',err);// Show error message to user\nalert('Failed to submit feedback. Please try again.');return;// Don't close dialog on error\n}finally{setPendingDialogOpen(false);setPendingIssue(null);// Check if there is another pending meeting\nawait fetchPending();}};return/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',direction:isRtl?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(AppBar,{position:\"fixed\",sx:{width:'100%',zIndex:theme.zIndex.drawer+1},children:/*#__PURE__*/_jsxs(Toolbar,{sx:{minHeight:{xs:56,sm:64,md:70},px:{xs:1,sm:2,md:3}},children:[/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",\"aria-label\":\"open drawer\",edge:isRtl?'end':'start',onClick:handleDrawerToggle,sx:{mr:isRtl?0:{xs:1,sm:2},ml:isRtl?{xs:1,sm:2}:0,display:{sm:'none'},p:{xs:1,sm:1.5}},children:/*#__PURE__*/_jsx(MenuIcon,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",noWrap:true,component:\"div\",sx:{flexGrow:1,fontSize:{xs:'1rem',sm:'1.1rem',md:'1.25rem'},fontFamily:'Tajawal, sans-serif',fontWeight:{xs:500,md:600}},children:t('brand.name')}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:{xs:0.5,sm:1,md:1.5}},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Button,{color:\"inherit\",startIcon:/*#__PURE__*/_jsx(TranslateIcon,{sx:{fontSize:{xs:'1rem',sm:'1.1rem',md:'1.2rem'}}}),endIcon:/*#__PURE__*/_jsx(KeyboardArrowDownIcon,{sx:{fontSize:{xs:'1rem',sm:'1.1rem',md:'1.2rem'}}}),onClick:handleLangMenuOpen,sx:{minWidth:{xs:80,sm:100,md:120},borderRadius:2,px:{xs:1,sm:1.5,md:2},py:{xs:0.5,sm:0.75,md:1},gap:{xs:0.5,sm:0.75,md:1},fontSize:{xs:'0.75rem',sm:'0.85rem',md:'0.9rem'},'& .MuiButton-startIcon':{margin:0},'& .MuiButton-endIcon':{margin:0,ml:0.5},'&:hover':{backgroundColor:alpha(theme.palette.common.white,0.1),transform:'translateY(-1px)'},transition:'all 0.2s ease-in-out'},children:i18n.language==='ar'?'العربية':'English'}),/*#__PURE__*/_jsxs(Menu,{anchorEl:langMenuAnchor,open:Boolean(langMenuAnchor),onClose:handleLangMenuClose,anchorOrigin:{vertical:'bottom',horizontal:'right'},transformOrigin:{vertical:'top',horizontal:'right'},slotProps:{paper:{elevation:0,sx:{mt:1.5,overflow:'visible',filter:'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))','&:before':{content:'\"\"',display:'block',position:'absolute',top:0,right:14,width:10,height:10,bgcolor:'background.paper',transform:'translateY(-50%) rotate(45deg)',zIndex:0}}}},children:[/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleLanguageChange('ar'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(LanguageIcon,{fontSize:\"small\"})}),\"\\u0627\\u0644\\u0639\\u0631\\u0628\\u064A\\u0629\"]}),/*#__PURE__*/_jsxs(MenuItem,{onClick:()=>handleLanguageChange('en'),children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(LanguageIcon,{fontSize:\"small\"})}),\"English\"]})]})]}),currentUser&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(IconButton,{onClick:event=>setAnchorEl(event.currentTarget),sx:{ml:{xs:0.5,sm:1,md:1.5},p:{xs:0.5,sm:0.75,md:1},'&:hover':{transform:'scale(1.05)',bgcolor:'rgba(255,255,255,0.1)'},transition:'all 0.2s ease-in-out'},children:/*#__PURE__*/_jsx(Avatar,{src:currentUser.profile_picture_url?currentUser.profile_picture_url.startsWith('http')?currentUser.profile_picture_url:`https://allemnionline.com${currentUser.profile_picture_url}`:'',alt:currentUser.full_name,sx:{width:{xs:32,sm:36,md:40},height:{xs:32,sm:36,md:40},bgcolor:'primary.main',border:'2px solid',borderColor:'background.paper',fontSize:{xs:'0.9rem',sm:'1rem',md:'1.1rem'},fontWeight:600},children:!currentUser.profile_picture_url&&((_currentUser$full_nam=currentUser.full_name)===null||_currentUser$full_nam===void 0?void 0:_currentUser$full_nam.charAt(0))})}),/*#__PURE__*/_jsxs(Menu,{anchorEl:anchorEl,open:Boolean(anchorEl),onClose:()=>setAnchorEl(null),onClick:()=>setAnchorEl(null),transformOrigin:{horizontal:isRtl?'left':'right',vertical:'top'},anchorOrigin:{horizontal:isRtl?'left':'right',vertical:'bottom'},children:[/*#__PURE__*/_jsxs(MenuItem,{onClick:handleProfileClick,children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(PersonIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('common.profile')})]}),/*#__PURE__*/_jsx(Divider,{}),/*#__PURE__*/_jsxs(MenuItem,{onClick:handleLogoutClick,children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(LogoutIcon,{fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('auth.logout')})]})]})]}),!currentUser&&/*#__PURE__*/_jsx(Button,{color:\"inherit\",onClick:()=>navigate('/login'),sx:{ml:{xs:0.5,sm:1,md:1.5},px:{xs:1.5,sm:2,md:2.5},py:{xs:0.5,sm:0.75,md:1},fontSize:{xs:'0.8rem',sm:'0.9rem',md:'1rem'},borderRadius:2,'&:hover':{backgroundColor:alpha(theme.palette.common.white,0.1),transform:'translateY(-1px)'},transition:'all 0.2s ease-in-out'},children:t('auth.login')})]})]})}),/*#__PURE__*/_jsx(Box,{component:\"nav\",sx:{width:{sm:drawerWidth},flexShrink:{sm:0}},children:/*#__PURE__*/_jsx(Drawer,{variant:isMobile?'temporary':'permanent',open:isMobile?mobileOpen:true,onClose:handleDrawerToggle,anchor:isRtl?'right':'left',ModalProps:{keepMounted:true},sx:{'& .MuiDrawer-paper':{boxSizing:'border-box',width:drawerWidth,top:{xs:'56px',sm:'64px',md:'70px'},height:{xs:'calc(100vh - 56px)',sm:'calc(100vh - 64px)',md:'calc(100vh - 70px)'},borderRight:isRtl?'none':'1px solid rgba(0, 0, 0, 0.12)',borderLeft:isRtl?'1px solid rgba(0, 0, 0, 0.12)':'none',overflowX:'hidden',position:'fixed',zIndex:1000,'&::-webkit-scrollbar':{width:'6px'},'&::-webkit-scrollbar-track':{background:'#f1f1f1'},'&::-webkit-scrollbar-thumb':{background:'#888',borderRadius:'3px'},'&::-webkit-scrollbar-thumb:hover':{background:'#555'}}},children:drawer})}),/*#__PURE__*/_jsx(Box,{component:\"main\",sx:{flexGrow:1,p:{xs:0.5,sm:1,md:1.5},width:'100%',marginTop:{xs:'56px',sm:'64px',md:'70px'},minHeight:`calc(100vh - ${isMobile?'56px':isTablet?'64px':'70px'})`,overflow:'auto'},children:/*#__PURE__*/_jsxs(Container,{maxWidth:\"xl\",sx:{px:{xs:1,sm:2,md:3},py:{xs:1,sm:1.5,md:2}},children:[children,(currentUser===null||currentUser===void 0?void 0:currentUser.role)==='student'&&/*#__PURE__*/_jsx(MeetingFeedbackDialog,{open:pendingDialogOpen&&pendingIssue&&dayjs.utc().isAfter(dayjs.utc(pendingIssue.datetime).add(pendingIssue.duration||50,'minute')),meeting:pendingIssue,timezone:(currentUser===null||currentUser===void 0?void 0:currentUser.timezone)||null,onSubmit:handleGlobalFeedbackSubmit,onClose:null/* Prevent manual close */})]})}),/*#__PURE__*/_jsxs(Dialog,{open:logoutDialogOpen,onClose:handleCancelLogout,\"aria-labelledby\":\"logout-dialog-title\",\"aria-describedby\":\"logout-dialog-description\",children:[/*#__PURE__*/_jsx(DialogTitle,{id:\"logout-dialog-title\",children:t('auth.logoutConfirmTitle')}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(DialogContentText,{id:\"logout-dialog-description\",children:t('auth.logoutConfirmMessage')})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCancelLogout,color:\"primary\",children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{onClick:handleConfirmLogout,color:\"primary\",autoFocus:true,children:t('common.confirm')})]})]})]});};export default Layout;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "useLocation", "axios", "Box", "CssBaseline", "Drawer", "AppBar", "<PERSON><PERSON><PERSON>", "List", "Typography", "Divider", "IconButton", "ListItem", "ListItemIcon", "ListItemText", "ListItemButton", "Container", "useTheme", "useMediaQuery", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Avatar", "<PERSON><PERSON>", "alpha", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "Badge", "Link", "MenuIcon", "Dashboard", "DashboardIcon", "Person", "PersonIcon", "School", "SchoolIcon", "Cha<PERSON>", "ChatIcon", "Search", "SearchIcon", "Category", "CategoryIcon", "Assignment", "AssignmentIcon", "Translate", "TranslateIcon", "KeyboardArrowDown", "KeyboardArrowDownIcon", "Language", "LanguageIcon", "Logout", "LogoutIcon", "AccountCircle", "AccountCircleIcon", "VideoCall", "VideoCallIcon", "ContactSupport", "ContactSupportIcon", "AccountBalanceWallet", "AccountBalanceWalletIcon", "Payment", "PaymentIcon", "TrendingUp", "TrendingUpIcon", "Email", "EmailIcon", "CalendarMonth", "CalendarMonthIcon", "Star", "StarIcon", "RateReview", "RateReviewIcon", "Edit", "EditIcon", "Delete", "DeleteIcon", "AccessTime", "AccessTimeIcon", "Policy", "PolicyIcon", "MenuBook", "MenuBookIcon", "ReportProblem", "ReportProblemIcon", "dayjs", "utc", "useAuth", "MeetingFeedbackDialog", "useUnreadMessages", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "extend", "drawerWidth", "Layout", "_ref", "_currentUser$full_nam", "children", "pendingDialogOpen", "setPendingDialogOpen", "pendingIssue", "setPendingIssue", "t", "i18n", "currentUser", "handleLogout", "navigate", "location", "theme", "isRtl", "language", "isMobile", "breakpoints", "down", "isTablet", "between", "mobileOpen", "setMobileOpen", "anchorEl", "setAnchorEl", "langMenuAnchor", "setLangMenuAnchor", "logoutDialogOpen", "setLogoutDialogOpen", "balance", "setBalance", "unreadCount", "fetchPending", "role", "data", "get", "success", "console", "log", "err", "error", "pathname", "booking_id", "findMatchingBooking", "bookings", "targetTime", "datetime", "candidates", "filter", "b", "teacher_name", "bTime", "timeDiff", "Math", "abs", "diff", "sort", "a", "aDiff", "bDiff", "length", "bestMatch", "meeting_id", "id", "prev", "warn", "document", "dir", "fetchBalance", "response", "handleDrawerToggle", "newState", "localStorage", "setItem", "handleLangMenuOpen", "event", "currentTarget", "handleLangMenuClose", "handleLanguageChange", "lang", "changeLanguage", "getMenuItems", "text", "icon", "path", "badge", "adminItems", "newTeacherItems", "studentItems", "handleProfileClick", "handleLogoutClick", "handleConfirmLogout", "handleCancelLogout", "drawer", "sx", "mx", "my", "p", "bgcolor", "borderRadius", "display", "alignItems", "justifyContent", "gap", "minHeight", "color", "fontSize", "textAlign", "variant", "opacity", "lineHeight", "fontWeight", "mt", "fontFamily", "map", "item", "component", "onClick", "selected", "px", "cursor", "mb", "backgroundColor", "min<PERSON><PERSON><PERSON>", "mr", "ml", "badgeContent", "max", "primary", "handleGlobalFeedbackSubmit", "meetingId", "values", "post", "issue_type", "description", "alert", "direction", "position", "width", "zIndex", "xs", "sm", "md", "edge", "noWrap", "flexGrow", "startIcon", "endIcon", "py", "margin", "palette", "common", "white", "transform", "transition", "open", "Boolean", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "slotProps", "paper", "elevation", "overflow", "content", "top", "right", "height", "src", "profile_picture_url", "startsWith", "alt", "full_name", "border", "borderColor", "char<PERSON>t", "flexShrink", "anchor", "ModalProps", "keepMounted", "boxSizing", "borderRight", "borderLeft", "overflowX", "background", "marginTop", "max<PERSON><PERSON><PERSON>", "isAfter", "add", "duration", "meeting", "timezone", "onSubmit", "autoFocus"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/Layout.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport axios from '../utils/axios';\nimport {\n  Box,\n  CssBaseline,\n  Drawer,\n  AppBar,\n  Toolbar,\n  List,\n  Typography,\n  Divider,\n  IconButton,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  ListItemButton,\n  Container,\n  useTheme,\n  useMediaQuery,\n  Tooltip,\n  Menu,\n  MenuItem,\n  Avatar,\n  Button,\n  alpha,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle,\n  Badge,\n  Link\n} from '@mui/material';\nimport {\n  Menu as MenuIcon,\n  Dashboard as DashboardIcon,\n  Person as PersonIcon,\n  School as SchoolIcon,\n  Chat as ChatIcon,\n  Search as SearchIcon,\n  Category as CategoryIcon,\n  Assignment as AssignmentIcon,\n  Translate as TranslateIcon,\n  KeyboardArrowDown as KeyboardArrowDownIcon,\n  Language as LanguageIcon,\n  Logout as LogoutIcon,\n  AccountCircle as AccountCircleIcon,\n  VideoCall as VideoCallIcon,\n  ContactSupport as ContactSupportIcon,\n  AccountBalanceWallet as AccountBalanceWalletIcon,\n  Payment as PaymentIcon,\n  TrendingUp as TrendingUpIcon,\n  Email as EmailIcon,\n  CalendarMonth as CalendarMonthIcon,\n  Star as StarIcon,\n  RateReview as RateReviewIcon,\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  AccessTime as AccessTimeIcon,\n  Policy as PolicyIcon,\n  MenuBook as MenuBookIcon,\n  ReportProblem as ReportProblemIcon\n} from '@mui/icons-material';\nimport dayjs from 'dayjs';\nimport utc from 'dayjs/plugin/utc';\n\nimport { useAuth } from '../contexts/AuthContext';\nimport MeetingFeedbackDialog from './MeetingFeedbackDialog';\nimport { useUnreadMessages } from '../contexts/UnreadMessagesContext';\n\ndayjs.extend(utc);\nconst drawerWidth = 240;\n\nconst Layout = ({ children }) => {\n  const [pendingDialogOpen, setPendingDialogOpen] = useState(false);\n  const [pendingIssue, setPendingIssue] = useState(null);\n  const { t, i18n } = useTranslation();\n  const { currentUser, handleLogout } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));\n  const [mobileOpen, setMobileOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [langMenuAnchor, setLangMenuAnchor] = useState(null);\n  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);\n  const [balance, setBalance] = useState(null);\n  const { unreadCount } = useUnreadMessages();\n\n  // Helper to fetch pending feedback\n  const fetchPending = async () => {\n    if (currentUser?.role !== 'student') return;\n    try {\n      const { data } = await axios.get('/meeting-issues/pending');\n      if (data.success && data.data) {\n        console.log('Found pending feedback issue:', data.data);\n        setPendingIssue(data.data);\n        setPendingDialogOpen(true);\n      } else {\n        console.log('No pending feedback issues found');\n        setPendingIssue(null);\n        setPendingDialogOpen(false);\n      }\n    } catch (err) {\n      console.error('Error fetching pending feedback', err);\n    }\n  };\n\n  // Fetch on mount / route change\n  useEffect(() => {\n    fetchPending();\n  }, [currentUser, location.pathname]);\n\n  // Try to attach booking_id to pending issue if missing\n  useEffect(() => {\n    if (!pendingIssue || pendingIssue.booking_id) return;\n\n    // Fetch bookings to find matching one\n    const findMatchingBooking = async () => {\n      try {\n        const { data } = await axios.get('/bookings');\n        if (data.success && data.data) {\n          const bookings = data.data;\n          const targetTime = dayjs.utc(pendingIssue.datetime);\n\n          // Find booking with same teacher and date within 2 hours window\n          const candidates = bookings.filter(b => {\n            if (b.teacher_name !== pendingIssue.teacher_name) return false;\n            const bTime = dayjs.utc(b.datetime);\n            const timeDiff = Math.abs(bTime.diff(targetTime, 'minutes'));\n            return timeDiff <= 120;\n          });\n\n          // Sort by closest time match\n          candidates.sort((a, b) => {\n            const aDiff = Math.abs(dayjs.utc(a.datetime).diff(targetTime, 'minutes'));\n            const bDiff = Math.abs(dayjs.utc(b.datetime).diff(targetTime, 'minutes'));\n            return aDiff - bDiff;\n          });\n\n          if (candidates.length > 0) {\n            const bestMatch = candidates[0];\n            console.log(`Global feedback: Linking meeting ${pendingIssue.meeting_id} -> booking ${bestMatch.id}`);\n            setPendingIssue(prev => ({ ...prev, booking_id: bestMatch.id }));\n          } else {\n            console.warn(`Global feedback: No matching booking found for meeting ${pendingIssue.meeting_id}`);\n          }\n        }\n      } catch (err) {\n        console.error('Error fetching bookings for global feedback:', err);\n      }\n    };\n\n    findMatchingBooking();\n  }, [pendingIssue]);\n\n  useEffect(() => {\n    document.dir = isRtl ? 'rtl' : 'ltr';\n  }, [isRtl]);\n\n  useEffect(() => {\n    if (currentUser) {\n      fetchBalance();\n    }\n  }, [currentUser]);\n\n  const fetchBalance = async () => {\n    try {\n      const response = await axios.get('/api/wallet/balance');\n      if (response.data.success) {\n        setBalance(response.data.balance);\n      }\n    } catch (error) {\n      console.error('Error fetching balance:', error);\n    }\n  };\n\n  const handleDrawerToggle = () => {\n    const newState = !mobileOpen;\n    setMobileOpen(newState);\n    if (isMobile) {\n      localStorage.setItem('drawerOpen', newState ? 'true' : 'false');\n    }\n    // Check for pending feedback when drawer opens\n    if (newState) {\n      fetchPending();\n    }\n  };\n\n\n\n  const handleLangMenuOpen = (event) => {\n    setLangMenuAnchor(event.currentTarget);\n  };\n\n  const handleLangMenuClose = () => {\n    setLangMenuAnchor(null);\n  };\n\n  const handleLanguageChange = (lang) => {\n    i18n.changeLanguage(lang);\n    localStorage.setItem('language', lang);\n    handleLangMenuClose();\n  };\n\n  const getMenuItems = () => {\n    if (currentUser?.role === 'platform_teacher') {\n      return [\n        { text: t('menu.dashboard'), icon: <DashboardIcon />, path: '/teacher/dashboard' },\n        { text: t('bookings.title'), icon: <CalendarMonthIcon />, path: '/teacher/bookings' },\n        { text: t('menu.meetings'), icon: <VideoCallIcon />, path: '/teacher/meetings' },\n        { text: t('teacher.myLessons'), icon: <MenuBookIcon />, path: '/teacher/my-lessons' },\n        { text: t('menu.profile'), icon: <PersonIcon />, path: '/teacher/profile' },\n        { text: t('menu.chat'), icon: <ChatIcon />, path: '/teacher/chat', badge: unreadCount },\n        { text: t('reviews.teacherReviews'), icon: <StarIcon />, path: '/teacher/reviews' },\n        { text: t('wallet.title'), icon: <AccountBalanceWalletIcon />, path: '/teacher/wallet' },\n        { text: t('withdrawal.title'), icon: <PaymentIcon />, path: '/teacher/withdrawal' },\n        { text: t('contactUs.title'), icon: <ContactSupportIcon />, path: '/teacher/contact-us' },\n        { text: t('myMessages.title'), icon: <EmailIcon />, path: '/teacher/my-messages' },\n        { text: t('menu.platformPolicy'), icon: <PolicyIcon />, path: '/platform-policy' },\n      ];\n    }\n\n    const adminItems = [\n      { text: t('nav.dashboard'), path: '/admin/dashboard', icon: <DashboardIcon /> },\n      { text: t('nav.applications'), path: '/admin/applications', icon: <AssignmentIcon /> },\n      { text: t('nav.teachers'), path: '/admin/teachers', icon: <SchoolIcon /> },\n      { text: t('nav.students'), path: '/admin/students', icon: <PersonIcon /> },\n      { text: t('nav.deletedUsers', 'المستخدمون المحذوفون'), path: '/admin/deleted-users', icon: <DeleteIcon /> },\n      { text: t('nav.profileUpdates'), path: '/admin/profile-updates', icon: <EditIcon /> },\n      { text: t('admin.meetingSessions.title'), path: '/admin/meeting-sessions', icon: <AccessTimeIcon /> },\n      { text: t('admin.meetingIssues.title', 'Meeting Issues'), path: '/admin/meeting-issues', icon: <ReportProblemIcon /> },\n      { text: t('nav.categories'), path: '/admin/categories', icon: <CategoryIcon /> },\n      { text: t('nav.languages'), path: '/admin/languages', icon: <LanguageIcon /> },\n      { text: t('nav.withdrawalManagement'), icon: <PaymentIcon />, path: '/admin/withdrawals' },\n      { text: t('admin.earnings.title'), icon: <TrendingUpIcon />, path: '/admin/earnings' },\n      { text: t('common.profile'), path: '/admin/profile', icon: <PersonIcon /> },\n      { text: t('wallet.title'), path: '/admin/wallet', icon: <AccountBalanceWalletIcon /> },\n      { text: t('admin.messages.title'), path: '/admin/messages', icon: <ContactSupportIcon /> },\n      { text: t('menu.platformPolicy'), path: '/platform-policy', icon: <PolicyIcon /> }\n    ];\n\n    const newTeacherItems = [\n      { text: t('nav.applications'), path: '/teacher/application', icon: <AssignmentIcon /> },\n      { text: t('common.profile'), path: '/teacher/profile', icon: <PersonIcon /> },\n      { text: t('menu.platformPolicy'), path: '/platform-policy', icon: <PolicyIcon /> }\n    ];\n\n    const studentItems = [\n      { text: t('nav.dashboard'), path: '/student/dashboard', icon: <DashboardIcon /> },\n      { text: t('nav.findTeacher'), path: '/student/find-teacher', icon: <SearchIcon /> },\n      { text: t('bookings.title'), path: '/student/bookings', icon: <CalendarMonthIcon /> },\n      { text: t('nav.myTeachers'), path: '/student/my-teachers', icon: <SchoolIcon /> },\n      { text: t('nav.meetings'), path: '/student/meetings', icon: <VideoCallIcon /> },\n      { text: t('nav.chat'), path: '/student/chat', icon: <ChatIcon />, badge: unreadCount },\n      { text: t('reviews.writeReview'), path: '/student/write-review', icon: <RateReviewIcon /> },\n      { text: t('common.profile'), path: '/student/profile', icon: <PersonIcon /> },\n      { text: t('wallet.title'), path: '/student/wallet', icon: <AccountBalanceWalletIcon /> },\n      { text: t('contactUs.title'), path: '/student/contact-us', icon: <ContactSupportIcon /> },\n      { text: t('myMessages.title'), path: '/student/my-messages', icon: <EmailIcon /> },\n      { text: t('menu.platformPolicy'), path: '/platform-policy', icon: <PolicyIcon /> }\n    ];\n\n    switch (currentUser?.role) {\n      case 'admin':\n        return adminItems;\n      case 'new_teacher':\n        return newTeacherItems;\n      case 'student':\n        return studentItems;\n      default:\n        return [];\n    }\n  };\n\n  const handleProfileClick = () => {\n    switch (currentUser?.role) {\n      case 'admin':\n        navigate('/admin/profile');\n        break;\n      case 'platform_teacher':\n      case 'new_teacher':\n        navigate('/teacher/profile');\n        break;\n      case 'student':\n        navigate('/student/profile');\n        break;\n      default:\n        navigate('/login');\n    }\n  };\n\n  const handleLogoutClick = () => {\n    setAnchorEl(null); // Close menu immediately for better UX\n    setLogoutDialogOpen(true);\n  };\n\n  const handleConfirmLogout = async () => {\n    setLogoutDialogOpen(false);\n    try {\n      await handleLogout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  const handleCancelLogout = () => {\n    setLogoutDialogOpen(false);\n  };\n\n  const drawer = (\n    <Box>\n      {/* Balance Display */}\n      {currentUser && balance !== null ? (\n        <Box sx={{\n          mx: 2,\n          my: 2,\n          p: 2,\n          bgcolor: 'primary.light',\n          borderRadius: 2,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: 1.5,\n          minHeight: 64\n        }}>\n          <AccountBalanceWalletIcon sx={{\n            color: 'primary.contrastText',\n            fontSize: '1.5rem'\n          }} />\n          <Box sx={{ textAlign: 'center' }}>\n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: 'primary.contrastText',\n                fontSize: '0.75rem',\n                opacity: 0.9,\n                display: 'block',\n                lineHeight: 1.2\n              }}\n            >\n              {t('wallet.balance')}\n            </Typography>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                color: 'primary.contrastText',\n                fontSize: '1.1rem',\n                fontWeight: 700,\n                lineHeight: 1.2,\n                mt: 0.5\n              }}\n            >\n              ${balance}\n            </Typography>\n          </Box>\n        </Box>\n      ) : (\n        <Box sx={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          p: 2,\n          minHeight: 64\n        }}>\n          <Typography\n            variant=\"h6\"\n            sx={{\n              fontFamily: 'Tajawal, sans-serif',\n              fontWeight: 600,\n              color: 'primary.main'\n            }}\n          >\n            {t('brand.name')}\n          </Typography>\n        </Box>\n      )}\n      <Divider />\n\n      <List>\n        {getMenuItems().map((item) => (\n          <ListItem\n            component=\"div\"\n            key={item.text}\n            onClick={() => {\n              if (location.pathname === item.path) {\n                navigate(0); // Reload current page\n              } else {\n                navigate(item.path);\n              }\n            }}\n            selected={location.pathname === item.path}\n            sx={{\n              minHeight: 48,\n              px: 2.5,\n              cursor: 'pointer',\n              borderRadius: 1,\n              mx: 1,\n              mb: 0.5,\n              '&:hover': {\n                backgroundColor: 'action.hover'\n              },\n              '&.Mui-selected': {\n                backgroundColor: 'primary.main',\n                color: 'primary.contrastText',\n                '&:hover': {\n                  backgroundColor: 'primary.dark'\n                },\n                '& .MuiListItemIcon-root': {\n                  color: 'primary.contrastText'\n                }\n              }\n            }}\n          >\n            <ListItemIcon\n              sx={{\n                minWidth: 0,\n                mr: isRtl ? 0 : 2,\n                ml: isRtl ? 2 : 0,\n                justifyContent: 'center',\n              }}\n            >\n              {item.badge && item.badge > 0 ? (\n                <Badge badgeContent={item.badge} color=\"error\" max={99}>\n                  {item.icon}\n                </Badge>\n              ) : (\n                item.icon\n              )}\n            </ListItemIcon>\n            <ListItemText\n              primary={item.text}\n              sx={{\n                '& .MuiListItemText-primary': {\n                  fontSize: '0.9rem',\n                  fontWeight: 500\n                }\n              }}\n            />\n          </ListItem>\n        ))}\n      </List>\n    </Box>\n  );\n\n  const handleGlobalFeedbackSubmit = async (meetingId, values) => {\n    try {\n      console.log('Submitting global feedback:', { meetingId, values });\n\n      await axios.post('/meeting-issues', {\n        meeting_id: meetingId,\n        booking_id: values.booking_id, // Include booking_id\n        issue_type: values.issue_type,\n        description: values.description\n      });\n\n      console.log('Global feedback submitted successfully');\n    } catch (err) {\n      console.error('Failed to submit global feedback', err);\n      // Show error message to user\n      alert('Failed to submit feedback. Please try again.');\n      return; // Don't close dialog on error\n    } finally {\n      setPendingDialogOpen(false);\n      setPendingIssue(null);\n      // Check if there is another pending meeting\n      await fetchPending();\n    }\n  };\n\n  return (\n    <Box sx={{ display: 'flex', direction: isRtl ? 'rtl' : 'ltr' }}>\n      <CssBaseline />\n      <AppBar\n        position=\"fixed\"\n        sx={{\n          width: '100%',\n          zIndex: theme.zIndex.drawer + 1\n        }}\n      >\n        <Toolbar sx={{ minHeight: { xs: 56, sm: 64, md: 70 }, px: { xs: 1, sm: 2, md: 3 } }}>\n          <IconButton\n            color=\"inherit\"\n            aria-label=\"open drawer\"\n            edge={isRtl ? 'end' : 'start'}\n            onClick={handleDrawerToggle}\n            sx={{\n              mr: isRtl ? 0 : { xs: 1, sm: 2 },\n              ml: isRtl ? { xs: 1, sm: 2 } : 0,\n              display: { sm: 'none' },\n              p: { xs: 1, sm: 1.5 }\n            }}\n          >\n            <MenuIcon />\n          </IconButton>\n          <Typography\n            variant=\"h6\"\n            noWrap\n            component=\"div\"\n            sx={{\n              flexGrow: 1,\n              fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' },\n              fontFamily: 'Tajawal, sans-serif',\n              fontWeight: { xs: 500, md: 600 }\n            }}\n          >\n            {t('brand.name')}\n          </Typography>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1, md: 1.5 } }}>\n            {/* Language Menu */}\n            <Box>\n              <Button\n                color=\"inherit\"\n                startIcon={<TranslateIcon sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' } }} />}\n                endIcon={<KeyboardArrowDownIcon sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' } }} />}\n                onClick={handleLangMenuOpen}\n                sx={{\n                  minWidth: { xs: 80, sm: 100, md: 120 },\n                  borderRadius: 2,\n                  px: { xs: 1, sm: 1.5, md: 2 },\n                  py: { xs: 0.5, sm: 0.75, md: 1 },\n                  gap: { xs: 0.5, sm: 0.75, md: 1 },\n                  fontSize: { xs: '0.75rem', sm: '0.85rem', md: '0.9rem' },\n                  '& .MuiButton-startIcon': {\n                    margin: 0\n                  },\n                  '& .MuiButton-endIcon': {\n                    margin: 0,\n                    ml: 0.5\n                  },\n                  '&:hover': {\n                    backgroundColor: alpha(theme.palette.common.white, 0.1),\n                    transform: 'translateY(-1px)'\n                  },\n                  transition: 'all 0.2s ease-in-out'\n                }}\n              >\n                {i18n.language === 'ar' ? 'العربية' : 'English'}\n              </Button>\n              <Menu\n                anchorEl={langMenuAnchor}\n                open={Boolean(langMenuAnchor)}\n                onClose={handleLangMenuClose}\n                anchorOrigin={{\n                  vertical: 'bottom',\n                  horizontal: 'right',\n                }}\n                transformOrigin={{\n                  vertical: 'top',\n                  horizontal: 'right',\n                }}\n                slotProps={{\n                  paper: {\n                    elevation: 0,\n                    sx: {\n                      mt: 1.5,\n                      overflow: 'visible',\n                      filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',\n                      '&:before': {\n                        content: '\"\"',\n                        display: 'block',\n                        position: 'absolute',\n                        top: 0,\n                        right: 14,\n                        width: 10,\n                        height: 10,\n                        bgcolor: 'background.paper',\n                        transform: 'translateY(-50%) rotate(45deg)',\n                        zIndex: 0,\n                      },\n                    },\n                  }\n                }}\n              >\n                <MenuItem onClick={() => handleLanguageChange('ar')}>\n                  <ListItemIcon>\n                    <LanguageIcon fontSize=\"small\" />\n                  </ListItemIcon>\n                  العربية\n                </MenuItem>\n                <MenuItem onClick={() => handleLanguageChange('en')}>\n                  <ListItemIcon>\n                    <LanguageIcon fontSize=\"small\" />\n                  </ListItemIcon>\n                  English\n                </MenuItem>\n              </Menu>\n            </Box>\n            {/* User Avatar */}\n            {currentUser && (\n              <>\n                <IconButton\n                  onClick={(event) => setAnchorEl(event.currentTarget)}\n                  sx={{\n                    ml: { xs: 0.5, sm: 1, md: 1.5 },\n                    p: { xs: 0.5, sm: 0.75, md: 1 },\n                    '&:hover': {\n                      transform: 'scale(1.05)',\n                      bgcolor: 'rgba(255,255,255,0.1)'\n                    },\n                    transition: 'all 0.2s ease-in-out'\n                  }}\n                >\n                  <Avatar\n                    src={currentUser.profile_picture_url ? (\n                      currentUser.profile_picture_url.startsWith('http')\n                        ? currentUser.profile_picture_url\n                        : `https://allemnionline.com${currentUser.profile_picture_url}`\n                    ) : ''}\n                    alt={currentUser.full_name}\n                    sx={{\n                      width: { xs: 32, sm: 36, md: 40 },\n                      height: { xs: 32, sm: 36, md: 40 },\n                      bgcolor: 'primary.main',\n                      border: '2px solid',\n                      borderColor: 'background.paper',\n                      fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },\n                      fontWeight: 600\n                    }}\n                  >\n                    {!currentUser.profile_picture_url && currentUser.full_name?.charAt(0)}\n                  </Avatar>\n                </IconButton>\n\n                <Menu\n                  anchorEl={anchorEl}\n                  open={Boolean(anchorEl)}\n                  onClose={() => setAnchorEl(null)}\n                  onClick={() => setAnchorEl(null)}\n                  transformOrigin={{ horizontal: isRtl ? 'left' : 'right', vertical: 'top' }}\n                  anchorOrigin={{ horizontal: isRtl ? 'left' : 'right', vertical: 'bottom' }}\n                >\n                  <MenuItem onClick={handleProfileClick}>\n                    <ListItemIcon>\n                      <PersonIcon fontSize=\"small\" />\n                    </ListItemIcon>\n                    <ListItemText primary={t('common.profile')} />\n                  </MenuItem>\n                  <Divider />\n                  <MenuItem onClick={handleLogoutClick}>\n                    <ListItemIcon>\n                      <LogoutIcon fontSize=\"small\" />\n                    </ListItemIcon>\n                    <ListItemText primary={t('auth.logout')} />\n                  </MenuItem>\n                </Menu>\n              </>\n            )}\n\n            {/* Login Button for non-authenticated users */}\n            {!currentUser && (\n              <Button\n                color=\"inherit\"\n                onClick={() => navigate('/login')}\n                sx={{\n                  ml: { xs: 0.5, sm: 1, md: 1.5 },\n                  px: { xs: 1.5, sm: 2, md: 2.5 },\n                  py: { xs: 0.5, sm: 0.75, md: 1 },\n                  fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },\n                  borderRadius: 2,\n                  '&:hover': {\n                    backgroundColor: alpha(theme.palette.common.white, 0.1),\n                    transform: 'translateY(-1px)'\n                  },\n                  transition: 'all 0.2s ease-in-out'\n                }}\n              >\n                {t('auth.login')}\n              </Button>\n            )}\n          </Box>\n        </Toolbar>\n      </AppBar>\n      <Box\n        component=\"nav\"\n        sx={{\n          width: { sm: drawerWidth },\n          flexShrink: { sm: 0 }\n        }}\n      >\n        <Drawer\n          variant={isMobile ? 'temporary' : 'permanent'}\n          open={isMobile ? mobileOpen : true}\n          onClose={handleDrawerToggle}\n          anchor={isRtl ? 'right' : 'left'}\n          ModalProps={{\n            keepMounted: true,\n          }}\n          sx={{\n            '& .MuiDrawer-paper': {\n              boxSizing: 'border-box',\n              width: drawerWidth,\n              top: { xs: '56px', sm: '64px', md: '70px' },\n              height: { xs: 'calc(100vh - 56px)', sm: 'calc(100vh - 64px)', md: 'calc(100vh - 70px)' },\n              borderRight: isRtl ? 'none' : '1px solid rgba(0, 0, 0, 0.12)',\n              borderLeft: isRtl ? '1px solid rgba(0, 0, 0, 0.12)' : 'none',\n              overflowX: 'hidden',\n              position: 'fixed',\n              zIndex: 1000,\n              '&::-webkit-scrollbar': {\n                width: '6px',\n              },\n              '&::-webkit-scrollbar-track': {\n                background: '#f1f1f1',\n              },\n              '&::-webkit-scrollbar-thumb': {\n                background: '#888',\n                borderRadius: '3px',\n              },\n              '&::-webkit-scrollbar-thumb:hover': {\n                background: '#555',\n              },\n            },\n          }}\n        >\n          {drawer}\n        </Drawer>\n      </Box>\n      <Box\n        component=\"main\"\n        sx={{\n          flexGrow: 1,\n          p: { xs: 0.5, sm: 1, md: 1.5 },\n          width: '100%',\n          marginTop: { xs: '56px', sm: '64px', md: '70px' },\n          minHeight: `calc(100vh - ${isMobile ? '56px' : isTablet ? '64px' : '70px'})`,\n          overflow: 'auto'\n        }}\n      >\n        <Container\n          maxWidth=\"xl\"\n          sx={{\n            px: { xs: 1, sm: 2, md: 3 },\n            py: { xs: 1, sm: 1.5, md: 2 }\n          }}\n        >\n          {children}\n\n      {/* Global Meeting Feedback Dialog */}\n      {currentUser?.role === 'student' && (\n        <MeetingFeedbackDialog\n           open={pendingDialogOpen && pendingIssue && dayjs.utc().isAfter(dayjs.utc(pendingIssue.datetime).add(pendingIssue.duration || 50, 'minute'))}\n           meeting={pendingIssue}\n           timezone={currentUser?.timezone || null}\n           onSubmit={handleGlobalFeedbackSubmit}\n           onClose={null /* Prevent manual close */}\n         />\n      )}\n        </Container>\n      </Box>\n      <Dialog\n        open={logoutDialogOpen}\n        onClose={handleCancelLogout}\n        aria-labelledby=\"logout-dialog-title\"\n        aria-describedby=\"logout-dialog-description\"\n      >\n        <DialogTitle id=\"logout-dialog-title\">\n          {t('auth.logoutConfirmTitle')}\n        </DialogTitle>\n        <DialogContent>\n          <DialogContentText id=\"logout-dialog-description\">\n            {t('auth.logoutConfirmMessage')}\n          </DialogContentText>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCancelLogout} color=\"primary\">\n            {t('common.cancel')}\n          </Button>\n          <Button onClick={handleConfirmLogout} color=\"primary\" autoFocus>\n            {t('common.confirm')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default Layout;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,MAAO,CAAAC,KAAK,KAAM,gBAAgB,CAClC,OACEC,GAAG,CACHC,WAAW,CACXC,MAAM,CACNC,MAAM,CACNC,OAAO,CACPC,IAAI,CACJC,UAAU,CACVC,OAAO,CACPC,UAAU,CACVC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,SAAS,CACTC,QAAQ,CACRC,aAAa,CACbC,OAAO,CACPC,IAAI,CACJC,QAAQ,CACRC,MAAM,CACNC,MAAM,CACNC,KAAK,CACLC,MAAM,CACNC,aAAa,CACbC,aAAa,CACbC,iBAAiB,CACjBC,WAAW,CACXC,KAAK,CACLC,IAAI,KACC,eAAe,CACtB,OACEX,IAAI,GAAI,CAAAY,QAAQ,CAChBC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,iBAAiB,GAAI,CAAAC,qBAAqB,CAC1CC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,cAAc,GAAI,CAAAC,kBAAkB,CACpCC,oBAAoB,GAAI,CAAAC,wBAAwB,CAChDC,OAAO,GAAI,CAAAC,WAAW,CACtBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,KAAK,GAAI,CAAAC,SAAS,CAClBC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,aAAa,GAAI,CAAAC,iBAAiB,KAC7B,qBAAqB,CAC5B,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,GAAG,KAAM,kBAAkB,CAElC,OAASC,OAAO,KAAQ,yBAAyB,CACjD,MAAO,CAAAC,qBAAqB,KAAM,yBAAyB,CAC3D,OAASC,iBAAiB,KAAQ,mCAAmC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEtEV,KAAK,CAACW,MAAM,CAACV,GAAG,CAAC,CACjB,KAAM,CAAAW,WAAW,CAAG,GAAG,CAEvB,KAAM,CAAAC,MAAM,CAAGC,IAAA,EAAkB,KAAAC,qBAAA,IAAjB,CAAEC,QAAS,CAAC,CAAAF,IAAA,CAC1B,KAAM,CAACG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5G,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC6G,YAAY,CAAEC,eAAe,CAAC,CAAG9G,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAAE+G,CAAC,CAAEC,IAAK,CAAC,CAAG9G,cAAc,CAAC,CAAC,CACpC,KAAM,CAAE+G,WAAW,CAAEC,YAAa,CAAC,CAAGtB,OAAO,CAAC,CAAC,CAC/C,KAAM,CAAAuB,QAAQ,CAAGhH,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAiH,QAAQ,CAAGhH,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAiH,KAAK,CAAGjG,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAkG,KAAK,CAAGN,IAAI,CAACO,QAAQ,GAAK,IAAI,CACpC,KAAM,CAAAC,QAAQ,CAAGnG,aAAa,CAACgG,KAAK,CAACI,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAAAC,QAAQ,CAAGtG,aAAa,CAACgG,KAAK,CAACI,WAAW,CAACG,OAAO,CAAC,IAAI,CAAE,IAAI,CAAC,CAAC,CACrE,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG9H,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC+H,QAAQ,CAAEC,WAAW,CAAC,CAAGhI,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAACiI,cAAc,CAAEC,iBAAiB,CAAC,CAAGlI,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACmI,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpI,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACqI,OAAO,CAAEC,UAAU,CAAC,CAAGtI,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAEuI,WAAY,CAAC,CAAGzC,iBAAiB,CAAC,CAAC,CAE3C;AACA,KAAM,CAAA0C,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAAAvB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,IAAK,SAAS,CAAE,OACrC,GAAI,CACF,KAAM,CAAEC,IAAK,CAAC,CAAG,KAAM,CAAArI,KAAK,CAACsI,GAAG,CAAC,yBAAyB,CAAC,CAC3D,GAAID,IAAI,CAACE,OAAO,EAAIF,IAAI,CAACA,IAAI,CAAE,CAC7BG,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAEJ,IAAI,CAACA,IAAI,CAAC,CACvD5B,eAAe,CAAC4B,IAAI,CAACA,IAAI,CAAC,CAC1B9B,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,IAAM,CACLiC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/ChC,eAAe,CAAC,IAAI,CAAC,CACrBF,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CACF,CAAE,MAAOmC,GAAG,CAAE,CACZF,OAAO,CAACG,KAAK,CAAC,iCAAiC,CAAED,GAAG,CAAC,CACvD,CACF,CAAC,CAED;AACA9I,SAAS,CAAC,IAAM,CACduI,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,CAACvB,WAAW,CAAEG,QAAQ,CAAC6B,QAAQ,CAAC,CAAC,CAEpC;AACAhJ,SAAS,CAAC,IAAM,CACd,GAAI,CAAC4G,YAAY,EAAIA,YAAY,CAACqC,UAAU,CAAE,OAE9C;AACA,KAAM,CAAAC,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACF,KAAM,CAAET,IAAK,CAAC,CAAG,KAAM,CAAArI,KAAK,CAACsI,GAAG,CAAC,WAAW,CAAC,CAC7C,GAAID,IAAI,CAACE,OAAO,EAAIF,IAAI,CAACA,IAAI,CAAE,CAC7B,KAAM,CAAAU,QAAQ,CAAGV,IAAI,CAACA,IAAI,CAC1B,KAAM,CAAAW,UAAU,CAAG3D,KAAK,CAACC,GAAG,CAACkB,YAAY,CAACyC,QAAQ,CAAC,CAEnD;AACA,KAAM,CAAAC,UAAU,CAAGH,QAAQ,CAACI,MAAM,CAACC,CAAC,EAAI,CACtC,GAAIA,CAAC,CAACC,YAAY,GAAK7C,YAAY,CAAC6C,YAAY,CAAE,MAAO,MAAK,CAC9D,KAAM,CAAAC,KAAK,CAAGjE,KAAK,CAACC,GAAG,CAAC8D,CAAC,CAACH,QAAQ,CAAC,CACnC,KAAM,CAAAM,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACH,KAAK,CAACI,IAAI,CAACV,UAAU,CAAE,SAAS,CAAC,CAAC,CAC5D,MAAO,CAAAO,QAAQ,EAAI,GAAG,CACxB,CAAC,CAAC,CAEF;AACAL,UAAU,CAACS,IAAI,CAAC,CAACC,CAAC,CAAER,CAAC,GAAK,CACxB,KAAM,CAAAS,KAAK,CAAGL,IAAI,CAACC,GAAG,CAACpE,KAAK,CAACC,GAAG,CAACsE,CAAC,CAACX,QAAQ,CAAC,CAACS,IAAI,CAACV,UAAU,CAAE,SAAS,CAAC,CAAC,CACzE,KAAM,CAAAc,KAAK,CAAGN,IAAI,CAACC,GAAG,CAACpE,KAAK,CAACC,GAAG,CAAC8D,CAAC,CAACH,QAAQ,CAAC,CAACS,IAAI,CAACV,UAAU,CAAE,SAAS,CAAC,CAAC,CACzE,MAAO,CAAAa,KAAK,CAAGC,KAAK,CACtB,CAAC,CAAC,CAEF,GAAIZ,UAAU,CAACa,MAAM,CAAG,CAAC,CAAE,CACzB,KAAM,CAAAC,SAAS,CAAGd,UAAU,CAAC,CAAC,CAAC,CAC/BV,OAAO,CAACC,GAAG,CAAC,oCAAoCjC,YAAY,CAACyD,UAAU,eAAeD,SAAS,CAACE,EAAE,EAAE,CAAC,CACrGzD,eAAe,CAAC0D,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEtB,UAAU,CAAEmB,SAAS,CAACE,EAAG,CAAC,CAAC,CAAC,CAClE,CAAC,IAAM,CACL1B,OAAO,CAAC4B,IAAI,CAAC,0DAA0D5D,YAAY,CAACyD,UAAU,EAAE,CAAC,CACnG,CACF,CACF,CAAE,MAAOvB,GAAG,CAAE,CACZF,OAAO,CAACG,KAAK,CAAC,8CAA8C,CAAED,GAAG,CAAC,CACpE,CACF,CAAC,CAEDI,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACtC,YAAY,CAAC,CAAC,CAElB5G,SAAS,CAAC,IAAM,CACdyK,QAAQ,CAACC,GAAG,CAAGrD,KAAK,CAAG,KAAK,CAAG,KAAK,CACtC,CAAC,CAAE,CAACA,KAAK,CAAC,CAAC,CAEXrH,SAAS,CAAC,IAAM,CACd,GAAIgH,WAAW,CAAE,CACf2D,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAAE,CAAC3D,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAA2D,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAxK,KAAK,CAACsI,GAAG,CAAC,qBAAqB,CAAC,CACvD,GAAIkC,QAAQ,CAACnC,IAAI,CAACE,OAAO,CAAE,CACzBN,UAAU,CAACuC,QAAQ,CAACnC,IAAI,CAACL,OAAO,CAAC,CACnC,CACF,CAAE,MAAOW,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CAAC,CAED,KAAM,CAAA8B,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,QAAQ,CAAG,CAAClD,UAAU,CAC5BC,aAAa,CAACiD,QAAQ,CAAC,CACvB,GAAIvD,QAAQ,CAAE,CACZwD,YAAY,CAACC,OAAO,CAAC,YAAY,CAAEF,QAAQ,CAAG,MAAM,CAAG,OAAO,CAAC,CACjE,CACA;AACA,GAAIA,QAAQ,CAAE,CACZvC,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAID,KAAM,CAAA0C,kBAAkB,CAAIC,KAAK,EAAK,CACpCjD,iBAAiB,CAACiD,KAAK,CAACC,aAAa,CAAC,CACxC,CAAC,CAED,KAAM,CAAAC,mBAAmB,CAAGA,CAAA,GAAM,CAChCnD,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAAoD,oBAAoB,CAAIC,IAAI,EAAK,CACrCvE,IAAI,CAACwE,cAAc,CAACD,IAAI,CAAC,CACzBP,YAAY,CAACC,OAAO,CAAC,UAAU,CAAEM,IAAI,CAAC,CACtCF,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAED,KAAM,CAAAI,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAAAxE,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,IAAK,kBAAkB,CAAE,CAC5C,MAAO,CACL,CAAEiD,IAAI,CAAE3E,CAAC,CAAC,gBAAgB,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAAC3D,aAAa,GAAE,CAAC,CAAEuJ,IAAI,CAAE,oBAAqB,CAAC,CAClF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,gBAAgB,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAACvB,iBAAiB,GAAE,CAAC,CAAEmH,IAAI,CAAE,mBAAoB,CAAC,CACrF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,eAAe,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAACnC,aAAa,GAAE,CAAC,CAAE+H,IAAI,CAAE,mBAAoB,CAAC,CAChF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,mBAAmB,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAACT,YAAY,GAAE,CAAC,CAAEqG,IAAI,CAAE,qBAAsB,CAAC,CACrF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,cAAc,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAACzD,UAAU,GAAE,CAAC,CAAEqJ,IAAI,CAAE,kBAAmB,CAAC,CAC3E,CAAEF,IAAI,CAAE3E,CAAC,CAAC,WAAW,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAACrD,QAAQ,GAAE,CAAC,CAAEiJ,IAAI,CAAE,eAAe,CAAEC,KAAK,CAAEtD,WAAY,CAAC,CACvF,CAAEmD,IAAI,CAAE3E,CAAC,CAAC,wBAAwB,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAACrB,QAAQ,GAAE,CAAC,CAAEiH,IAAI,CAAE,kBAAmB,CAAC,CACnF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,cAAc,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAAC/B,wBAAwB,GAAE,CAAC,CAAE2H,IAAI,CAAE,iBAAkB,CAAC,CACxF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,kBAAkB,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAAC7B,WAAW,GAAE,CAAC,CAAEyH,IAAI,CAAE,qBAAsB,CAAC,CACnF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,iBAAiB,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAACjC,kBAAkB,GAAE,CAAC,CAAE6H,IAAI,CAAE,qBAAsB,CAAC,CACzF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,kBAAkB,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAACzB,SAAS,GAAE,CAAC,CAAEqH,IAAI,CAAE,sBAAuB,CAAC,CAClF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,qBAAqB,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAACX,UAAU,GAAE,CAAC,CAAEuG,IAAI,CAAE,kBAAmB,CAAC,CACnF,CACH,CAEA,KAAM,CAAAE,UAAU,CAAG,CACjB,CAAEJ,IAAI,CAAE3E,CAAC,CAAC,eAAe,CAAC,CAAE6E,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAE3F,IAAA,CAAC3D,aAAa,GAAE,CAAE,CAAC,CAC/E,CAAEqJ,IAAI,CAAE3E,CAAC,CAAC,kBAAkB,CAAC,CAAE6E,IAAI,CAAE,qBAAqB,CAAED,IAAI,cAAE3F,IAAA,CAAC/C,cAAc,GAAE,CAAE,CAAC,CACtF,CAAEyI,IAAI,CAAE3E,CAAC,CAAC,cAAc,CAAC,CAAE6E,IAAI,CAAE,iBAAiB,CAAED,IAAI,cAAE3F,IAAA,CAACvD,UAAU,GAAE,CAAE,CAAC,CAC1E,CAAEiJ,IAAI,CAAE3E,CAAC,CAAC,cAAc,CAAC,CAAE6E,IAAI,CAAE,iBAAiB,CAAED,IAAI,cAAE3F,IAAA,CAACzD,UAAU,GAAE,CAAE,CAAC,CAC1E,CAAEmJ,IAAI,CAAE3E,CAAC,CAAC,kBAAkB,CAAE,sBAAsB,CAAC,CAAE6E,IAAI,CAAE,sBAAsB,CAAED,IAAI,cAAE3F,IAAA,CAACf,UAAU,GAAE,CAAE,CAAC,CAC3G,CAAEyG,IAAI,CAAE3E,CAAC,CAAC,oBAAoB,CAAC,CAAE6E,IAAI,CAAE,wBAAwB,CAAED,IAAI,cAAE3F,IAAA,CAACjB,QAAQ,GAAE,CAAE,CAAC,CACrF,CAAE2G,IAAI,CAAE3E,CAAC,CAAC,6BAA6B,CAAC,CAAE6E,IAAI,CAAE,yBAAyB,CAAED,IAAI,cAAE3F,IAAA,CAACb,cAAc,GAAE,CAAE,CAAC,CACrG,CAAEuG,IAAI,CAAE3E,CAAC,CAAC,2BAA2B,CAAE,gBAAgB,CAAC,CAAE6E,IAAI,CAAE,uBAAuB,CAAED,IAAI,cAAE3F,IAAA,CAACP,iBAAiB,GAAE,CAAE,CAAC,CACtH,CAAEiG,IAAI,CAAE3E,CAAC,CAAC,gBAAgB,CAAC,CAAE6E,IAAI,CAAE,mBAAmB,CAAED,IAAI,cAAE3F,IAAA,CAACjD,YAAY,GAAE,CAAE,CAAC,CAChF,CAAE2I,IAAI,CAAE3E,CAAC,CAAC,eAAe,CAAC,CAAE6E,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAE3F,IAAA,CAACzC,YAAY,GAAE,CAAE,CAAC,CAC9E,CAAEmI,IAAI,CAAE3E,CAAC,CAAC,0BAA0B,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAAC7B,WAAW,GAAE,CAAC,CAAEyH,IAAI,CAAE,oBAAqB,CAAC,CAC1F,CAAEF,IAAI,CAAE3E,CAAC,CAAC,sBAAsB,CAAC,CAAE4E,IAAI,cAAE3F,IAAA,CAAC3B,cAAc,GAAE,CAAC,CAAEuH,IAAI,CAAE,iBAAkB,CAAC,CACtF,CAAEF,IAAI,CAAE3E,CAAC,CAAC,gBAAgB,CAAC,CAAE6E,IAAI,CAAE,gBAAgB,CAAED,IAAI,cAAE3F,IAAA,CAACzD,UAAU,GAAE,CAAE,CAAC,CAC3E,CAAEmJ,IAAI,CAAE3E,CAAC,CAAC,cAAc,CAAC,CAAE6E,IAAI,CAAE,eAAe,CAAED,IAAI,cAAE3F,IAAA,CAAC/B,wBAAwB,GAAE,CAAE,CAAC,CACtF,CAAEyH,IAAI,CAAE3E,CAAC,CAAC,sBAAsB,CAAC,CAAE6E,IAAI,CAAE,iBAAiB,CAAED,IAAI,cAAE3F,IAAA,CAACjC,kBAAkB,GAAE,CAAE,CAAC,CAC1F,CAAE2H,IAAI,CAAE3E,CAAC,CAAC,qBAAqB,CAAC,CAAE6E,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAE3F,IAAA,CAACX,UAAU,GAAE,CAAE,CAAC,CACnF,CAED,KAAM,CAAA0G,eAAe,CAAG,CACtB,CAAEL,IAAI,CAAE3E,CAAC,CAAC,kBAAkB,CAAC,CAAE6E,IAAI,CAAE,sBAAsB,CAAED,IAAI,cAAE3F,IAAA,CAAC/C,cAAc,GAAE,CAAE,CAAC,CACvF,CAAEyI,IAAI,CAAE3E,CAAC,CAAC,gBAAgB,CAAC,CAAE6E,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAE3F,IAAA,CAACzD,UAAU,GAAE,CAAE,CAAC,CAC7E,CAAEmJ,IAAI,CAAE3E,CAAC,CAAC,qBAAqB,CAAC,CAAE6E,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAE3F,IAAA,CAACX,UAAU,GAAE,CAAE,CAAC,CACnF,CAED,KAAM,CAAA2G,YAAY,CAAG,CACnB,CAAEN,IAAI,CAAE3E,CAAC,CAAC,eAAe,CAAC,CAAE6E,IAAI,CAAE,oBAAoB,CAAED,IAAI,cAAE3F,IAAA,CAAC3D,aAAa,GAAE,CAAE,CAAC,CACjF,CAAEqJ,IAAI,CAAE3E,CAAC,CAAC,iBAAiB,CAAC,CAAE6E,IAAI,CAAE,uBAAuB,CAAED,IAAI,cAAE3F,IAAA,CAACnD,UAAU,GAAE,CAAE,CAAC,CACnF,CAAE6I,IAAI,CAAE3E,CAAC,CAAC,gBAAgB,CAAC,CAAE6E,IAAI,CAAE,mBAAmB,CAAED,IAAI,cAAE3F,IAAA,CAACvB,iBAAiB,GAAE,CAAE,CAAC,CACrF,CAAEiH,IAAI,CAAE3E,CAAC,CAAC,gBAAgB,CAAC,CAAE6E,IAAI,CAAE,sBAAsB,CAAED,IAAI,cAAE3F,IAAA,CAACvD,UAAU,GAAE,CAAE,CAAC,CACjF,CAAEiJ,IAAI,CAAE3E,CAAC,CAAC,cAAc,CAAC,CAAE6E,IAAI,CAAE,mBAAmB,CAAED,IAAI,cAAE3F,IAAA,CAACnC,aAAa,GAAE,CAAE,CAAC,CAC/E,CAAE6H,IAAI,CAAE3E,CAAC,CAAC,UAAU,CAAC,CAAE6E,IAAI,CAAE,eAAe,CAAED,IAAI,cAAE3F,IAAA,CAACrD,QAAQ,GAAE,CAAC,CAAEkJ,KAAK,CAAEtD,WAAY,CAAC,CACtF,CAAEmD,IAAI,CAAE3E,CAAC,CAAC,qBAAqB,CAAC,CAAE6E,IAAI,CAAE,uBAAuB,CAAED,IAAI,cAAE3F,IAAA,CAACnB,cAAc,GAAE,CAAE,CAAC,CAC3F,CAAE6G,IAAI,CAAE3E,CAAC,CAAC,gBAAgB,CAAC,CAAE6E,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAE3F,IAAA,CAACzD,UAAU,GAAE,CAAE,CAAC,CAC7E,CAAEmJ,IAAI,CAAE3E,CAAC,CAAC,cAAc,CAAC,CAAE6E,IAAI,CAAE,iBAAiB,CAAED,IAAI,cAAE3F,IAAA,CAAC/B,wBAAwB,GAAE,CAAE,CAAC,CACxF,CAAEyH,IAAI,CAAE3E,CAAC,CAAC,iBAAiB,CAAC,CAAE6E,IAAI,CAAE,qBAAqB,CAAED,IAAI,cAAE3F,IAAA,CAACjC,kBAAkB,GAAE,CAAE,CAAC,CACzF,CAAE2H,IAAI,CAAE3E,CAAC,CAAC,kBAAkB,CAAC,CAAE6E,IAAI,CAAE,sBAAsB,CAAED,IAAI,cAAE3F,IAAA,CAACzB,SAAS,GAAE,CAAE,CAAC,CAClF,CAAEmH,IAAI,CAAE3E,CAAC,CAAC,qBAAqB,CAAC,CAAE6E,IAAI,CAAE,kBAAkB,CAAED,IAAI,cAAE3F,IAAA,CAACX,UAAU,GAAE,CAAE,CAAC,CACnF,CAED,OAAQ4B,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,EACvB,IAAK,OAAO,CACV,MAAO,CAAAqD,UAAU,CACnB,IAAK,aAAa,CAChB,MAAO,CAAAC,eAAe,CACxB,IAAK,SAAS,CACZ,MAAO,CAAAC,YAAY,CACrB,QACE,MAAO,EAAE,CACb,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,OAAQhF,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,EACvB,IAAK,OAAO,CACVtB,QAAQ,CAAC,gBAAgB,CAAC,CAC1B,MACF,IAAK,kBAAkB,CACvB,IAAK,aAAa,CAChBA,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,IAAK,SAAS,CACZA,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,QACEA,QAAQ,CAAC,QAAQ,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAA+E,iBAAiB,CAAGA,CAAA,GAAM,CAC9BlE,WAAW,CAAC,IAAI,CAAC,CAAE;AACnBI,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED,KAAM,CAAA+D,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC/D,mBAAmB,CAAC,KAAK,CAAC,CAC1B,GAAI,CACF,KAAM,CAAAlB,YAAY,CAAC,CAAC,CACtB,CAAE,MAAO8B,KAAK,CAAE,CACdH,OAAO,CAACG,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACxC,CACF,CAAC,CAED,KAAM,CAAAoD,kBAAkB,CAAGA,CAAA,GAAM,CAC/BhE,mBAAmB,CAAC,KAAK,CAAC,CAC5B,CAAC,CAED,KAAM,CAAAiE,MAAM,cACVnG,KAAA,CAAC5F,GAAG,EAAAoG,QAAA,EAEDO,WAAW,EAAIoB,OAAO,GAAK,IAAI,cAC9BnC,KAAA,CAAC5F,GAAG,EAACgM,EAAE,CAAE,CACPC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CAAC,CACLC,CAAC,CAAE,CAAC,CACJC,OAAO,CAAE,eAAe,CACxBC,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,GAAG,CAAE,GAAG,CACRC,SAAS,CAAE,EACb,CAAE,CAAAtG,QAAA,eACAV,IAAA,CAAC/B,wBAAwB,EAACqI,EAAE,CAAE,CAC5BW,KAAK,CAAE,sBAAsB,CAC7BC,QAAQ,CAAE,QACZ,CAAE,CAAE,CAAC,cACLhH,KAAA,CAAC5F,GAAG,EAACgM,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAS,CAAE,CAAAzG,QAAA,eAC/BV,IAAA,CAACpF,UAAU,EACTwM,OAAO,CAAC,SAAS,CACjBd,EAAE,CAAE,CACFW,KAAK,CAAE,sBAAsB,CAC7BC,QAAQ,CAAE,SAAS,CACnBG,OAAO,CAAE,GAAG,CACZT,OAAO,CAAE,OAAO,CAChBU,UAAU,CAAE,GACd,CAAE,CAAA5G,QAAA,CAEDK,CAAC,CAAC,gBAAgB,CAAC,CACV,CAAC,cACbb,KAAA,CAACtF,UAAU,EACTwM,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFW,KAAK,CAAE,sBAAsB,CAC7BC,QAAQ,CAAE,QAAQ,CAClBK,UAAU,CAAE,GAAG,CACfD,UAAU,CAAE,GAAG,CACfE,EAAE,CAAE,GACN,CAAE,CAAA9G,QAAA,EACH,GACE,CAAC2B,OAAO,EACC,CAAC,EACV,CAAC,EACH,CAAC,cAENrC,IAAA,CAAC1F,GAAG,EAACgM,EAAE,CAAE,CACPM,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBL,CAAC,CAAE,CAAC,CACJO,SAAS,CAAE,EACb,CAAE,CAAAtG,QAAA,cACAV,IAAA,CAACpF,UAAU,EACTwM,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFmB,UAAU,CAAE,qBAAqB,CACjCF,UAAU,CAAE,GAAG,CACfN,KAAK,CAAE,cACT,CAAE,CAAAvG,QAAA,CAEDK,CAAC,CAAC,YAAY,CAAC,CACN,CAAC,CACV,CACN,cACDf,IAAA,CAACnF,OAAO,GAAE,CAAC,cAEXmF,IAAA,CAACrF,IAAI,EAAA+F,QAAA,CACF+E,YAAY,CAAC,CAAC,CAACiC,GAAG,CAAEC,IAAI,eACvBzH,KAAA,CAACnF,QAAQ,EACP6M,SAAS,CAAC,KAAK,CAEfC,OAAO,CAAEA,CAAA,GAAM,CACb,GAAIzG,QAAQ,CAAC6B,QAAQ,GAAK0E,IAAI,CAAC/B,IAAI,CAAE,CACnCzE,QAAQ,CAAC,CAAC,CAAC,CAAE;AACf,CAAC,IAAM,CACLA,QAAQ,CAACwG,IAAI,CAAC/B,IAAI,CAAC,CACrB,CACF,CAAE,CACFkC,QAAQ,CAAE1G,QAAQ,CAAC6B,QAAQ,GAAK0E,IAAI,CAAC/B,IAAK,CAC1CU,EAAE,CAAE,CACFU,SAAS,CAAE,EAAE,CACbe,EAAE,CAAE,GAAG,CACPC,MAAM,CAAE,SAAS,CACjBrB,YAAY,CAAE,CAAC,CACfJ,EAAE,CAAE,CAAC,CACL0B,EAAE,CAAE,GAAG,CACP,SAAS,CAAE,CACTC,eAAe,CAAE,cACnB,CAAC,CACD,gBAAgB,CAAE,CAChBA,eAAe,CAAE,cAAc,CAC/BjB,KAAK,CAAE,sBAAsB,CAC7B,SAAS,CAAE,CACTiB,eAAe,CAAE,cACnB,CAAC,CACD,yBAAyB,CAAE,CACzBjB,KAAK,CAAE,sBACT,CACF,CACF,CAAE,CAAAvG,QAAA,eAEFV,IAAA,CAAChF,YAAY,EACXsL,EAAE,CAAE,CACF6B,QAAQ,CAAE,CAAC,CACXC,EAAE,CAAE9G,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB+G,EAAE,CAAE/G,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwF,cAAc,CAAE,QAClB,CAAE,CAAApG,QAAA,CAEDiH,IAAI,CAAC9B,KAAK,EAAI8B,IAAI,CAAC9B,KAAK,CAAG,CAAC,cAC3B7F,IAAA,CAAC/D,KAAK,EAACqM,YAAY,CAAEX,IAAI,CAAC9B,KAAM,CAACoB,KAAK,CAAC,OAAO,CAACsB,GAAG,CAAE,EAAG,CAAA7H,QAAA,CACpDiH,IAAI,CAAChC,IAAI,CACL,CAAC,CAERgC,IAAI,CAAChC,IACN,CACW,CAAC,cACf3F,IAAA,CAAC/E,YAAY,EACXuN,OAAO,CAAEb,IAAI,CAACjC,IAAK,CACnBY,EAAE,CAAE,CACF,4BAA4B,CAAE,CAC5BY,QAAQ,CAAE,QAAQ,CAClBK,UAAU,CAAE,GACd,CACF,CAAE,CACH,CAAC,GAvDGI,IAAI,CAACjC,IAwDF,CACX,CAAC,CACE,CAAC,EACJ,CACN,CAED,KAAM,CAAA+C,0BAA0B,CAAG,KAAAA,CAAOC,SAAS,CAAEC,MAAM,GAAK,CAC9D,GAAI,CACF9F,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAE,CAAE4F,SAAS,CAAEC,MAAO,CAAC,CAAC,CAEjE,KAAM,CAAAtO,KAAK,CAACuO,IAAI,CAAC,iBAAiB,CAAE,CAClCtE,UAAU,CAAEoE,SAAS,CACrBxF,UAAU,CAAEyF,MAAM,CAACzF,UAAU,CAAE;AAC/B2F,UAAU,CAAEF,MAAM,CAACE,UAAU,CAC7BC,WAAW,CAAEH,MAAM,CAACG,WACtB,CAAC,CAAC,CAEFjG,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CACvD,CAAE,MAAOC,GAAG,CAAE,CACZF,OAAO,CAACG,KAAK,CAAC,kCAAkC,CAAED,GAAG,CAAC,CACtD;AACAgG,KAAK,CAAC,8CAA8C,CAAC,CACrD,OAAQ;AACV,CAAC,OAAS,CACRnI,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,eAAe,CAAC,IAAI,CAAC,CACrB;AACA,KAAM,CAAA0B,YAAY,CAAC,CAAC,CACtB,CACF,CAAC,CAED,mBACEtC,KAAA,CAAC5F,GAAG,EAACgM,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEoC,SAAS,CAAE1H,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAZ,QAAA,eAC7DV,IAAA,CAACzF,WAAW,GAAE,CAAC,cACfyF,IAAA,CAACvF,MAAM,EACLwO,QAAQ,CAAC,OAAO,CAChB3C,EAAE,CAAE,CACF4C,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE9H,KAAK,CAAC8H,MAAM,CAAC9C,MAAM,CAAG,CAChC,CAAE,CAAA3F,QAAA,cAEFR,KAAA,CAACxF,OAAO,EAAC4L,EAAE,CAAE,CAAEU,SAAS,CAAE,CAAEoC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAAEvB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAA5I,QAAA,eAClFV,IAAA,CAAClF,UAAU,EACTmM,KAAK,CAAC,SAAS,CACf,aAAW,aAAa,CACxBsC,IAAI,CAAEjI,KAAK,CAAG,KAAK,CAAG,OAAQ,CAC9BuG,OAAO,CAAE/C,kBAAmB,CAC5BwB,EAAE,CAAE,CACF8B,EAAE,CAAE9G,KAAK,CAAG,CAAC,CAAG,CAAE8H,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAChChB,EAAE,CAAE/G,KAAK,CAAG,CAAE8H,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAG,CAAC,CAChCzC,OAAO,CAAE,CAAEyC,EAAE,CAAE,MAAO,CAAC,CACvB5C,CAAC,CAAE,CAAE2C,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CACtB,CAAE,CAAA3I,QAAA,cAEFV,IAAA,CAAC7D,QAAQ,GAAE,CAAC,CACF,CAAC,cACb6D,IAAA,CAACpF,UAAU,EACTwM,OAAO,CAAC,IAAI,CACZoC,MAAM,MACN5B,SAAS,CAAC,KAAK,CACftB,EAAE,CAAE,CACFmD,QAAQ,CAAE,CAAC,CACXvC,QAAQ,CAAE,CAAEkC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,SAAU,CAAC,CACrD7B,UAAU,CAAE,qBAAqB,CACjCF,UAAU,CAAE,CAAE6B,EAAE,CAAE,GAAG,CAAEE,EAAE,CAAE,GAAI,CACjC,CAAE,CAAA5I,QAAA,CAEDK,CAAC,CAAC,YAAY,CAAC,CACN,CAAC,cACbb,KAAA,CAAC5F,GAAG,EAACgM,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEE,GAAG,CAAE,CAAEqC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAE,CAAA5I,QAAA,eAEnFR,KAAA,CAAC5F,GAAG,EAAAoG,QAAA,eACFV,IAAA,CAACtE,MAAM,EACLuL,KAAK,CAAC,SAAS,CACfyC,SAAS,cAAE1J,IAAA,CAAC7C,aAAa,EAACmJ,EAAE,CAAE,CAAEY,QAAQ,CAAE,CAAEkC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAE,CAAE,CAAE,CAAE,CAC3FK,OAAO,cAAE3J,IAAA,CAAC3C,qBAAqB,EAACiJ,EAAE,CAAE,CAAEY,QAAQ,CAAE,CAAEkC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAE,CAAE,CAAE,CAAE,CACjGzB,OAAO,CAAE3C,kBAAmB,CAC5BoB,EAAE,CAAE,CACF6B,QAAQ,CAAE,CAAEiB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,GAAI,CAAC,CACtC3C,YAAY,CAAE,CAAC,CACfoB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC7BM,EAAE,CAAE,CAAER,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,IAAI,CAAEC,EAAE,CAAE,CAAE,CAAC,CAChCvC,GAAG,CAAE,CAAEqC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,IAAI,CAAEC,EAAE,CAAE,CAAE,CAAC,CACjCpC,QAAQ,CAAE,CAAEkC,EAAE,CAAE,SAAS,CAAEC,EAAE,CAAE,SAAS,CAAEC,EAAE,CAAE,QAAS,CAAC,CACxD,wBAAwB,CAAE,CACxBO,MAAM,CAAE,CACV,CAAC,CACD,sBAAsB,CAAE,CACtBA,MAAM,CAAE,CAAC,CACTxB,EAAE,CAAE,GACN,CAAC,CACD,SAAS,CAAE,CACTH,eAAe,CAAEvM,KAAK,CAAC0F,KAAK,CAACyI,OAAO,CAACC,MAAM,CAACC,KAAK,CAAE,GAAG,CAAC,CACvDC,SAAS,CAAE,kBACb,CAAC,CACDC,UAAU,CAAE,sBACd,CAAE,CAAAxJ,QAAA,CAEDM,IAAI,CAACO,QAAQ,GAAK,IAAI,CAAG,SAAS,CAAG,SAAS,CACzC,CAAC,cACTrB,KAAA,CAAC3E,IAAI,EACHwG,QAAQ,CAAEE,cAAe,CACzBkI,IAAI,CAAEC,OAAO,CAACnI,cAAc,CAAE,CAC9BoI,OAAO,CAAEhF,mBAAoB,CAC7BiF,YAAY,CAAE,CACZC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,OACd,CAAE,CACFC,eAAe,CAAE,CACfF,QAAQ,CAAE,KAAK,CACfC,UAAU,CAAE,OACd,CAAE,CACFE,SAAS,CAAE,CACTC,KAAK,CAAE,CACLC,SAAS,CAAE,CAAC,CACZtE,EAAE,CAAE,CACFkB,EAAE,CAAE,GAAG,CACPqD,QAAQ,CAAE,SAAS,CACnBrH,MAAM,CAAE,2CAA2C,CACnD,UAAU,CAAE,CACVsH,OAAO,CAAE,IAAI,CACblE,OAAO,CAAE,OAAO,CAChBqC,QAAQ,CAAE,UAAU,CACpB8B,GAAG,CAAE,CAAC,CACNC,KAAK,CAAE,EAAE,CACT9B,KAAK,CAAE,EAAE,CACT+B,MAAM,CAAE,EAAE,CACVvE,OAAO,CAAE,kBAAkB,CAC3BuD,SAAS,CAAE,gCAAgC,CAC3Cd,MAAM,CAAE,CACV,CACF,CACF,CACF,CAAE,CAAAzI,QAAA,eAEFR,KAAA,CAAC1E,QAAQ,EAACqM,OAAO,CAAEA,CAAA,GAAMvC,oBAAoB,CAAC,IAAI,CAAE,CAAA5E,QAAA,eAClDV,IAAA,CAAChF,YAAY,EAAA0F,QAAA,cACXV,IAAA,CAACzC,YAAY,EAAC2J,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrB,CAAC,6CAEjB,EAAU,CAAC,cACXhH,KAAA,CAAC1E,QAAQ,EAACqM,OAAO,CAAEA,CAAA,GAAMvC,oBAAoB,CAAC,IAAI,CAAE,CAAA5E,QAAA,eAClDV,IAAA,CAAChF,YAAY,EAAA0F,QAAA,cACXV,IAAA,CAACzC,YAAY,EAAC2J,QAAQ,CAAC,OAAO,CAAE,CAAC,CACrB,CAAC,UAEjB,EAAU,CAAC,EACP,CAAC,EACJ,CAAC,CAELjG,WAAW,eACVf,KAAA,CAAAE,SAAA,EAAAM,QAAA,eACEV,IAAA,CAAClF,UAAU,EACT+M,OAAO,CAAG1C,KAAK,EAAKnD,WAAW,CAACmD,KAAK,CAACC,aAAa,CAAE,CACrDkB,EAAE,CAAE,CACF+B,EAAE,CAAE,CAAEe,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC/B7C,CAAC,CAAE,CAAE2C,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,IAAI,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC/B,SAAS,CAAE,CACTW,SAAS,CAAE,aAAa,CACxBvD,OAAO,CAAE,uBACX,CAAC,CACDwD,UAAU,CAAE,sBACd,CAAE,CAAAxJ,QAAA,cAEFV,IAAA,CAACvE,MAAM,EACLyP,GAAG,CAAEjK,WAAW,CAACkK,mBAAmB,CAClClK,WAAW,CAACkK,mBAAmB,CAACC,UAAU,CAAC,MAAM,CAAC,CAC9CnK,WAAW,CAACkK,mBAAmB,CAC/B,4BAA4BlK,WAAW,CAACkK,mBAAmB,EAAE,CAC/D,EAAG,CACPE,GAAG,CAAEpK,WAAW,CAACqK,SAAU,CAC3BhF,EAAE,CAAE,CACF4C,KAAK,CAAE,CAAEE,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CACjC2B,MAAM,CAAE,CAAE7B,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAClC5C,OAAO,CAAE,cAAc,CACvB6E,MAAM,CAAE,WAAW,CACnBC,WAAW,CAAE,kBAAkB,CAC/BtE,QAAQ,CAAE,CAAEkC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACpD/B,UAAU,CAAE,GACd,CAAE,CAAA7G,QAAA,CAED,CAACO,WAAW,CAACkK,mBAAmB,IAAA1K,qBAAA,CAAIQ,WAAW,CAACqK,SAAS,UAAA7K,qBAAA,iBAArBA,qBAAA,CAAuBgL,MAAM,CAAC,CAAC,CAAC,EAC/D,CAAC,CACC,CAAC,cAEbvL,KAAA,CAAC3E,IAAI,EACHwG,QAAQ,CAAEA,QAAS,CACnBoI,IAAI,CAAEC,OAAO,CAACrI,QAAQ,CAAE,CACxBsI,OAAO,CAAEA,CAAA,GAAMrI,WAAW,CAAC,IAAI,CAAE,CACjC6F,OAAO,CAAEA,CAAA,GAAM7F,WAAW,CAAC,IAAI,CAAE,CACjCyI,eAAe,CAAE,CAAED,UAAU,CAAElJ,KAAK,CAAG,MAAM,CAAG,OAAO,CAAEiJ,QAAQ,CAAE,KAAM,CAAE,CAC3ED,YAAY,CAAE,CAAEE,UAAU,CAAElJ,KAAK,CAAG,MAAM,CAAG,OAAO,CAAEiJ,QAAQ,CAAE,QAAS,CAAE,CAAA7J,QAAA,eAE3ER,KAAA,CAAC1E,QAAQ,EAACqM,OAAO,CAAE5B,kBAAmB,CAAAvF,QAAA,eACpCV,IAAA,CAAChF,YAAY,EAAA0F,QAAA,cACXV,IAAA,CAACzD,UAAU,EAAC2K,QAAQ,CAAC,OAAO,CAAE,CAAC,CACnB,CAAC,cACflH,IAAA,CAAC/E,YAAY,EAACuN,OAAO,CAAEzH,CAAC,CAAC,gBAAgB,CAAE,CAAE,CAAC,EACtC,CAAC,cACXf,IAAA,CAACnF,OAAO,GAAE,CAAC,cACXqF,KAAA,CAAC1E,QAAQ,EAACqM,OAAO,CAAE3B,iBAAkB,CAAAxF,QAAA,eACnCV,IAAA,CAAChF,YAAY,EAAA0F,QAAA,cACXV,IAAA,CAACvC,UAAU,EAACyJ,QAAQ,CAAC,OAAO,CAAE,CAAC,CACnB,CAAC,cACflH,IAAA,CAAC/E,YAAY,EAACuN,OAAO,CAAEzH,CAAC,CAAC,aAAa,CAAE,CAAE,CAAC,EACnC,CAAC,EACP,CAAC,EACP,CACH,CAGA,CAACE,WAAW,eACXjB,IAAA,CAACtE,MAAM,EACLuL,KAAK,CAAC,SAAS,CACfY,OAAO,CAAEA,CAAA,GAAM1G,QAAQ,CAAC,QAAQ,CAAE,CAClCmF,EAAE,CAAE,CACF+B,EAAE,CAAE,CAAEe,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC/BvB,EAAE,CAAE,CAAEqB,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC/BM,EAAE,CAAE,CAAER,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,IAAI,CAAEC,EAAE,CAAE,CAAE,CAAC,CAChCpC,QAAQ,CAAE,CAAEkC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,MAAO,CAAC,CACpD3C,YAAY,CAAE,CAAC,CACf,SAAS,CAAE,CACTuB,eAAe,CAAEvM,KAAK,CAAC0F,KAAK,CAACyI,OAAO,CAACC,MAAM,CAACC,KAAK,CAAE,GAAG,CAAC,CACvDC,SAAS,CAAE,kBACb,CAAC,CACDC,UAAU,CAAE,sBACd,CAAE,CAAAxJ,QAAA,CAEDK,CAAC,CAAC,YAAY,CAAC,CACV,CACT,EACE,CAAC,EACC,CAAC,CACJ,CAAC,cACTf,IAAA,CAAC1F,GAAG,EACFsN,SAAS,CAAC,KAAK,CACftB,EAAE,CAAE,CACF4C,KAAK,CAAE,CAAEG,EAAE,CAAE/I,WAAY,CAAC,CAC1BoL,UAAU,CAAE,CAAErC,EAAE,CAAE,CAAE,CACtB,CAAE,CAAA3I,QAAA,cAEFV,IAAA,CAACxF,MAAM,EACL4M,OAAO,CAAE5F,QAAQ,CAAG,WAAW,CAAG,WAAY,CAC9C2I,IAAI,CAAE3I,QAAQ,CAAGK,UAAU,CAAG,IAAK,CACnCwI,OAAO,CAAEvF,kBAAmB,CAC5B6G,MAAM,CAAErK,KAAK,CAAG,OAAO,CAAG,MAAO,CACjCsK,UAAU,CAAE,CACVC,WAAW,CAAE,IACf,CAAE,CACFvF,EAAE,CAAE,CACF,oBAAoB,CAAE,CACpBwF,SAAS,CAAE,YAAY,CACvB5C,KAAK,CAAE5I,WAAW,CAClByK,GAAG,CAAE,CAAE3B,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CAC3C2B,MAAM,CAAE,CAAE7B,EAAE,CAAE,oBAAoB,CAAEC,EAAE,CAAE,oBAAoB,CAAEC,EAAE,CAAE,oBAAqB,CAAC,CACxFyC,WAAW,CAAEzK,KAAK,CAAG,MAAM,CAAG,+BAA+B,CAC7D0K,UAAU,CAAE1K,KAAK,CAAG,+BAA+B,CAAG,MAAM,CAC5D2K,SAAS,CAAE,QAAQ,CACnBhD,QAAQ,CAAE,OAAO,CACjBE,MAAM,CAAE,IAAI,CACZ,sBAAsB,CAAE,CACtBD,KAAK,CAAE,KACT,CAAC,CACD,4BAA4B,CAAE,CAC5BgD,UAAU,CAAE,SACd,CAAC,CACD,4BAA4B,CAAE,CAC5BA,UAAU,CAAE,MAAM,CAClBvF,YAAY,CAAE,KAChB,CAAC,CACD,kCAAkC,CAAE,CAClCuF,UAAU,CAAE,MACd,CACF,CACF,CAAE,CAAAxL,QAAA,CAED2F,MAAM,CACD,CAAC,CACN,CAAC,cACNrG,IAAA,CAAC1F,GAAG,EACFsN,SAAS,CAAC,MAAM,CAChBtB,EAAE,CAAE,CACFmD,QAAQ,CAAE,CAAC,CACXhD,CAAC,CAAE,CAAE2C,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CAC9BJ,KAAK,CAAE,MAAM,CACbiD,SAAS,CAAE,CAAE/C,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,MAAO,CAAC,CACjDtC,SAAS,CAAE,gBAAgBxF,QAAQ,CAAG,MAAM,CAAGG,QAAQ,CAAG,MAAM,CAAG,MAAM,GAAG,CAC5EkJ,QAAQ,CAAE,MACZ,CAAE,CAAAnK,QAAA,cAEFR,KAAA,CAAC/E,SAAS,EACRiR,QAAQ,CAAC,IAAI,CACb9F,EAAE,CAAE,CACFyB,EAAE,CAAE,CAAEqB,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3BM,EAAE,CAAE,CAAER,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAC9B,CAAE,CAAA5I,QAAA,EAEDA,QAAQ,CAGZ,CAAAO,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwB,IAAI,IAAK,SAAS,eAC9BzC,IAAA,CAACH,qBAAqB,EACnBsK,IAAI,CAAExJ,iBAAiB,EAAIE,YAAY,EAAInB,KAAK,CAACC,GAAG,CAAC,CAAC,CAAC0M,OAAO,CAAC3M,KAAK,CAACC,GAAG,CAACkB,YAAY,CAACyC,QAAQ,CAAC,CAACgJ,GAAG,CAACzL,YAAY,CAAC0L,QAAQ,EAAI,EAAE,CAAE,QAAQ,CAAC,CAAE,CAC5IC,OAAO,CAAE3L,YAAa,CACtB4L,QAAQ,CAAE,CAAAxL,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwL,QAAQ,GAAI,IAAK,CACxCC,QAAQ,CAAEjE,0BAA2B,CACrC4B,OAAO,CAAE,IAAK,0BAA2B,CAC1C,CACH,EACY,CAAC,CACT,CAAC,cACNnK,KAAA,CAACtE,MAAM,EACLuO,IAAI,CAAEhI,gBAAiB,CACvBkI,OAAO,CAAEjE,kBAAmB,CAC5B,kBAAgB,qBAAqB,CACrC,mBAAiB,2BAA2B,CAAA1F,QAAA,eAE5CV,IAAA,CAAChE,WAAW,EAACuI,EAAE,CAAC,qBAAqB,CAAA7D,QAAA,CAClCK,CAAC,CAAC,yBAAyB,CAAC,CAClB,CAAC,cACdf,IAAA,CAAClE,aAAa,EAAA4E,QAAA,cACZV,IAAA,CAACjE,iBAAiB,EAACwI,EAAE,CAAC,2BAA2B,CAAA7D,QAAA,CAC9CK,CAAC,CAAC,2BAA2B,CAAC,CACd,CAAC,CACP,CAAC,cAChBb,KAAA,CAACrE,aAAa,EAAA6E,QAAA,eACZV,IAAA,CAACtE,MAAM,EAACmM,OAAO,CAAEzB,kBAAmB,CAACa,KAAK,CAAC,SAAS,CAAAvG,QAAA,CACjDK,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,cACTf,IAAA,CAACtE,MAAM,EAACmM,OAAO,CAAE1B,mBAAoB,CAACc,KAAK,CAAC,SAAS,CAAC0F,SAAS,MAAAjM,QAAA,CAC5DK,CAAC,CAAC,gBAAgB,CAAC,CACd,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAR,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}