const jwt = require('jsonwebtoken');
const config = require('../config/auth.config');
const db = require('../config/db');

const authenticateToken = async (req, res, next) => {
  try {
    console.log('=== Auth Middleware ===');
    console.log('Headers:', req.headers);

    const token = req.headers.authorization;

    if (!token) {
      console.error('Auth error: No token provided');
      return res.status(401).json({ success: false, message: 'Authentication error: No token provided' });
    }

    console.log('Token received:', token.substring(0, 20) + '...');

    // Remove Bearer prefix if present
    const tokenValue = token.startsWith('Bearer ') ? token.slice(7) : token;
    console.log('Token value (first 20 chars):', tokenValue.substring(0, 20) + '...');

    try {
      const decoded = jwt.verify(tokenValue, config.secret);
      console.log('Token decoded successfully:', { id: decoded.id, role: decoded.role });

      if (!decoded || !decoded.id) {
        console.error('Auth error: Invalid token payload', decoded);
        return res.status(401).json({ success: false, message: 'Authentication error: Invalid token' });
      }

      // Get user from database with status
      console.log('Fetching user from database with ID:', decoded.id);
      const [users] = await db.pool.execute(
        'SELECT id, full_name, role, status, delete_scheduled_at FROM users WHERE id = ?',
        [decoded.id]
      );

      console.log('Database query result:', users);

      if (!users || users.length === 0) {
        console.error('Auth error: User not found', decoded.id);
        return res.status(401).json({ success: false, message: 'Authentication error: User not found' });
      }

      const user = users[0];

      // Check if user account is deleted or pending deletion
      if (user.status === 'deleted') {
        console.log('Auth error: Account deleted', user.id);
        return res.status(401).json({
          success: false,
          message: 'تم حذف هذا الحساب',
          message_en: 'Account has been deleted',
          accountStatus: 'deleted',
          requiresLogout: true
        });
      }

      if (user.status === 'pending_deletion') {
        // السماح للمستخدمين المجدولين للحذف بالوصول لصفحات معينة (بدون البروفايل)
        const allowedPaths = ['/cancel-delete', '/timezone', '/request-delete', '/verify-delete'];
        const isAllowedPath = allowedPaths.some(path => req.path.includes(path));

        if (!isAllowedPath) {
          console.log('Auth error: Account pending deletion, path not allowed', user.id, req.path);
          return res.status(401).json({
            success: false,
            message: 'هذا الحساب مجدول للحذف',
            message_en: 'Account is scheduled for deletion',
            accountStatus: 'pending_deletion',
            deleteScheduledAt: user.delete_scheduled_at,
            requiresLogout: true
          });
        }

        console.log('Auth: Allowing pending deletion user access to:', req.path);
      }

      console.log('Auth success:', {
        userId: user.id,
        role: user.role,
        name: user.full_name,
        status: user.status
      });

      // Attach user data to request
      req.user = user;
      next();
    } catch (err) {
      console.error('Auth error:', err);
      return res.status(401).json({ success: false, message: 'Authentication error: Invalid token' });
    }
  } catch (error) {
    console.error('Auth error:', error);
    return res.status(500).json({ success: false, message: 'Authentication error' });
  }
};

const isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({
      message: 'Access denied. Admin privileges required.',
      type: 'auth'
    });
  }
};

const isTeacher = (req, res, next) => {
  if (req.user && (req.user.role === 'platform_teacher' || req.user.role === 'new_teacher')) {
    next();
  } else {
    res.status(403).json({
      message: 'Access denied. Teacher privileges required.',
      type: 'auth'
    });
  }
};

const isStudent = (req, res, next) => {
  if (req.user && req.user.role === 'student') {
    next();
  } else {
    res.status(403).json({
      message: 'Access denied. Student privileges required.',
      type: 'auth'
    });
  }
};

module.exports = {
  authenticateToken,
  isAdmin,
  isTeacher,
  isStudent
};
