require('dotenv').config();

module.exports = {
    // Server configuration
    port: process.env.PORT || 5000,
    host: process.env.HOST || '0.0.0.0',
    frontendUrl: process.env.CLIENT_URL || 'https://allemnionline.com',

  // Email configuration
  email: {
    // SendGrid configuration
    sendgridApiKey: process.env.SENDGRID_API_KEY,
    fromName: process.env.EMAIL_FROM_NAME || 'Allemni online',
    fromAddress: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>',

    // Legacy email configuration (as fallback)
    host: process.env.EMAIL_HOST || 'smtp.sendgrid.net',
    port: process.env.EMAIL_PORT || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    user: process.env.EMAIL_USER || 'apikey',
    password: process.env.EMAIL_PASSWORD || process.env.SENDGRID_API_KEY
  },

  // Email templates
  emailTemplates: {
    applicationApproval: process.env.EMAIL_TEMPLATE_APPROVAL || '',
    applicationRejection: process.env.EMAIL_TEMPLATE_REJECTION || ''
  }
};
