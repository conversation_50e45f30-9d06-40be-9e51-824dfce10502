import React, { createContext, useContext, useEffect, useState } from 'react';
import io from 'socket.io-client';
import { useAuth } from './AuthContext';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const { currentUser, token, isAuthenticated } = useAuth();
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    let timeoutId;

    if (isAuthenticated && token && currentUser) {
      console.log('SocketContext: Attempting to connect with:', {
        isAuthenticated,
        hasToken: !!token,
        userId: currentUser?.id
      });

      // Add a small delay to ensure token is available
      timeoutId = setTimeout(() => {
        console.log('SocketContext: Creating new socket connection...');
        const socketUrl = process.env.REACT_APP_API_URL || 'https://allemnionline.com';
        console.log('SocketContext: Connecting to socket URL:', socketUrl);

        const newSocket = io(socketUrl, {
          auth: {
            token: token // Remove Bearer prefix since it's handled in socket.js
          },
          // Prefer polling first so connection succeeds even if WebSocket upgrade is blocked by the proxy
          transports: ['polling', 'websocket'],
          reconnection: true,
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          timeout: 10000
        });

        newSocket.on('connect', () => {
          console.log('SocketContext: Socket connected successfully');
          setIsConnected(true);
        });

        newSocket.on('disconnect', (reason) => {
          console.log('SocketContext: Socket disconnected:', reason);
          setIsConnected(false);
        });

        newSocket.on('connect_error', (error) => {
          console.error('SocketContext: Socket connection error:', error);
          setIsConnected(false);
          // Don't retry immediately if it's an authentication error
          if (error.message && error.message.includes('Authentication')) {
            console.log('SocketContext: Authentication error, not retrying');
            return;
          }
        });

        setSocket(newSocket);
      }, 500);
    } else {
      // Only log if there's an actual issue, not just normal unauthenticated state
      if (token && !isAuthenticated) {
        console.log('SocketContext: Not connecting - authentication issue:', {
          isAuthenticated,
          hasToken: !!token,
          hasUser: !!currentUser
        });
      }

      if (socket) {
        console.log('SocketContext: User not authenticated, disconnecting socket');
        socket.disconnect();
        setSocket(null);
        setIsConnected(false);
      }
    }

    return () => {
      clearTimeout(timeoutId);
      if (socket) {
        console.log('SocketContext: Cleaning up socket connection');
        socket.disconnect();
        setSocket(null);
        setIsConnected(false);
      }
    };
  }, [isAuthenticated, token, currentUser]);

  const value = {
    socket,
    isConnected
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

export default SocketContext;
