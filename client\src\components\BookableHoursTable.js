import React from 'react';
import { useTranslation } from 'react-i18next';
import { format, addDays } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import moment from 'moment-timezone';
import { formatDateInStudentTimezone } from '../utils/timezone';
import {
  Box,
  Typography,
  Paper,
  Tooltip,
  Chip,
  useTheme,
  alpha,
  CircularProgress,
  Button
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

const BookableHoursTable = ({
  availableHours,
  loading = false,
  showStats = true,
  onSlotSelect,
  currentWeekStart,
  daysOfWeek: propDaysOfWeek,
  isSlotInPast,
  studentTimezone
}) => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';

  // Define time slots - full hours from 00:00 to 23:00
  const timeSlots = [];
  for (let hour = 0; hour < 24; hour++) {
    const startTime = `${hour.toString().padStart(2, '0')}:00`;
    const endTime = hour < 23 ? `${(hour + 1).toString().padStart(2, '0')}:00` : '00:00';
    timeSlots.push({
      key: `${startTime}-${endTime}`,
      label: startTime,
      hour,
      minute: 0,
      // Include both half-hour slots for this hour
      firstHalf: `${startTime}-${hour.toString().padStart(2, '0')}:30`,
      secondHalf: hour < 23 ? `${hour.toString().padStart(2, '0')}:30-${endTime}` : '23:30-00:00'
    });
  }

  // Define days of the week (use prop if provided, otherwise default)
  const daysOfWeek = propDaysOfWeek ? propDaysOfWeek.map(day => ({
    key: day,
    label: t(`days.${day}`)
  })) : [
    { key: 'monday', label: t('days.monday') },
    { key: 'tuesday', label: t('days.tuesday') },
    { key: 'wednesday', label: t('days.wednesday') },
    { key: 'thursday', label: t('days.thursday') },
    { key: 'friday', label: t('days.friday') },
    { key: 'saturday', label: t('days.saturday') },
    { key: 'sunday', label: t('days.sunday') }
  ];

  // Get total selected hours
  const getTotalSelectedHours = () => {
    if (!availableHours) return 0;
    return Object.values(availableHours).reduce((total, daySlots) => total + (daySlots ? daySlots.length : 0), 0);
  };

  // Get abbreviated day names for mobile
  const getAbbreviatedDayName = (dayKey) => {
    const abbreviations = {
      monday: t('days.mondayShort') || 'Mon',
      tuesday: t('days.tuesdayShort') || 'Tue',
      wednesday: t('days.wednesdayShort') || 'Wed',
      thursday: t('days.thursdayShort') || 'Thu',
      friday: t('days.fridayShort') || 'Fri',
      saturday: t('days.saturdayShort') || 'Sat',
      sunday: t('days.sundayShort') || 'Sun'
    };
    return abbreviations[dayKey] || dayKey.substring(0, 3);
  };

  // Get consecutive hour blocks for a day
  const getConsecutiveHourBlocks = (daySlots) => {
    if (!daySlots || daySlots.length === 0) return [];

    const blocks = [];
    const sortedSlots = [...daySlots].sort();

    for (let i = 0; i < sortedSlots.length; i++) {
      const currentSlot = sortedSlots[i];
      const [startTime] = currentSlot.split('-');
      const [hours, minutes] = startTime.split(':').map(Number);

      // Only process slots that start at :00 (beginning of hour)
      if (minutes === 0) {
        // Check if the next 30-minute slot is also available
        let nextSlot;
        if (hours === 23) {
          // Special case for 23:00 - next slot is 23:30-00:00
          nextSlot = `23:30-00:00`;
        } else {
          nextSlot = `${hours.toString().padStart(2, '0')}:30-${(hours + 1).toString().padStart(2, '0')}:00`;
        }

        if (sortedSlots.includes(nextSlot)) {
          // This is a full hour block
          blocks.push({
            type: 'full',
            startSlot: currentSlot,
            endSlot: nextSlot,
            hour: hours
          });
          // Skip the next slot since it's part of this full hour
          i++;
        } else {
          // This is just a half hour
          blocks.push({
            type: 'half',
            slot: currentSlot,
            hour: hours,
            minute: minutes
          });
        }
      } else {
        // This is a :30 slot, check if it's not part of a full hour we already processed
        const prevSlot = `${hours.toString().padStart(2, '0')}:00-${hours.toString().padStart(2, '0')}:30`;
        if (!sortedSlots.includes(prevSlot)) {
          // This is just a half hour
          blocks.push({
            type: 'half',
            slot: currentSlot,
            hour: hours,
            minute: minutes
          });
        }
      }
    }

    return blocks;
  };

  // Check if a slot is part of a full hour block
  const isPartOfFullHour = (day, timeSlotKey) => {
    if (!availableHours || !availableHours[day]) return false;

    const blocks = getConsecutiveHourBlocks(availableHours[day]);
    return blocks.some(block =>
      block.type === 'full' && (block.startSlot === timeSlotKey || block.endSlot === timeSlotKey)
    );
  };

  // Check if this is the first slot of a full hour (should show as full hour)
  const isFullHourStart = (day, timeSlotKey) => {
    if (!availableHours || !availableHours[day]) return false;

    const blocks = getConsecutiveHourBlocks(availableHours[day]);
    const fullHourBlock = blocks.find(block =>
      block.type === 'full' && block.startSlot === timeSlotKey
    );

    if (!fullHourBlock) return false;

    // Check if both slots are available (not in the past)
    const firstSlotPast = isSlotInPast && isSlotInPast(day, fullHourBlock.startSlot);
    const secondSlotPast = isSlotInPast && isSlotInPast(day, fullHourBlock.endSlot);

    // Show as full hour only if both slots are available
    return !firstSlotPast && !secondSlotPast;
  };

  // Check if this is the second slot of a full hour (should be hidden or shown as individual)
  const isFullHourSecondSlot = (day, timeSlotKey) => {
    if (!availableHours || !availableHours[day]) return false;

    const blocks = getConsecutiveHourBlocks(availableHours[day]);
    const fullHourBlock = blocks.find(block =>
      block.type === 'full' && block.endSlot === timeSlotKey
    );

    if (!fullHourBlock) return false;

    // Check if both slots are available
    const firstSlotPast = isSlotInPast && isSlotInPast(day, fullHourBlock.startSlot);
    const secondSlotPast = isSlotInPast && isSlotInPast(day, fullHourBlock.endSlot);

    // If both slots are available, hide the second slot (part of full hour display)
    if (!firstSlotPast && !secondSlotPast) {
      return true; // Hide as part of full hour
    }

    // If first slot is past but second is not, don't hide (show as individual)
    return false;
  };

  // Handle slot selection
  const handleSlotClick = (day, timeSlot) => {
    if (onSlotSelect) {
      onSlotSelect(day, timeSlot.key);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!availableHours || getTotalSelectedHours() === 0) {
    return (
      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          {t('teacher.noAvailableHours')}
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper elevation={3} sx={{ mb: 4, borderRadius: 2 }}>
      {/* Header with Stats */}
      {showStats && (
        <Box sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          p: 3,
          color: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 2
        }}>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
              📅 {t('booking.selectTimeSlot')}
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9, fontWeight: 'medium' }}>
              {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.8 }}>
              {t('booking.clickToSelectSlot')}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', alignItems: 'center' }}>
            <Chip
              icon={<EventAvailableIcon />}
              label={`${getTotalSelectedHours()} ${t('teacher.timeSlots')}`}
              sx={{
                bgcolor: 'rgba(255,255,255,0.2)',
                color: 'white',
                fontWeight: 'bold',
                '& .MuiChip-icon': { color: 'white' }
              }}
            />
            <Box sx={{ display: 'flex', gap: 1, fontSize: '0.75rem', opacity: 0.9 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Box sx={{ width: 12, height: 12, bgcolor: 'rgba(76, 175, 80, 0.8)', borderRadius: 1 }} />
                <span>{t('booking.availableSlot')}</span>
              </Box>
            </Box>
          </Box>
        </Box>
      )}

      {/* Calendar Table */}
      <Box sx={{ overflow: 'auto' }}>
        {/* Days Header */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '60px repeat(7, minmax(80px, 1fr))',
            sm: '80px repeat(7, minmax(100px, 1fr))',
            md: '120px repeat(7, minmax(120px, 1fr))',
          },
          bgcolor: alpha(theme.palette.primary.main, 0.05),
          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          position: 'sticky',
          top: 0,
          zIndex: 10
        }}>
          <Box sx={{
            p: { xs: 1.5, sm: 2, md: 2.5 },
            minHeight: { xs: '60px', sm: '75px', md: '90px' },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
          }}>
            <Typography variant="body2" sx={{
              fontWeight: 'bold',
              color: theme.palette.text.secondary,
              fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' }
            }}>
              ⏰ {t('teacher.time')}
            </Typography>
          </Box>
          {daysOfWeek.map((day, index) => {
            // Calculate the date for this day using student's timezone
            let dayDate;
            let formattedDate;

            if (currentWeekStart && studentTimezone) {
              // Get the date in student's timezone
              const baseDate = addDays(currentWeekStart, index);
              const baseDateStr = format(baseDate, 'yyyy-MM-dd');
              const studentDateStr = formatDateInStudentTimezone(`${baseDateStr} 12:00:00`, studentTimezone, 'YYYY-MM-DD');
              dayDate = moment(studentDateStr, 'YYYY-MM-DD').toDate();
            } else {
              dayDate = currentWeekStart ? addDays(currentWeekStart, index) : new Date();
            }

            const isRtl = t('direction') === 'rtl';

            return (
              <Box
                key={day.key}
                sx={{
                  p: { xs: 1.5, sm: 2, md: 2.5 },
                  minHeight: { xs: '60px', sm: '75px', md: '90px' },
                  textAlign: 'center',
                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  '&:last-child': { borderRight: 'none' },
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <Typography variant="h6" sx={{
                  fontWeight: 'bold',
                  color: theme.palette.primary.main,
                  fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' },
                  lineHeight: 1.2
                }}>
                  {/* Day name */}
                  <Box component="span" sx={{ display: { xs: 'none', md: 'block' } }}>
                    {day.label}
                  </Box>
                  <Box component="span" sx={{ display: { xs: 'none', sm: 'block', md: 'none' } }}>
                    {day.label.length > 6 ? day.label.substring(0, 6) : day.label}
                  </Box>
                  <Box component="span" sx={{ display: { xs: 'block', sm: 'none' } }}>
                    {getAbbreviatedDayName(day.key)}
                  </Box>
                </Typography>

                {/* Date */}
                <Typography variant="caption" sx={{
                  display: 'block',
                  color: theme.palette.text.secondary,
                  fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' },
                  mt: 0.5
                }}>
                  {format(dayDate, 'MMM d', { locale: isRtl ? ar : enUS })}
                </Typography>
              </Box>
            );
          })}
        </Box>

        {/* Time Slots Grid */}
        <Box>
          {timeSlots.map((timeSlot, index) => {
            const isHourStart = timeSlot.minute === 0;
            return (
              <Box
                key={timeSlot.key}
                sx={{
                  display: 'grid',
                  gridTemplateColumns: {
                    xs: '60px repeat(7, minmax(80px, 1fr))',
                    sm: '80px repeat(7, minmax(100px, 1fr))',
                    md: '120px repeat(7, minmax(120px, 1fr))',
                  },
                  borderBottom: `1px solid ${alpha(theme.palette.divider, isHourStart ? 0.3 : 0.1)}`,
                  bgcolor: index % 4 < 2 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                {/* Time Label */}
                <Box sx={{
                  p: { xs: 0.5, sm: 1, md: 1.5 },
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  bgcolor: isHourStart ? alpha(theme.palette.primary.main, 0.05) : 'transparent'
                }}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: isHourStart ? 'bold' : 'medium',
                      color: isHourStart ? theme.palette.primary.main : theme.palette.text.secondary,
                      fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' }
                    }}
                  >
                    {timeSlot.label}
                  </Typography>
                </Box>

                {/* Day Cells */}
                {daysOfWeek.map((day) => {
                  // Check if first half or second half is available
                  const firstHalfAvailable = availableHours[day.key] && availableHours[day.key].includes(timeSlot.firstHalf);
                  const secondHalfAvailable = availableHours[day.key] && availableHours[day.key].includes(timeSlot.secondHalf);

                  // Check if slots are in the past
                  const firstHalfPast = isSlotInPast && isSlotInPast(day.key, timeSlot.firstHalf);
                  const secondHalfPast = isSlotInPast && isSlotInPast(day.key, timeSlot.secondHalf);

                  return (
                    <Box
                      key={`${day.key}-${timeSlot.key}`}
                      sx={{
                        p: { xs: 0.2, sm: 0.4, md: 0.6 },
                        minHeight: { xs: '80px', sm: '100px', md: '120px' }, // Increased height for better content fit
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        '&:last-child': { borderRight: 'none' },
                        position: 'relative'
                      }}
                    >
                      {/* Dividing line in the middle */}
                      <Box
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: '5%',
                          right: '5%',
                          height: '2px',
                          bgcolor: alpha(theme.palette.divider, 0.6),
                          zIndex: 1,
                          borderRadius: '1px'
                        }}
                      />

                      {/* First Half (Top) */}
                      {firstHalfAvailable && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            height: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            zIndex: 2
                          }}
                        >
                          {firstHalfPast ? (
                            <Box sx={{
                              width: '90%',
                              height: '80%',
                              borderRadius: 1.5,
                              background: alpha(theme.palette.grey[400], 0.3),
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              border: `1px solid ${alpha(theme.palette.grey[400], 0.5)}`,
                              opacity: 0.5
                            }}>
                              <AccessTimeIcon
                                sx={{
                                  color: theme.palette.grey[500],
                                  fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' }
                                }}
                              />
                            </Box>
                          ) : (
                            <Tooltip
                              title={`${t('booking.clickToBook')} - ${timeSlot.firstHalf}`}
                              arrow
                            >
                              <Button
                                variant="contained"
                                size="small"
                                onClick={() => handleSlotClick(day.key, { key: timeSlot.firstHalf })}
                                sx={{
                                  width: '80%',
                                  height: '35%',
                                  minWidth: 'auto',
                                  borderRadius: 0.5,
                                  background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.8)} 0%, ${alpha(theme.palette.success.dark, 0.9)} 100%)`,
                                  boxShadow: '0 2px 8px rgba(76, 175, 80, 0.3)',
                                  '&:hover': {
                                    background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                                    boxShadow: '0 4px 12px rgba(76, 175, 80, 0.4)',
                                    transform: 'scale(1.02)',
                                  },
                                  p: 0,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  transition: 'all 0.2s ease-in-out'
                                }}
                              >
                                <AccessTimeIcon
                                  sx={{
                                    color: 'white',
                                    fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' }
                                  }}
                                />
                              </Button>
                            </Tooltip>
                          )}
                        </Box>
                      )}

                      {/* Second Half (Bottom) */}
                      {secondHalfAvailable && (
                        <Box
                          sx={{
                            position: 'absolute',
                            bottom: 0,
                            left: 0,
                            right: 0,
                            height: '50%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            zIndex: 2
                          }}
                        >
                          {secondHalfPast ? (
                            <Box sx={{
                              width: '90%',
                              height: '80%',
                              borderRadius: 1.5,
                              background: alpha(theme.palette.grey[400], 0.3),
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              border: `1px solid ${alpha(theme.palette.grey[400], 0.5)}`,
                              opacity: 0.5
                            }}>
                              <AccessTimeIcon
                                sx={{
                                  color: theme.palette.grey[500],
                                  fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' }
                                }}
                              />
                            </Box>
                          ) : (
                            <Tooltip
                              title={`${t('booking.clickToBook')} - ${timeSlot.secondHalf}`}
                              arrow
                            >
                              <Button
                                variant="contained"
                                size="small"
                                onClick={() => handleSlotClick(day.key, { key: timeSlot.secondHalf })}
                                sx={{
                                  width: '80%',
                                  height: '35%',
                                  minWidth: 'auto',
                                  borderRadius: 0.5,
                                  background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.8)} 0%, ${alpha(theme.palette.success.dark, 0.9)} 100%)`,
                                  boxShadow: '0 2px 8px rgba(76, 175, 80, 0.3)',
                                  '&:hover': {
                                    background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                                    boxShadow: '0 4px 12px rgba(76, 175, 80, 0.4)',
                                    transform: 'scale(1.02)',
                                  },
                                  p: 0,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  transition: 'all 0.2s ease-in-out'
                                }}
                              >
                                <AccessTimeIcon
                                  sx={{
                                    color: 'white',
                                    fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' }
                                  }}
                                />
                              </Button>
                            </Tooltip>
                          )}
                        </Box>
                      )}



                      {/* Empty state */}
                      {!firstHalfAvailable && !secondHalfAvailable && (
                        <Box sx={{
                          width: '90%',
                          height: { xs: '65px', sm: '85px', md: '105px' },
                          borderRadius: 1.5,
                          border: `1px dashed ${alpha(theme.palette.grey[400], 0.5)}`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          bgcolor: alpha(theme.palette.grey[100], 0.3),
                          zIndex: 2
                        }}>
                          <RadioButtonUncheckedIcon
                            sx={{
                              color: alpha(theme.palette.grey[400], 0.7),
                              fontSize: { xs: '1rem', sm: '1.2rem', md: '1.4rem' }
                            }}
                          />
                        </Box>
                      )}
                    </Box>
                  );
                })}
              </Box>
            );
          })}
        </Box>
      </Box>
    </Paper>
  );
};

export default BookableHoursTable;
