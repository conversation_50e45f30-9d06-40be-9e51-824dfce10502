-- إضافة حقول الحذف الناعم لجدول المستخدمين
-- تاريخ التنفيذ: 2024-07-29

-- إضافة حقل تاريخ الحذف
ALTER TABLE users 
ADD COLUMN deleted_at DATETIME NULL DEFAULT NULL 
COMMENT 'تاريخ حذف المستخدم (NULL = نشط، تاريخ = محذوف)';

-- إض<PERSON><PERSON>ة حقل سبب الحذف
ALTER TABLE users 
ADD COLUMN deletion_reason VARCHAR(500) NULL DEFAULT NULL 
COMMENT 'سبب حذف المستخدم';

-- إ<PERSON><PERSON><PERSON><PERSON> حقل معرف من قام بالحذف (للحذف الإداري)
ALTER TABLE users 
ADD COLUMN deleted_by INT NULL DEFAULT NULL 
COMMENT 'معرف المدير الذي قام بالحذف (NULL = حذف ذاتي)';

-- إضافة فهرس لتحسين الأداء
CREATE INDEX idx_users_deleted_at ON users(deleted_at);
CREATE INDEX idx_users_role_deleted ON users(role, deleted_at);

-- إضافة قيد خارجي لمعرف من قام بالحذف
ALTER TABLE users 
ADD CONSTRAINT fk_users_deleted_by 
FOREIGN KEY (deleted_by) REFERENCES users(id) 
ON DELETE SET NULL;

-- تحديث المستخدمين الحاليين المحذوفين (إن وجدوا)
-- هذا للمستخدمين الذين لديهم status = 'deleted' بالفعل
UPDATE users 
SET deleted_at = NOW(), 
    deletion_reason = 'Migrated from old deletion system'
WHERE status = 'deleted' AND deleted_at IS NULL;

-- إضافة تعليق على الجدول
ALTER TABLE users 
COMMENT = 'جدول المستخدمين مع دعم الحذف الناعم';

-- عرض النتيجة
SELECT 
    'Soft delete fields added successfully' as status,
    COUNT(*) as total_users,
    COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as active_users,
    COUNT(CASE WHEN deleted_at IS NOT NULL THEN 1 END) as deleted_users
FROM users;
