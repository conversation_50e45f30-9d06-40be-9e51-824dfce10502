import axios from 'axios';

const API_BASE_URL = "https://api.videosdk.live";
const VIDEOSDK_TOKEN = process.env.REACT_APP_VIDEOSDK_TOKEN;
const API_AUTH_URL = process.env.REACT_APP_AUTH_URL;

export const getToken = async () => {
  if (VIDEOSDK_TOKEN && API_AUTH_URL) {
    console.error(
      "Error: Provide only ONE PARAMETER - either Token or Auth API"
    );
    return null;
  } else if (VIDEOSDK_TOKEN) {
    return VIDEOSDK_TOKEN;
  } else if (API_AUTH_URL) {
    const res = await fetch(`${API_AUTH_URL}/get-token`, {
      method: "GET",
    });
    const { token } = await res.json();
    return token;
  } else {
    // Get token from our backend
    try {
      const response = await axios.post('/api/meetings/videosdk-token');
      if (response.data.success) {
        return response.data.token;
      } else {
        throw new Error(response.data.message || 'Failed to get token');
      }
    } catch (error) {
      console.error("Error getting token from backend: ", error);
      throw new Error("Failed to get VideoSDK token from server");
    }
  }
};

export const createMeeting = async ({ token }) => {
  const url = `${API_BASE_URL}/v2/rooms`;
  const options = {
    method: "POST",
    headers: { Authorization: token, "Content-Type": "application/json" },
  };

  const response = await fetch(url, options);
  const data = await response.json();

  if (data.roomId) {
    return { meetingId: data.roomId, err: null };
  } else {
    return { meetingId: null, err: data.error };
  }
};

export const validateMeeting = async ({ roomId, token }) => {
  const url = `${API_BASE_URL}/v2/rooms/validate/${roomId}`;

  const options = {
    method: "GET",
    headers: { Authorization: token, "Content-Type": "application/json" },
  };

  const response = await fetch(url, options);

  if (response.status === 400) {
    const data = await response.text();
    return { meetingId: null, err: data };
  }

  const data = await response.json();

  if (data.roomId) {
    return { meetingId: data.roomId, err: null };
  } else {
    return { meetingId: null, err: data.error };
  }
};

// Additional functions for integration with our backend
export const createMeetingWithBackend = async (meetingData) => {
  try {
    const response = await axios.post('/api/meetings', meetingData);
    return response.data;
  } catch (error) {
    console.error('Error creating meeting in backend:', error);
    throw error;
  }
};

export const joinMeetingSession = async (meetingId) => {
  try {
    const response = await axios.post(`/api/meetings/${meetingId}/join`);
    return response.data;
  } catch (error) {
    console.error('Error joining meeting session:', error);
    throw error;
  }
};

export const leaveMeetingSession = async (meetingId, sessionId) => {
  try {
    const response = await axios.post(`/api/meetings/${meetingId}/leave`, {
      sessionId
    });
    return response.data;
  } catch (error) {
    console.error('Error leaving meeting session:', error);
    throw error;
  }
};

export const sendHeartbeat = async (meetingId, sessionId) => {
  try {
    const response = await axios.post(`/api/meetings/${meetingId}/heartbeat`, {
      sessionId
    });
    return response.data;
  } catch (error) {
    console.error('Error sending heartbeat:', error);
    throw error;
  }
};
