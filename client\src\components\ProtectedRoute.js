import React, { useEffect, useState } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import UserStatusHandler from '../utils/userStatusHandler';
import {
  Box,
  CircularProgress,
  Alert,
  AlertTitle,
  Button,
  Typography,
  Stack,
  AppBar,
  Toolbar,
  IconButton,
  Menu,
  MenuItem,
  Container,
  Paper,
  alpha,
  useTheme
} from '@mui/material';
import {
  Warning as WarningIcon,
  ExitToApp as LogoutIcon,
  Cancel as CancelIcon,
  Schedule as ScheduleIcon,
  Translate as TranslateIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { format, differenceInDays, differenceInHours, differenceInMinutes, differenceInSeconds } from 'date-fns';
import { ar } from 'date-fns/locale';
import axios from '../utils/axios';

/**
 * مكون حماية الطرق - يتحقق من تسجيل الدخول وحالة المستخدم
 */
const ProtectedRoute = ({ children, allowPendingDeletion = false }) => {
  const { isAuthenticated, currentUser, handleLogout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const [statusCheck, setStatusCheck] = useState({ loading: true });
  const [cancelLoading, setCancelLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [userTimezone, setUserTimezone] = useState(null);
  const [timezoneLoading, setTimezoneLoading] = useState(true);
  const [displayTime, setDisplayTime] = useState(new Date());
  const [autoLogoutTriggered, setAutoLogoutTriggered] = useState(false);
  const [showDeletionWarning, setShowDeletionWarning] = useState(false);

  // دالة تحويل UTC offset (مثل UTC+03:00) إلى دقائق
  const parseTimezoneOffset = (timezone) => {
    if (!timezone || !timezone.includes('UTC')) return 0;

    // استخراج الإشارة والساعات والدقائق من تنسيق UTC±HH:MM
    const match = timezone.match(/UTC([+-])(\d{2}):(\d{2})/);
    if (!match) return 0;

    const sign = match[1] === '+' ? 1 : -1;
    const hours = parseInt(match[2], 10);
    const minutes = parseInt(match[3], 10);

    // إرجاع إجمالي الدقائق مع الإشارة الصحيحة
    return sign * (hours * 60 + minutes);
  };

  // دالة لعرض الوقت الحالي حسب المنطقة الزمنية للمستخدم
  const getCurrentTimeInTimezone = (timezone, time = new Date()) => {
    if (!timezone) return time.toLocaleString();

    try {
      if (timezone.includes('UTC')) {
        // البدء من UTC (GMT+0) ثم إضافة offset المنطقة الزمنية
        const offsetMinutes = parseTimezoneOffset(timezone);

        // الحصول على الوقت بـ UTC
        const utcTime = new Date(time.getTime() + (time.getTimezoneOffset() * 60 * 1000));

        // إضافة offset المنطقة الزمنية للمستخدم
        const userLocalTime = new Date(utcTime.getTime() + (offsetMinutes * 60 * 1000));

        console.log('🕐 Time calculation:', {
          originalTime: time.toISOString(),
          browserOffset: time.getTimezoneOffset(),
          utcTime: utcTime.toISOString(),
          userTimezone: timezone,
          userOffset: offsetMinutes,
          userLocalTime: userLocalTime.toISOString()
        });

        return userLocalTime.toLocaleString(i18n.language === 'ar' ? 'ar-EG' : 'en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });
      } else {
        // استخدام timezone name مباشرة
        return time.toLocaleString(i18n.language === 'ar' ? 'ar-EG' : 'en-US', {
          timeZone: timezone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });
      }
    } catch (error) {
      console.error('Error formatting time for timezone:', timezone, error);
      return time.toLocaleString();
    }
  };

  // دالة لحساب الوقت المتبقي للحذف مع المنطقة الزمنية
  const getTimeRemaining = (deleteScheduledAt) => {
    if (!deleteScheduledAt) return null;

    // حساب الفرق بالثواني مباشرة بين التواريخ
    // deleteScheduledAt يأتي من الخادم كـ UTC
    // currentTime هو الوقت الحالي المحلي

    const deleteDate = new Date(deleteScheduledAt);
    const now = new Date();

    // حساب الفرق بالثواني
    const totalSeconds = Math.floor((deleteDate.getTime() - now.getTime()) / 1000);

    console.log('⏰ Time remaining calculation:', {
      deleteScheduledAt,
      deleteDate: deleteDate.toISOString(),
      now: now.toISOString(),
      diffMs: deleteDate.getTime() - now.getTime(),
      totalSeconds,
      userTimezone
    });

    if (totalSeconds <= 0) {
      // الوقت انتهى - تسجيل خروج تلقائي
      if (!autoLogoutTriggered) {
        console.log('🚨 Deletion time reached - triggering auto logout');
        setAutoLogoutTriggered(true);
        setShowDeletionWarning(true);

        // حفظ رسالة للعرض في صفحة تسجيل الدخول
        localStorage.setItem('accountStatusMessage', JSON.stringify({
          message: 'تم حذف هذا الحساب تلقائياً',
          message_en: 'Account has been automatically deleted',
          type: 'deleted'
        }));

        // تسجيل خروج فوري
        setTimeout(() => {
          handleLogout();
        }, 3000);
      }

      return '🚨 ' + t('pendingDeletion.accountDeleted');
    }

    // تحذير عندما يبقى أقل من 60 ثانية
    if (totalSeconds <= 60 && !showDeletionWarning) {
      setShowDeletionWarning(true);
    }

    return formatTimeRemaining(totalSeconds);
  };

  // دالة مساعدة لتنسيق التاريخ حسب المنطقة الزمنية للمستخدم
  const formatDateInUserTimezone = (dateString, formatString = 'dd/MM/yyyy HH:mm') => {
    let displayDate = new Date(dateString);

    // تطبيق المنطقة الزمنية للمستخدم إذا كانت متوفرة
    if (userTimezone && userTimezone.includes('UTC')) {
      const offsetMinutes = parseTimezoneOffset(userTimezone);

      // البدء من UTC ثم إضافة offset المنطقة الزمنية
      const utcDate = new Date(displayDate.getTime() + (displayDate.getTimezoneOffset() * 60 * 1000));
      displayDate = new Date(utcDate.getTime() + (offsetMinutes * 60 * 1000));

      console.log('📅 Date formatting:', {
        originalDate: dateString,
        parsedDate: new Date(dateString).toISOString(),
        browserOffset: new Date(dateString).getTimezoneOffset(),
        utcDate: utcDate.toISOString(),
        userTimezone: userTimezone,
        userOffset: offsetMinutes,
        finalDisplayDate: displayDate.toISOString(),
        formattedResult: format(displayDate, formatString, { locale: i18n.language === 'ar' ? ar : undefined })
      });
    }

    return format(displayDate, formatString, {
      locale: i18n.language === 'ar' ? ar : undefined
    });
  };

  // دالة مساعدة لتنسيق الوقت المتبقي
  const formatTimeRemaining = (totalSeconds) => {
    const days = Math.floor(totalSeconds / (24 * 60 * 60));
    const hours = Math.floor((totalSeconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((totalSeconds % (60 * 60)) / 60);
    const seconds = totalSeconds % 60;

    if (days > 0) {
      const dayText = days === 1 ? t('pendingDeletion.day') : t('pendingDeletion.days');
      const hourText = hours === 1 ? t('pendingDeletion.hour') : t('pendingDeletion.hours');
      const minuteText = minutes === 1 ? t('pendingDeletion.minute') : t('pendingDeletion.minutes');
      const secondText = seconds === 1 ? t('pendingDeletion.second') : t('pendingDeletion.seconds');
      return `${days} ${dayText}, ${hours} ${hourText}, ${minutes} ${minuteText}, ${seconds} ${secondText}`;
    } else if (hours > 0) {
      const hourText = hours === 1 ? t('pendingDeletion.hour') : t('pendingDeletion.hours');
      const minuteText = minutes === 1 ? t('pendingDeletion.minute') : t('pendingDeletion.minutes');
      const secondText = seconds === 1 ? t('pendingDeletion.second') : t('pendingDeletion.seconds');
      return `${hours} ${hourText}, ${minutes} ${minuteText}, ${seconds} ${secondText}`;
    } else if (minutes > 0) {
      const minuteText = minutes === 1 ? t('pendingDeletion.minute') : t('pendingDeletion.minutes');
      const secondText = seconds === 1 ? t('pendingDeletion.second') : t('pendingDeletion.seconds');
      return `${minutes} ${minuteText}, ${seconds} ${secondText}`;
    } else {
      const secondText = seconds === 1 ? t('pendingDeletion.second') : t('pendingDeletion.seconds');
      return `${seconds} ${secondText}`;
    }
  };

  // دالة إلغاء الحذف
  const handleCancelDeletion = async () => {
    try {
      setCancelLoading(true);
      await axios.post('/users/cancel-delete');

      // إعادة تحميل بيانات المستخدم
      window.location.reload();
    } catch (error) {
      console.error('Error cancelling deletion:', error);
      alert(t('pendingDeletion.cancelError'));
    } finally {
      setCancelLoading(false);
    }
  };

  // دالة تغيير اللغة
  const handleLanguageChange = (language) => {
    i18n.changeLanguage(language);
    localStorage.setItem('language', language);
  };

  useEffect(() => {
    const checkUserStatus = async () => {
      // إذا لم يكن مسجل دخول، لا نحتاج للتحقق من الحالة
      if (!isAuthenticated) {
        setStatusCheck({ loading: false, valid: false, reason: 'not_authenticated' });
        return;
      }

      // انتظار تحميل بيانات المستخدم
      if (!currentUser) {
        setStatusCheck({ loading: true, valid: false });
        return;
      }

      // التحقق من حالة المستخدم للمستخدمين المسجلين فقط
      const result = await UserStatusHandler.checkUserStatus();
      
      if (!result.valid) {
        if (result.reason === 'deleted' || result.reason === 'unauthorized') {
          // حفظ رسالة الحالة في localStorage
          localStorage.setItem('accountStatusMessage', JSON.stringify({
            message: result.message || 'تم حذف هذا الحساب',
            message_en: result.message_en || 'Account has been deleted',
            accountStatus: result.reason,
            deleteScheduledAt: result.deleteScheduledAt
          }));

          // حساب محذوف أو توكن منتهي الصلاحية - تسجيل خروج فوري
          handleLogout();
          setStatusCheck({
            loading: false,
            valid: false,
            reason: result.reason,
            message: result.message || 'تم حذف هذا الحساب'
          });
          return;
        }

        if (result.reason === 'error') {
          // خطأ في الشبكة أو الخادم
          setStatusCheck({
            loading: false,
            valid: false,
            reason: 'error',
            message: result.message
          });
          return;
        }
      }

      // التعامل مع المستخدمين المجدولين للحذف
      if (result.reason === 'pending_deletion' && !allowPendingDeletion) {
        // حساب مجدول للحذف ولا يُسمح بالوصول
        const deleteScheduledAt = result.deleteScheduledAt || currentUser?.delete_scheduled_at;

        // إذا كان المستخدم يحاول الوصول لصفحة البروفايل، لا نعرض الصفحة المخصصة
        // بل نتركه يتم توجيهه تلقائياً للصفحة المخصصة عبر عدم السماح بالوصول
        console.log('🚫 Pending deletion user trying to access restricted page:', location.pathname);

        setStatusCheck({
          loading: false,
          valid: false,
          reason: 'pending_deletion',
          message: t('pendingDeletion.message'),
          user: result.user || currentUser,
          deleteScheduledAt: deleteScheduledAt
        });
        return;
      }

      setStatusCheck({ loading: false, valid: true });
    };

    checkUserStatus();
  }, [isAuthenticated, currentUser, allowPendingDeletion, handleLogout, t, i18n.language]);

  // جلب المنطقة الزمنية عند تحميل المكون
  useEffect(() => {
    console.log('Timezone useEffect triggered:', {
      isAuthenticated,
      currentUser,
      hasTimezoneProperty: currentUser && 'timezone' in currentUser,
      timezone: currentUser?.timezone,
      userTimezone
    });

    if (isAuthenticated && currentUser) {
      // تحقق من وجود خاصية timezone في currentUser
      if ('timezone' in currentUser) {
        console.log('Setting timezone from currentUser:', currentUser.timezone);
        setUserTimezone(currentUser.timezone || null);
        setTimezoneLoading(false);
      } else {
        console.log('Timezone property not found in currentUser, setting to null');
        setUserTimezone(null);
        setTimezoneLoading(false);
      }
    } else if (isAuthenticated) {
      // إذا كان مصادق عليه ولكن currentUser لم يتم تحميله بعد، انتظر
      console.log('Waiting for currentUser to load...');
      setTimezoneLoading(true);
    }
  }, [isAuthenticated, currentUser]);

  // useEffect إضافي للتأكد من تحديث المنطقة الزمنية عند تغيير currentUser.timezone
  useEffect(() => {
    if (currentUser && 'timezone' in currentUser && currentUser.timezone !== userTimezone) {
      console.log('Updating timezone from currentUser.timezone change:', currentUser.timezone);
      setUserTimezone(currentUser.timezone || null);
      setTimezoneLoading(false);
    }
  }, [currentUser?.timezone, userTimezone]);

  // عداد الثواني للوقت المتبقي والوقت المعروض
  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date();
      setCurrentTime(now);
      setDisplayTime(now);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // غير مسجل دخول - إعادة توجيه فورية بدون loading
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // جاري التحميل للمستخدمين المسجلين فقط
  if (statusCheck.loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        flexDirection="column"
        gap={2}
      >
        <CircularProgress size={40} />
        <div>جاري التحقق من حالة الحساب...</div>
      </Box>
    );
  }

  // حساب محذوف - إعادة توجيه لصفحة تسجيل الدخول
  if (statusCheck.reason === 'deleted' || statusCheck.reason === 'unauthorized') {
    return <Navigate to="/login" replace />;
  }

  // حساب مجدول للحذف ولا يُسمح بالوصول
  if (statusCheck.reason === 'pending_deletion' && !allowPendingDeletion) {
    const deleteScheduledAt = statusCheck.deleteScheduledAt || currentUser?.delete_scheduled_at;
    const timeRemaining = getTimeRemaining(deleteScheduledAt);

    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          backgroundColor: '#f5f5f5',
          '& @keyframes blink': {
            '0%, 50%': { opacity: 1 },
            '51%, 100%': { opacity: 0.6 }
          },
          '& @keyframes pulse': {
            '0%': {
              transform: 'scale(1)',
              boxShadow: '0 4px 16px rgba(244,67,54,0.15)'
            },
            '50%': {
              transform: 'scale(1.005)',
              boxShadow: '0 6px 20px rgba(244,67,54,0.25)'
            },
            '100%': {
              transform: 'scale(1)',
              boxShadow: '0 4px 16px rgba(244,67,54,0.15)'
            }
          }
        }}
      >
        {/* Simple Header for Pending Deletion Page - Same design as main header */}
        <AppBar
          position="fixed"
          elevation={0}
          sx={{
            background: `linear-gradient(to right, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
            borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}`
          }}
        >
          <Toolbar
            sx={{
              minHeight: { xs: 56, sm: 64, md: 70 },
              justifyContent: 'space-between',
              px: { xs: 1, sm: 2, md: 3 }
            }}
          >
            {/* Platform Name */}
            <Typography
              variant="h6"
              component="div"
              sx={{
                fontFamily: 'Tajawal, sans-serif',
                fontSize: { xs: '0.9rem', sm: '1.1rem', md: '1.25rem', lg: '1.4rem' },
                fontWeight: { xs: 500, md: 600 },
                textAlign: 'left'
              }}
            >
              {t('appName')}
            </Typography>

            {/* Language Toggle */}
            <Button
              color="inherit"
              onClick={() => i18n.changeLanguage(i18n.language === 'ar' ? 'en' : 'ar')}
              sx={{
                fontFamily: 'Tajawal, sans-serif',
                fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
                px: { xs: 1, sm: 1.5, md: 2 },
                py: { xs: 0.5, sm: 0.75, md: 1 },
                borderRadius: 1,
                '&:hover': {
                  backgroundColor: alpha(theme.palette.common.white, 0.1)
                }
              }}
            >
              {i18n.language === 'ar' ? 'English' : 'العربية'}
            </Button>
          </Toolbar>
        </AppBar>

        {/* Main Content */}
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
            p: 2,
            minHeight: '100vh', // Full viewport height
            pt: { xs: '64px', sm: '72px', md: '78px' }, // Add padding top for fixed header
            direction: i18n.language === 'ar' ? 'rtl' : 'ltr'
          }}
        >
          <Container maxWidth="lg" sx={{ width: '100%', px: { xs: 2, sm: 3, md: 4 } }}>
            {/* User Info Card */}
            <Paper
              elevation={3}
              sx={{
                p: { xs: 3, sm: 4, md: 5 },
                mb: 3,
                borderRadius: 3,
                backgroundColor: 'rgba(255,255,255,0.95)',
                backdropFilter: 'blur(15px)',
                width: '100%',
                maxWidth: '100%'
              }}
            >
              <Typography
                variant="h5"
                sx={{
                  mb: 3,
                  fontWeight: 'bold',
                  textAlign: i18n.language === 'ar' ? 'right' : 'left',
                  fontSize: { xs: '1.3rem', sm: '1.5rem', md: '1.7rem' },
                  color: 'primary.main'
                }}
              >
                {t('pendingDeletion.userInfo')}
              </Typography>
              <Stack spacing={1.5}>
                <Typography variant="body1" sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' }, fontWeight: 500 }}>
                  <strong>{t('pendingDeletion.userName')}:</strong> {currentUser?.full_name || 'N/A'}
                </Typography>
                <Typography variant="body1" sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' }, fontWeight: 500 }}>
                  <strong>{t('pendingDeletion.userEmail')}:</strong> {currentUser?.email || 'N/A'}
                </Typography>
                <Typography variant="body1" sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' }, fontWeight: 500 }}>
                  <strong>{t('pendingDeletion.userTimezone')}:</strong> {
                    timezoneLoading ?
                      t('common.loading') :
                      userTimezone ?
                        `${userTimezone}` :
                        t('pendingDeletion.noTimezone')
                  }
                </Typography>
                <Typography variant="body1" sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' }, fontWeight: 500 }}>
                  <strong>{t('pendingDeletion.currentTime')}:</strong> {
                    userTimezone ?
                      getCurrentTimeInTimezone(userTimezone, displayTime) :
                      displayTime.toLocaleString(i18n.language === 'ar' ? 'ar-EG' : 'en-US')
                  }
                </Typography>
              </Stack>
            </Paper>

            {/* Critical Warning Alert - Shown when less than 60 seconds */}
            {showDeletionWarning && (
              <Alert
                severity="error"
                icon={<WarningIcon />}
                sx={{
                  width: '100%',
                  borderRadius: 2,
                  boxShadow: '0 4px 16px rgba(244,67,54,0.2)',
                  border: '1px solid rgba(244,67,54,0.3)',
                  textAlign: i18n.language === 'ar' ? 'right' : 'left',
                  mb: 2,
                  animation: 'pulse 1.5s infinite',
                  '& .MuiAlert-icon': {
                    fontSize: '1.5rem'
                  }
                }}
              >
                <AlertTitle sx={{ display: 'flex', alignItems: 'center', gap: 1, fontSize: '1rem' }}>
                  🚨 {t('pendingDeletion.urgentWarning', 'URGENT WARNING!')}
                </AlertTitle>
                <Typography variant="body2" sx={{ fontWeight: 'bold', fontSize: '0.9rem' }}>
                  {t('pendingDeletion.finalWarning')}
                </Typography>
              </Alert>
            )}

            {/* Main Warning Alert */}
            <Alert
              severity={showDeletionWarning ? "error" : "warning"}
              icon={<ScheduleIcon />}
              sx={{
                width: '100%',
                borderRadius: 3,
                boxShadow: showDeletionWarning ? '0 6px 20px rgba(244,67,54,0.2)' : '0 6px 20px rgba(0,0,0,0.15)',
                border: showDeletionWarning ? '2px solid rgba(244,67,54,0.3)' : '2px solid rgba(255,193,7,0.3)',
                textAlign: i18n.language === 'ar' ? 'right' : 'left',
                p: { xs: 2, sm: 3, md: 4 },
                '& .MuiAlert-icon': {
                  fontSize: { xs: '1.8rem', sm: '2rem', md: '2.2rem' }
                }
              }}
            >
              <AlertTitle sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                fontSize: { xs: '1.3rem', sm: '1.5rem', md: '1.7rem' },
                fontWeight: 'bold'
              }}>
                <ScheduleIcon />
                {t('pendingDeletion.title')}
              </AlertTitle>

              <Typography variant="body1" sx={{
                mb: 2,
                fontSize: { xs: '1.1rem', sm: '1.2rem', md: '1.3rem' },
                fontWeight: 500
              }}>
                {statusCheck.message}
              </Typography>

              {timeRemaining && (
                <Typography
                  variant="h6"
                  sx={{
                    mb: 2,
                    color: showDeletionWarning ? 'error.main' : 'warning.main',
                    fontWeight: 'bold',
                    fontSize: {
                      xs: showDeletionWarning ? '1.3rem' : '1.2rem',
                      sm: showDeletionWarning ? '1.5rem' : '1.3rem',
                      md: showDeletionWarning ? '1.7rem' : '1.5rem'
                    },
                    animation: showDeletionWarning ? 'blink 1s infinite' : 'none',
                    textAlign: 'center',
                    p: { xs: 2, sm: 2.5, md: 3 },
                    borderRadius: 2,
                    backgroundColor: showDeletionWarning ? 'rgba(244,67,54,0.1)' : 'rgba(255,193,7,0.1)',
                    border: showDeletionWarning ? '1px solid rgba(244,67,54,0.3)' : '1px solid rgba(255,193,7,0.3)'
                  }}
                >
                  ⏰ {t('pendingDeletion.timeRemaining')}: {timeRemaining}
                </Typography>
              )}

              {deleteScheduledAt && (
                <Typography variant="body2" sx={{
                  mb: 3,
                  display: 'block',
                  color: 'text.secondary',
                  fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
                  fontWeight: 500
                }}>
                  📅 {t('pendingDeletion.scheduledDate')}: {formatDateInUserTimezone(deleteScheduledAt)}
                </Typography>
              )}

          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            sx={{ mt: 3 }}
            justifyContent="center"
          >
            <Button
              variant="contained"
              color="success"
              startIcon={<CancelIcon />}
              onClick={handleCancelDeletion}
              disabled={cancelLoading}
              size="large"
              sx={{
                borderRadius: 2,
                px: { xs: 4, sm: 5, md: 6 },
                py: { xs: 1.5, sm: 2, md: 2.5 },
                fontSize: { xs: '1.1rem', sm: '1.2rem', md: '1.3rem' },
                fontWeight: 'bold',
                boxShadow: '0 4px 12px rgba(76, 175, 80, 0.3)',
                minWidth: { xs: '200px', sm: '220px', md: '250px' },
                '&:hover': {
                  boxShadow: '0 6px 16px rgba(76, 175, 80, 0.4)',
                  transform: 'translateY(-2px)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              {cancelLoading ? t('pendingDeletion.cancelling') : t('pendingDeletion.cancelDeletion')}
            </Button>

            <Button
              variant="outlined"
              color="error"
              startIcon={<LogoutIcon />}
              onClick={handleLogout}
              size="large"
              sx={{
                borderRadius: 2,
                px: { xs: 4, sm: 5, md: 6 },
                py: { xs: 1.5, sm: 2, md: 2.5 },
                fontSize: { xs: '1.1rem', sm: '1.2rem', md: '1.3rem' },
                fontWeight: 'bold',
                borderWidth: 2,
                minWidth: { xs: '200px', sm: '220px', md: '250px' },
                '&:hover': {
                  borderWidth: 2,
                  backgroundColor: 'rgba(244, 67, 54, 0.08)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(244, 67, 54, 0.2)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              {t('pendingDeletion.logout')}
            </Button>
          </Stack>
            </Alert>
          </Container>
        </Box>

        {/* Footer - Fixed at bottom */}
        <Box
          component="footer"
          sx={{
            bgcolor: 'primary.dark',
            color: 'white',
            py: { xs: 1.5, sm: 2 },
            direction: i18n.language === 'ar' ? 'rtl' : 'ltr',
            borderTop: '1px solid rgba(255,255,255,0.1)',
            width: '100%',
            mt: 'auto'
          }}
        >
          <Typography
            variant="body2"
            align="center"
            sx={{
              fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
              fontFamily: 'Tajawal, sans-serif',
              fontWeight: 600,
              margin: 0
            }}
          >
            {t('footer.copyright')}
          </Typography>
        </Box>

      </Box>
    );
  }

  // كل شيء على ما يرام
  return children;
};

export default ProtectedRoute;
