/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

/** WithRequired type helpers */
type WithRequired<T, K extends keyof T> = T & { [P in K]-?: T[P] };

export interface paths {
    "/v1/billing/plans": {
        /**
         * List plans
         * @description Lists billing plans.
         */
        get: operations["plans.list"];
        /**
         * Create plan
         * @description Creates a plan that defines pricing and billing cycle details for subscriptions.
         */
        post: operations["plans.create"];
    };
    "/v1/billing/plans/{id}": {
        /**
         * Show plan details
         * @description Shows details for a plan, by ID.
         */
        get: operations["plans.get"];
        /**
         * Update plan
         * @description Updates a plan with the `CREATED` or `ACTIVE` status. For an `INACTIVE` plan, you can make only status updates.<br/>You can patch these attributes and objects:<table><thead><tr><th>Attribute or object</th><th>Operations</th></tr></thead><tbody><tr><td><code>description</code></td><td>replace</td></tr><tr><td><code>payment_preferences.auto_bill_outstanding</code></td><td>replace</td></tr><tr><td><code>taxes.percentage</code></td><td>replace</td></tr><tr><td><code>payment_preferences.payment_failure_threshold</code></td><td>replace</td></tr><tr><td><code>payment_preferences.setup_fee</code></td><td>replace</td></tr><tr><td><code>payment_preferences.setup_fee_failure_action</code></td><td>replace</td></tr><tr><td><code>name</code></td><td>replace</td></tr></tbody></table>
         */
        patch: operations["plans.patch"];
    };
    "/v1/billing/plans/{id}/activate": {
        /**
         * Activate plan
         * @description Activates a plan, by ID.
         */
        post: operations["plans.activate"];
    };
    "/v1/billing/plans/{id}/deactivate": {
        /**
         * Deactivate plan
         * @description Deactivates a plan, by ID.
         */
        post: operations["plans.deactivate"];
    };
    "/v1/billing/plans/{id}/update-pricing-schemes": {
        /**
         * Update pricing
         * @description Updates pricing for a plan. For example, you can update a regular billing cycle from $5 per month to $7 per month.
         */
        post: operations["plans.update-pricing-schemes"];
    };
    "/v1/billing/subscriptions": {
        /**
         * Create subscription
         * @description Creates a subscription.
         */
        post: operations["subscriptions.create"];
    };
    "/v1/billing/subscriptions/{id}": {
        /**
         * Show subscription details
         * @description Shows details for a subscription, by ID.
         */
        get: operations["subscriptions.get"];
        /**
         * Update subscription
         * @description Updates a subscription which could be in <code>ACTIVE</code> or <code>SUSPENDED</code> status. You can override plan level default attributes by providing customised values for plan path in the patch request.<br /> <ul> <li>You cannot update attributes that have already completed (Example - trial cycles can’t be updated if completed).</li> <li>Once overridden, changes to plan resource will not impact subscription.</li> <li>Any price update will not impact billing cycles within next 10 days (Applicable only for subscriptions funded by PayPal account).</li> </ul> Following are the fields eligible for patch.<table><thead><tr><th>Attribute or object</th><th>Operations</th></tr></thead><tbody><tr><td><code>billing_info.outstanding_balance</code></td><td>replace</td></tr><tr><td><code>custom_id</code></td><td>add,replace</td></tr><tr><td><code>plan.billing_cycles[@sequence==n].<br/>pricing_scheme.fixed_price</code></td><td>add,replace</td></tr><tr><td><code>plan.billing_cycles[@sequence==n].<br/>pricing_scheme.tiers</code></td><td>replace</td></tr><tr><td><code>plan.billing_cycles[@sequence==n].<br/>total_cycles</code></td><td>replace</td></tr><tr><td><code>plan.payment_preferences.<br/>auto_bill_outstanding</code></td><td>replace</td></tr><tr><td><code>plan.payment_preferences.<br/>payment_failure_threshold</code></td><td>replace</td></tr><tr><td><code>plan.taxes.inclusive</code></td><td>add,replace</td></tr><tr><td><code>plan.taxes.percentage</code></td><td>add,replace</td></tr><tr><td><code>shipping_amount</code></td><td>add,replace</td></tr><tr><td><code>start_time</code></td><td>replace</td></tr><tr><td><code>subscriber.shipping_address</code></td><td>add,replace</td></tr><tr><td><code>subscriber.payment_source (for subscriptions funded<br/>by card payments)</code></td><td>replace</td></tr></tbody></table>
         */
        patch: operations["subscriptions.patch"];
    };
    "/v1/billing/subscriptions/{id}/revise": {
        /**
         * Revise plan or quantity of subscription
         * @description Updates the quantity of the product or service in a subscription. You can also use this method to switch the plan and update the `shipping_amount`, `shipping_address` values for the subscription. This type of update requires the buyer's consent.
         */
        post: operations["subscriptions.revise"];
    };
    "/v1/billing/subscriptions/{id}/suspend": {
        /**
         * Suspend subscription
         * @description Suspends the subscription.
         */
        post: operations["subscriptions.suspend"];
    };
    "/v1/billing/subscriptions/{id}/cancel": {
        /**
         * Cancel subscription
         * @description Cancels the subscription.
         */
        post: operations["subscriptions.cancel"];
    };
    "/v1/billing/subscriptions/{id}/activate": {
        /**
         * Activate subscription
         * @description Activates the subscription.
         */
        post: operations["subscriptions.activate"];
    };
    "/v1/billing/subscriptions/{id}/capture": {
        /**
         * Capture authorized payment on subscription
         * @description Captures an authorized payment from the subscriber on the subscription.
         */
        post: operations["subscriptions.capture"];
    };
    "/v1/billing/subscriptions/{id}/transactions": {
        /**
         * List transactions for subscription
         * @description Lists transactions for a subscription.
         */
        get: operations["subscriptions.transactions"];
    };
}

export type webhooks = Record<string, never>;

export interface components {
    schemas: {
        400: {
            issues?: {
                /** @enum {string} */
                issue?: "INVALID_PARAMETER_VALUE";
                /** @enum {string} */
                description?: "The value of a field is invalid.";
            }[];
        };
        401: {
            issues?: {
                /** @enum {string} */
                issue?: "INVALID_ACCOUNT_STATUS";
                /** @enum {string} */
                description?: "Account validations failed for the user.";
            }[];
        };
        403: {
            issues?: {
                /** @enum {string} */
                issue?: "PERMISSION_DENIED";
                /** @enum {string} */
                description?: "You do not have permission to access or perform operations on this resource.";
            }[];
        };
        404: {
            issues?: {
                /** @enum {string} */
                issue?: "INVALID_RESOURCE_ID";
                /** @enum {string} */
                description?: "Specified resource ID does not exist. Please check the resource ID and try again.";
            }[];
        };
        422: {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_MISMATCH";
                      /** @enum {string} */
                      description?: "All currency codes in the request should be of similar value.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MULTIPLE_FREE_TRIAL_BILLING_CYCLES_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Only one free trial billing cycle is allowed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MORE_THAN_TWO_TRIAL_BILLING_CYCLE_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Only two trial billing cycles are allowed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REGULAR_BILLING_CYCLE";
                      /** @enum {string} */
                      description?: "Plan should have at least one regular billing cycle.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MULTIPLE_REGULAR_BILLING_CYCLES_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Only one regular billing cycle is allowed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_BILLING_CYCLE_SEQUENCE";
                      /** @enum {string} */
                      description?: "Billing cycle sequence should start with `1` and be consecutive.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_BILLING_CYCLE_SEQUENCE";
                      /** @enum {string} */
                      description?: "Trial Billing cycle should precede regular billing cycle.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_TRIAL_BILLING_TOTAL_CYCLES";
                      /** @enum {string} */
                      description?: "Total cycles for trial billing must be greater than '0'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_AMOUNT";
                      /** @enum {string} */
                      description?: "Free tiers are not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "Tier(s) are missing for some quantities.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "OVERLAPPING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "The specified quantity overlaps with multiple pricing tiers.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_MODEL";
                      /** @enum {string} */
                      description?: "The specified pricing model is not supported for trial billing cycle.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "FIXED_PRICE_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Fixed price is not supported for tiered pricing schemes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_QUANTITY";
                      /** @enum {string} */
                      description?: "Tier starting quantity must be less than ending quantity.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_QUANTITY_SUPPORTED";
                      /** @enum {string} */
                      description?: "Quantity is always supported for volume and tiered plans.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_NOT_SUPPORTED_FOR_RECEIVER";
                      /** @enum {string} */
                      description?: "This currency cannot be accepted for this recipient’s account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_METADATA_CUSTOM_NOTE";
                      /** @enum {string} */
                      description?: "Merchant custom note cannot exceed 255 characters.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_METADATA_INVOICE_ID";
                      /** @enum {string} */
                      description?: "Invoice id cannot exceed 127 characters.";
                  }
            )[];
        };
        /**
         * Error Details
         * @description The error details. Required for client-side `4XX` errors.
         */
        error_details: {
            /** @description The field that caused the error. If this field is in the body, set this value to the field's JSON pointer value. Required for client-side errors. */
            field?: string;
            /** @description The value of the field that caused the error. */
            value?: string;
            /**
             * @description The location of the field that caused the error. Value is `body`, `path`, or `query`.
             * @default body
             */
            location?: string;
            /** @description The unique, fine-grained application-level error code. */
            issue: string;
            /** @description The human-readable description for an issue. The description can change over the lifetime of an API, so clients must not depend on this value. */
            description?: string;
        };
        /** @description The default error response. */
        error_default:
            | components["schemas"]["error_400"]
            | components["schemas"]["error_401"]
            | components["schemas"]["error_403"]
            | components["schemas"]["error_404"]
            | components["schemas"]["error_409"]
            | components["schemas"]["error_415"]
            | components["schemas"]["error_422"]
            | components["schemas"]["error_500"]
            | components["schemas"]["error_503"];
        /**
         * 400 Error
         * @description Error response for 400
         */
        error_400: {
            /** @enum {string} */
            name?: "INVALID_REQUEST";
            /** @enum {string} */
            message?: "Request is not well-formed, syntactically incorrect, or violates schema.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 401 Error
         * @description Error response for 401
         */
        error_401: {
            /** @enum {string} */
            name?: "AUTHENTICATION_FAILURE";
            /** @enum {string} */
            message?: "Authentication failed due to missing authorization header, or invalid authentication credentials.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 403 Error
         * @description Error response for 403
         */
        error_403: {
            /** @enum {string} */
            name?: "NOT_AUTHORIZED";
            /** @enum {string} */
            message?: "Authorization failed due to insufficient permissions.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 404 Error
         * @description Error response for 404
         */
        error_404: {
            /** @enum {string} */
            name?: "RESOURCE_NOT_FOUND";
            /** @enum {string} */
            message?: "The specified resource does not exist.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 409 Error
         * @description Error response for 409
         */
        error_409: {
            /** @enum {string} */
            name?: "RESOURCE_CONFLICT";
            /** @enum {string} */
            message?: "The server has detected a conflict while processing this request.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 409 Error
         * @description Error response for 415
         */
        error_415: {
            /** @enum {string} */
            name?: "UNSUPPORTED_MEDIA_TYPE";
            /** @enum {string} */
            message?: "The server does not support the request payload's media type.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 422 Error
         * @description Error response for 422
         */
        error_422: {
            /** @enum {string} */
            name?: "UNPROCESSABLE_ENTITY";
            /** @enum {string} */
            message?: "The requested action could not be performed, semantically incorrect, or failed business validation.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 500 Error
         * @description Error response for 500
         * @example {
         *   "name": "INTERNAL_SERVER_ERROR",
         *   "message": "An internal server error occurred.",
         *   "debug_id": "90957fca61718",
         *   "information_link": "https://developer.paypal.com/api/orders/v2/#error-INTERNAL_SERVER_ERROR"
         * }
         */
        error_500: {
            /** @enum {string} */
            name?: "INTERNAL_SERVER_ERROR";
            /** @enum {string} */
            message?: "An internal server error occurred.";
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /**
             * @description The information link, or URI, that shows detailed information about this error for the developer.
             * @enum {string}
             */
            information_link?: "https://developer.paypal.com/api/orders/v2/#error-INTERNAL_SERVER_ERROR";
        };
        /**
         * 503 Error
         * @description Error response for 503
         * @example {
         *   "name": "SERVICE_UNAVAILABLE",
         *   "message": "Service Unavailable.",
         *   "debug_id": "90957fca61718",
         *   "information_link": "https://developer.paypal.com/docs/api/orders/v2/#error-SERVICE_UNAVAILABLE"
         * }
         */
        error_503: {
            /** @enum {string} */
            name?: "SERVICE_UNAVAILABLE";
            /** @enum {string} */
            message?: "Service Unavailable.";
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * Format: ppaas_common_currency_code_v2
         * @description The [three-character ISO-4217 currency code](/docs/integration/direct/rest/currency-codes/) that identifies the currency.
         */
        currency_code: string;
        /**
         * Money
         * @description The currency and amount for a financial transaction, such as a balance or payment due.
         */
        money: {
            currency_code: components["schemas"]["currency_code"];
            /** @description The value, which might be:<ul><li>An integer for currencies like `JPY` that are not typically fractional.</li><li>A decimal fraction for currencies like `TND` that are subdivided into thousandths.</li></ul>For the required number of decimal places for a currency code, see [Currency Codes](/docs/integration/direct/rest/currency-codes/). */
            value: string;
        };
        /**
         * Pricing Tier
         * @description The pricing tier details.
         */
        pricing_tier: {
            /** @description The starting quantity for the tier. */
            starting_quantity: string;
            /** @description The ending quantity for the tier. Optional for the last tier. */
            ending_quantity?: string;
            /** @description The pricing amount for the tier. */
            amount: components["schemas"]["money"];
        };
        /**
         * Format: ppaas_date_time_v3
         * @description The date and time, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). Seconds are required while fractional seconds are optional.<blockquote><strong>Note:</strong> The regular expression provides guidance but does not reject all invalid dates.</blockquote>
         */
        date_time: string;
        /**
         * Pricing Scheme
         * @description The pricing scheme details.
         */
        pricing_scheme: {
            /** @description The version of the pricing scheme. */
            version?: number;
            /** @description The fixed amount to charge for the subscription. The changes to fixed amount are applicable to both existing and future subscriptions. For existing subscriptions, payments within 10 days of price change are not affected. */
            fixed_price?: components["schemas"]["money"];
            /**
             * @description The pricing model for tiered plan. The `tiers` parameter is required.
             * @enum {string}
             */
            pricing_model?: "VOLUME" | "TIERED";
            /** @description An array of pricing tiers which are used for billing volume/tiered plans. pricing_model field has to be specified. */
            tiers?: components["schemas"]["pricing_tier"][];
            /** @description The date and time when this pricing scheme was created, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            create_time?: components["schemas"]["date_time"];
            /** @description The date and time when this pricing scheme was last updated, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            update_time?: components["schemas"]["date_time"];
        };
        /**
         * Billing Cycle Frequency
         * @description The frequency of the billing cycle.
         */
        frequency: {
            /**
             * @description The interval at which the subscription is charged or billed.
             * @enum {string}
             */
            interval_unit: "DAY" | "WEEK" | "MONTH" | "YEAR";
            /**
             * @description The number of intervals after which a subscriber is billed. For example, if the `interval_unit` is `DAY` with an `interval_count` of  `2`, the subscription is billed once every two days. The following table lists the maximum allowed values for the `interval_count` for each `interval_unit`:<table><thead><tr><th><code>Interval unit</code></th><th>Maximum interval count</th></tr></thead><tbody><tr><td><code>DAY</code></td><td align="right">365</td></tr><tr><td><code>WEEK</code></td><td align="right">52</td></tr><tr><td><code>MONTH</code></td><td align="right">12</td></tr><tr><td><code>YEAR</code></td><td align="right">1</td></tr></tbody></table>
             * @default 1
             */
            interval_count?: number;
        };
        /**
         * Billing Cycle
         * @description The billing cycle details.
         */
        billing_cycle: {
            /** @description The active pricing scheme for this billing cycle. A free trial billing cycle does not require a pricing scheme. */
            pricing_scheme?: components["schemas"]["pricing_scheme"];
            /** @description The frequency details for this billing cycle. */
            frequency: components["schemas"]["frequency"];
            /**
             * @description The tenure type of the billing cycle. In case of a plan having trial cycle, only 2 trial cycles are allowed per plan.
             * @enum {string}
             */
            tenure_type: "REGULAR" | "TRIAL";
            /** @description The order in which this cycle is to run among other billing cycles. For example, a trial billing cycle has a `sequence` of `1` while a regular billing cycle has a `sequence` of `2`, so that trial cycle runs before the regular cycle. */
            sequence: number;
            /**
             * @description The number of times this billing cycle gets executed. Trial billing cycles can only be executed a finite number of times (value between <code>1</code> and <code>999</code> for <code>total_cycles</code>). Regular billing cycles can be executed infinite times (value of <code>0</code> for <code>total_cycles</code>) or a finite number of times (value between <code>1</code> and <code>999</code> for <code>total_cycles</code>).
             * @default 1
             */
            total_cycles?: number;
        };
        /**
         * Payment Preferences
         * @description The payment preferences for a subscription.
         */
        payment_preferences: {
            /**
             * @description Indicates whether to automatically bill the outstanding amount in the next billing cycle.
             * @default true
             */
            auto_bill_outstanding?: boolean;
            /** @description The initial set-up fee for the service. */
            setup_fee?: components["schemas"]["money"];
            /**
             * @description The action to take on the subscription if the initial payment for the setup fails.
             * @default CANCEL
             * @enum {string}
             */
            setup_fee_failure_action?: "CONTINUE" | "CANCEL";
            /**
             * @description The maximum number of payment failures before a subscription is suspended. For example, if `payment_failure_threshold` is `2`, the subscription automatically updates to the `SUSPEND` state if two consecutive payments fail.
             * @default 0
             */
            payment_failure_threshold?: number;
        };
        /**
         * Format: ppaas_common_percentage_v2
         * @description The percentage, as a fixed-point, signed decimal number. For example, define a 19.99% interest rate as `19.99`.
         */
        percentage: string;
        /**
         * Taxes
         * @description The tax details.
         */
        taxes: {
            /** @description The tax percentage on the billing amount. */
            percentage: components["schemas"]["percentage"];
            /**
             * @description Indicates whether the tax was already included in the billing amount.
             * @default true
             */
            inclusive?: boolean;
        };
        /**
         * Link Description
         * @description The request-related [HATEOAS link](/docs/api/reference/api-responses/#hateoas-links) information.
         */
        link_description: {
            /** @description The complete target URL. To make the related call, combine the method with this [URI Template-formatted](https://tools.ietf.org/html/rfc6570) link. For pre-processing, include the `$`, `(`, and `)` characters. The `href` is the key HATEOAS component that links a completed call with a subsequent call. */
            href: string;
            /** @description The [link relation type](https://tools.ietf.org/html/rfc5988#section-4), which serves as an ID for a link that unambiguously describes the semantics of the link. See [Link Relations](https://www.iana.org/assignments/link-relations/link-relations.xhtml). */
            rel: string;
            /**
             * @description The HTTP method required to make the related call.
             * @enum {string}
             */
            method?:
                | "GET"
                | "POST"
                | "PUT"
                | "DELETE"
                | "HEAD"
                | "CONNECT"
                | "OPTIONS"
                | "PATCH";
        };
        /**
         * Plan
         * @description The plan details.
         */
        plan: {
            /** @description The unique PayPal-generated ID for the plan. */
            id?: string;
            /** @description The ID for the product. */
            product_id?: string;
            /** @description The plan name. */
            name?: string;
            /**
             * @description The plan status.
             * @enum {string}
             */
            status?: "CREATED" | "INACTIVE" | "ACTIVE";
            /** @description The detailed description of the plan. */
            description?: string;
            /** @description An array of billing cycles for trial billing and regular billing. A plan can have at most two trial cycles and only one regular cycle. */
            billing_cycles?: components["schemas"]["billing_cycle"][];
            payment_preferences?: components["schemas"]["payment_preferences"];
            taxes?: components["schemas"]["taxes"];
            /**
             * @description Indicates whether you can subscribe to this plan by providing a quantity for the goods or service.
             * @default false
             */
            quantity_supported?: boolean;
            /** @description The date and time when the plan was created, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            create_time?: components["schemas"]["date_time"];
            /** @description The date and time when the plan was last updated, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            update_time?: components["schemas"]["date_time"];
            /** @description An array of request-related [HATEOAS links](/docs/api/reference/api-responses/#hateoas-links). */
            links?: readonly components["schemas"]["link_description"][];
        };
        /**
         * Plan Collection
         * @description The list of plans with details.
         */
        plan_collection: {
            /** @description An array of plans. */
            plans?: components["schemas"]["plan"][];
            /** @description The total number of items. */
            total_items?: number;
            /** @description The total number of pages. */
            total_pages?: number;
            /** @description An array of request-related [HATEOAS links](/docs/api/reference/api-responses/#hateoas-links). */
            links?: readonly components["schemas"]["link_description"][];
        };
        /**
         * Create Plan Request
         * @description The create plan request details.
         */
        plan_request_POST: {
            /** @description The ID of the product created through Catalog Products API. */
            product_id: string;
            /** @description The plan name. */
            name: string;
            /**
             * @description The initial state of the plan. Allowed input values are CREATED and ACTIVE.
             * @default ACTIVE
             * @enum {string}
             */
            status?: "CREATED" | "INACTIVE" | "ACTIVE";
            /** @description The detailed description of the plan. */
            description?: string;
            /** @description An array of billing cycles for trial billing and regular billing. A plan can have at most two trial cycles and only one regular cycle. */
            billing_cycles: components["schemas"]["billing_cycle"][];
            payment_preferences: components["schemas"]["payment_preferences"];
            taxes?: components["schemas"]["taxes"];
            /**
             * @description Indicates whether you can subscribe to this plan by providing a quantity for the goods or service.
             * @default false
             */
            quantity_supported?: boolean;
        };
        "plans.create-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field is missing.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_MIN_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is too short.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_MAX_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is too long.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_INTEGER_MIN_VALUE";
                      /** @enum {string} */
                      description?: "The integer value of a field is too small.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_INTEGER_MAX_VALUE";
                      /** @enum {string} */
                      description?: "The integer value of a field is too large.";
                  }
            )[];
        };
        /**
         * Patch
         * @description The JSON patch object to apply partial updates to resources.
         */
        patch: {
            /**
             * @description The operation.
             * @enum {string}
             */
            op: "add" | "remove" | "replace" | "move" | "copy" | "test";
            /** @description The <a href="https://tools.ietf.org/html/rfc6901">JSON Pointer</a> to the target document location at which to complete the operation. */
            path?: string;
            /**
             * Patch Value
             * @description The value to apply. The <code>remove</code> operation does not require a value.
             */
            value?: unknown;
            /** @description The <a href="https://tools.ietf.org/html/rfc6901">JSON Pointer</a> to the target document location from which to move the value. Required for the <code>move</code> operation. */
            from?: string;
        };
        /**
         * Patch Request
         * @description An array of JSON patch objects to apply partial updates to resources.
         */
        patch_request: components["schemas"]["patch"][];
        "plans.patch-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_PATCH_OPERATION";
                      /** @enum {string} */
                      description?: "The specified patch operation not supported for this field.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PATCH_PATH";
                      /** @enum {string} */
                      description?: "The specified field cannot be patched.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PATCH_PATH";
                      /** @enum {string} */
                      description?: "Multiple operations on the same field are not allowed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The field is not eligible for '$value' patch operation.";
                  }
            )[];
        };
        "plans.patch-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PLAN_STATUS_INACTIVE";
                      /** @enum {string} */
                      description?: "Status update is the only patchable filed on an inactive plan.";
                  }
            )[];
        };
        "plans.activate-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PLAN_STATUS_INVALID";
                      /** @enum {string} */
                      description?: "Invalid plan status for activate action; plan status should be either created or inactive.";
                  }
            )[];
        };
        "plans.deactivate-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PLAN_STATUS_INVALID";
                      /** @enum {string} */
                      description?: "Invalid plan status for deactivate action; plan status should be active.";
                  }
            )[];
        };
        /**
         * Update Pricing Scheme
         * @description The update pricing scheme request details.
         */
        update_pricing_scheme_request: {
            /** @description The billing cycle sequence. */
            billing_cycle_sequence: number;
            pricing_scheme: components["schemas"]["pricing_scheme"];
        };
        /**
         * Update Pricing Scheme Request
         * @description The update pricing scheme request details.
         */
        update_pricing_schemes_list_request: {
            /** @description An array of pricing schemes. */
            pricing_schemes: components["schemas"]["update_pricing_scheme_request"][];
        };
        "plans.update-pricing-schemes-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field is missing.";
                  }
            )[];
        };
        "plans.update-pricing-schemes-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_MISMATCH";
                      /** @enum {string} */
                      description?: "The currency code is different from the plan's currency code.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_BILLING_CYCLE_SEQUENCE";
                      /** @enum {string} */
                      description?: "The provided billing cycle sequence is not available.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_SCHEME";
                      /** @enum {string} */
                      description?: "The new pricing scheme should be of the same type as that of the old one.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_AMOUNT";
                      /** @enum {string} */
                      description?: "Free tiers are not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "Tier(s) are missing for some quantities.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "OVERLAPPING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "The specified quantity overlaps with multiple pricing tiers.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_MODEL";
                      /** @enum {string} */
                      description?: "The specified pricing model is not supported for trial billing cycle.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "FIXED_PRICE_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Fixed price is not supported for tiered pricing schemes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_QUANTITY";
                      /** @enum {string} */
                      description?: "Tier starting quantity must be less than ending quantity.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PRICING_SCHEME_UPDATE_NOT_ALLOWED";
                      /** @enum {string} */
                      description?: "Pricing scheme update is not allowed for the plan.";
                  }
            )[];
        };
        /**
         * Format: merchant_common_email_address_v2
         * @description The internationalized email address.<blockquote><strong>Note:</strong> Up to 64 characters are allowed before and 255 characters are allowed after the <code>@</code> sign. However, the generally accepted maximum length for an email address is 254 characters. The pattern verifies that an unquoted <code>@</code> sign exists.</blockquote>
         */
        email: string;
        /**
         * PayPal Account Identifier
         * Format: ppaas_payer_id_v3
         * @description The account identifier for a PayPal account.
         */
        account_id: string;
        /**
         * Payer Base
         * @description The customer who approves and pays for the order. The customer is also known as the payer.
         */
        payer_base: {
            /** @description The email address of the payer. */
            email_address?: components["schemas"]["email"];
            /** @description The PayPal-assigned ID for the payer. */
            payer_id?: components["schemas"]["account_id"];
        };
        /**
         * Name
         * @description The name of the party.
         */
        name: {
            /** @description The prefix, or title, to the party's name. */
            prefix?: string;
            /** @description When the party is a person, the party's given, or first, name. */
            given_name?: string;
            /** @description When the party is a person, the party's surname or family name. Also known as the last name. Required when the party is a person. Use also to store multiple surnames including the matronymic, or mother's, surname. */
            surname?: string;
            /** @description When the party is a person, the party's middle name. Use also to store multiple middle names including the patronymic, or father's, middle name. */
            middle_name?: string;
            /** @description The suffix for the party's name. */
            suffix?: string;
            /** @description DEPRECATED. The party's alternate name. Can be a business name, nickname, or any other name that cannot be split into first, last name. Required when the party is a business. */
            alternate_full_name?: string;
            /** @description When the party is a person, the party's full name. */
            full_name?: string;
        };
        /**
         * Phone Type
         * @description The phone type.
         * @enum {string}
         */
        phone_type: "FAX" | "HOME" | "MOBILE" | "OTHER" | "PAGER";
        /**
         * Phone
         * @description The phone number, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en).
         */
        phone: {
            /** @description The country calling code (CC), in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). The combined length of the CC and the national number must not be greater than 15 digits. The national number consists of a national destination code (NDC) and subscriber number (SN). */
            country_code: string;
            /** @description The national number, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). The combined length of the country calling code (CC) and the national number must not be greater than 15 digits. The national number consists of a national destination code (NDC) and subscriber number (SN). */
            national_number: string;
            /** @description The extension number. */
            extension_number?: string;
        };
        /**
         * Phone With Type
         * @description The phone information.
         */
        phone_with_type: {
            phone_type?: components["schemas"]["phone_type"];
            /** @description The phone number, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). Supports only the `national_number` property. */
            phone_number: components["schemas"]["phone"];
        };
        /**
         * Format: ppaas_date_notime_v2
         * @description The stand-alone date, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). To represent special legal values, such as a date of birth, you should use dates with no associated time or time-zone data. Whenever possible, use the standard `date_time` type. This regular expression does not validate all dates. For example, February 31 is valid and nothing is known about leap years.
         */
        date_no_time: string;
        /**
         * Tax Information
         * @description The tax ID of the customer. The customer is also known as the payer. Both `tax_id` and `tax_id_type` are required.
         */
        tax_info: {
            /** @description The customer's tax ID value. */
            tax_id: string;
            /**
             * @description The customer's tax ID type.
             * @enum {string}
             */
            tax_id_type: "BR_CPF" | "BR_CNPJ";
        };
        /**
         * Format: ppaas_common_country_code_v2
         * @description The [two-character ISO 3166-1 code](/docs/integration/direct/rest/country-codes/) that identifies the country or region.<blockquote><strong>Note:</strong> The country code for Great Britain is <code>GB</code> and not <code>UK</code> as used in the top-level domain names for that country. Use the `C2` country code for China worldwide for comparable uncontrolled price (CUP) method, bank card, and cross-border transactions.</blockquote>
         */
        country_code: string;
        /**
         * Portable Postal Address (Medium-Grained)
         * @description The portable international postal address. Maps to [AddressValidationMetadata](https://github.com/googlei18n/libaddressinput/wiki/AddressValidationMetadata) and HTML 5.1 [Autofilling form controls: the autocomplete attribute](https://www.w3.org/TR/html51/sec-forms.html#autofilling-form-controls-the-autocomplete-attribute).
         */
        address_portable: {
            /** @description The first line of the address. For example, number or street. For example, `173 Drury Lane`. Required for data entry and compliance and risk checks. Must contain the full address. */
            address_line_1?: string;
            /** @description The second line of the address. For example, suite or apartment number. */
            address_line_2?: string;
            /** @description The third line of the address, if needed. For example, a street complement for Brazil, direction text, such as `next to Walmart`, or a landmark in an Indian address. */
            address_line_3?: string;
            /** @description The neighborhood, ward, or district. Smaller than `admin_area_level_3` or `sub_locality`. Value is:<ul><li>The postal sorting code for Guernsey and many French territories, such as French Guiana.</li><li>The fine-grained administrative levels in China.</li></ul> */
            admin_area_4?: string;
            /** @description A sub-locality, suburb, neighborhood, or district. Smaller than `admin_area_level_2`. Value is:<ul><li>Brazil. Suburb, bairro, or neighborhood.</li><li>India. Sub-locality or district. Street name information is not always available but a sub-locality or district can be a very small area.</li></ul> */
            admin_area_3?: string;
            /** @description A city, town, or village. Smaller than `admin_area_level_1`. */
            admin_area_2?: string;
            /** @description The highest level sub-division in a country, which is usually a province, state, or ISO-3166-2 subdivision. Format for postal delivery. For example, `CA` and not `California`. Value, by country, is:<ul><li>UK. A county.</li><li>US. A state.</li><li>Canada. A province.</li><li>Japan. A prefecture.</li><li>Switzerland. A kanton.</li></ul> */
            admin_area_1?: string;
            /** @description The postal code, which is the zip code or equivalent. Typically required for countries with a postal code or an equivalent. See [postal code](https://en.wikipedia.org/wiki/Postal_code). */
            postal_code?: string;
            country_code: components["schemas"]["country_code"];
            /**
             * Address Details
             * @description The non-portable additional address details that are sometimes needed for compliance, risk, or other scenarios where fine-grain address information might be needed. Not portable with common third party and open source. Redundant with core fields.<br/>For example, `address_portable.address_line_1` is usually a combination of `address_details.street_number`, `street_name`, and `street_type`.
             */
            address_details?: {
                /** @description The street number. */
                street_number?: string;
                /** @description The street name. Just `Drury` in `Drury Lane`. */
                street_name?: string;
                /** @description The street type. For example, avenue, boulevard, road, or expressway. */
                street_type?: string;
                /** @description The delivery service. Post office box, bag number, or post office name. */
                delivery_service?: string;
                /** @description A named locations that represents the premise. Usually a building name or number or collection of buildings with a common name or number. For example, <code>Craven House</code>. */
                building_name?: string;
                /** @description The first-order entity below a named building or location that represents the sub-premises. Usually a single building within a collection of buildings with a common name. Can be a flat, story, floor, room, or apartment. */
                sub_building?: string;
            };
        };
        /**
         * Customer
         * Format: payer_v1
         * @description The customer who approves and pays for the order. The customer is also known as the payer.
         */
        payer: components["schemas"]["payer_base"] & {
            /** @description The name of the payer. Supports only the `given_name` and `surname` properties. */
            name?: components["schemas"]["name"];
            /** @description The phone number of the customer. Available only when you enable the **Contact Telephone Number** option in the <a href="https://www.paypal.com/cgi-bin/customerprofileweb?cmd=_profile-website-payments">**Profile & Settings**</a> for the merchant's PayPal account. The `phone.phone_number` supports only `national_number`. */
            phone?: components["schemas"]["phone_with_type"];
            /** @description The birth date of the payer in `YYYY-MM-DD` format. */
            birth_date?: components["schemas"]["date_no_time"];
            /** @description The tax information of the payer. Required only for Brazilian payer's. Both `tax_id` and `tax_id_type` are required. */
            tax_info?: components["schemas"]["tax_info"];
            /** @description The address of the payer. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. Also referred to as the billing address of the customer. */
            address?: components["schemas"]["address_portable"];
        };
        /**
         * Shipping Details
         * @description The shipping details.
         */
        shipping_detail: {
            /** @description The name of the person to whom to ship the items. Supports only the `full_name` property. */
            name?: components["schemas"]["name"];
            /**
             * @description The method by which the payer wants to get their items from the payee e.g shipping, in-person pickup. Either type or options but not both may be present.
             * @enum {string}
             */
            type?: "SHIPPING" | "PICKUP_IN_PERSON";
            /** @description The address of the person to whom to ship the items. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. */
            address?: components["schemas"]["address_portable"];
        };
        /** @description The year and month, in ISO-8601 `YYYY-MM` date format. See [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
        date_year_month: string;
        /**
         * Card Brand
         * @description The card network or brand. Applies to credit, debit, gift, and payment cards.
         * @enum {string}
         */
        card_brand:
            | "VISA"
            | "MASTERCARD"
            | "DISCOVER"
            | "AMEX"
            | "SOLO"
            | "JCB"
            | "STAR"
            | "DELTA"
            | "SWITCH"
            | "MAESTRO"
            | "CB_NATIONALE"
            | "CONFIGOGA"
            | "CONFIDIS"
            | "ELECTRON"
            | "CETELEM"
            | "CHINA_UNION_PAY";
        /**
         * Card
         * @description The payment card to use to fund a payment. Can be a credit or debit card.
         */
        card: {
            /** @description The PayPal-generated ID for the card. */
            id?: string;
            /** @description The card holder's name as it appears on the card. */
            name?: string;
            /** @description The primary account number (PAN) for the payment card. */
            number: string;
            /** @description The card expiration year and month, in [Internet date format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            expiry: components["schemas"]["date_year_month"];
            /** @description The three- or four-digit security code of the card. Also known as the CVV, CVC, CVN, CVE, or CID. This parameter cannot be present in the request when `payment_initiator=MERCHANT`. */
            security_code?: string;
            /** @description The last digits of the payment card. */
            last_digits?: string;
            /** @description The card brand or network. Typically used in the response. */
            card_type?: components["schemas"]["card_brand"];
            /** @description The billing address for this card. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. */
            billing_address?: components["schemas"]["address_portable"];
        };
        /**
         * Payment Source
         * @description The payment source definition. To be eligible to create subscription using debit or credit card, you will need to sign up here (https://www.paypal.com/bizsignup/entry/product/ppcp). Please note, its available only for non-3DS cards and for merchants in US and AU regions.
         */
        payment_source: {
            card?: components["schemas"]["card"];
        };
        /**
         * Subscriber Request Information
         * @description The subscriber request information .
         */
        subscriber_request: components["schemas"]["payer"] & {
            shipping_address?: components["schemas"]["shipping_detail"];
            payment_source?: components["schemas"]["payment_source"];
        };
        /**
         * Format: ppaas_common_language_v3
         * @description The [language tag](https://tools.ietf.org/html/bcp47#section-2) for the language in which to localize the error-related strings, such as messages, issues, and suggested actions. The tag is made up of the [ISO 639-2 language code](https://www.loc.gov/standards/iso639-2/php/code_list.php), the optional [ISO-15924 script tag](https://www.unicode.org/iso15924/codelists.html), and the [ISO-3166 alpha-2 country code](/docs/integration/direct/rest/country-codes/).
         */
        language: string;
        /**
         * @description The merchant-preferred payment methods.
         * @default UNRESTRICTED
         * @enum {string}
         */
        payee_payment_method_preference:
            | "UNRESTRICTED"
            | "IMMEDIATE_PAYMENT_REQUIRED";
        /**
         * Payment Method
         * @description The customer and merchant payment preferences.
         */
        payment_method: {
            /**
             * @description The customer-selected payment method on the merchant site.
             * @default PAYPAL
             */
            payer_selected?: string;
            payee_preferred?: components["schemas"]["payee_payment_method_preference"];
            /**
             * @description NACHA (the regulatory body governing the ACH network) requires that API callers (merchants, partners) obtain the consumer’s explicit authorization before initiating a transaction. To stay compliant, you’ll need to make sure that you retain a compliant authorization for each transaction that you originate to the ACH Network using this API. ACH transactions are categorized (using SEC codes) by how you capture authorization from the Receiver (the person whose bank account is being debited or credited). PayPal supports the following SEC codes.
             * @default WEB
             * @enum {string}
             */
            standard_entry_class_code?: "TEL" | "WEB" | "CCD" | "PPD";
        };
        /**
         * Application Context
         * @description The application context, which customizes the payer experience during the subscription approval process with PayPal.
         */
        application_context: {
            /** @description The label that overrides the business name in the PayPal account on the PayPal site. */
            brand_name?: string;
            /** @description The BCP 47-formatted locale of pages that the PayPal payment experience shows. PayPal supports a five-character code. For example, `da-DK`, `he-IL`, `id-ID`, `ja-JP`, `no-NO`, `pt-BR`, `ru-RU`, `sv-SE`, `th-TH`, `zh-CN`, `zh-HK`, or `zh-TW`. */
            locale?: components["schemas"]["language"];
            /**
             * @description The location from which the shipping address is derived.
             * @default GET_FROM_FILE
             * @enum {string}
             */
            shipping_preference?:
                | "GET_FROM_FILE"
                | "NO_SHIPPING"
                | "SET_PROVIDED_ADDRESS";
            /**
             * @description Configures the label name to `Continue` or `Subscribe Now` for subscription consent experience.
             * @default SUBSCRIBE_NOW
             * @enum {string}
             */
            user_action?: "CONTINUE" | "SUBSCRIBE_NOW";
            /** @description The customer and merchant payment preferences. Currently only PAYPAL payment method is supported. */
            payment_method?: components["schemas"]["payment_method"];
            /**
             * Format: uri
             * @description The URL where the customer is redirected after the customer approves the payment.
             */
            return_url: string;
            /**
             * Format: uri
             * @description The URL where the customer is redirected after the customer cancels the payment.
             */
            cancel_url: string;
        };
        /**
         * Billing Cycle Override
         * @description The billing cycle details to override at subscription level. The subscription billing cycle definition has to adhere to the plan billing cycle definition.
         */
        billing_cycle_override: {
            /** @description The active pricing scheme for this billing cycle. A free trial billing cycle does not require a pricing scheme. */
            pricing_scheme?: components["schemas"]["pricing_scheme"];
            /** @description The order in which this cycle is to run among other billing cycles. For example, a trial billing cycle has a `sequence` of `1` while a regular billing cycle has a `sequence` of `2`, so that trial cycle runs before the regular cycle. */
            sequence: number;
            /** @description The number of times this billing cycle gets executed. Trial billing cycles can only be executed a finite number of times (value between <code>1</code> and <code>999</code> for <code>total_cycles</code>). Regular billing cycles can be executed infinite times (value of <code>0</code> for <code>total_cycles</code>) or a finite number of times (value between <code>1</code> and <code>999</code> for <code>total_cycles</code>). */
            total_cycles?: number;
        };
        /**
         * Payment Preferences Override
         * @description The payment preferences to override at subscription level.
         */
        payment_preferences_override: {
            /** @description Indicates whether to automatically bill the outstanding amount in the next billing cycle. */
            auto_bill_outstanding?: boolean;
            /** @description The initial set-up fee for the service. */
            setup_fee?: components["schemas"]["money"];
            /**
             * @description The action to take on the subscription if the initial payment for the setup fails.
             * @enum {string}
             */
            setup_fee_failure_action?: "CONTINUE" | "CANCEL";
            /** @description The maximum number of payment failures before a subscription is suspended. For example, if `payment_failure_threshold` is `2`, the subscription automatically updates to the `SUSPEND` state if two consecutive payments fail. */
            payment_failure_threshold?: number;
        };
        /**
         * Taxes Override
         * @description The tax details.
         */
        taxes_override: {
            /** @description The tax percentage on the billing amount. */
            percentage?: components["schemas"]["percentage"];
            /** @description Indicates whether the tax was already included in the billing amount. */
            inclusive?: boolean;
        };
        /**
         * Plan Override
         * @description An inline plan object to customise the subscription. You can override plan level default attributes by providing customised values for the subscription in this object.
         */
        plan_override: {
            /** @description An array of billing cycles for trial billing and regular billing. The subscription billing cycle definition has to adhere to the plan billing cycle definition. */
            billing_cycles?: components["schemas"]["billing_cycle_override"][];
            payment_preferences?: components["schemas"]["payment_preferences_override"];
            taxes?: components["schemas"]["taxes_override"];
        };
        /**
         * Create Subscription Request
         * @description The create subscription request details.
         */
        subscription_request_post: {
            /** @description The ID of the plan. */
            plan_id: string;
            /**
             * @description The date and time when the subscription started, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6).
             * @default Current time
             */
            start_time?: components["schemas"]["date_time"];
            /** @description The quantity of the product in the subscription. */
            quantity?: string;
            /** @description The shipping charges. */
            shipping_amount?: components["schemas"]["money"];
            subscriber?: components["schemas"]["subscriber_request"];
            /**
             * @deprecated
             * @description DEPRECATED. Indicates whether the subscription auto-renews after the billing cycles complete.
             * @default false
             */
            auto_renewal?: boolean;
            application_context?: components["schemas"]["application_context"];
            /** @description The custom id for the subscription. Can be invoice id. */
            custom_id?: string;
            /** @description An inline plan object to customise the subscription. You can override plan level default attributes by providing customised values for the subscription in this object. */
            plan?: components["schemas"]["plan_override"];
        };
        /**
         * Subscription Status
         * @description The subscription status details.
         */
        subscription_status: {
            /**
             * @description The status of the subscription.
             * @enum {string}
             */
            status?:
                | "APPROVAL_PENDING"
                | "APPROVED"
                | "ACTIVE"
                | "SUSPENDED"
                | "CANCELLED"
                | "EXPIRED";
            /** @description The reason or notes for the status of the subscription. */
            status_change_note?: string;
            status_update_time?: components["schemas"]["date_time"];
        };
        /**
         * @description Liability shift indicator. The outcome of the issuer's authentication.
         * @enum {string}
         */
        liability_shift: "YES" | "NO" | "POSSIBLE" | "UNKNOWN";
        /**
         * @description Transactions status result identifier. The outcome of the issuer's authentication.
         * @enum {string}
         */
        pares_status: "Y" | "N" | "U" | "A" | "C" | "R" | "D" | "I";
        /**
         * @description Status of Authentication eligibility.
         * @enum {string}
         */
        enrolled: "Y" | "N" | "U" | "B";
        /**
         * The 3D Secure Authentication Response
         * @description Results of 3D Secure Authentication.
         */
        three_d_secure_authentication_response: {
            /** @description The outcome of the issuer's authentication. */
            authentication_status?: components["schemas"]["pares_status"];
            /** @description Status of authentication eligibility. */
            enrollment_status?: components["schemas"]["enrolled"];
        };
        /**
         * Authentication Response
         * @description Results of Authentication such as 3D Secure.
         */
        authentication_response: {
            liability_shift?: components["schemas"]["liability_shift"];
            three_d_secure?: components["schemas"]["three_d_secure_authentication_response"];
        };
        /**
         * Card Response
         * @description The payment card to use to fund a payment. Card can be a credit or debit card.
         */
        card_response: {
            /** @description The last digits of the payment card. */
            last_digits?: string;
            /** @description The card brand or network. Typically used in the response. */
            brand?: components["schemas"]["card_brand"];
            /**
             * @description The payment card type.
             * @enum {string}
             */
            type?: "CREDIT" | "DEBIT" | "PREPAID" | "UNKNOWN";
            authentication_result?: components["schemas"]["authentication_response"];
        };
        /**
         * Card Response with billing address and name
         * @description The payment card used to fund the payment. Card can be a credit or debit card.
         */
        card_response_with_billing_address: components["schemas"]["card_response"] & {
            /** @description The card holder's name as it appears on the card. */
            name?: string;
            /** @description The billing address for this card. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. */
            billing_address?: components["schemas"]["address_portable"];
            /** @description The card expiration year and month, in [Internet date format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            expiry?: components["schemas"]["date_year_month"];
            /** @description Currency code of the given instrument */
            currency_code?: components["schemas"]["currency_code"];
        };
        /**
         * Payment Source Response
         * @description The payment source used to fund the payment.
         */
        payment_source_response: {
            card?: components["schemas"]["card_response_with_billing_address"];
        };
        /**
         * Subscriber Response Information
         * @description The subscriber response information.
         */
        subscriber: components["schemas"]["payer"] & {
            shipping_address?: components["schemas"]["shipping_detail"];
            payment_source?: components["schemas"]["payment_source_response"];
        };
        /**
         * Billing Cycle Execution Details
         * @description The regular and trial execution details for a billing cycle.
         */
        cycle_execution: {
            /**
             * @description The type of the billing cycle.
             * @enum {string}
             */
            tenure_type: "REGULAR" | "TRIAL";
            /** @description The order in which to run this cycle among other billing cycles. */
            sequence: number;
            /** @description The number of billing cycles that have completed. */
            cycles_completed: number;
            /** @description For a finite billing cycle, cycles_remaining is the number of remaining cycles. For an infinite billing cycle, cycles_remaining is set as 0. */
            cycles_remaining?: number;
            /** @description The active pricing scheme version for the billing cycle. */
            current_pricing_scheme_version?: number;
            /** @description The number of times this billing cycle gets executed. Trial billing cycles can only be executed a finite number of times (value between <code>1</code> and <code>999</code> for <code>total_cycles</code>). Regular billing cycles can be executed infinite times (value of <code>0</code> for <code>total_cycles</code>) or a finite number of times (value between <code>1</code> and <code>999</code> for <code>total_cycles</code>). */
            total_cycles?: number;
        };
        /**
         * Last Payment Details
         * @description The details for the last payment.
         */
        last_payment_details: WithRequired<
            {
                /** @description The last payment amount. */
                amount?: components["schemas"]["money"];
                /** @description The date and time when the last payment was made, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
                time?: components["schemas"]["date_time"];
            },
            "amount" | "time"
        >;
        /**
         * Failed Payment Details
         * @description The details for the failed payment of the subscription.
         */
        failed_payment_details: {
            /** @description The failed payment amount. */
            amount: components["schemas"]["money"];
            /** @description The date and time when the failed payment was made, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            time: components["schemas"]["date_time"];
            /**
             * @description The reason code for the payment failure.
             * @enum {string}
             */
            reason_code?:
                | "PAYMENT_DENIED"
                | "INTERNAL_SERVER_ERROR"
                | "PAYEE_ACCOUNT_RESTRICTED"
                | "PAYER_ACCOUNT_RESTRICTED"
                | "PAYER_CANNOT_PAY"
                | "SENDING_LIMIT_EXCEEDED"
                | "TRANSACTION_RECEIVING_LIMIT_EXCEEDED"
                | "CURRENCY_MISMATCH";
            /** @description The time when the retry attempt for the failed payment occurs, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            next_payment_retry_time?: components["schemas"]["date_time"];
        };
        /**
         * Subscription Billing Information
         * @description The billing details for the subscription. If the subscription was or is active, these fields are populated.
         */
        subscription_billing_info: {
            /** @description The total pending bill amount, to be paid by the subscriber. */
            outstanding_balance: components["schemas"]["money"];
            /** @description The trial and regular billing executions. */
            cycle_executions?: readonly components["schemas"]["cycle_execution"][];
            /** @description The details for the last payment of the subscription. */
            last_payment?: components["schemas"]["last_payment_details"];
            /** @description The next date and time for billing this subscription, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            next_billing_time?: components["schemas"]["date_time"];
            /** @description The date and time when the final billing cycle occurs, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            final_payment_time?: components["schemas"]["date_time"];
            /** @description The number of consecutive payment failures. Resets to `0` after a successful payment. If this reaches the `payment_failure_threshold` value, the subscription updates to the `SUSPENDED` state. */
            failed_payments_count: number;
            /** @description The details for the last failed payment of the subscription. */
            last_failed_payment?: components["schemas"]["failed_payment_details"];
        };
        /**
         * Subscription
         * @description The subscription details.
         */
        subscription: components["schemas"]["subscription_status"] & {
            /** @description The PayPal-generated ID for the subscription. */
            id?: string;
            /** @description The ID of the plan. */
            plan_id?: string;
            start_time?: components["schemas"]["date_time"];
            /** @description The quantity of the product in the subscription. */
            quantity?: string;
            shipping_amount?: components["schemas"]["money"];
            subscriber?: components["schemas"]["subscriber"];
            billing_info?: components["schemas"]["subscription_billing_info"];
            create_time?: components["schemas"]["date_time"];
            update_time?: components["schemas"]["date_time"];
            /** @description The custom id for the subscription. Can be invoice id. */
            custom_id?: string;
            /** @description Indicates whether the subscription has overridden any plan attributes. */
            plan_overridden?: boolean;
            /** @description Inline plan details. */
            plan?: components["schemas"]["plan"];
            /** @description An array of request-related [HATEOAS links](/docs/api/reference/api-responses/#hateoas-links). */
            links?: readonly components["schemas"]["link_description"][];
        };
        "subscriptions.create-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_MAX_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is too long.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_INTEGER_MIN_VALUE";
                      /** @enum {string} */
                      description?: "The integer value of a field is too small.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_INTEGER_MAX_VALUE";
                      /** @enum {string} */
                      description?: "The integer value of a field is too large.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "Start time must be a valid future date and time.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUEST_BODY";
                      /** @enum {string} */
                      description?: "Request body is missing.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field is missing.";
                  }
            )[];
        };
        "subscriptions.create-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PLAN_STATUS_INVALID";
                      /** @enum {string} */
                      description?: "Invalid plan status for subscription creation; plan status should be active.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIPTION_CANNOT_HAVE_QUANTITY";
                      /** @enum {string} */
                      description?: "Subscription can't have quantity as the plan does not support quantity.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_SUBSCRIPTIONS_NOT_ENABLED";
                      /** @enum {string} */
                      description?: "The account is not setup to be able to process subscriptions funded by card payments. Please contact PayPal customer support.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "3DS_CARDS_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Cards that require 3DS authentication not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_BILLING_CYCLE_SEQUENCE";
                      /** @enum {string} */
                      description?: "The provided billing cycle sequence is not available.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_SCHEME";
                      /** @enum {string} */
                      description?: "The override plan pricing scheme should be of the same type as that of the original plan.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_AMOUNT";
                      /** @enum {string} */
                      description?: "Free tiers are not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "Tier(s) are missing for some quantities.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "OVERLAPPING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "The specified quantity overlaps with multiple pricing tiers.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_MODEL";
                      /** @enum {string} */
                      description?: "The specified pricing model is not supported for trial billing cycle.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "FIXED_PRICE_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Fixed price is not supported for tiered pricing schemes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_QUANTITY";
                      /** @enum {string} */
                      description?: "Tier starting quantity must be less than ending quantity.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_MISMATCH";
                      /** @enum {string} */
                      description?: "The currency code is different from the plan's currency code.";
                  }
            )[];
        };
        "subscriptions.patch-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_PATCH_OPERATION";
                      /** @enum {string} */
                      description?: "The specified patch operation not supported for this field.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PATCH_PATH";
                      /** @enum {string} */
                      description?: "The specified field cannot be patched.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PATCH_PATH";
                      /** @enum {string} */
                      description?: "Multiple operations on the same field are not allowed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_MAX_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is too long.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_MIN_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is too short.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_INTEGER_MIN_VALUE";
                      /** @enum {string} */
                      description?: "The integer value of a field is too small.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_INTEGER_MAX_VALUE";
                      /** @enum {string} */
                      description?: "The integer value of a field is too large.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUEST_BODY";
                      /** @enum {string} */
                      description?: "Request body is missing.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field is missing.";
                  }
            )[];
        };
        "subscriptions.patch-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIPTION_STATUS_INVALID";
                      /** @enum {string} */
                      description?: "Invalid subscription status for patch action; subscription status should be either active or suspended.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_SUBSCRIPTIONS_NOT_ENABLED";
                      /** @enum {string} */
                      description?: "The account is not setup to be able to process subscriptions funded by card payments. Please contact PayPal customer support.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "3DS_CARDS_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Cards that require 3DS authentication not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BILLING_CYCLE_EXECUTION_COMPLETED";
                      /** @enum {string} */
                      description?: "Update cannot be performed on a billing cycle that has completed execution.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AMOUNT_GREATER_THAN_OUTSTANDING_BALANCE";
                      /** @enum {string} */
                      description?: "The new outstanding balance cannot be greater than the current outstanding balance.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_BILLING_TOTAL_CYCLES";
                      /** @enum {string} */
                      description?: "The total cycles cannot be less than the number of billing cycles completed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_AMOUNT";
                      /** @enum {string} */
                      description?: "Free tiers are not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "Tier(s) are missing for some quantities.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "OVERLAPPING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "The specified quantity overlaps with multiple pricing tiers.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_MODEL";
                      /** @enum {string} */
                      description?: "The specified pricing model is not supported for trial billing cycle.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "FIXED_PRICE_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Fixed price is not supported for tiered pricing schemes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_QUANTITY";
                      /** @enum {string} */
                      description?: "Tier starting quantity must be less than ending quantity.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_START_TIME";
                      /** @enum {string} */
                      description?: "The start time cannot be updated for a subscription that has been activated.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_MISMATCH";
                      /** @enum {string} */
                      description?: "The currency code is different from the subscription's currency code.";
                  }
            )[];
        };
        /**
         * Subscription Modify Plan Request
         * @description The request to update the quantity of the product or service in a subscription. You can also use this method to switch the plan and update the `shipping_amount` and `shipping_address` values for the subscription. This type of update requires the buyer's consent.
         */
        subscription_revise_request: {
            /** @description The unique PayPal-generated ID for the plan. */
            plan_id?: string;
            /** @description The quantity of the product or service in the subscription. */
            quantity?: string;
            /** @description The shipping charges. */
            shipping_amount?: components["schemas"]["money"];
            /** @description The shipping address of the subscriber. */
            shipping_address?: components["schemas"]["shipping_detail"];
            application_context?: components["schemas"]["application_context"];
            /** @description An inline plan object to customise the subscription. You can override plan level default attributes by providing customised values for the subscription in this object. Any existing overrides will not be carried forward during subscription revise. */
            plan?: components["schemas"]["plan_override"];
        };
        /**
         * Update Product Quantity in Subscription Response
         * @description The response to a request to update the quantity of the product or service in a subscription. You can also use this method to switch the plan and update the `shipping_amount` and `shipping_address` values for the subscription. This type of update requires the buyer's consent.
         */
        subscription_revise_response: components["schemas"]["subscription_revise_request"] & {
            /** @description Indicates whether the subscription has overridden any plan attributes. */
            plan_overridden?: boolean;
            /** @description An array of request-related [HATEOAS links](/docs/api/reference/api-responses/#hateoas-links). */
            links?: readonly components["schemas"]["link_description"][];
        };
        "subscriptions.revise-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_INTEGER_MIN_VALUE";
                      /** @enum {string} */
                      description?: "The integer value of a field is too small.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_INTEGER_MAX_VALUE";
                      /** @enum {string} */
                      description?: "The integer value of a field is too large.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUEST_BODY";
                      /** @enum {string} */
                      description?: "Request body is missing.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field is missing.";
                  }
            )[];
        };
        "subscriptions.revise-404": {
            issues?: {
                /** @enum {string} */
                issue?: "INVALID_RESOURCE_ID";
                /** @enum {string} */
                description?: "Requested resource ID was not found.";
            }[];
        };
        "subscriptions.revise-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PLAN_PRODUCT_NOT_COMPATIBLE";
                      /** @enum {string} */
                      description?: "The old and the new plans should be for the same product.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_BILLING_CYCLE_SEQUENCE";
                      /** @enum {string} */
                      description?: "The provided billing cycle sequence is not available.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_SCHEME";
                      /** @enum {string} */
                      description?: "The override plan pricing scheme should be of the same type as that of the original plan.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_AMOUNT";
                      /** @enum {string} */
                      description?: "Free tiers are not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "Tier(s) are missing for some quantities.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "OVERLAPPING_PRICING_SCHEME_TIERS";
                      /** @enum {string} */
                      description?: "The specified quantity overlaps with multiple pricing tiers.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_MODEL";
                      /** @enum {string} */
                      description?: "The specified pricing model is not supported for trial billing cycle.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "FIXED_PRICE_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Fixed price is not supported for tiered pricing schemes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PRICING_TIER_QUANTITY";
                      /** @enum {string} */
                      description?: "Tier starting quantity must be less than ending quantity.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_MISMATCH";
                      /** @enum {string} */
                      description?: "The currency code is different from the plan's currency code.";
                  }
            )[];
        };
        /**
         * Suspend Subscription
         * @description The suspend subscription request details.
         */
        subscription_suspend_request: {
            /** @description The reason for suspenson of the subscription. */
            reason: string;
        };
        "subscriptions.suspend-400": {
            issues?: {
                /** @enum {string} */
                issue?: "INVALID_STRING_MAX_LENGTH";
                /** @enum {string} */
                description?: "The value of a field is too long.";
            }[];
        };
        "subscriptions.suspend-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIPTION_STATUS_INVALID";
                      /** @enum {string} */
                      description?: "Invalid subscription status for suspend action; subscription status should be active.";
                  }
            )[];
        };
        /**
         * Cancel Subscription Request
         * @description The cancel subscription request details.
         */
        subscription_cancel_request: {
            /** @description The reason for the cancellation of a subscription. */
            reason: string;
        };
        "subscriptions.cancel-400": {
            issues?: {
                /** @enum {string} */
                issue?: "INVALID_STRING_MAX_LENGTH";
                /** @enum {string} */
                description?: "The value of a field is too long.";
            }[];
        };
        "subscriptions.cancel-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIPTION_STATUS_INVALID";
                      /** @enum {string} */
                      description?: "Invalid subscription status for cancel action; subscription status should be active or suspended.";
                  }
            )[];
        };
        /**
         * Activate Subscription Request
         * @description The activate subscription request details.
         */
        subscription_activate_request: {
            /** @description The reason for activation of a subscription. Required to reactivate the subscription. */
            reason?: string;
        };
        "subscriptions.activate-400": {
            issues?: {
                /** @enum {string} */
                issue?: "INVALID_STRING_MAX_LENGTH";
                /** @enum {string} */
                description?: "The value of a field is too long.";
            }[];
        };
        "subscriptions.activate-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIPTION_STATUS_INVALID";
                      /** @enum {string} */
                      description?: "Invalid subscription status for activate action; subscription status should be suspended.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIPTION_CANNOT_BE_ACTIVATED";
                      /** @enum {string} */
                      description?: "Subscription cannot be activated after payment failure threshold has reached.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIPTION_CANNOT_BE_ACTIVATED";
                      /** @enum {string} */
                      description?: "This subscription should be activated by the system.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIPTION_CANNOT_BE_ACTIVATED";
                      /** @enum {string} */
                      description?: "This subscription should be activated by the merchant.";
                  }
            )[];
        };
        /**
         * Charge Amount from Subscriber
         * @description The charge amount from the subscriber.
         */
        subscription_capture_request: {
            /** @description The reason or note for the subscription charge. */
            note: string;
            /**
             * @description The type of capture.
             * @enum {string}
             */
            capture_type: "OUTSTANDING_BALANCE";
            /** @description The amount of the outstanding balance. This value cannot be greater than the current outstanding balance amount. */
            amount: components["schemas"]["money"];
        };
        /**
         * Capture Status Details
         * @description The details of the captured payment status.
         */
        capture_status_details: {
            /**
             * @description The reason why the captured payment status is `PENDING` or `DENIED`.
             * @enum {string}
             */
            reason?:
                | "BUYER_COMPLAINT"
                | "CHARGEBACK"
                | "ECHECK"
                | "INTERNATIONAL_WITHDRAWAL"
                | "OTHER"
                | "PENDING_REVIEW"
                | "RECEIVING_PREFERENCE_MANDATES_MANUAL_ACTION"
                | "REFUNDED"
                | "TRANSACTION_APPROVED_AWAITING_FUNDING"
                | "UNILATERAL"
                | "VERIFICATION_REQUIRED";
        };
        /**
         * Capture Status
         * @description The status of a captured payment.
         */
        capture_status: {
            /**
             * @description The status of the captured payment.
             * @enum {string}
             */
            status?:
                | "COMPLETED"
                | "DECLINED"
                | "PARTIALLY_REFUNDED"
                | "PENDING"
                | "REFUNDED";
            /** @description The details of the captured payment status. */
            status_details?: components["schemas"]["capture_status_details"];
        };
        /**
         * Amount with Breakdown
         * @description The breakdown details for the amount. Includes the gross, tax, fee, and shipping amounts.
         */
        amount_with_breakdown: {
            /** @description The amount for this transaction. */
            gross_amount: components["schemas"]["money"];
            /** @description The item total for the transaction. */
            total_item_amount?: components["schemas"]["money"];
            /** @description The fee details for the transaction. */
            fee_amount?: components["schemas"]["money"];
            /** @description The shipping amount for the transaction. */
            shipping_amount?: components["schemas"]["money"];
            /** @description The tax amount for the transaction. */
            tax_amount?: components["schemas"]["money"];
            /** @description The net amount that the payee receives for this transaction in their PayPal account. The net amount is computed as <code>gross_amount</code> minus the <code>paypal_fee</code>. */
            net_amount?: components["schemas"]["money"];
        };
        /**
         * Format: ppaas_common_email_address_v2
         * @description The internationalized email address.<blockquote><strong>Note:</strong> Up to 64 characters are allowed before and 255 characters are allowed after the <code>@</code> sign. However, the generally accepted maximum length for an email address is 254 characters. The pattern verifies that an unquoted <code>@</code> sign exists.</blockquote>
         */
        email_address: string;
        /**
         * Transaction Details
         * @description The transaction details.
         */
        transaction: WithRequired<
            components["schemas"]["capture_status"] & {
                /** @description The PayPal-generated transaction ID. */
                id?: string;
                amount_with_breakdown?: components["schemas"]["amount_with_breakdown"];
                /** @description The name of the customer. */
                payer_name?: components["schemas"]["name"];
                /** @description The email ID of the customer. */
                payer_email?: components["schemas"]["email_address"];
                /** @description The date and time when the transaction was processed, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
                time?: components["schemas"]["date_time"];
            },
            "id" | "amount_with_breakdown" | "time"
        >;
        "subscriptions.capture-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUEST_BODY";
                      /** @enum {string} */
                      description?: "Request body is missing.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_MAX_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is too long.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field is missing.";
                  }
            )[];
        };
        "subscriptions.capture-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "USER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "User account locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIBER_ACCOUNT_LOCKED";
                      /** @enum {string} */
                      description?: "Subscriber Account Locked.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIBER_ACCOUNT_CLOSED";
                      /** @enum {string} */
                      description?: "Subscriber Account Closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIBER_ACCOUNT_RESTRICTED";
                      /** @enum {string} */
                      description?: "Subscriber Account Restricted.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SUBSCRIPTION_STATUS_INVALID";
                      /** @enum {string} */
                      description?: "Invalid subscription status for capture action; subscription status should be active or suspended or expired.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ZERO_OUTSTANDING_BALANCE";
                      /** @enum {string} */
                      description?: "Current outstanding balance should be greater than zero.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_MISMATCH";
                      /** @enum {string} */
                      description?: "The currency code is different from the subscription's currency code.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AMOUNT_GREATER_THAN_OUTSTANDING_BALANCE";
                      /** @enum {string} */
                      description?: "The capture amount can not be greater than the current outstanding balance.";
                  }
            )[];
        };
        /**
         * List Transactions
         * @description The list transactions for a subscription request details.
         */
        transactions_list: {
            /** @description An array of transactions. */
            transactions?: components["schemas"]["transaction"][];
            /** @description The total number of items. */
            total_items?: number;
            /** @description The total number of pages. */
            total_pages?: number;
            /** @description An array of request-related [HATEOAS links](/docs/api/reference/api-responses/#hateoas-links). */
            links?: readonly components["schemas"]["link_description"][];
        };
        "subscriptions.transactions-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field is missing.";
                  }
            )[];
        };
    };
    responses: {
        /** @description The default response. */
        default: {
            content: {
                "application/json": components["schemas"]["error_default"];
            };
        };
    };
    parameters: {
        /** @description The media type. Required for operations with a request body. The value is `application/<format>`, where the `format` is `json`. */
        content_type: string;
        /** @description The preferred server response upon successful completion of the request. Value is:<ul><li><code>return=minimal</code>. The server returns a minimal response to optimize communication between the API caller and the server. A minimal response includes the <code>id</code>, <code>status</code> and HATEOAS links.</li><li><code>return=representation</code>. The server returns a complete resource representation, including the current state of the resource.</li></ul> */
        prefer?: string;
        /** @description The server stores keys for 72 hours. */
        paypal_request_id?: string;
        /** @description Filters the response by a Product ID. */
        product_id?: string;
        /** @description Filters the response by list of plan IDs. Filter supports upto <code>70</code> plan IDs. URLs should not exceed a length of <code>2000</code> characters. */
        plan_ids?: string;
        /** @description The number of items to return in the response. */
        page_size?: number;
        /** @description A non-zero integer which is the start index of the entire list of items to return in the response. The combination of `page=1` and `page_size=20` returns the first 20 items. The combination of `page=2` and `page_size=20` returns the next 20 items. */
        page?: number;
        /** @description Indicates whether to show the total count in the response. */
        total_required?: boolean;
        /** @description Filters the response by list of subscription statuses. */
        statuses?: "ACTIVE" | "SUSPENDED" | "CANCELLED" | "EXPIRED";
        /** @description The ID of the subscription. */
        id: string;
        /** @description List of fields that are to be returned in the response. Possible value for fields are last_failed_payment and plan. */
        fields?: string;
        /** @description The start time of the range of transactions to list. */
        start_time: string;
        /** @description The end time of the range of transactions to list. */
        end_time: string;
    };
    requestBodies: {
        patch_request?: {
            content: {
                "application/json": components["schemas"]["patch_request"];
            };
        };
    };
    headers: never;
    pathItems: never;
}

export type $defs = Record<string, never>;

export type external = Record<string, never>;

export interface operations {
    /**
     * List plans
     * @description Lists billing plans.
     */
    "plans.list": {
        parameters: {
            query?: {
                product_id?: components["parameters"]["product_id"];
                plan_ids?: components["parameters"]["plan_ids"];
                page_size?: components["parameters"]["page_size"];
                page?: components["parameters"]["page"];
                total_required?: components["parameters"]["total_required"];
                statuses?: components["parameters"]["statuses"];
            };
            header: {
                "Content-Type": components["parameters"]["content_type"];
                Prefer?: components["parameters"]["prefer"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `200 OK` status code and a JSON response body that lists billing plans. */
            200: {
                content: {
                    "application/json": components["schemas"]["plan_collection"];
                };
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Create plan
     * @description Creates a plan that defines pricing and billing cycle details for subscriptions.
     */
    "plans.create": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
                Prefer?: components["parameters"]["prefer"];
                "PayPal-Request-Id"?: components["parameters"]["paypal_request_id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["plan_request_POST"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `200 OK` status code and a JSON response body that shows billing plan details. */
            200: {
                content: {
                    "application/json": components["schemas"]["plan"];
                };
            };
            /** @description A successful request returns the HTTP `201 Created` status code and a JSON response body that shows billing plan details. */
            201: {
                content: {
                    "application/json": components["schemas"]["plan"];
                };
            };
            /** @description Bad Request. Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["plans.create-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Show plan details
     * @description Shows details for a plan, by ID.
     */
    "plans.get": {
        parameters: {
            query?: {
                fields?: components["parameters"]["fields"];
            };
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `200 OK` status code and a JSON response body that shows plan details. */
            200: {
                content: {
                    "application/json": components["schemas"]["plan"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Update plan
     * @description Updates a plan with the `CREATED` or `ACTIVE` status. For an `INACTIVE` plan, you can make only status updates.<br/>You can patch these attributes and objects:<table><thead><tr><th>Attribute or object</th><th>Operations</th></tr></thead><tbody><tr><td><code>description</code></td><td>replace</td></tr><tr><td><code>payment_preferences.auto_bill_outstanding</code></td><td>replace</td></tr><tr><td><code>taxes.percentage</code></td><td>replace</td></tr><tr><td><code>payment_preferences.payment_failure_threshold</code></td><td>replace</td></tr><tr><td><code>payment_preferences.setup_fee</code></td><td>replace</td></tr><tr><td><code>payment_preferences.setup_fee_failure_action</code></td><td>replace</td></tr><tr><td><code>name</code></td><td>replace</td></tr></tbody></table>
     */
    "plans.patch": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody: components["requestBodies"]["patch_request"];
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with no JSON response body. */
            204: {
                content: never;
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["plans.patch-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["plans.patch-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Activate plan
     * @description Activates a plan, by ID.
     */
    "plans.activate": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with no JSON response body. */
            204: {
                content: never;
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["plans.activate-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Deactivate plan
     * @description Deactivates a plan, by ID.
     */
    "plans.deactivate": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with no JSON response body. */
            204: {
                content: never;
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["plans.deactivate-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Update pricing
     * @description Updates pricing for a plan. For example, you can update a regular billing cycle from $5 per month to $7 per month.
     */
    "plans.update-pricing-schemes": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["update_pricing_schemes_list_request"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with no JSON response body. */
            204: {
                content: never;
            };
            /** @description Bad Request. Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["plans.update-pricing-schemes-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["plans.update-pricing-schemes-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Create subscription
     * @description Creates a subscription.
     */
    "subscriptions.create": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
                Prefer?: components["parameters"]["prefer"];
                "PayPal-Request-Id"?: components["parameters"]["paypal_request_id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["subscription_request_post"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `200 OK` status code and a JSON response body that shows subscription details. */
            200: {
                content: {
                    "application/json": components["schemas"]["subscription"];
                };
            };
            /** @description A successful request returns the HTTP `201 Created` status code and a JSON response body that shows subscription details. */
            201: {
                content: {
                    "application/json": components["schemas"]["subscription"];
                };
            };
            /** @description Bad Request. Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["subscriptions.create-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["subscriptions.create-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Show subscription details
     * @description Shows details for a subscription, by ID.
     */
    "subscriptions.get": {
        parameters: {
            query?: {
                fields?: components["parameters"]["fields"];
            };
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `200 OK` status code and a JSON response body that shows subscription details. */
            200: {
                content: {
                    "application/json": components["schemas"]["subscription"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Update subscription
     * @description Updates a subscription which could be in <code>ACTIVE</code> or <code>SUSPENDED</code> status. You can override plan level default attributes by providing customised values for plan path in the patch request.<br /> <ul> <li>You cannot update attributes that have already completed (Example - trial cycles can’t be updated if completed).</li> <li>Once overridden, changes to plan resource will not impact subscription.</li> <li>Any price update will not impact billing cycles within next 10 days (Applicable only for subscriptions funded by PayPal account).</li> </ul> Following are the fields eligible for patch.<table><thead><tr><th>Attribute or object</th><th>Operations</th></tr></thead><tbody><tr><td><code>billing_info.outstanding_balance</code></td><td>replace</td></tr><tr><td><code>custom_id</code></td><td>add,replace</td></tr><tr><td><code>plan.billing_cycles[@sequence==n].<br/>pricing_scheme.fixed_price</code></td><td>add,replace</td></tr><tr><td><code>plan.billing_cycles[@sequence==n].<br/>pricing_scheme.tiers</code></td><td>replace</td></tr><tr><td><code>plan.billing_cycles[@sequence==n].<br/>total_cycles</code></td><td>replace</td></tr><tr><td><code>plan.payment_preferences.<br/>auto_bill_outstanding</code></td><td>replace</td></tr><tr><td><code>plan.payment_preferences.<br/>payment_failure_threshold</code></td><td>replace</td></tr><tr><td><code>plan.taxes.inclusive</code></td><td>add,replace</td></tr><tr><td><code>plan.taxes.percentage</code></td><td>add,replace</td></tr><tr><td><code>shipping_amount</code></td><td>add,replace</td></tr><tr><td><code>start_time</code></td><td>replace</td></tr><tr><td><code>subscriber.shipping_address</code></td><td>add,replace</td></tr><tr><td><code>subscriber.payment_source (for subscriptions funded<br/>by card payments)</code></td><td>replace</td></tr></tbody></table>
     */
    "subscriptions.patch": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody: components["requestBodies"]["patch_request"];
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with no JSON response body. */
            204: {
                content: never;
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["subscriptions.patch-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["subscriptions.patch-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Revise plan or quantity of subscription
     * @description Updates the quantity of the product or service in a subscription. You can also use this method to switch the plan and update the `shipping_amount`, `shipping_address` values for the subscription. This type of update requires the buyer's consent.
     */
    "subscriptions.revise": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["subscription_revise_request"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `200 OK` status code and a JSON response body that shows subscription details. */
            200: {
                content: {
                    "application/json": components["schemas"]["subscription_revise_response"];
                };
            };
            /** @description Bad Request. Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["subscriptions.revise-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["subscriptions.revise-404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["subscriptions.revise-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Suspend subscription
     * @description Suspends the subscription.
     */
    "subscriptions.suspend": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["subscription_suspend_request"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with no JSON response body. */
            204: {
                content: never;
            };
            /** @description Bad Request. Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["subscriptions.suspend-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["subscriptions.suspend-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Cancel subscription
     * @description Cancels the subscription.
     */
    "subscriptions.cancel": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["subscription_cancel_request"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with no JSON response body. */
            204: {
                content: never;
            };
            /** @description Bad Request. Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["subscriptions.cancel-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["subscriptions.cancel-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Activate subscription
     * @description Activates the subscription.
     */
    "subscriptions.activate": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["subscription_activate_request"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with no JSON response body. */
            204: {
                content: never;
            };
            /** @description Bad Request. Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["subscriptions.activate-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["subscriptions.activate-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Capture authorized payment on subscription
     * @description Captures an authorized payment from the subscriber on the subscription.
     */
    "subscriptions.capture": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
                "PayPal-Request-Id"?: components["parameters"]["paypal_request_id"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["subscription_capture_request"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `200 OK` status code and a JSON response body that shows subscription details. */
            200: {
                content: {
                    "application/json": components["schemas"]["transaction"];
                };
            };
            /** @description Request Accepted. */
            202: {
                content: never;
            };
            /** @description Bad Request. Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["subscriptions.capture-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["subscriptions.capture-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * List transactions for subscription
     * @description Lists transactions for a subscription.
     */
    "subscriptions.transactions": {
        parameters: {
            query: {
                start_time: components["parameters"]["start_time"];
                end_time: components["parameters"]["end_time"];
            };
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `200 OK` status code and a JSON response body that shows subscription details. */
            200: {
                content: {
                    "application/json": components["schemas"]["transactions_list"];
                };
            };
            /** @description Bad Request. Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["subscriptions.transactions-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
}
