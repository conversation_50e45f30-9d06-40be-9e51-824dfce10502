import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Box,
  Tooltip,
  Divider,
  Button,
  useTheme,
  alpha,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
} from '@mui/material';
import {
  Translate as TranslateIcon,
  AccountCircle as AccountCircleIcon,
  Logout as LogoutIcon,
  Login as LoginIcon,
  Search as SearchIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  Menu as MenuIcon,
  Home as HomeIcon,
  Info as InfoIcon,
  School as SchoolIcon,
  Security as SecurityIcon,
  Description as PolicyIcon,
  ContactSupport as ContactSupportIcon,
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const Header = () => {
  const { t, i18n } = useTranslation();
  const { user, logout, currentUser } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down('md'));
  const [anchorEl, setAnchorEl] = useState(null);
  const [langMenuAnchor, setLangMenuAnchor] = useState(null);
  const [policiesMenuAnchor, setPoliciesMenuAnchor] = useState(null);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Don't show header for pending deletion users (they have their own header)
  if ((user && user.status === 'pending_deletion') || (currentUser && currentUser.status === 'pending_deletion')) {
    return null;
  }

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLangMenuOpen = (event) => {
    setLangMenuAnchor(event.currentTarget);
  };

  const handleLangMenuClose = () => {
    setLangMenuAnchor(null);
  };

  const handlePoliciesMenuOpen = (event) => {
    setPoliciesMenuAnchor(event.currentTarget);
  };

  const handlePoliciesMenuClose = () => {
    setPoliciesMenuAnchor(null);
  };

  const handleLanguageChange = (lang) => {
    i18n.changeLanguage(lang);
    localStorage.setItem('language', lang);
    handleLangMenuClose();
    setMobileMenuOpen(false);
  };

  const handlePolicyClick = (policyPath) => {
    navigate(policyPath);
    handlePoliciesMenuClose();
    setMobileMenuOpen(false);
  };

  const handleProfileClick = () => {
    handleMenuClose();
    setMobileMenuOpen(false);
    switch (user?.role) {
      case 'admin':
        navigate('/admin/profile');
        break;
      case 'platform_teacher':
      case 'new_teacher':
        navigate('/teacher/profile');
        break;
      case 'student':
        navigate('/student/profile');
        break;
      default:
        navigate('/login');
    }
  };

  const handleLogout = async () => {
    handleMenuClose();
    setMobileMenuOpen(false);
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleMobileMenuToggle = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const mobileMenu = (
    <Drawer
      anchor={i18n.language === 'ar' ? 'right' : 'left'}
      open={mobileMenuOpen}
      onClose={() => setMobileMenuOpen(false)}
      slotProps={{
        paper: {
          sx: {
            width: { xs: '85vw', sm: 320 },
            maxWidth: 400,
            background: theme.palette.background.default,
            color: theme.palette.text.primary,
            borderRadius: i18n.language === 'ar' ? '15px 0 0 15px' : '0 15px 15px 0'
          }
        }
      }}
    >
      <Box sx={{ p: { xs: 2, sm: 3 } }}>
        <Typography
          variant="h6"
          sx={{
            mb: 2,
            fontFamily: 'Tajawal, sans-serif',
            fontSize: { xs: '1.1rem', sm: '1.25rem' },
            fontWeight: 600,
            textAlign: 'center'
          }}
        >
          {t('appName')}
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <List sx={{ '& .MuiListItem-root': { borderRadius: 2, mb: 0.5 } }}>
          <ListItem
            button
            onClick={() => { navigate('/'); setMobileMenuOpen(false); }}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><HomeIcon /></ListItemIcon>
            <ListItemText
              primary={t('nav.home')}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          <ListItem
            button
            onClick={() => { navigate('/about-us'); setMobileMenuOpen(false); }}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><InfoIcon /></ListItemIcon>
            <ListItemText
              primary={t('aboutUs')}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          <ListItem
            button
            onClick={() => { navigate('/contact-us'); setMobileMenuOpen(false); }}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><ContactSupportIcon /></ListItemIcon>
            <ListItemText
              primary={t('contact.title')}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          <ListItem
            button
            onClick={() => handlePolicyClick('/privacy-policy')}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><SecurityIcon /></ListItemIcon>
            <ListItemText
              primary={t('footer.privacy')}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          <ListItem
            button
            onClick={() => handlePolicyClick('/refund-policy')}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><PolicyIcon /></ListItemIcon>
            <ListItemText
              primary={i18n.language === 'ar' ? 'سياسة الاسترداد' : 'Refund Policy'}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          <ListItem
            button
            onClick={() => handlePolicyClick('/booking-payment-policy')}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><PolicyIcon /></ListItemIcon>
            <ListItemText
              primary={i18n.language === 'ar' ? 'سياسة الحجز والدفع' : 'Booking & Payment Policy'}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          <ListItem
            button
            onClick={() => handlePolicyClick('/booking-cancellation-policy')}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><PolicyIcon /></ListItemIcon>
            <ListItemText
              primary={i18n.language === 'ar' ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation Policy'}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          <ListItem
            button
            onClick={() => handlePolicyClick('/terms-and-conditions')}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><PolicyIcon /></ListItemIcon>
            <ListItemText
              primary={i18n.language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions'}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          <ListItem
            button
            onClick={() => { navigate('/find-teacher'); setMobileMenuOpen(false); }}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><SchoolIcon /></ListItemIcon>
            <ListItemText
              primary={t('nav.findTeacher')}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          <Divider sx={{ my: 1 }} />
          <ListItem
            button
            onClick={() => handleLanguageChange(i18n.language === 'ar' ? 'en' : 'ar')}
            sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
          >
            <ListItemIcon><TranslateIcon /></ListItemIcon>
            <ListItemText
              primary={i18n.language === 'ar' ? 'English' : 'العربية'}
              primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
            />
          </ListItem>
          {user ? (
            <>
              <ListItem
                button
                onClick={handleProfileClick}
                sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
              >
                <ListItemIcon><AccountCircleIcon /></ListItemIcon>
                <ListItemText
                  primary={t('nav.profile')}
                  primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
                />
              </ListItem>
              <ListItem
                button
                onClick={handleLogout}
                sx={{ '&:hover': { bgcolor: alpha(theme.palette.error.main, 0.1) } }}
              >
                <ListItemIcon><LogoutIcon /></ListItemIcon>
                <ListItemText
                  primary={t('nav.logout')}
                  primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
                />
              </ListItem>
            </>
          ) : (
            <ListItem
              button
              onClick={() => { navigate('/login'); setMobileMenuOpen(false); }}
              sx={{ '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.1) } }}
            >
              <ListItemIcon><LoginIcon /></ListItemIcon>
              <ListItemText
                primary={t('nav.login')}
                primaryTypographyProps={{ fontSize: { xs: '0.9rem', sm: '1rem' } }}
              />
            </ListItem>
          )}
        </List>
      </Box>
    </Drawer>
  );

  return (
    <AppBar
      position="fixed"
      elevation={0}
      sx={{
        background: `linear-gradient(to right, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`,
        borderBottom: `1px solid ${alpha(theme.palette.common.white, 0.1)}`
      }}
    >
      <Toolbar
        sx={{
          minHeight: { xs: 56, sm: 64, md: 70 },
          justifyContent: 'space-between',
          px: { xs: 1, sm: 2, md: 3 }
        }}
      >
        {isSmallScreen && (
          <IconButton
            color="inherit"
            edge="start"
            onClick={handleMobileMenuToggle}
            sx={{
              mr: { xs: 1, sm: 2 },
              p: { xs: 1, sm: 1.5 }
            }}
          >
            <MenuIcon />
          </IconButton>
        )}

        <Typography
          variant="h6"
          component="div"
          sx={{
            cursor: 'pointer',
            fontFamily: 'Tajawal, sans-serif',
            fontSize: { xs: '0.9rem', sm: '1.1rem', md: '1.25rem', lg: '1.4rem' },
            fontWeight: { xs: 500, md: 600 },
            flexGrow: isSmallScreen ? 1 : 0,
            textAlign: isSmallScreen ? 'center' : 'left',
            mx: isSmallScreen ? 2 : 0
          }}
          onClick={() => navigate('/')}
        >
          {t('appName')}
        </Typography>

        {!isSmallScreen && (
          <>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: { sm: 1, md: 1.5, lg: 2 },
              flexWrap: 'nowrap'
            }}>
              <Button
                color="inherit"
                onClick={() => navigate('/about-us')}
                sx={{
                  fontFamily: 'Tajawal, sans-serif',
                  whiteSpace: 'nowrap',
                  fontSize: { sm: '0.8rem', md: '0.9rem', lg: '1rem' },
                  px: { sm: 1, md: 1.5, lg: 2 },
                  py: { sm: 0.5, md: 0.75, lg: 1 },
                  gap: { sm: 0.5, md: 0.75 },
                  minWidth: { sm: 'auto', md: 'auto' }
                }}
              >
                {t('aboutUs')}
              </Button>

              <Button
                color="inherit"
                startIcon={<ContactSupportIcon sx={{ mr: { sm: 0.5, md: 0.75 }, fontSize: { sm: '1rem', md: '1.1rem' } }} />}
                onClick={() => navigate('/contact-us')}
                sx={{
                  fontFamily: 'Tajawal, sans-serif',
                  whiteSpace: 'nowrap',
                  fontSize: { sm: '0.8rem', md: '0.9rem', lg: '1rem' },
                  px: { sm: 1, md: 1.5, lg: 2 },
                  py: { sm: 0.5, md: 0.75, lg: 1 },
                  gap: { sm: 0.5, md: 0.75 },
                  minWidth: { sm: 'auto', md: 'auto' },
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.common.white, 0.1)
                  }
                }}
              >
                {t('contact.title')}
              </Button>

              <Button
                color="inherit"
                startIcon={<PolicyIcon sx={{ mr: { sm: 0.5, md: 0.75 }, fontSize: { sm: '1rem', md: '1.1rem' } }} />}
                endIcon={<KeyboardArrowDownIcon sx={{ fontSize: { sm: '1rem', md: '1.1rem' } }} />}
                onClick={handlePoliciesMenuOpen}
                sx={{
                  fontFamily: 'Tajawal, sans-serif',
                  whiteSpace: 'nowrap',
                  fontSize: { sm: '0.8rem', md: '0.9rem', lg: '1rem' },
                  px: { sm: 1, md: 1.5, lg: 2 },
                  py: { sm: 0.5, md: 0.75, lg: 1 },
                  gap: { sm: 0.5, md: 0.75 },
                  minWidth: { sm: 'auto', md: 'auto' },
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.common.white, 0.1)
                  }
                }}
              >
                {i18n.language === 'ar' ? 'السياسات' : 'Policies'}
              </Button>

              <Button
                color="inherit"
                startIcon={<SearchIcon sx={{ fontSize: { sm: '1rem', md: '1.1rem' } }} />}
                onClick={() => navigate('/find-teacher')}
                sx={{
                  borderRadius: 2,
                  px: { sm: 1.5, md: 2, lg: 2.5 },
                  py: { sm: 0.5, md: 0.75, lg: 1 },
                  gap: { sm: 0.5, md: 0.75 },
                  gap: { sm: 0.5, md: 0.75, lg: 1 },
                  whiteSpace: 'nowrap',
                  fontSize: { sm: '0.8rem', md: '0.9rem', lg: '1rem' },
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.common.white, 0.1)
                  }
                }}
              >
                {t('nav.findTeacher')}
              </Button>

              <Button
                color="inherit"
                startIcon={<TranslateIcon sx={{ fontSize: { sm: '1rem', md: '1.1rem' } }} />}
                endIcon={<KeyboardArrowDownIcon sx={{ fontSize: { sm: '1rem', md: '1.1rem' } }} />}
                onClick={handleLangMenuOpen}
                sx={{
                  minWidth: { sm: 80, md: 100, lg: 120 },
                  borderRadius: 2,
                  px: { sm: 1, md: 1.5, lg: 2 },
                  py: { sm: 0.5, md: 0.75, lg: 1 },
                  gap: { sm: 0.5, md: 0.75 },
                  gap: { sm: 0.5, md: 0.75, lg: 1 },
                  whiteSpace: 'nowrap',
                  fontSize: { sm: '0.75rem', md: '0.85rem', lg: '1rem' },
                  '& .MuiButton-startIcon': {
                    margin: 0
                  },
                  '& .MuiButton-endIcon': {
                    margin: 0,
                    ml: 0.5
                  },
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.common.white, 0.1)
                  }
                }}
              >
                {i18n.language === 'ar' ? 'العربية' : 'English'}
              </Button>

              {user ? (
                <>
                  <Tooltip title={t('nav.profile')}>
                    <IconButton
                      onClick={handleMenuOpen}
                      sx={{
                        p: 0,
                        ml: { sm: 0.5, md: 1, lg: 1.5 },
                        border: `2px solid ${alpha(theme.palette.common.white, 0.2)}`,
                        '&:hover': {
                          border: `2px solid ${alpha(theme.palette.common.white, 0.3)}`,
                          transform: 'scale(1.05)'
                        },
                        transition: 'all 0.2s ease-in-out'
                      }}
                    >
                      {user.profile_picture_url ? (
                        <Avatar
                          src={user.profile_picture_url}
                          alt={user.full_name}
                          sx={{
                            width: { sm: 28, md: 32, lg: 36 },
                            height: { sm: 28, md: 32, lg: 36 }
                          }}
                        />
                      ) : (
                        <Avatar sx={{
                          width: { sm: 28, md: 32, lg: 36 },
                          height: { sm: 28, md: 32, lg: 36 },
                          bgcolor: theme.palette.secondary.main,
                          fontSize: { sm: '0.9rem', md: '1rem', lg: '1.1rem' }
                        }}>
                          {user.full_name?.charAt(0).toUpperCase()}
                        </Avatar>
                      )}
                    </IconButton>
                  </Tooltip>
                </>
              ) : (
                <Button
                  color="inherit"
                  startIcon={<LoginIcon sx={{ fontSize: { sm: '1rem', md: '1.1rem' } }} />}
                  onClick={() => navigate('/login')}
                  sx={{
                    borderRadius: 2,
                    px: { sm: 1.5, md: 2, lg: 2.5 },
                    py: { sm: 0.5, md: 0.75, lg: 1 },
                  gap: { sm: 0.5, md: 0.75 },
                    gap: { sm: 0.5, md: 0.75, lg: 1 },
                    whiteSpace: 'nowrap',
                    fontSize: { sm: '0.8rem', md: '0.9rem', lg: '1rem' },
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.common.white, 0.1),
                      transform: 'translateY(-1px)'
                    },
                    transition: 'all 0.2s ease-in-out'
                  }}
                >
                  {t('nav.login')}
                </Button>
              )}
            </Box>
          </>
        )}

        {/* Policies Menu */}
        <Menu
          anchorEl={policiesMenuAnchor}
          open={Boolean(policiesMenuAnchor)}
          onClose={handlePoliciesMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          PaperProps={{
            elevation: 0,
            sx: {
              mt: 1.5,
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          }}
        >
          <MenuItem onClick={() => handlePolicyClick('/privacy-policy')}>
            <ListItemIcon>
              <SecurityIcon fontSize="small" />
            </ListItemIcon>
            {t('footer.privacy')}
          </MenuItem>
          <MenuItem onClick={() => handlePolicyClick('/refund-policy')}>
            <ListItemIcon>
              <PolicyIcon fontSize="small" sx={{ mr: 0.5 }} />
            </ListItemIcon>
            {i18n.language === 'ar' ? 'سياسة الاسترداد' : 'Refund Policy'}
          </MenuItem>
          <MenuItem onClick={() => handlePolicyClick('/booking-payment-policy')}>
            <ListItemIcon>
              <PolicyIcon fontSize="small" sx={{ mr: 0.5 }} />
            </ListItemIcon>
            {i18n.language === 'ar' ? 'سياسة الحجز والدفع' : 'Booking & Payment Policy'}
          </MenuItem>
          <MenuItem onClick={() => handlePolicyClick('/booking-cancellation-policy')}>
            <ListItemIcon>
              <PolicyIcon fontSize="small" sx={{ mr: 0.5 }} />
            </ListItemIcon>
            {i18n.language === 'ar' ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation Policy'}
          </MenuItem>
          <MenuItem onClick={() => handlePolicyClick('/terms-and-conditions')}>
            <ListItemIcon>
              <PolicyIcon fontSize="small" sx={{ mr: 0.5 }} />
            </ListItemIcon>
            {i18n.language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions'}
          </MenuItem>
        </Menu>

        <Menu
          anchorEl={langMenuAnchor}
          open={Boolean(langMenuAnchor)}
          onClose={handleLangMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          PaperProps={{
            elevation: 0,
            sx: {
              mt: 1.5,
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          }}
        >
          <MenuItem onClick={() => handleLanguageChange('ar')}>
            العربية
          </MenuItem>
          <MenuItem onClick={() => handleLanguageChange('en')}>
            English
          </MenuItem>
        </Menu>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          PaperProps={{
            elevation: 0,
            sx: {
              mt: 1.5,
              overflow: 'visible',
              filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
              '&:before': {
                content: '""',
                display: 'block',
                position: 'absolute',
                top: 0,
                right: 14,
                width: 10,
                height: 10,
                bgcolor: 'background.paper',
                transform: 'translateY(-50%) rotate(45deg)',
                zIndex: 0,
              },
            },
          }}
        >
          <MenuItem onClick={handleProfileClick}>
            <ListItemIcon>
              <AccountCircleIcon fontSize="small" />
            </ListItemIcon>
            {t('nav.profile')}
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleLogout}>
            <ListItemIcon>
              <LogoutIcon fontSize="small" />
            </ListItemIcon>
            {t('nav.logout')}
          </MenuItem>
        </Menu>

        {mobileMenu}
      </Toolbar>
    </AppBar>
  );
};

export default Header;
