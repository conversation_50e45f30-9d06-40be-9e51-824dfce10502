const db = require('../config/db');
const moment = require('moment');
const { calculateCommission } = require('../utils/commission');

class MeetingStatusService {
  /**
   * Determine the logical status for a booking based on current time and pending issues.
   * This helper is exported for reuse in debug routes to guarantee consistency.
   * @param {Object} booking Row from bookings table (must include id, datetime, duration, status)
   * @returns {Promise<'scheduled'|'ongoing'|'completed'|'issue_reported'>}
   */
  static async determineBookingStatus(booking) {
    // Don't change cancelled bookings
    if (booking.status === 'cancelled') {
      return 'cancelled';
    }

    const now = moment.utc();
    const bookingStart = moment.utc(booking.datetime);
    const bookingEnd = moment.utc(booking.datetime).add(booking.duration || 50, 'minutes');

    let newStatus;
    if (now.isBefore(bookingStart)) {
      newStatus = 'scheduled';
    } else if (now.isBetween(bookingStart, bookingEnd)) {
      newStatus = 'ongoing';
    } else {
      try {
        const result = await db.pool.execute(
          `SELECT 1 FROM meeting_issues WHERE booking_id = ? AND status = 'pending' AND issue_type IN ('pending','teacher_absent','technical_issue') LIMIT 1`,
          [booking.id]
        );
        
        // Handle different result structures
        let issueRows;
        if (Array.isArray(result) && result.length > 0) {
          issueRows = result[0];
        } else if (result && result[0]) {
          issueRows = result[0];
        } else {
          issueRows = [];
        }
        
        if (issueRows.length === 0) {
          // تحقق إذا كان الحجز مكتمل ولم يجب الطالب على الفيدباك
          if (booking.status === 'completed') {
            // نبحث عن أي سجل في meeting_issues لهذا الحجز (أي نوع)
            const [anyIssueRows] = await db.pool.execute(
              `SELECT * FROM meeting_issues WHERE booking_id = ? LIMIT 1`,
              [booking.id]
            );
            if (anyIssueRows.length === 0) {
              // لا يوجد أي فيدباك، ننشئ pending ونحول الحالة ل issue_reported
              const [meetingRows] = await db.pool.execute(
                `SELECT id as meeting_id, student_id FROM meetings WHERE student_id IS NOT NULL AND meeting_date = ? LIMIT 1`,
                [booking.datetime]
              );
              let meeting_id = null;
              let student_id = null;
              if (meetingRows.length > 0) {
                meeting_id = meetingRows[0].meeting_id;
                student_id = meetingRows[0].student_id;
              }
              if (meeting_id && student_id) {
                // Check if pending issue already exists to avoid duplicate
                const [existingPending] = await db.pool.execute(
                  `SELECT id FROM meeting_issues WHERE meeting_id = ? AND user_id = ? AND issue_type = 'pending' LIMIT 1`,
                  [meeting_id, student_id]
                );

                if (existingPending.length === 0) {
                  // No existing pending issue, create one
                  await db.pool.execute(
                    `INSERT INTO meeting_issues (meeting_id, user_id, booking_id, issue_type, description, status, created_at)
                     VALUES (?, ?, ?, 'pending', '', 'pending', NOW())`,
                    [meeting_id, student_id, booking.id]
                  );
                  console.log(`Created pending issue for meeting ${meeting_id}, user ${student_id}, booking ${booking.id}`);
                } else {
                  console.log(`Pending issue already exists for meeting ${meeting_id}, user ${student_id}`);
                }
                // تحديث حالة الحجز في قاعدة البيانات
                await db.pool.execute(
                  'UPDATE bookings SET status = ? WHERE id = ?',
                  ['issue_reported', booking.id]
                );
                // لوج تحقق من التحديث
                const [check] = await db.pool.execute('SELECT status FROM bookings WHERE id = ?', [booking.id]);
                console.log('Booking status after update:', check[0]);
                newStatus = 'issue_reported';
              } else {
                newStatus = 'issue_reported';
              }
            } else {
              // يوجد سجل لكن ليس pending، نترك الحالة كما هي
              newStatus = booking.status;
            }
          } else {
            // لم يكن مكتمل، نترك الحالة كما هي (أو نطبق منطق آخر إذا أردت)
            newStatus = 'completed';
          }
        } else {
          newStatus = 'issue_reported';
        }
      } catch (error) {
        console.error('Error checking meeting issues for booking', booking.id, ':', error);
        newStatus = 'completed';
      }
    }
    return newStatus;
  }

  constructor() {
    this.updateInterval = null;
  }

  /**
   * Start the automatic meeting status update service
   */
  start() {
    console.log('Starting meeting status update service...');
    
    // Update immediately on start
    this.updateMeetingStatuses();
    
    // Then update every minute
    this.updateInterval = setInterval(() => {
      this.updateMeetingStatuses();
    }, 60000); // 60 seconds
  }

  /**
   * Stop the automatic meeting status update service
   */
  stop() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      console.log('Meeting status update service stopped');
    }
  }

  /**
   * Update meeting statuses based on current time
   */
  async updateMeetingStatuses() {
    try {
      const now = moment.utc().format('YYYY-MM-DD HH:mm:ss');

      // Get all scheduled meetings
      let meetings = [];
      try {
        const meetingsResult = await db.pool.execute(`
          SELECT id, meeting_date, duration, status
          FROM meetings
          WHERE status IN ('scheduled', 'ongoing', 'completed')
        `);
        meetings = (meetingsResult && Array.isArray(meetingsResult) && meetingsResult[0]) ? meetingsResult[0] : [];
      } catch (meetingError) {
        console.error('Error fetching meetings:', meetingError);
        meetings = [];
      }

      // Get all bookings that need status updates (including completed ones that might have issues)
      let bookings = [];
      try {
        const bookingsResult = await db.pool.execute(`
          SELECT id, datetime, duration, status
          FROM bookings
          WHERE status IN ('scheduled', 'ongoing', 'completed', 'issue_reported')
          AND status != 'cancelled'
        `);
        bookings = (bookingsResult && Array.isArray(bookingsResult) && bookingsResult[0]) ? bookingsResult[0] : [];
      } catch (bookingError) {
        console.error('Error fetching bookings:', bookingError);
        bookings = [];
      }

      let updatedCount = 0;

      // Update meetings
      for (const meeting of meetings) {
        const meetingStart = moment.utc(meeting.meeting_date);
        const meetingEnd = moment.utc(meeting.meeting_date).add(meeting.duration, 'minutes');
        const currentTime = moment.utc(now);

        let newStatus = meeting.status;

        // Determine new status based on time
        if (currentTime.isBefore(meetingStart)) {
          newStatus = 'scheduled';
        } else if (currentTime.isBetween(meetingStart, meetingEnd)) {
          newStatus = 'ongoing';
        } else if (currentTime.isAfter(meetingEnd)) {
          // Before marking completed, check for any active issues linked to this meeting
          const result = await db.pool.execute(
            `SELECT 1 FROM meeting_issues WHERE meeting_id = ? AND status = 'pending' AND issue_type IN ('pending','teacher_absent','technical_issue') LIMIT 1`,
            [meeting.id]
          );
          const issueRows = (result && Array.isArray(result) && result[0]) ? result[0] : [];
          if (issueRows.length > 0) {
            newStatus = 'issue_reported';
          } else {
            newStatus = 'completed';
          }
        }

        // Update status if it has changed
        if (newStatus !== meeting.status) {
          await db.pool.execute(
            'UPDATE meetings SET status = ?, updated_at = NOW() WHERE id = ?',
            [newStatus, meeting.id]
          );

          updatedCount++;
          console.log(`Updated meeting ${meeting.id} status from ${meeting.status} to ${newStatus}`);

          // If meeting is completed, apply commission system
          if (newStatus === 'completed' && meeting.status !== 'completed') {
            await this.applyCommissionForCompletedMeeting(meeting.id);
          }
        }
      }

      // Update bookings
      for (const booking of bookings) {
        // Don't change cancelled bookings
        if (booking.status === 'cancelled') {
          continue;
        }

        const bookingStart = moment.utc(booking.datetime);
        const bookingEnd = moment.utc(booking.datetime).add(booking.duration || 50, 'minutes');
        const currentTime = moment.utc(now);

        let newStatus = booking.status;

        // Determine new status based on time
        if (currentTime.isBefore(bookingStart)) {
          newStatus = 'scheduled';
        } else if (currentTime.isBetween(bookingStart, bookingEnd)) {
          newStatus = 'ongoing';
        } else if (currentTime.isAfter(bookingEnd)) {
          // Before marking completed, check for any active issues
          const [issueRows] = await db.pool.execute(
            `SELECT * FROM meeting_issues WHERE booking_id = ? AND issue_type IN ('pending', 'teacher_absent', 'technical_issue') AND status = 'pending' LIMIT 1`,
            [booking.id]
          );
          if (issueRows.length === 0) {
            // تحقق إذا كان الحجز مكتمل ولم يجب الطالب على الفيدباك
            if (booking.status === 'completed') {
              // نبحث عن أي سجل في meeting_issues لهذا الحجز (أي نوع)
              const [anyIssueRows] = await db.pool.execute(
                `SELECT * FROM meeting_issues WHERE booking_id = ? LIMIT 1`,
                [booking.id]
              );
              if (anyIssueRows.length === 0) {
                // لا يوجد أي فيدباك، ننشئ pending ونحول الحالة ل issue_reported
                const [meetingRows] = await db.pool.execute(
                  `SELECT id as meeting_id, student_id FROM meetings WHERE student_id IS NOT NULL AND meeting_date = ? LIMIT 1`,
                  [booking.datetime]
                );
                let meeting_id = null;
                let student_id = null;
                if (meetingRows.length > 0) {
                  meeting_id = meetingRows[0].meeting_id;
                  student_id = meetingRows[0].student_id;
                }
                if (meeting_id && student_id) {
                  // Check if pending issue already exists to avoid duplicate
                  const [existingPending] = await db.pool.execute(
                    `SELECT id FROM meeting_issues WHERE meeting_id = ? AND user_id = ? AND issue_type = 'pending' LIMIT 1`,
                    [meeting_id, student_id]
                  );

                  if (existingPending.length === 0) {
                    // No existing pending issue, create one
                    await db.pool.execute(
                      `INSERT INTO meeting_issues (meeting_id, user_id, booking_id, issue_type, description, status, created_at)
                       VALUES (?, ?, ?, 'pending', '', 'pending', NOW())`,
                      [meeting_id, student_id, booking.id]
                    );
                    console.log(`Created pending issue for meeting ${meeting_id}, user ${student_id}, booking ${booking.id}`);
                  } else {
                    console.log(`Pending issue already exists for meeting ${meeting_id}, user ${student_id}`);
                  }
                  newStatus = 'issue_reported';
                } else {
                  newStatus = 'issue_reported';
                }
              } else {
                // يوجد سجل لكن ليس pending، نترك الحالة كما هي
                newStatus = booking.status;
              }
            } else {
              // لم يكن مكتمل، نطبق المنطق السابق
              const [meetingRows] = await db.pool.execute(
                `SELECT id as meeting_id, student_id FROM meetings WHERE student_id IS NOT NULL AND meeting_date = ? LIMIT 1`,
                [booking.datetime]
              );
              let meeting_id = null;
              let student_id = null;
              if (meetingRows.length > 0) {
                meeting_id = meetingRows[0].meeting_id;
                student_id = meetingRows[0].student_id;
              }
              if (meeting_id && student_id) {
                // Check if pending issue already exists to avoid duplicate
                const [existingPending] = await db.pool.execute(
                  `SELECT id FROM meeting_issues WHERE meeting_id = ? AND user_id = ? AND issue_type = 'pending' LIMIT 1`,
                  [meeting_id, student_id]
                );

                if (existingPending.length === 0) {
                  // No existing pending issue, create one
                  await db.pool.execute(
                    `INSERT INTO meeting_issues (meeting_id, user_id, booking_id, issue_type, description, status, created_at)
                     VALUES (?, ?, ?, 'pending', '', 'pending', NOW())`,
                    [meeting_id, student_id, booking.id]
                  );
                  console.log(`Created pending issue for meeting ${meeting_id}, user ${student_id}, booking ${booking.id}`);
                } else {
                  console.log(`Pending issue already exists for meeting ${meeting_id}, user ${student_id}`);
                }
                newStatus = 'issue_reported';
              } else {
                newStatus = 'issue_reported';
              }
            }
          } else {
            newStatus = 'issue_reported';
          }
        }

        // Update status if it has changed
        if (newStatus !== booking.status) {
          await db.pool.execute(
            'UPDATE bookings SET status = ?, updated_at = NOW() WHERE id = ?',
            [newStatus, booking.id]
          );

          updatedCount++;
          console.log(`Updated booking ${booking.id} status from ${booking.status} to ${newStatus}`);
        }
      }

      if (updatedCount > 0) {
        console.log(`Updated ${updatedCount} meeting/booking statuses at ${now}`);
      }

    } catch (error) {
      console.error('Error updating meeting statuses:', error);
    }
  }

  /**
   * Update a specific booking status based on issues
   * @param {string} bookingId - Booking ID
   * @returns {Promise<string>} - New status
   */
  async updateSpecificBookingStatus(bookingId) {
    try {
      const bookingsResult = await db.pool.execute(
        'SELECT id, datetime, duration, status FROM bookings WHERE id = ?',
        [bookingId]
      );
      const bookings = (bookingsResult && Array.isArray(bookingsResult) && bookingsResult[0]) ? bookingsResult[0] : [];

      if (bookings.length === 0) {
        throw new Error('Booking not found');
      }

      const booking = bookings[0];
      const now = moment.utc();
      const bookingStart = moment.utc(booking.datetime);
      const bookingEnd = moment.utc(booking.datetime).add(booking.duration || 50, 'minutes');

      let newStatus = booking.status;

      // Don't change cancelled bookings
      if (booking.status === 'cancelled') {
        return booking.status;
      }

      // Determine new status based on time
      if (now.isBefore(bookingStart)) {
        newStatus = 'scheduled';
      } else if (now.isBetween(bookingStart, bookingEnd)) {
        newStatus = 'ongoing';
      } else if (now.isAfter(bookingEnd)) {
        // Before marking completed, check for any active issues
        const result = await db.pool.execute(
          `SELECT 1 FROM meeting_issues WHERE booking_id = ? AND status = 'pending' AND issue_type IN ('pending','teacher_absent','technical_issue') LIMIT 1`,
          [bookingId]
        );
        const issueRows = (result && Array.isArray(result) && result[0]) ? result[0] : [];
        newStatus = issueRows.length > 0 ? 'issue_reported' : 'completed';
      }

      // Update status if it has changed
      if (newStatus !== booking.status) {
        await db.pool.execute(
          'UPDATE bookings SET status = ?, updated_at = NOW() WHERE id = ?',
          [newStatus, bookingId]
        );
        
        console.log(`Updated booking ${bookingId} status from ${booking.status} to ${newStatus}`);
      }

      return newStatus;

    } catch (error) {
      console.error('Error updating specific booking status:', error);
      throw error;
    }
  }

  /**
   * Update a specific meeting status
   * @param {string} meetingId - Meeting ID
   * @returns {Promise<string>} - New status
   */
  async updateSpecificMeetingStatus(meetingId) {
    try {
      const meetingsResult = await db.pool.execute(
        'SELECT id, meeting_date, duration, status FROM meetings WHERE id = ?',
        [meetingId]
      );
      const meetings = (meetingsResult && Array.isArray(meetingsResult) && meetingsResult[0]) ? meetingsResult[0] : [];

      if (meetings.length === 0) {
        throw new Error('Meeting not found');
      }

      const meeting = meetings[0];
      const now = moment.utc();
      const meetingStart = moment.utc(meeting.meeting_date);
      const meetingEnd = moment.utc(meeting.meeting_date).add(meeting.duration, 'minutes');

      let newStatus = meeting.status;

      // Don't change cancelled meetings
      if (meeting.status === 'cancelled') {
        return meeting.status;
      }

      // Determine new status based on time
      if (now.isBefore(meetingStart)) {
        newStatus = 'scheduled';
      } else if (now.isBetween(meetingStart, meetingEnd)) {
        newStatus = 'ongoing';
      } else if (now.isAfter(meetingEnd)) {
        // Before marking completed, check for any active issues linked to this meeting
                  const result = await db.pool.execute(
            `SELECT 1 FROM meeting_issues WHERE meeting_id = ? AND status = 'pending' AND issue_type IN ('pending','teacher_absent','technical_issue') LIMIT 1`,
            [meetingId]
          );
          const issueRows = (result && Array.isArray(result) && result[0]) ? result[0] : [];
          newStatus = issueRows.length > 0 ? 'issue_reported' : 'completed';
      }

      // Update status if it has changed
      if (newStatus !== meeting.status) {
        await db.pool.execute(
          'UPDATE meetings SET status = ?, updated_at = NOW() WHERE id = ?',
          [newStatus, meetingId]
        );
        
        console.log(`Updated meeting ${meetingId} status from ${meeting.status} to ${newStatus}`);
      }

      return newStatus;

    } catch (error) {
      console.error('Error updating specific meeting status:', error);
      throw error;
    }
  }

  /**
   * Get meeting status without updating database
   * @param {string} meetingDate - Meeting date
   * @param {number} duration - Meeting duration in minutes
   * @param {string} currentStatus - Current status from database
   * @returns {string} - Calculated status
   */
  calculateMeetingStatus(meetingDate, duration, currentStatus) {
    // Don't change cancelled meetings
    if (currentStatus === 'cancelled') {
      return 'cancelled';
    }

    const now = moment.utc();
    const meetingStart = moment.utc(meetingDate);
    const meetingEnd = moment.utc(meetingDate).add(duration, 'minutes');

    if (now.isBefore(meetingStart)) {
      return 'scheduled';
    } else if (now.isBetween(meetingStart, meetingEnd)) {
      return 'ongoing';
    } else if (now.isAfter(meetingEnd)) {
      return 'completed';
    }

    return 'scheduled';
  }

  /**
   * Apply commission system when a meeting is completed
   * @param {string} meetingId - Meeting ID
   */
  async applyCommissionForCompletedMeeting(meetingId) {
    const connection = await db.pool.getConnection();

    try {
      await connection.beginTransaction();

      // Check if commission has already been applied
      const existingEarningsResult = await connection.query(
        'SELECT id FROM admin_earnings WHERE meeting_id = ?',
        [meetingId]
      );
      const existingEarnings = (existingEarningsResult && Array.isArray(existingEarningsResult) && existingEarningsResult[0]) ? existingEarningsResult[0] : [];

      if (existingEarnings.length > 0) {
        console.log(`Commission already applied for meeting ${meetingId}`);
        await connection.rollback();
        return;
      }

      // Get meeting details with teacher info
      const meetingsResult = await connection.query(
        `SELECT m.*, tp.user_id as teacher_user_id
         FROM meetings m
         JOIN teacher_profiles tp ON m.teacher_id = tp.id
         WHERE m.id = ?`,
        [meetingId]
      );
      const meetings = (meetingsResult && Array.isArray(meetingsResult) && meetingsResult[0]) ? meetingsResult[0] : [];

      if (meetings.length === 0) {
        console.log(`Meeting ${meetingId} not found for commission calculation`);
        await connection.rollback();
        return;
      }

      const meeting = meetings[0];
      const lessonAmount = parseFloat(meeting.amount);

      // Calculate commission
      const commission = calculateCommission(lessonAmount);

      // Add teacher earnings to their balance (after commission)
      await connection.query(
        'UPDATE users SET balance = balance + ? WHERE id = ?',
        [commission.teacherEarnings, meeting.teacher_user_id]
      );

      // Record admin earnings
      await connection.query(
        `INSERT INTO admin_earnings
         (meeting_id, teacher_id, student_id, lesson_amount, commission_rate, commission_amount, teacher_earnings)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          meetingId,
          meeting.teacher_user_id,
          meeting.student_id,
          lessonAmount,
          commission.commissionRate,
          commission.commissionAmount,
          commission.teacherEarnings
        ]
      );

      await connection.commit();
      console.log(`Commission applied for meeting ${meetingId}: Teacher gets $${commission.teacherEarnings}, Admin gets $${commission.commissionAmount}`);

    } catch (error) {
      await connection.rollback();
      console.error(`Error applying commission for meeting ${meetingId}:`, error);
    } finally {
      connection.release();
    }
  }
}

// Create singleton instance
const meetingStatusService = new MeetingStatusService();

// Export both the singleton instance and the class for static method access
module.exports = meetingStatusService;
module.exports.MeetingStatusService = MeetingStatusService;
