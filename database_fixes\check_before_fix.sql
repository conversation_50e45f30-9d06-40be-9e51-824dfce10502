-- Check current state before applying fixes
-- Run this script first to understand the current situation

-- 1. Count of NULL booking_id records
SELECT 
    'Total meeting_issues records' as metric,
    COUNT(*) as count
FROM meeting_issues
UNION ALL
SELECT 
    'Records with NULL booking_id' as metric,
    COUNT(*) as count
FROM meeting_issues 
WHERE booking_id IS NULL
UNION ALL
SELECT 
    'Records with valid booking_id' as metric,
    COUNT(*) as count
FROM meeting_issues 
WHERE booking_id IS NOT NULL;

-- 2. Show NULL booking_id records with details
SELECT 
    mi.id,
    mi.meeting_id,
    mi.user_id,
    mi.issue_type,
    mi.status,
    mi.created_at,
    m.student_id,
    m.teacher_id,
    m.meeting_date,
    u.full_name as student_name,
    t.full_name as teacher_name
FROM meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
JOIN users u ON m.student_id = u.id
JOIN users t ON m.teacher_id = t.id
WHERE mi.booking_id IS NULL
ORDER BY mi.created_at DESC;

-- 3. Check if we can find potential bookings for NULL records
SELECT 
    mi.id as issue_id,
    mi.meeting_id,
    mi.issue_type,
    m.meeting_date,
    m.student_id,
    m.teacher_id,
    u.full_name as student_name,
    t.full_name as teacher_name,
    b.id as potential_booking_id,
    b.datetime as booking_datetime,
    b.status as booking_status,
    ABS(TIMESTAMPDIFF(MINUTE, b.datetime, m.meeting_date)) as time_diff_minutes
FROM meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
JOIN users u ON m.student_id = u.id
JOIN users t ON m.teacher_id = t.id
LEFT JOIN bookings b ON b.student_id = m.student_id
LEFT JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id AND tp.user_id = m.teacher_id
WHERE mi.booking_id IS NULL
  AND b.id IS NOT NULL
  AND DATE(b.datetime) = DATE(m.meeting_date)
  AND b.status != 'cancelled'
  AND ABS(TIMESTAMPDIFF(MINUTE, b.datetime, m.meeting_date)) <= 120
ORDER BY mi.id, time_diff_minutes ASC;

-- 4. Check for orphaned records (meetings without any possible bookings)
SELECT 
    mi.id as issue_id,
    mi.meeting_id,
    mi.issue_type,
    m.meeting_date,
    m.student_id,
    m.teacher_id,
    u.full_name as student_name,
    t.full_name as teacher_name,
    'No matching booking found' as note
FROM meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
JOIN users u ON m.student_id = u.id
JOIN users t ON m.teacher_id = t.id
WHERE mi.booking_id IS NULL
  AND NOT EXISTS (
    SELECT 1 
    FROM bookings b
    JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
    WHERE b.student_id = m.student_id
      AND tp.user_id = m.teacher_id
      AND DATE(b.datetime) = DATE(m.meeting_date)
      AND b.status != 'cancelled'
      AND ABS(TIMESTAMPDIFF(MINUTE, b.datetime, m.meeting_date)) <= 120
  )
ORDER BY mi.created_at DESC;

-- 5. Check current table structure
DESCRIBE meeting_issues;
