# إصلاح عرض رسالة "تم حذف الحساب" في صفحة تسجيل الدخول

## المشكلة
رسالة "تم حذف الحساب" لم تكن تظهر في أعلى صفحة تسجيل الدخول عندما يحاول المستخدم المحذوف تسجيل الدخول.

## الحل المطبق

### 1. حفظ رسالة الحالة في localStorage
```javascript
// في جميع المكونات التي تكتشف المستخدم المحذوف
localStorage.setItem('accountStatusMessage', JSON.stringify({
  message: 'تم حذف هذا الحساب',
  accountStatus: 'deleted',
  deleteScheduledAt: deleteScheduledAt
}));
```

### 2. تحديث AuthContext
```javascript
// client/src/contexts/AuthContext.js
.catch(error => {
  // حفظ معلومات حالة الحساب من الخطأ
  if (error.response && error.response.data && error.response.data.accountStatus) {
    localStorage.setItem('accountStatusMessage', JSON.stringify({
      message: error.response.data.message,
      accountStatus: error.response.data.accountStatus,
      deleteScheduledAt: error.response.data.deleteScheduledAt
    }));
  }
  handleLogout();
});
```

### 3. تحديث صفحة تسجيل الدخول
```javascript
// client/src/pages/auth/Login.js
useEffect(() => {
  // التحقق من navigation state أولاً
  if (location.state && location.state.message) {
    setAccountStatusMessage(location.state.message);
    setError(location.state.message);
    return;
  }

  // التحقق من localStorage للرسائل المحفوظة
  const savedMessage = localStorage.getItem('accountStatusMessage');
  if (savedMessage) {
    const messageData = JSON.parse(savedMessage);
    setAccountStatusMessage(messageData.message);
    setError(messageData.message);
    
    // تحديث location.state للتوافق
    location.state = {
      message: messageData.message,
      accountStatus: messageData.accountStatus
    };
    
    // حذف الرسالة بعد عرضها
    localStorage.removeItem('accountStatusMessage');
  }
}, [location.state]);
```

### 4. تحديث جميع المكونات
- `UserStatusHandler.js` - حفظ رسالة عند اكتشاف المستخدم المحذوف
- `UserStatusChecker.js` - حفظ رسالة عند إعادة التوجيه
- `ProtectedRoute.js` - حفظ رسالة عند منع الوصول

## كيف يعمل النظام الآن

### 1. اكتشاف المستخدم المحذوف
```
المستخدم يحاول الوصول → التحقق من الحالة → اكتشاف "deleted"
↓
حفظ الرسالة في localStorage → تسجيل خروج → إعادة توجيه لـ /login
```

### 2. عرض الرسالة في صفحة تسجيل الدخول
```
تحميل صفحة /login → التحقق من localStorage → عرض الرسالة → حذف الرسالة
```

### 3. أنواع الرسائل المدعومة
- **deleted**: "تم حذف هذا الحساب"
- **pending_deletion**: "هذا الحساب مجدول للحذف"
- **unauthorized**: "انتهت صلاحية جلسة تسجيل الدخول"

## المزايا الجديدة

✅ **رسالة واضحة** في أعلى صفحة تسجيل الدخول
✅ **تعمل من أي مكان** في التطبيق
✅ **لا تفقد الرسالة** عند إعادة التحميل
✅ **تحذف الرسالة تلقائياً** بعد العرض
✅ **تدعم جميع حالات المستخدم**

## الملفات المحدثة
- `client/src/contexts/AuthContext.js`
- `client/src/pages/auth/Login.js`
- `client/src/utils/userStatusHandler.js`
- `client/src/components/UserStatusChecker.js`
- `client/src/components/ProtectedRoute.js`

## النتيجة
الآن عندما يحاول المستخدم المحذوف تسجيل الدخول، ستظهر رسالة واضحة في أعلى صفحة تسجيل الدخول تخبره أن حسابه تم حذفه.
