# تطبيق نظام الحذف الناعم - دليل التشغيل

## 🚀 خطوات التطبيق

### المرحلة 1: تحديث قاعدة البيانات

```sql
-- تشغيل هذا الملف لإضافة حقول الحذف الناعم
SOURCE server/database/migrations/add_soft_delete_fields.sql;
```

**أو تشغيل الأوامر يدوياً:**

```sql
-- إضافة حقول الحذف الناعم
ALTER TABLE users 
ADD COLUMN deleted_at DATETIME NULL DEFAULT NULL,
ADD COLUMN deletion_reason VARCHAR(500) NULL DEFAULT NULL,
ADD COLUMN deleted_by INT NULL DEFAULT NULL;

-- إضافة فهارس للأداء
CREATE INDEX idx_users_deleted_at ON users(deleted_at);
CREATE INDEX idx_users_role_deleted ON users(role, deleted_at);

-- إ<PERSON><PERSON><PERSON><PERSON> قيد خارجي
ALTER TABLE users 
ADD CONSTRAINT fk_users_deleted_by 
FOREIGN KEY (deleted_by) REFERENCES users(id) 
ON DELETE SET NULL;
```

### المرحلة 2: إعادة تشغيل الخادم

```bash
# إعادة تشغيل الخادم لتطبيق التحديثات
npm restart
# أو
node server/index.js
```

## 🎯 كيف يعمل النظام الجديد

### الحذف الناعم (Soft Delete):
- **لا يحذف** البيانات من قاعدة البيانات
- **يضع علامة** على المستخدم كمحذوف
- **يحتفظ** بجميع البيانات للمراجعة أو الاسترداد

### مثال على الحذف الناعم:
```sql
-- قبل الحذف
SELECT id, full_name, email, deleted_at FROM users WHERE id = 123;
-- النتيجة: 123 | أحمد محمد | <EMAIL> | NULL

-- بعد الحذف الناعم
SELECT id, full_name, email, deleted_at FROM users WHERE id = 123;
-- النتيجة: 123 | أحمد محمد | <EMAIL> | 2024-07-29 10:30:00
```

## 📋 الوظائف الجديدة

### 1. صفحة المستخدمين المحذوفين (للمديرين)
**الرابط:** `/admin/deleted-users`

**الوظائف:**
- عرض قائمة المستخدمين المحذوفين
- البحث والفلترة
- استرداد المستخدمين
- الحذف النهائي (إذا لزم الأمر)

### 2. عرض الطلاب المحذوفين في صفحات المعلم
**في صفحة الحجوزات:**
```
الطالب: أحمد محمد (محذوف)
التاريخ: 15 يناير 2024
الحالة: مكتمل
```

### 3. إحصائيات الحذف
**الرابط:** `/admin/deleted-users/stats/overview`

**البيانات:**
- عدد الطلاب المحذوفين
- عدد المعلمين المحذوفين
- إجمالي المحذوفين
- إجمالي النشطين

## 🔧 API الجديد

### استرداد مستخدم محذوف:
```javascript
POST /admin/deleted-users/:id/restore
```

### حذف نهائي:
```javascript
DELETE /admin/deleted-users/:id/permanent
```

### عرض المحذوفين:
```javascript
GET /admin/deleted-users
```

## 🛡️ الأمان والحماية

### 1. الفلترة التلقائية:
جميع الاستعلامات تتجاهل المستخدمين المحذوفين تلقائياً:
```sql
-- مثال: البحث عن المعلمين
SELECT * FROM users 
WHERE role = 'platform_teacher' 
AND deleted_at IS NULL  -- ← يضاف تلقائياً
```

### 2. عرض آمن للبيانات:
```sql
-- في صفحة الحجوزات
CASE 
  WHEN u.deleted_at IS NOT NULL THEN CONCAT(u.full_name, ' (محذوف)')
  ELSE u.full_name 
END as student_name
```

## 📊 مقارنة النظام القديم والجديد

| الجانب | النظام القديم | النظام الجديد |
|---------|---------------|---------------|
| **حذف البيانات** | نهائي ❌ | قابل للاسترداد ✅ |
| **صفحات المعلم** | تنكسر ❌ | تعمل بشكل طبيعي ✅ |
| **التقارير** | غير دقيقة ❌ | دقيقة ومفصلة ✅ |
| **الامتثال القانوني** | صعب ❌ | مرن ✅ |
| **سرعة التطبيق** | سريع ✅ | متوسط ⚠️ |

## 🧪 الاختبار

### اختبار الحذف الناعم:
1. حذف طالب من لوحة الإدارة
2. التحقق من أن الطالب لا يظهر في القوائم
3. التحقق من أن حجوزاته تظهر كـ "طالب محذوف"
4. استرداد الطالب من صفحة المحذوفين
5. التحقق من عودة البيانات كما كانت

### اختبار الحذف المجدول:
1. طلب حذف حساب من الملف الشخصي
2. تأكيد الحذف برمز التحقق
3. انتظار 10 أيام أو تشغيل السكريبت يدوياً
4. التحقق من الحذف الناعم

## ⚠️ تحذيرات مهمة

### 1. النسخ الاحتياطية:
```bash
# أخذ نسخة احتياطية قبل التطبيق
mysqldump -u username -p database_name > backup_before_soft_delete.sql
```

### 2. الأداء:
- الاستعلامات قد تكون أبطأ قليلاً
- يُنصح بمراقبة الأداء بعد التطبيق

### 3. مساحة التخزين:
- البيانات المحذوفة تبقى في قاعدة البيانات
- قد تحتاج لتنظيف دوري للبيانات القديمة

## 🔄 العودة للنظام القديم (إذا لزم الأمر)

```sql
-- حذف نهائي لجميع المستخدمين المحذوفين ناعم
-- تحذير: هذا سيحذف البيانات نهائياً!

-- 1. حذف البيانات المرتبطة
DELETE FROM bookings WHERE student_id IN (SELECT id FROM users WHERE deleted_at IS NOT NULL);
-- ... باقي الجداول

-- 2. حذف المستخدمين
DELETE FROM users WHERE deleted_at IS NOT NULL;

-- 3. حذف الحقول الجديدة
ALTER TABLE users 
DROP COLUMN deleted_at,
DROP COLUMN deletion_reason,
DROP COLUMN deleted_by;
```

## 📞 الدعم

في حالة وجود مشاكل:
1. تحقق من سجلات الخادم
2. تأكد من تطبيق تحديثات قاعدة البيانات
3. راجع الاستعلامات للتأكد من إضافة `deleted_at IS NULL`

---

**تاريخ التطبيق:** 2024-07-29  
**الإصدار:** 2.0 - Soft Delete Implementation
