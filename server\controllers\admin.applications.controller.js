const db = require('../db');

exports.getApplications = async (req, res) => {
    const connection = await db.pool.getConnection();
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const status = req.query.status;
        const search = req.query.search;

        let query = `
            SELECT 
                tp.*,
                u.full_name,
                u.email,
                u.profile_picture_url
            FROM teacher_profiles tp
            JOIN users u ON tp.user_id = u.id
            WHERE 1=1
        `;

        let countQuery = `
            SELECT COUNT(*) as total
            FROM teacher_profiles tp
            JOIN users u ON tp.user_id = u.id
            WHERE 1=1
        `;

        const queryParams = [];

        if (status) {
            query += ` AND tp.status = ?`;
            countQuery += ` AND tp.status = ?`;
            queryParams.push(status);
        }

        if (search) {
            query += ` AND (u.full_name LIKE ? OR u.email LIKE ? OR tp.country LIKE ? OR tp.native_language LIKE ?)`;
            countQuery += ` AND (u.full_name LIKE ? OR u.email LIKE ? OR tp.country LIKE ? OR tp.native_language LIKE ?)`;
            const searchTerm = `%${search}%`;
            queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
        }

        // Add sorting
        query += ` ORDER BY tp.created_at DESC`;

        // Add pagination
        query += ` LIMIT ? OFFSET ?`;
        queryParams.push(limit, offset);

        // Execute queries
        const [applications] = await connection.execute(query, queryParams);
        const [countResult] = await connection.execute(countQuery, queryParams.slice(0, -2));

        res.json({
            applications,
            total: countResult[0].total,
            page,
            limit
        });
    } catch (error) {
        console.error('Error fetching applications:', error);
        res.status(500).json({ message: 'Error fetching applications' });
    } finally {
        connection.release();
    }
};

exports.updateApplicationStatus = async (req, res) => {
    const connection = await db.pool.getConnection();
    try {
        const { id } = req.params;
        const { status } = req.body;

        if (!['pending', 'approved', 'rejected'].includes(status)) {
            return res.status(400).json({ message: 'Invalid status' });
        }

        // Start transaction
        await connection.beginTransaction();

        // Update application status
        await connection.execute(
            'UPDATE teacher_profiles SET status = ? WHERE id = ?',
            [status, id]
        );

        // If approved, update user role to platform_teacher
        if (status === 'approved') {
            await connection.execute(`
                UPDATE users u
                JOIN teacher_profiles tp ON u.id = tp.user_id
                SET u.role = 'platform_teacher'
                WHERE tp.id = ?
            `, [id]);
        }

        // Commit transaction
        await connection.commit();

        // Get the updated application
        const [applications] = await connection.execute(`
            SELECT 
                tp.*,
                u.full_name,
                u.email,
                u.profile_picture_url
            FROM teacher_profiles tp
            JOIN users u ON tp.user_id = u.id
            WHERE tp.id = ?
        `, [id]);

        const application = applications[0];

        // Emit socket event for real-time updates
        req.app.get('io').emit('application_status_changed', {
            applicationId: id,
            status,
            application
        });

        res.json({ message: 'Application status updated successfully', application });
    } catch (error) {
        await connection.rollback();
        console.error('Error updating application status:', error);
        res.status(500).json({ message: 'Error updating application status' });
    } finally {
        connection.release();
    }
};
