const mysql = require('mysql2/promise');
const config = require('../config/db.config');

// الحصول على جميع طلبات تعديل البيانات
const getAllProfileUpdates = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    const { status = 'all', page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    // بناء الاستعلام حسب الحالة
    let whereClause = '';
    let queryParams = [];

    if (status && status !== 'all') {
      whereClause = 'WHERE status = ?';
      queryParams.push(status);
    }

    console.log('Filter status:', status, 'whereClause:', whereClause, 'queryParams:', queryParams);

    // اختبار وجود الجدول
    console.log('Testing table existence...');
    try {
      const [testResult] = await connection.execute('SELECT COUNT(*) as count FROM teacher_profile_updates');
      console.log('Table exists, count:', testResult[0].count);
    } catch (testError) {
      console.error('Table test failed:', testError);
      return res.status(500).json({
        success: false,
        message: 'جدول تعديلات البيانات غير موجود أو حدث خطأ في قاعدة البيانات'
      });
    }

    // إذا كان الجدول فارغ، إرجاع نتيجة فارغة
    const [countCheck] = await connection.execute('SELECT COUNT(*) as count FROM teacher_profile_updates');
    if (countCheck[0].count === 0) {
      return res.json({
        success: true,
        updates: [],
        pagination: {
          currentPage: parseInt(page),
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: parseInt(limit)
        }
      });
    }

    // استعلام آمن مع prepared statements للفلتر فقط
    const query = `SELECT * FROM teacher_profile_updates ${whereClause} ORDER BY created_at DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

    console.log('Executing query:', query, 'with params:', queryParams);

    // الحصول على طلبات التعديل فقط
    const [updates] = queryParams.length > 0
      ? await connection.execute(query, queryParams)
      : await connection.execute(query);

    console.log('Query result:', updates.length, 'updates found');

    // إذا لم توجد نتائج، إرجاع قائمة فارغة
    if (updates.length === 0) {
      return res.json({
        success: true,
        updates: [],
        pagination: {
          currentPage: parseInt(page),
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: parseInt(limit)
        }
      });
    }

    // إضافة البيانات الحالية لكل طلب تعديل
    for (let update of updates) {
      try {
        // جلب بيانات المستخدم الحالية
        const [userResult] = await connection.execute(
          'SELECT full_name, email FROM users WHERE id = ?',
          [update.user_id]
        );

        // جلب بيانات ملف المعلم الحالية
        const [profileResult] = await connection.execute(
          'SELECT country, residence, profile_picture_url, intro_video_url, cv, qualifications, teaching_experience FROM teacher_profiles WHERE id = ?',
          [update.teacher_profile_id]
        );

        if (userResult.length > 0) {
          update.current_full_name = userResult[0].full_name;
          update.current_email = userResult[0].email;
        }

        if (profileResult.length > 0) {
          update.current_country = profileResult[0].country;
          update.current_residence = profileResult[0].residence;
          update.current_profile_picture_url = profileResult[0].profile_picture_url;
          update.current_intro_video_url = profileResult[0].intro_video_url;
          update.current_cv = profileResult[0].cv;
          update.current_qualifications = profileResult[0].qualifications;
          update.current_teaching_experience = profileResult[0].teaching_experience;
        }
      } catch (joinError) {
        console.error('Error fetching related data for update:', update.id, joinError);
        // إضافة قيم افتراضية في حالة الخطأ
        update.current_full_name = 'غير متوفر';
        update.current_email = 'غير متوفر';
      }
    }

    // الحصول على العدد الإجمالي
    const countQuery = `SELECT COUNT(*) as total FROM teacher_profile_updates ${whereClause}`;

    console.log('Executing count query:', countQuery, 'with params:', queryParams);

    const [countResult] = queryParams.length > 0
      ? await connection.execute(countQuery, queryParams)
      : await connection.execute(countQuery);

    const total = countResult[0].total;
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      updates,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    });

  } catch (error) {
    console.error('Error getting profile updates:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب طلبات التعديل'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

// الحصول على تفاصيل طلب تعديل محدد
const getProfileUpdateDetails = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    const { id } = req.params;

    const [updates] = await connection.execute(
      `SELECT 
        tpu.*,
        u.full_name as current_full_name,
        u.email as current_email,
        tp.country as current_country,
        tp.residence as current_residence,
        tp.profile_picture_url as current_profile_picture_url,
        tp.intro_video_url as current_intro_video_url,
        tp.cv as current_cv,
        tp.qualifications as current_qualifications,
        tp.teaching_experience as current_teaching_experience,
        admin.full_name as admin_name
       FROM teacher_profile_updates tpu
       JOIN users u ON tpu.user_id = u.id
       JOIN teacher_profiles tp ON tpu.teacher_profile_id = tp.id
       LEFT JOIN users admin ON tpu.admin_id = admin.id
       WHERE tpu.id = ?`,
      [id]
    );

    if (updates.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على طلب التعديل'
      });
    }

    res.json({
      success: true,
      update: updates[0]
    });

  } catch (error) {
    console.error('Error getting profile update details:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب تفاصيل طلب التعديل'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

// الموافقة على طلب التعديل
const approveProfileUpdate = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);
    await connection.beginTransaction();

    const { id } = req.params;
    const { adminNotes = '' } = req.body;

    // الحصول على تفاصيل طلب التعديل
    const [updates] = await connection.execute(
      'SELECT * FROM teacher_profile_updates WHERE id = ? AND status = "pending"',
      [id]
    );

    if (updates.length === 0) {
      await connection.rollback();
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على طلب التعديل أو تم مراجعته بالفعل'
      });
    }

    const update = updates[0];

    // تحديث بيانات المستخدم
    await connection.execute(
      'UPDATE users SET full_name = ?, email = ? WHERE id = ?',
      [update.full_name, update.email, update.user_id]
    );

    // تحديث بيانات ملف المعلم
    const updateFields = [];
    const updateValues = [];

    if (update.country) {
      updateFields.push('country = ?');
      updateValues.push(update.country);
    }
    if (update.residence) {
      updateFields.push('residence = ?');
      updateValues.push(update.residence);
    }
    if (update.profile_picture_url) {
      updateFields.push('profile_picture_url = ?');
      updateValues.push(update.profile_picture_url);
    }
    if (update.intro_video_url) {
      updateFields.push('intro_video_url = ?');
      updateValues.push(update.intro_video_url);
    }
    if (update.cv) {
      updateFields.push('cv = ?');
      updateValues.push(update.cv);
    }
    if (update.qualifications) {
      updateFields.push('qualifications = ?');
      updateValues.push(update.qualifications);
    }
    if (update.teaching_experience !== null) {
      updateFields.push('teaching_experience = ?');
      updateValues.push(update.teaching_experience);
    }

    if (updateFields.length > 0) {
      updateValues.push(update.teacher_profile_id);
      await connection.execute(
        `UPDATE teacher_profiles SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );
    }

    // تحديث حالة طلب التعديل
    await connection.execute(
      `UPDATE teacher_profile_updates 
       SET status = 'approved', admin_id = ?, admin_notes = ?, reviewed_at = NOW() 
       WHERE id = ?`,
      [req.user.id, adminNotes, id]
    );

    await connection.commit();

    res.json({
      success: true,
      message: 'تم الموافقة على طلب التعديل وتطبيق التغييرات بنجاح'
    });

  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('Error approving profile update:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء الموافقة على طلب التعديل'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

// رفض طلب التعديل
const rejectProfileUpdate = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    const { id } = req.params;
    const { adminNotes = '' } = req.body;

    // تحديث حالة طلب التعديل
    const [result] = await connection.execute(
      `UPDATE teacher_profile_updates 
       SET status = 'rejected', admin_id = ?, admin_notes = ?, reviewed_at = NOW() 
       WHERE id = ? AND status = 'pending'`,
      [req.user.id, adminNotes, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على طلب التعديل أو تم مراجعته بالفعل'
      });
    }

    res.json({
      success: true,
      message: 'تم رفض طلب التعديل بنجاح'
    });

  } catch (error) {
    console.error('Error rejecting profile update:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء رفض طلب التعديل'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

module.exports = {
  getAllProfileUpdates,
  getProfileUpdateDetails,
  approveProfileUpdate,
  rejectProfileUpdate
};
