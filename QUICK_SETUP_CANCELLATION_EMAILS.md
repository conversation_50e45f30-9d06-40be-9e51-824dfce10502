# إعداد سريع لميزة إيميلات إلغاء الحجز
# Quick Setup for Booking Cancellation Emails

## خطوات التطبيق السريع - Quick Implementation Steps

### 1. قاعدة البيانات - Database
```bash
# تشغيل ملف SQL لإضافة الأعمدة الجديدة
mysql -u your_username -p teach_me_islam_arabic < apply_cancellation_reason_migration.sql
```

### 2. إعادة تشغيل الخادم - Restart Server
```bash
# في مجلد server
npm restart
# أو
node server.js
```

### 3. اختبار الإيميلات - Test Emails
```bash
# اختبار سريع للتأكد من عمل الإيميلات
cd server
node test/testCancellationEmails.js --format-only
```

## التحقق من عمل الميزة - Verify Feature Works

### 1. تحقق من قاعدة البيانات
```sql
DESCRIBE bookings;
-- يجب أن ترى الأعمدة الجديدة:
-- cancellation_reason (TEXT)
-- cancelled_by (ENUM)
-- cancelled_at (TIMESTAMP)
```

### 2. تحقق من إعدادات الإيميل
- تأكد من وجود `SENDGRID_API_KEY` في متغيرات البيئة
- تحقق من ملف `server/config/email.js`

### 3. اختبار سريع
1. اذهب إلى `/teacher/bookings` أو `/student/bookings`
2. اضغط "إلغاء" على أي حجز
3. اكتب سبب الإلغاء
4. اضغط "نعم، إلغاء الحجز"
5. تحقق من وصول الإيميلات

## استكشاف الأخطاء - Troubleshooting

### مشكلة: الإيميلات لا ترسل
```bash
# تحقق من logs الخادم
tail -f server.log
# أو تحقق من console
```

**الحلول المحتملة:**
- تحقق من `SENDGRID_API_KEY`
- تحقق من اتصال الإنترنت
- تحقق من صحة عناوين الإيميل في قاعدة البيانات

### مشكلة: خطأ في قاعدة البيانات
```sql
-- تحقق من وجود الأعمدة
SHOW COLUMNS FROM bookings LIKE '%cancel%';

-- إذا لم تكن موجودة، شغل الأوامر يدوياً:
ALTER TABLE bookings ADD COLUMN cancellation_reason TEXT NULL AFTER status;
ALTER TABLE bookings ADD COLUMN cancelled_by ENUM('student', 'teacher') NULL AFTER cancellation_reason;
ALTER TABLE bookings ADD COLUMN cancelled_at TIMESTAMP NULL AFTER cancelled_by;
```

### مشكلة: خطأ في الواجهة الأمامية
```bash
# أعد تشغيل React
cd client
npm start
```

## اختبار شامل - Complete Testing

### 1. اختبار المعلم يلغي الدرس
1. سجل دخول كمعلم
2. اذهب إلى `/teacher/bookings`
3. ألغِ حجز
4. تحقق من وصول إيميلين (للمعلم والطالب)

### 2. اختبار الطالب يلغي الدرس
1. سجل دخول كطالب
2. اذهب إلى `/student/bookings`
3. ألغِ حجز
4. تحقق من وصول إيميلين (للطالب والمعلم)

## الملفات المهمة - Important Files

```
server/
├── templates/bookingCancellationEmails.js     # قوالب الإيميل
├── services/bookingCancellationEmailService.js # خدمة الإيميل
├── routes/bookings.routes.js                  # API محدث
└── test/testCancellationEmails.js            # اختبارات

client/src/
├── pages/teacher/Bookings.js                 # صفحة المعلم محدثة
├── pages/student/Bookings.js                 # صفحة الطالب محدثة
└── i18n/i18n.js                             # ترجمات محدثة

Database:
├── apply_cancellation_reason_migration.sql   # تحديث قاعدة البيانات
```

## نصائح مهمة - Important Tips

1. **الإيميلات غير متزامنة**: لا تؤثر على سرعة الاستجابة
2. **مقاومة للأخطاء**: فشل الإيميل لا يؤثر على الإلغاء
3. **ثنائية اللغة**: جميع الإيميلات بالعربية والإنجليزية
4. **سبب الإلغاء اختياري**: يمكن ترك الحقل فارغاً

## الدعم - Support

إذا واجهت أي مشاكل:
1. تحقق من logs الخادم
2. تحقق من إعدادات قاعدة البيانات
3. تحقق من إعدادات الإيميل
4. جرب الاختبارات المتوفرة

---

✅ **الميزة جاهزة للاستخدام!**
🎉 **Feature Ready to Use!**
