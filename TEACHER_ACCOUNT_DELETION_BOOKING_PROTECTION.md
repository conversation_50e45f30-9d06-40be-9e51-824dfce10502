# حماية حذف حساب المدرس - منع الحذف مع وجود حجوزات نشطة

## 📋 **نظرة عامة - Overview**

تم تطوير نظام حماية شامل لمنع المدرسين من حذف حساباتهم إذا كان لديهم حجوزات نشطة أو قادمة، لضمان عدم الإضرار بالطلاب.

## 🎯 **الهدف - Objective**

منع المدرس من حذف حسابه إذا كان لديه:
- ✅ **حجوزات مجدولة** (scheduled)
- ✅ **حجوزات جارية** (ongoing)
- ✅ **اجتماعات مستقبلية** في أي وقت قادم
- ✅ **إجباره على إلغاء الحجوزات أولاً** قبل الحذف

## 🔧 **التحديثات المطبقة - Applied Updates**

### 1. **Backend - الخادم**

#### أ) API Endpoint جديد:
```javascript
GET /bookings/teacher/active-check
```

**الوظيفة:**
- جلب معرف المدرس من teacher_profiles
- عد الحجوزات النشطة في جدول bookings
- عد الاجتماعات النشطة في جدول meetings
- إرجاع النتيجة مع إمكانية الحذف

**الاستعلام:**
```sql
-- عد الحجوزات النشطة
SELECT COUNT(*) as count
FROM bookings b
WHERE b.teacher_profile_id = ?
AND b.status IN ('scheduled', 'ongoing')
AND b.datetime >= NOW()

-- عد الاجتماعات النشطة
SELECT COUNT(*) as count
FROM meetings m
WHERE m.teacher_id = ?
AND m.status IN ('scheduled', 'ongoing')
AND m.meeting_date >= NOW()
```

**الاستجابة:**
```json
{
  "success": true,
  "canDelete": false,
  "activeBookings": 5,
  "message": "Found 5 active booking(s). Cancel all bookings before deleting account."
}
```

### 2. **Frontend - الواجهة الأمامية**

#### أ) State Management جديد:
```javascript
const [activeBookingsCount, setActiveBookingsCount] = useState(0);
const [checkingBookings, setCheckingBookings] = useState(false);
```

#### ب) دالة التحقق من الحجوزات:
```javascript
const checkActiveBookings = async () => {
  const response = await axios.get('/bookings/teacher/active-check');
  return response.data;
};
```

#### ج) useEffect لجلب البيانات:
```javascript
useEffect(() => {
  const fetchActiveBookingsCount = async () => {
    try {
      setCheckingBookings(true);
      const bookingsCheck = await checkActiveBookings();
      setActiveBookingsCount(bookingsCheck.activeBookings || 0);
    } finally {
      setCheckingBookings(false);
    }
  };
  fetchActiveBookingsCount();
}, []);
```

### 3. **تحديث دالة حذف الحساب**

#### قبل التحديث:
```javascript
const handleDeleteAccount = async () => {
  // إرسال طلب الحذف مباشرة
  const response = await axios.post('/api/users/request-delete');
};
```

#### بعد التحديث:
```javascript
const handleDeleteAccount = async () => {
  // التحقق من الحجوزات النشطة أولاً
  const bookingsCheck = await checkActiveBookings();
  
  if (!bookingsCheck.canDelete) {
    setError('Cannot delete account. You have active bookings...');
    return;
  }
  
  // المتابعة مع الحذف إذا لم توجد حجوزات
  const response = await axios.post('/api/users/request-delete');
};
```

### 4. **تحسينات واجهة المستخدم**

#### أ) تحذيرات ديناميكية في نافذة الحذف:
```javascript
{checkingBookings ? (
  <CircularProgress /> // جاري التحقق
) : activeBookingsCount > 0 ? (
  <Alert severity="error">
    You have {activeBookingsCount} active booking(s)
  </Alert>
) : (
  <Alert severity="success">
    No active bookings found
  </Alert>
)}
```

#### ب) تعطيل زر الحذف:
```javascript
<Button
  disabled={loading || checkingBookings || activeBookingsCount > 0}
  onClick={handleDeleteAccount}
>
  Send Verification Code
</Button>
```

#### ج) زر سريع للذهاب للحجوزات:
```javascript
<Button
  variant="outlined"
  onClick={() => navigate('/teacher/bookings')}
>
  Go to Bookings
</Button>
```

## 🎨 **تجربة المستخدم - User Experience**

### 1. **حالة عدم وجود حجوزات:**
```
✅ عرض: "No active bookings found"
✅ زر الحذف: مفعل
✅ اللون: أخضر (نجاح)
```

### 2. **حالة وجود حجوزات نشطة:**
```
❌ عرض: "You have 5 active booking(s)"
❌ زر الحذف: معطل
❌ اللون: أحمر (خطأ)
❌ زر إضافي: "Go to Bookings"
```

### 3. **حالة التحقق:**
```
🔄 عرض: "Checking active bookings..."
🔄 زر الحذف: معطل مؤقتاً
🔄 أيقونة: دائرة تحميل
```

## 🌐 **الترجمات - Translations**

### الإنجليزية:
```javascript
checkingBookings: 'Checking active bookings...',
activeBookingsWarning: 'You have active booking(s). You must cancel all your bookings before deleting your account.',
cancelBookingsFirst: 'Please go to your bookings page and cancel all active bookings first.',
noActiveBookings: 'No active bookings found. You can proceed with account deletion.',
goToBookings: 'Go to Bookings',
hasActiveBookings: 'Cannot delete account. You have active bookings. Please cancel all your bookings first.'
```

### العربية:
```javascript
checkingBookings: 'جاري التحقق من الحجوزات النشطة...',
activeBookingsWarning: 'لديك حجوزات نشطة. يجب إلغاء جميع حجوزاتك قبل حذف حسابك.',
cancelBookingsFirst: 'يرجى الذهاب إلى صفحة الحجوزات وإلغاء جميع الحجوزات النشطة أولاً.',
noActiveBookings: 'لا توجد حجوزات نشطة. يمكنك المتابعة مع حذف الحساب.',
goToBookings: 'الذهاب للحجوزات',
hasActiveBookings: 'لا يمكن حذف الحساب. لديك حجوزات نشطة. يرجى إلغاء جميع حجوزاتك أولاً.'
```

## 📊 **تدفق العملية - Process Flow**

### 1. **المدرس يفتح صفحة البروفايل:**
```
1. تحميل الصفحة
2. استدعاء API للتحقق من الحجوزات النشطة
3. عرض النتيجة في الواجهة
```

### 2. **المدرس يضغط على "حذف الحساب":**
```
1. فتح نافذة التأكيد
2. عرض حالة الحجوزات (نشطة/لا توجد)
3. تفعيل/تعطيل زر الحذف حسب الحالة
```

### 3. **إذا كان هناك حجوزات نشطة:**
```
1. عرض تحذير أحمر
2. تعطيل زر الحذف
3. عرض زر "الذهاب للحجوزات"
4. منع المتابعة مع الحذف
```

### 4. **إذا لم توجد حجوزات نشطة:**
```
1. عرض رسالة نجاح خضراء
2. تفعيل زر الحذف
3. السماح بالمتابعة مع الحذف
```

## ✅ **الفوائد - Benefits**

1. **🛡️ حماية الطلاب:** منع إلغاء الحجوزات المفاجئ
2. **📋 شفافية:** المدرس يعرف بالضبط ما عليه فعله
3. **🎯 سهولة الاستخدام:** زر مباشر للذهاب للحجوزات
4. **⚡ استجابة فورية:** تحقق فوري من الحالة
5. **🌐 دعم متعدد اللغات:** ترجمات كاملة
6. **💼 احترافية:** تجربة مستخدم محسنة

## 🚀 **الخطوات التالية - Next Steps**

1. **اختبار النظام:** التأكد من عمل جميع الحالات
2. **تطبيق مماثل:** للطلاب إذا لزم الأمر
3. **إشعارات:** إضافة إشعارات عند محاولة الحذف
4. **تقارير:** إحصائيات عن محاولات الحذف المرفوضة

---

**تاريخ التطوير:** 2024-07-31  
**المطور:** Augment Agent  
**الحالة:** ✅ مكتمل ومجهز للاختبار
