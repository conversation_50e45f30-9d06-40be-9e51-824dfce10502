import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Grid,
  Paper,
  Typography,
  Box,
  Avatar,
  Button,
  Chip,
  Rating,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  CircularProgress,
  Alert,
  useTheme,
  Tooltip
} from '@mui/material';
import {
  Language as LanguageIcon,
  School as SchoolIcon,
  AttachMoney as MoneyIcon,
  AccessTime as AccessTimeIcon,
  PlayCircleOutline as PlayCircleOutlineIcon,
  ArrowBack as ArrowBackIcon,
  Verified as VerifiedIcon
} from '@mui/icons-material';
import axios from 'axios';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import AvailableHoursTable from '../components/AvailableHoursTable';

const TeacherDetails = () => {
  const { id } = useParams();
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const isRtl = i18n.language === 'ar';

  const [teacher, setTeacher] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);


  useEffect(() => {
    const fetchTeacherDetails = async () => {
      try {
        setLoading(true);
        const { data } = await axios.get(`/api/teachers/${id}`);
        if (data.success) {
          setTeacher(data.data);
        }
      } catch (error) {
        console.error('Error fetching teacher details:', error);
        setError(t('teacherDetails.errorFetching'));
      } finally {
        setLoading(false);
      }
    };

    fetchTeacherDetails();
  }, [id, t]);





  // Function to check if a URL is a local video file
  const isLocalVideoFile = (url) => {
    if (!url) return false;
    return url.startsWith('/uploads/') && (
      url.endsWith('.mp4') ||
      url.endsWith('.webm') ||
      url.endsWith('.ogg')
    );
  };

  // Function to check if a URL is a YouTube video
  const isYoutubeVideo = (url) => {
    if (!url) return false;
    return url.includes('youtube.com') || url.includes('youtu.be');
  };

  // Function to extract YouTube video ID from URL
  const getYoutubeVideoId = (url) => {
    if (!url) return null;

    // Regular expressions to match different YouTube URL formats
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);

    return (match && match[2].length === 11) ? match[2] : null;
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 8, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error || !teacher) {
    return (
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Alert severity="error">{error || t('teacherDetails.teacherNotFound')}</Alert>
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate('/find-teacher')}
            startIcon={<ArrowBackIcon />}
          >
            {t('teacherDetails.backToSearch')}
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 8 }}>
      {/* Back Button */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="outlined"
          color="primary"
          onClick={() => navigate('/find-teacher')}
          startIcon={<ArrowBackIcon />}
          sx={{ borderRadius: 2 }}
        >
          {t('teacherDetails.backToSearch')}
        </Button>
      </Box>

      {/* Teacher Profile Header */}
      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
        <Grid container spacing={3}>
          {/* Teacher Image and Basic Info */}
          <Grid item xs={12} md={3} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <Avatar
              src={teacher.profile_picture_url}
              alt={teacher.full_name}
              sx={{
                width: { xs: 120, md: 150 },
                height: { xs: 120, md: 150 },
                mb: 2,
                border: '4px solid',
                borderColor: 'primary.main'
              }}
            />
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h5" component="h1" sx={{ fontWeight: 'bold', mr: 1 }}>
                {teacher.full_name}
              </Typography>
              {teacher.is_verified && (
                <Tooltip title={t('search.verifiedTeacher')}>
                  <VerifiedIcon color="primary" />
                </Tooltip>
              )}
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Rating value={teacher.average_rating} precision={0.5} readOnly />
              <Typography variant="body2" sx={{ ml: 1 }}>
                ({teacher.review_count} {t('teacherDetails.reviews')})
              </Typography>
            </Box>
            <Box sx={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 1 }}>

            </Box>
          </Grid>

          {/* Teacher Details */}
          <Grid item xs={12} md={9}>
            <Grid container spacing={3}>
              {/* Subjects */}
              <Grid item xs={12}>
                <Box>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                    <SchoolIcon color="primary" />
                    {t('teacherDetails.teachingSubjects')}
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 4 }}>
                    {teacher.subjects.map((subject, index) => (
                      <Chip
                        key={index}
                        label={subject}
                        color="primary"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Box>
              </Grid>

              {/* Languages */}
              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                    <LanguageIcon color="primary" />
                    {t('teacherDetails.languages')}
                  </Typography>
                  <Box sx={{ ml: 4 }}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>{t('teacherDetails.nativeLanguage')}:</strong> {teacher.native_language}
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>{t('teacherDetails.teachingLanguages')}:</strong>
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, ml: 2 }}>
                      {teacher.teaching_languages.map((language, index) => (
                        <Typography key={index} variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                          • {language}
                        </Typography>
                      ))}
                    </Box>
                  </Box>
                </Box>
              </Grid>

              {/* Qualifications and Experience */}
              <Grid item xs={12} md={6}>
                <Box>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                    <AccessTimeIcon color="primary" />
                    {t('teacherDetails.experience')}
                  </Typography>
                  <Box sx={{ ml: 4 }}>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      {t('teacherDetails.yearsOfExperience', { years: teacher.teaching_experience })}
                    </Typography>
                    <Typography variant="body1" sx={{ mb: 1 }}>
                      <strong>{t('teacherDetails.qualifications')}:</strong>
                    </Typography>
                    <Typography variant="body2" sx={{ ml: 2 }}>
                      {teacher.qualifications}
                    </Typography>
                  </Box>
                </Box>
              </Grid>

              {/* Price Information */}
              <Grid item xs={12}>
                <Box>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                    <MoneyIcon color="primary" />
                    {t('teacherDetails.paymentInfo')}
                  </Typography>
                  <Box sx={{ ml: 4 }}>
                    <Typography variant="body1" sx={{ mb: 0.5, fontWeight: 'bold', fontSize: '1.2rem', color: 'primary.main' }}>
                      ${teacher.price_per_lesson}/hr
                    </Typography>
                    {teacher.trial_lesson_price && (
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 0.2 }}>
                        Trial: ${teacher.trial_lesson_price}
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Paper>

      {/* Introduction Video Section */}
      {teacher.intro_video_url && (
        <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
          <Typography variant="h5" gutterBottom sx={{ borderBottom: 1, borderColor: 'divider', pb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
            <PlayCircleOutlineIcon color="primary" />
            {t('teacherDetails.introVideo')}
          </Typography>
          <Box sx={{ mt: 2, position: 'relative', paddingTop: '56.25%', width: '100%' }}>
            {isYoutubeVideo(teacher.intro_video_url) ? (
              // YouTube video embed
              <iframe
                src={`https://www.youtube.com/embed/${getYoutubeVideoId(teacher.intro_video_url)}`}
                title="YouTube video player"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  borderRadius: '8px'
                }}
              />
            ) : isLocalVideoFile(teacher.intro_video_url) ? (
              // Local video file
              <video
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  borderRadius: '8px'
                }}
                src={`https://allemnionline.com${teacher.intro_video_url}`}
                controls
                preload="metadata"
              />
            ) : (
              // External video URL (not YouTube)
              <Box sx={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', textAlign: 'center' }}>
                <Typography variant="body1">
                  <a href={teacher.intro_video_url} target="_blank" rel="noopener noreferrer">
                    {t('teacherDetails.openVideo')}
                  </a>
                </Typography>
              </Box>
            )}
          </Box>
        </Paper>
      )}

      {/* Available Hours Section */}
      {teacher.available_hours && (() => {
        try {
          const parsedHours = typeof teacher.available_hours === 'string'
            ? JSON.parse(teacher.available_hours)
            : teacher.available_hours;
          return (
            <AvailableHoursTable
              availableHours={parsedHours}
              loading={loading}
              showStats={true}
            />
          );
        } catch (error) {
          console.error('Error parsing available hours:', error);
          return null;
        }
      })()}

      {/* Reviews Section */}
      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
        <Typography variant="h5" gutterBottom sx={{ borderBottom: 1, borderColor: 'divider', pb: 1 }}>
          {t('teacherDetails.studentReviews')}
        </Typography>
        {teacher.reviews && teacher.reviews.length > 0 ? (
          <List>
            {teacher.reviews.map((review) => (
              <ListItem key={review.id} alignItems="flex-start" sx={{ borderBottom: '1px solid', borderColor: 'divider', py: 2 }}>
                <ListItemAvatar>
                  <Avatar src={review.student_profile_picture} alt={review.student_name}>
                    {review.student_name.charAt(0)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="subtitle1" component="span">
                        {review.student_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {format(new Date(review.created_at), 'PPP', { locale: isRtl ? ar : enUS })}
                      </Typography>
                    </Box>
                  }
                  secondary={
                    <Box sx={{ mt: 1 }}>
                      <Rating value={review.rating} readOnly size="small" sx={{ mb: 1 }} />
                      <Typography variant="body2" color="text.primary" sx={{ mb: 2 }}>
                        {review.comment}
                      </Typography>

                      {/* Teacher Reply */}
                      {review.reply_text && (
                        <Box sx={{
                          bgcolor: 'primary.light',
                          p: 2,
                          borderRadius: 1,
                          mt: 2,
                          border: '1px solid',
                          borderColor: 'primary.main'
                        }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 'bold' }}>
                              {t('reviews.teacherReply')}:
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ color: 'primary.dark' }}>
                            {review.reply_text}
                          </Typography>
                          {review.reply_created_at && (
                            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                              {format(new Date(review.reply_created_at), 'PPP', { locale: isRtl ? ar : enUS })}
                            </Typography>
                          )}
                        </Box>
                      )}
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        ) : (
          <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
            {t('teacherDetails.noReviews')}
          </Typography>
        )}
      </Paper>




    </Container>
  );
};

export default TeacherDetails;
