'use strict';

/**
 * Dependencies
 */
const splitNameEmail = require('./split-name-email');

/**
 * Tests
 */
describe('splitNameEmail', function() {
  it('should not split strings without < symbol', function() {
    const [name, email] = splitNameEmail('<EMAIL>');
    expect(name).to.equal('');
    expect(email).to.equal('<EMAIL>');
  });
  it('should split strings with < symbol', function() {
    const [name, email] = splitNameEmail('Tester <<EMAIL>>');
    expect(name).to.equal('Tester');
    expect(email).to.equal('<EMAIL>');
  });
});
