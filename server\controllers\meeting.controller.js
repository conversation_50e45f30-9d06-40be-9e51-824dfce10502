const db = require('../config/db');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');

// Get all meetings for a teacher
exports.getTeacherMeetings = async (req, res) => {
  try {
    const teacherId = req.user.id;

    const [meetings] = await db.pool.execute(
      `SELECT m.*,
        CASE
          WHEN s.deleted_at IS NOT NULL THEN CONCAT(s.full_name, ' (محذوف)')
          ELSE s.full_name
        END as student_name,
        CASE
          WHEN s.deleted_at IS NOT NULL THEN '<EMAIL>'
          ELSE s.email
        END as student_email,
        CASE
          WHEN s.deleted_at IS NOT NULL THEN NULL
          ELSE s.profile_picture_url
        END as student_profile_picture,
        s.deleted_at as student_deleted_at
      FROM meetings m
      LEFT JOIN users s ON m.student_id = s.id
      WHERE m.teacher_id = ?
      ORDER BY m.meeting_date DESC`,
      [teacherId]
    );

    // Return the meetings array directly for compatibility with frontend
    res.json(meetings);
  } catch (error) {
    console.error('Error fetching teacher meetings:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch meetings' });
  }
};

// Get all meetings for a student
exports.getStudentMeetings = async (req, res) => {
  try {
    const studentId = req.user.id;
    const meetingStatusService = require('../services/meetingStatusService');

    const [meetings] = await db.pool.execute(
      `SELECT m.*,
        CASE
          WHEN t.deleted_at IS NOT NULL THEN CONCAT(t.full_name, ' (محذوف)')
          ELSE t.full_name
        END as teacher_name,
        CASE
          WHEN t.deleted_at IS NOT NULL THEN '<EMAIL>'
          ELSE t.email
        END as teacher_email,
        CASE
          WHEN t.deleted_at IS NOT NULL THEN NULL
          ELSE t.profile_picture_url
        END as teacher_profile_picture,
        t.deleted_at as teacher_deleted_at,
        scd.timezone as student_timezone
      FROM meetings m
      LEFT JOIN users t ON m.teacher_id = t.id
      LEFT JOIN student_completion_data scd ON m.student_id = scd.user_id
      WHERE m.student_id = ?
      ORDER BY m.meeting_date DESC`,
      [studentId]
    );

    // Update meeting statuses before returning
    const updatedMeetings = await Promise.all(meetings.map(async (meeting) => {
      try {
        const updatedStatus = await meetingStatusService.updateSpecificMeetingStatus(meeting.id);
        return { ...meeting, status: updatedStatus };
      } catch (error) {
        console.error(`Error updating status for meeting ${meeting.id}:`, error);
        return meeting;
      }
    }));

    // Return the meetings in an object with a meetings property
    res.json({
      success: true,
      meetings: updatedMeetings
    });
  } catch (error) {
    console.error('Error fetching student meetings:', error);
    res.status(500).json({ success: false, message: 'Failed to fetch meetings' });
  }
};

// Create a new meeting
exports.createMeeting = async (req, res) => {
  try {
    console.log('Creating meeting with data:', req.body);

    // Validate required fields
    const { student_email, meeting_name, meeting_date, duration } = req.body;
    if (!student_email || !meeting_name || !meeting_date || !duration) {
      console.error('Missing required fields:', { student_email, meeting_name, meeting_date, duration });
      return res.status(400).json({
        success: false,
        message: 'Missing required fields: student_email, meeting_name, meeting_date, duration'
      });
    }

    const teacherId = req.user.id;
    console.log('Teacher ID:', teacherId);

    // Calculate amount if not provided
    let { amount } = req.body;
    if (!amount) {
      // Get teacher's price per lesson
      try {
        const [teacherProfiles] = await db.pool.execute(
          'SELECT price_per_lesson FROM teacher_profiles WHERE user_id = ?',
          [teacherId]
        );

        if (teacherProfiles.length > 0) {
          const pricePerLesson = parseFloat(teacherProfiles[0].price_per_lesson || 0);
          amount = duration === '50' ? pricePerLesson : pricePerLesson / 2;
        } else {
          amount = 0; // Default if no teacher profile found
        }
      } catch (priceError) {
        console.error('Error getting teacher price:', priceError);
        amount = 0; // Default if error
      }
    }

    // Find student ID from email
    let studentId;
    try {
      const [students] = await db.pool.execute(
        'SELECT id FROM users WHERE email = ? AND role = "student"',
        [student_email]
      );

      if (students.length === 0) {
        return res.status(404).json({ success: false, message: 'Student not found with this email' });
      }

      studentId = students[0].id;
      console.log('Found student ID:', studentId);
    } catch (studentError) {
      console.error('Error finding student:', studentError);
      return res.status(500).json({ success: false, message: 'Failed to find student' });
    }

    // Generate a unique room name
    const roomName = uuidv4();
    console.log('Generated room name:', roomName);

    // Create meeting in database
    try {
      // Generate a unique ID for the meeting
      const meetingId = uuidv4();
      console.log('Generated meeting ID:', meetingId);

      // Create VideoSDK meeting first and make sure we have a valid token
      let videosdkMeetingId = null;
      try {
        // 1. Obtain / generate VideoSDK token
        let videoSdkToken = process.env.VIDEOSDK_TOKEN;
        if (!videoSdkToken) {
          const apiKey = process.env.VIDEOSDK_API_KEY;
          const secretKey = process.env.VIDEOSDK_SECRET_KEY;
          if (apiKey && secretKey) {
            const jwt = require('jsonwebtoken');
            videoSdkToken = jwt.sign(
              {
                apikey: apiKey,
                permissions: ["allow_join", "allow_mod"],
                iat: Math.floor(Date.now() / 1000),
                exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60 // 24 hours
              },
              secretKey,
              { algorithm: 'HS256' }
            );
          } else {
            console.warn('VideoSDK API credentials (API key / secret) are missing');
          }
        }

        if (!videoSdkToken) {
          throw new Error('Unable to generate VideoSDK token – aborting meeting creation');
        }

        // 2. Call VideoSDK to create a room
        const axiosInstance = require('axios');
        const vsdkRes = await axiosInstance.post(
          'https://api.videosdk.live/v2/rooms',
          {},
          {
            headers: {
              Authorization: videoSdkToken,
              'Content-Type': 'application/json'
            }
          }
        );

        // 3. Extract room/meeting id from response
        if (vsdkRes.data && (vsdkRes.data.roomId || vsdkRes.data.meetingId)) {
          videosdkMeetingId = vsdkRes.data.roomId || vsdkRes.data.meetingId;
          console.log('VideoSDK meeting created with ID:', videosdkMeetingId);
        } else {
          throw new Error('VideoSDK returned an unexpected response: ' + JSON.stringify(vsdkRes.data));
        }
      } catch (videosdkError) {
        console.error('Error creating VideoSDK meeting:', videosdkError.message || videosdkError);
        return res.status(500).json({ success: false, message: 'Failed to create VideoSDK meeting' });
      }

      // إدراج الميتنج في قاعدة البيانات مع VideoSDK meeting ID
      await db.pool.execute(
        `INSERT INTO meetings
          (id, teacher_id, student_id, meeting_name, room_name, videosdk_meeting_id, meeting_date, duration, amount, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'scheduled')`,
        [meetingId, teacherId, studentId, meeting_name, roomName, videosdkMeetingId, meeting_date, duration, amount]
      );

      console.log('Meeting created in database with ID:', meetingId, 'VideoSDK ID:', videosdkMeetingId);

      res.json({
        success: true,
        message: 'Meeting created successfully',
        meetingId: meetingId,
        roomName,
        videosdkMeetingId
      });
    } catch (dbError) {
      console.error('Database error creating meeting:', dbError);
      res.status(500).json({ success: false, message: 'Database error creating meeting' });
    }
  } catch (error) {
    console.error('Error creating meeting:', error);
    res.status(500).json({ success: false, message: 'Failed to create meeting: ' + error.message });
  }
};

// Update meeting status
exports.updateMeetingStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['scheduled', 'ongoing', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ success: false, message: 'Invalid status' });
    }

    // Update meeting status
    await db.pool.execute(
      'UPDATE meetings SET status = ? WHERE id = ?',
      [status, id]
    );

    res.json({ success: true, message: 'Meeting status updated successfully' });
  } catch (error) {
    console.error('Error updating meeting status:', error);
    res.status(500).json({ success: false, message: 'Failed to update meeting status' });
  }
};

// Validate room name
exports.validateRoom = async (req, res) => {
  try {
    const { roomName } = req.params;

    // Check if room exists in database
    const [meetings] = await db.pool.execute(
      'SELECT * FROM meetings WHERE room_name = ?',
      [roomName]
    );

    if (meetings.length === 0) {
      return res.status(404).json({ success: false, message: 'Meeting not found' });
    }

    const meeting = meetings[0];

    // Check if user is authorized to join this meeting
    const userId = req.user.id;
    if (meeting.teacher_id !== userId && meeting.student_id !== userId) {
      return res.status(403).json({ success: false, message: 'Not authorized to join this meeting' });
    }

    res.json({ success: true, meeting });
  } catch (error) {
    console.error('Error validating room:', error);
    res.status(500).json({ success: false, message: 'Failed to validate room' });
  }
};

// Generate token for VideoSDK
exports.generateVideoSDKToken = async (req, res) => {
  try {
    const { userId, role } = req.body;

    // First, try to use the pre-generated token from environment
    const preGeneratedToken = process.env.VIDEOSDK_TOKEN;
    if (preGeneratedToken) {
      return res.json({ success: true, token: preGeneratedToken });
    }

    // If no pre-generated token, create one using API key and secret
    const apiKey = process.env.VIDEOSDK_API_KEY;
    const secret = process.env.VIDEOSDK_SECRET_KEY;

    if (!apiKey || !secret) {
      return res.status(500).json({
        success: false,
        message: 'VideoSDK API key or secret not configured'
      });
    }

    // Generate JWT token for VideoSDK
    const jwt = require('jsonwebtoken');

    const payload = {
      apikey: apiKey,
      permissions: ["allow_join", "allow_mod"],
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours expiry
    };

    const token = jwt.sign(payload, secret, { algorithm: 'HS256' });

    res.json({ success: true, token });
  } catch (error) {
    console.error('Error generating VideoSDK token:', error);
    res.status(500).json({ success: false, message: 'Failed to generate VideoSDK token' });
  }
};

// Validate VideoSDK meeting
exports.validateVideoSDKMeeting = async (req, res) => {
  try {
    const { meetingId } = req.body;

    if (!meetingId) {
      return res.status(400).json({ success: false, message: 'Meeting ID is required' });
    }

    // Check if meeting exists in our database
    const [meetings] = await db.pool.execute(
      'SELECT * FROM meetings WHERE room_name = ? OR id = ?',
      [meetingId, meetingId]
    );

    if (meetings.length === 0) {
      return res.status(404).json({ success: false, message: 'Meeting not found' });
    }

    const meeting = meetings[0];

    // Check if user is authorized to join this meeting
    const userId = req.user.id;
    if (meeting.teacher_id !== userId && meeting.student_id !== userId) {
      return res.status(403).json({ success: false, message: 'Not authorized to join this meeting' });
    }

    res.json({ success: true, meeting });
  } catch (error) {
    console.error('Error validating VideoSDK meeting:', error);
    res.status(500).json({ success: false, message: 'Failed to validate meeting' });
  }
};

// Join meeting - track user join time
exports.joinMeeting = async (req, res) => {
  try {
    const meetingId = req.params.id;
    const userId = req.user.id;
    const userRole = req.user.role === 'platform_teacher' ? 'teacher' : 'student';

    console.log('User joining meeting:', { meetingId, userId, userRole });

    // Check if meeting exists
    const [meetings] = await db.pool.execute(
      'SELECT * FROM meetings WHERE id = ?',
      [meetingId]
    );

    if (meetings.length === 0) {
      return res.status(404).json({ success: false, message: 'Meeting not found' });
    }

    // Check if user has an active session for this meeting
    const [activeSessions] = await db.pool.execute(
      'SELECT * FROM meeting_sessions WHERE meeting_id = ? AND user_id = ? AND session_status = "active"',
      [meetingId, userId]
    );

    if (activeSessions.length > 0) {
      return res.json({
        success: true,
        message: 'User already has an active session',
        sessionId: activeSessions[0].id,
        joinTime: activeSessions[0].join_time
      });
    }

    // Create new session record
    const [result] = await db.pool.execute(
      'INSERT INTO meeting_sessions (meeting_id, user_id, user_role, join_time, session_status) VALUES (?, ?, ?, NOW(), "active")',
      [meetingId, userId, userRole]
    );

    const sessionId = result.insertId;

    // Update meeting session start time if this is the first user to join
    const [sessionCount] = await db.pool.execute(
      'SELECT COUNT(*) as count FROM meeting_sessions WHERE meeting_id = ? AND session_status = "active"',
      [meetingId]
    );

    if (sessionCount[0].count === 1) {
      await db.pool.execute(
        'UPDATE meetings SET session_start_time = NOW() WHERE id = ?',
        [meetingId]
      );
    }

    // Get the join time for response
    const [newSession] = await db.pool.execute(
      'SELECT join_time FROM meeting_sessions WHERE id = ?',
      [sessionId]
    );

    res.json({
      success: true,
      message: 'Successfully joined meeting',
      sessionId: sessionId,
      joinTime: newSession[0].join_time
    });
  } catch (error) {
    console.error('Error joining meeting:', error);
    res.status(500).json({ success: false, message: 'Failed to join meeting' });
  }
};

// Leave meeting - track user leave time and calculate duration
exports.leaveMeeting = async (req, res) => {
  try {
    const meetingId = req.params.id;
    const userId = req.user.id;

    console.log('User leaving meeting:', { meetingId, userId });

    // Find active session
    const [activeSessions] = await db.pool.execute(
      'SELECT * FROM meeting_sessions WHERE meeting_id = ? AND user_id = ? AND session_status = "active"',
      [meetingId, userId]
    );

    if (activeSessions.length === 0) {
      return res.status(404).json({ success: false, message: 'No active session found' });
    }

    const session = activeSessions[0];
    const joinTime = new Date(session.join_time);
    const leaveTime = new Date();
    const durationSeconds = Math.floor((leaveTime - joinTime) / 1000);

    // Update session with leave time and duration
    await db.pool.execute(
      'UPDATE meeting_sessions SET leave_time = NOW(), duration_seconds = ?, session_status = "ended" WHERE id = ?',
      [durationSeconds, session.id]
    );

    // Update total time in meetings table
    const userRole = session.user_role;
    const timeColumn = userRole === 'teacher' ? 'teacher_total_time_seconds' : 'student_total_time_seconds';

    await db.pool.execute(
      `UPDATE meetings SET ${timeColumn} = ${timeColumn} + ? WHERE id = ?`,
      [durationSeconds, meetingId]
    );

    // Check if all users have left the meeting
    const [remainingActiveSessions] = await db.pool.execute(
      'SELECT COUNT(*) as count FROM meeting_sessions WHERE meeting_id = ? AND session_status = "active"',
      [meetingId]
    );

    if (remainingActiveSessions[0].count === 0) {
      await db.pool.execute(
        'UPDATE meetings SET session_end_time = NOW() WHERE id = ?',
        [meetingId]
      );
    }

    res.json({
      success: true,
      message: 'Successfully left meeting',
      durationSeconds: durationSeconds,
      totalMinutes: Math.floor(durationSeconds / 60)
    });
  } catch (error) {
    console.error('Error leaving meeting:', error);
    res.status(500).json({ success: false, message: 'Failed to leave meeting' });
  }
};

// Get meeting sessions for a specific meeting
exports.getMeetingSessions = async (req, res) => {
  try {
    const meetingId = req.params.id;

    const [sessions] = await db.pool.execute(`
      SELECT
        ms.*,
        u.full_name,
        u.email
      FROM meeting_sessions ms
      JOIN users u ON ms.user_id = u.id
      WHERE ms.meeting_id = ?
      ORDER BY ms.join_time DESC
    `, [meetingId]);

    res.json({ success: true, sessions });
  } catch (error) {
    console.error('Error getting meeting sessions:', error);
    res.status(500).json({ success: false, message: 'Failed to get meeting sessions' });
  }
};


