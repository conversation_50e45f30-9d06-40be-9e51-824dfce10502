-- Script to check for duplicate feedback issues and missing booking_ids

-- 1. Check for duplicate pending issues (same meeting + user)
SELECT 
    meeting_id,
    user_id,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id) as issue_ids,
    GROUP_CONCAT(booking_id) as booking_ids
FROM meeting_issues 
WHERE issue_type = 'pending'
GROUP BY meeting_id, user_id
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 2. Check for meeting_issues with NULL booking_id
SELECT 
    mi.id,
    mi.meeting_id,
    mi.user_id,
    mi.issue_type,
    mi.booking_id,
    m.meeting_date,
    m.student_id,
    m.teacher_id,
    u.full_name as student_name,
    t.full_name as teacher_name
FROM meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
JOIN users u ON m.student_id = u.id
JOIN users t ON m.teacher_id = t.id
WHERE mi.booking_id IS NULL
ORDER BY mi.created_at DESC;

-- 3. Check for meetings that might have corresponding bookings but missing booking_id
SELECT 
    mi.id as issue_id,
    mi.meeting_id,
    mi.booking_id as current_booking_id,
    m.meeting_date,
    m.student_id,
    m.teacher_id,
    b.id as potential_booking_id,
    b.datetime as booking_datetime,
    ABS(TIMESTAMPDIFF(MINUTE, b.datetime, m.meeting_date)) as time_diff_minutes,
    b.status as booking_status
FROM meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
LEFT JOIN bookings b ON b.student_id = m.student_id
LEFT JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id AND tp.user_id = m.teacher_id
WHERE mi.booking_id IS NULL
  AND b.id IS NOT NULL
  AND DATE(b.datetime) = DATE(m.meeting_date)
  AND b.status != 'cancelled'
  AND ABS(TIMESTAMPDIFF(MINUTE, b.datetime, m.meeting_date)) <= 120
ORDER BY mi.created_at DESC, time_diff_minutes ASC;

-- 4. Summary statistics
SELECT 
    'Total meeting_issues' as metric,
    COUNT(*) as count
FROM meeting_issues
UNION ALL
SELECT 
    'Pending issues' as metric,
    COUNT(*) as count
FROM meeting_issues 
WHERE issue_type = 'pending'
UNION ALL
SELECT 
    'Issues with NULL booking_id' as metric,
    COUNT(*) as count
FROM meeting_issues 
WHERE booking_id IS NULL
UNION ALL
SELECT 
    'Duplicate pending issues' as metric,
    COUNT(*) as count
FROM (
    SELECT meeting_id, user_id
    FROM meeting_issues 
    WHERE issue_type = 'pending'
    GROUP BY meeting_id, user_id
    HAVING COUNT(*) > 1
) as duplicates;
