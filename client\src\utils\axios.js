import axios from 'axios';

const instance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '',
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add a request interceptor
instance.interceptors.request.use(
  (config) => {
    // Get the token from localStorage
    const token = localStorage.getItem('token');

    // If token exists, add it to the headers
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // For multipart/form-data, let the browser set the Content-Type
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }

    // Add /api prefix to all requests except for socket.io
    if (!config.url.startsWith('/socket.io') && !config.url.startsWith('/api')) {
      config.url = `/api${config.url}`;
    }

    // Add a timestamp to prevent caching
    const separator = config.url.includes('?') ? '&' : '?';
    config.url = `${config.url}${separator}_t=${Date.now()}`;

    console.log('Request config:', {
      url: config.url,
      method: config.method,
      hasToken: !!token,
      token: token ? `${token.substring(0, 10)}...` : null,
      headers: config.headers
    }); // Debug log

    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor
instance.interceptors.response.use(
  (response) => {
    // Log successful responses for debugging
    console.log('API Response:', {
      url: response.config.url,
      method: response.config.method,
      status: response.status,
      data: response.data
    });

    // Save teacher response for debugging
    if (response.config.url && response.config.url.includes('/teachers/')) {
      window.lastTeacherResponse = response.data;
      console.log('Teacher response saved:', response.data);
    }

    return response;
  },
  (error) => {
    // Log error responses for debugging
    console.error('API Error Details:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      headers: error.response?.headers,
      message: error.message,
      fullError: error
    });

    // Ensure error has response data for proper handling
    if (error.response && error.response.data) {
      // Error response is properly formatted
      return Promise.reject(error);
    } else if (error.response) {
      // Response exists but no data, create a proper error structure
      const enhancedError = new Error(error.response.statusText || 'Request failed');
      enhancedError.response = {
        ...error.response,
        data: {
          success: false,
          message: error.response.statusText || 'Request failed'
        }
      };
      return Promise.reject(enhancedError);
    } else {
      // Network error or other issue
      const enhancedError = new Error(error.message || 'Network error');
      enhancedError.response = {
        status: 0,
        data: {
          success: false,
          message: error.message || 'Network error'
        }
      };
      return Promise.reject(enhancedError);
    }
  }
);

export default instance;
