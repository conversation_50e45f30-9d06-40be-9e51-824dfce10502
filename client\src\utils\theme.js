import { createTheme, alpha } from '@mui/material/styles';

const theme = createTheme({
  direction: 'rtl',
  palette: {
    primary: {
      main: '#1B5E20',
      light: '#2E7D32',
      dark: '#1B5E20',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#FFD700',
      light: '#FFE57F',
      dark: '#FFC107',
      contrastText: '#1B5E20',
    },
    background: {
      default: '#FAFAFA',
      paper: '#FFFFFF',
    },
  },
  typography: {
    fontFamily: [
      '"Noto Naskh Arabic"',
      '"Amiri"',
      'sans-serif',
    ].join(','),
    h1: {
      fontFamily: '"<PERSON><PERSON>", serif',
      fontWeight: 700,
    },
    h2: {
      fontFamily: '"Amiri", serif',
      fontWeight: 700,
    },
    h3: {
      fontFamily: '"Amiri", serif',
      fontWeight: 700,
    },
    h4: {
      fontFamily: '"<PERSON><PERSON>", serif',
      fontWeight: 600,
    },
    h5: {
      fontFamily: '"<PERSON><PERSON>", serif',
      fontWeight: 600,
    },
    h6: {
      fontFamily: '"<PERSON><PERSON>", serif',
      fontWeight: 600,
    },
    subtitle1: {
      fontFamily: '"Noto Naskh Arabic", serif',
    },
    subtitle2: {
      fontFamily: '"Noto Naskh Arabic", serif',
    },
    body1: {
      fontFamily: '"Noto Naskh Arabic", serif',
    },
    body2: {
      fontFamily: '"Noto Naskh Arabic", serif',
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '30px',
          textTransform: 'none',
          fontSize: '1rem',
          padding: '8px 24px',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 10px rgba(0, 0, 0, 0.15)',
          },
        },
        contained: {
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '15px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: '0 8px 20px rgba(0, 0, 0, 0.1)',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: '15px',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: '12px',
            '&:hover .MuiOutlinedInput-notchedOutline': {
              borderColor: alpha('#1B5E20', 0.5),
            },
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.05)',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: '30px',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'scale(1.05)',
          },
        },
      },
    },
  },
  shape: {
    borderRadius: 12,
  },
  shadows: [
    'none',
    '0 2px 4px rgba(0,0,0,0.05)',
    '0 4px 8px rgba(0,0,0,0.05)',
    '0 6px 12px rgba(0,0,0,0.05)',
    '0 8px 16px rgba(0,0,0,0.05)',
    // ... continue with default shadows
  ],
});

export default theme;
