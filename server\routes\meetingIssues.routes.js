const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const db = require('../config/db');
const { calculateCommission } = require('../utils/commission');

// Function to apply commission for a completed booking
async function applyCommissionForBooking(bookingId) {
  const connection = await db.pool.getConnection();

  try {
    await connection.beginTransaction();

    // Get booking details with teacher info
    const [bookingRows] = await connection.execute(
      `SELECT
         b.*,
         tp.user_id as teacher_user_id,
         u.full_name as teacher_name
       FROM bookings b
       JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
       JOIN users u ON tp.user_id = u.id
       WHERE b.id = ?`,
      [bookingId]
    );

    if (bookingRows.length === 0) {
      console.log(`Booking ${bookingId} not found for commission calculation`);
      await connection.rollback();
      return;
    }

    const booking = bookingRows[0];

    // Check if commission has already been applied
    const [existingEarnings] = await connection.execute(
      'SELECT id FROM admin_earnings WHERE meeting_id = ?',
      [`booking_${bookingId}`]
    );

    if (existingEarnings.length > 0) {
      console.log(`Commission already applied for booking ${bookingId}`);
      await connection.rollback();
      return;
    }

    // Calculate lesson amount based on duration
    const duration = parseInt(booking.duration) || 50;
    const isHalfLesson = duration === 25;
    const fullLessonPrice = parseFloat(booking.price_per_lesson) || 0;
    const actualLessonPrice = isHalfLesson ? fullLessonPrice / 2 : fullLessonPrice;

    // Calculate commission
    const commission = calculateCommission(actualLessonPrice);

    // Add teacher earnings to their balance
    await connection.execute(
      'UPDATE users SET balance = balance + ? WHERE id = ?',
      [commission.teacherEarnings, booking.teacher_user_id]
    );

    // Record the commission payment
    await connection.execute(
      `INSERT INTO payments (booking_id, teacher_profile_id, student_id, amount, status, type, payment_method, created_at)
       VALUES (?, ?, ?, ?, 'completed', 'commission_lesson', 'system', NOW())`,
      [bookingId, booking.teacher_profile_id, booking.student_id, commission.teacherEarnings]
    );

    // Record admin earnings (using booking_id as meeting_id for compatibility)
    await connection.execute(
      `INSERT INTO admin_earnings (meeting_id, teacher_id, student_id, lesson_amount, commission_rate, commission_amount, teacher_earnings)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        `booking_${bookingId}`, // Use booking_id with prefix to distinguish from meeting_id
        booking.teacher_user_id,
        booking.student_id,
        actualLessonPrice,
        commission.commissionRate,
        commission.commissionAmount,
        commission.teacherEarnings
      ]
    );

    await connection.commit();
    console.log(`💰 Commission applied for booking ${bookingId} via feedback:`, {
      teacherUserId: booking.teacher_user_id,
      teacherName: booking.teacher_name,
      fullLessonPrice: fullLessonPrice,
      actualLessonPrice: actualLessonPrice,
      duration: duration,
      isHalfLesson: isHalfLesson,
      commissionRate: commission.commissionRate,
      teacherEarnings: commission.teacherEarnings,
      commissionAmount: commission.commissionAmount
    });

  } catch (error) {
    await connection.rollback();
    console.error(`Error applying commission for booking ${bookingId}:`, error);
  } finally {
    connection.release();
  }
}

// Submit a meeting issue (student)
router.post('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    let { meeting_id, issue_type, description, booking_id } = req.body;

    // Ensure booking_id is numeric; otherwise try to find it
    if (booking_id !== undefined && booking_id !== null) {
      if (/^\d+$/.test(booking_id)) {
        booking_id = parseInt(booking_id, 10);
      } else {
        booking_id = null;
      }
    }

    // If booking_id is still null, try to find it using meeting_id
    if (!booking_id && meeting_id) {
      console.log(`Attempting to find booking_id for meeting_id: ${meeting_id}`);
      try {
        // Get meeting details first
        const [meetingRows] = await db.pool.execute(
          `SELECT student_id, teacher_id, meeting_date FROM meetings WHERE id = ? LIMIT 1`,
          [meeting_id]
        );

        if (meetingRows.length > 0) {
          const meeting = meetingRows[0];

          // Try to find corresponding booking
          const [bookingRows] = await db.pool.execute(
            `SELECT b.id
             FROM bookings b
             JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
             WHERE b.student_id = ? AND tp.user_id = ?
             AND DATE(b.datetime) = DATE(?)
             AND b.status != 'cancelled'
             AND ABS(TIMESTAMPDIFF(MINUTE, b.datetime, ?)) <= 120
             ORDER BY ABS(TIMESTAMPDIFF(MINUTE, b.datetime, ?)) ASC
             LIMIT 1`,
            [meeting.student_id, meeting.teacher_id, meeting.meeting_date, meeting.meeting_date, meeting.meeting_date]
          );

          if (bookingRows.length > 0) {
            booking_id = bookingRows[0].id;
            console.log(`Found booking_id: ${booking_id} for meeting_id: ${meeting_id}`);
          } else {
            console.log(`No booking found for meeting_id: ${meeting_id}`);
          }
        }
      } catch (findErr) {
        console.error('Error finding booking_id:', findErr);
      }
    }

    if (!meeting_id || !issue_type) {
      return res.status(400).json({ success: false, message: 'Missing fields' });
    }

    // For non-pending issues, booking_id is required
    if (issue_type !== 'pending' && !booking_id) {
      return res.status(400).json({
        success: false,
        message: 'booking_id is required for feedback submission'
      });
    }

    // Special handling for 'pending' type to prevent duplicates
    if (issue_type === 'pending') {
      // Check if a pending issue already exists for this meeting and user
      const [existingPending] = await db.pool.execute(
        `SELECT id FROM meeting_issues WHERE meeting_id = ? AND user_id = ? AND issue_type = 'pending' LIMIT 1`,
        [meeting_id, userId]
      );

      if (existingPending.length) {
        // Update existing pending issue with booking_id if provided
        if (booking_id) {
          await db.pool.execute(
            `UPDATE meeting_issues SET booking_id = ?, updated_at = NOW() WHERE id = ?`,
            [booking_id, existingPending[0].id]
          );
        }
        return res.json({ success: true, message: 'Pending issue already exists, updated if needed' });
      }
    }

    // 1. Check for an existing pending record for this meeting/user
    const [pendingRows] = await db.pool.execute(
      `SELECT id FROM meeting_issues WHERE meeting_id = ? AND user_id = ? AND issue_type = 'pending' LIMIT 1`,
      [meeting_id, userId]
    );

    if (pendingRows.length) {
      // Update the pending row, mark it resolved
      const newStatus = issue_type === 'no_issue' ? 'resolved' : 'pending';
      if (booking_id !== undefined && booking_id !== null) {
        await db.pool.execute(
          `UPDATE meeting_issues
           SET issue_type = ?, description = ?, status = ?, booking_id = ?, updated_at = NOW()
           WHERE id = ?`,
          [issue_type, description || '', newStatus, booking_id, pendingRows[0].id]
        );
      } else {
        // keep existing booking_id
        await db.pool.execute(
          `UPDATE meeting_issues
           SET issue_type = ?, description = ?, status = ?, updated_at = NOW()
           WHERE id = ?`,
          [issue_type, description || '', newStatus, pendingRows[0].id]
        );
      }
    } else {
      // Insert a brand-new row (teacher complaint or feedback)
      let status = 'resolved';
      if (['student_attended_no_commission', 'student_absent'].includes(issue_type)) {
        status = 'pending';
      }
      await db.pool.execute(
        `INSERT INTO meeting_issues (meeting_id, user_id, booking_id, issue_type, description, status, created_at)
         VALUES (?, ?, ?, ?, ?, ?, NOW())`,
        [meeting_id, userId, booking_id ?? null, issue_type, description || '', status]
      );
    }

    // update booking status based on feedback and apply commission if successful
    try {
      // Directly update booking status using booking_id
      if (booking_id) {
        const newStatus = issue_type === 'no_issue' ? 'completed' : 'issue_reported';
        const [updateRes] = await db.pool.execute(
          `UPDATE bookings SET status = ? WHERE id = ?`,
          [newStatus, booking_id]
        );
        console.log('Booking status update', {
          booking_id,
          newStatus,
          affectedRows: updateRes.affectedRows
        });

        // If lesson completed successfully, apply commission to teacher
        if (issue_type === 'no_issue') {
          console.log(`Lesson completed successfully, applying commission for booking ${booking_id}`);
          await applyCommissionForBooking(booking_id);
        }
      }
    } catch (bkErr) {
      console.error('Error updating booking status after feedback', bkErr);
    }

    // Also trigger immediate status update for all bookings with pending issues
    try {
      const meetingStatusService = require('../services/meetingStatusService');
      if (booking_id) {
        await meetingStatusService.updateSpecificBookingStatus(booking_id);
        console.log(`Immediately updated booking ${booking_id} status after issue creation`);
      }
    } catch (updateErr) {
      console.error('Error in immediate booking status update:', updateErr);
    }

    res.json({ success: true });
  } catch (err) {
    console.error('Error saving meeting issue', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get all issues (admin only or by bookingIds)
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { bookingIds } = req.query;
    if (bookingIds) {
      // جلب الشكاوى المرتبطة بقائمة bookingIds
      const ids = bookingIds.split(',').map(id => parseInt(id, 10)).filter(Boolean);
      if (!ids.length) return res.json({ success: true, data: [] });
      const [issues] = await db.pool.execute(
        `SELECT * FROM meeting_issues WHERE booking_id IN (${ids.map(() => '?').join(',')})`,
        ids
      );
      return res.json({ success: true, data: issues });
    }
    // إذا لم يوجد bookingIds، فقط الإدارة ترى كل الشكاوى
    if (req.user.role !== 'admin') {
      return res.status(403).json({ success: false, message: 'Forbidden' });
    }
    const [issues] = await db.pool.execute(
      `SELECT
         mi.id,
         mi.meeting_id,
         mi.user_id,
         mi.booking_id,
         mi.issue_type,
         mi.description,
         mi.admin_reply,
         mi.status,
         mi.created_at,
         mi.updated_at,
         u.full_name as user_name,
         u.email as user_email,
         m.meeting_name,
         m.meeting_date,
         m.duration,
         m.room_name,
         b.datetime as booking_datetime,
         b.duration as booking_duration,
         b.status as booking_status,
         CASE
           WHEN mi.booking_id IS NOT NULL THEN 'booking'
           WHEN mi.meeting_id IS NOT NULL THEN 'meeting'
           ELSE 'unknown'
         END as issue_source
       FROM meeting_issues mi
       LEFT JOIN users u ON mi.user_id = u.id
       LEFT JOIN meetings m ON mi.meeting_id = m.id
       LEFT JOIN bookings b ON mi.booking_id = b.id
       ORDER BY mi.created_at DESC`
    );
    res.json({ success: true, data: issues });
  } catch (err) {
    console.error('Error fetching issues', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

// Get first pending issue for current student
router.get('/pending', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    // First try to find an existing pending issue for this user
    const [rows] = await db.pool.execute(
      `SELECT mi.*, m.meeting_name, m.room_name, m.meeting_date AS datetime, m.duration, u.full_name AS teacher_name
       FROM meeting_issues mi
       LEFT JOIN meetings m ON mi.meeting_id = m.id
       LEFT JOIN users u ON m.teacher_id = u.id
       WHERE mi.user_id = ? AND mi.issue_type = 'pending'
       ORDER BY mi.created_at ASC
       LIMIT 1`,
      [userId]
    );

    if (rows.length) {
      return res.json({ success: true, data: rows[0] });
    }

    // If no pending issue exists, try to find meetings without feedback
    // Exclude meetings that are associated with cancelled bookings
    const [meetings] = await db.pool.execute(
      `SELECT m.*, u.full_name AS teacher_name
       FROM meetings m
       LEFT JOIN users u ON m.teacher_id = u.id
       WHERE m.student_id = ? AND m.meeting_date <= NOW()
       AND NOT EXISTS (
         SELECT 1 FROM meeting_issues mi
         WHERE mi.meeting_id = m.id AND mi.user_id = ?
       )
       AND NOT EXISTS (
         SELECT 1 FROM bookings b
         JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
         WHERE b.student_id = m.student_id
         AND tp.user_id = m.teacher_id
         AND DATE(b.datetime) = DATE(m.meeting_date)
         AND b.status = 'cancelled'
       )
       ORDER BY m.meeting_date DESC
       LIMIT 1`,
      [userId, userId]
    );

    if (meetings.length) {
      const meeting = meetings[0];
      
      // Try to find the corresponding booking using multiple strategies
      // Try primary lookup joining teacher_profiles to match teacher user_id
      // Exclude cancelled bookings and look for closest time match within 2 hours
      const [bookings] = await db.pool.execute(
        `SELECT b.*
         FROM bookings b
         JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
         WHERE b.student_id = ? AND tp.user_id = ?
         AND DATE(b.datetime) = DATE(?)
         AND b.status != 'cancelled'
         AND ABS(TIMESTAMPDIFF(MINUTE, b.datetime, ?)) <= 120
         ORDER BY ABS(TIMESTAMPDIFF(MINUTE, b.datetime, ?)) ASC
         LIMIT 1`,
        [meeting.student_id, meeting.teacher_id, meeting.meeting_date, meeting.meeting_date, meeting.meeting_date]
      );

      let booking;
      if (!bookings.length) {
        // Try alternative lookup using just student and teacher
        // Exclude cancelled bookings
        const [altBookings] = await db.pool.execute(
          `SELECT b.*
           FROM bookings b
           WHERE b.student_id = ? AND b.teacher_profile_id = ?
           AND b.status = 'completed'
           AND b.status != 'cancelled'
           ORDER BY b.datetime DESC
           LIMIT 1`,
          [meeting.student_id, meeting.teacher_id]
        );

        if (!altBookings.length) {
          console.log(`No booking found for meeting ID: ${meeting.id}, Teacher: ${meeting.teacher_id}, Student: ${meeting.student_id}, Date: ${meeting.meeting_date}`);
          console.log('Proceeding without matched booking; will create pending issue with NULL booking_id');
          booking = null;
        }
        booking = altBookings[0];
      } else {
        booking = bookings[0];
      }

      // Create a new pending issue (handle duplicates gracefully)
      let newIssueId;
      try {
        const [result] = await db.pool.execute(
          `INSERT INTO meeting_issues (meeting_id, user_id, booking_id, issue_type, created_at)
           VALUES (?, ?, ?, 'pending', NOW())`,
          [meeting.id, userId, booking ? booking.id : null]
        );
        newIssueId = result.insertId;
      } catch (insertErr) {
        if (insertErr.code === 'ER_DUP_ENTRY') {
          // Duplicate pending issue exists, fetch it
          const [existing] = await db.pool.execute(
            `SELECT id FROM meeting_issues WHERE meeting_id = ? AND user_id = ? AND issue_type = 'pending' LIMIT 1`,
            [meeting.id, userId]
          );
          if (existing.length) {
            newIssueId = existing[0].id;
          } else {
            throw insertErr; // rethrow if something unexpected
          }
        } else {
          throw insertErr;
        }
      }

      const [newIssue] = await db.pool.execute(
        `SELECT mi.*, m.meeting_name, m.room_name, m.meeting_date AS datetime, m.duration, u.full_name AS teacher_name
         FROM meeting_issues mi
         LEFT JOIN meetings m ON mi.meeting_id = m.id
         LEFT JOIN users u ON m.teacher_id = u.id
         WHERE mi.id = ?`,
        [newIssueId]
      );

      // Trigger immediate booking status update if booking_id exists
      if (booking && booking.id) {
        try {
          const meetingStatusService = require('../services/meetingStatusService');
          await meetingStatusService.updateSpecificBookingStatus(booking.id);
          console.log(`Immediately updated booking ${booking.id} status after pending issue creation`);
        } catch (updateErr) {
          console.error('Error in immediate booking status update for pending issue:', updateErr);
        }
      }

      return res.json({ success: true, data: newIssue[0] });
    }

    console.log('No pending issues or meetings to process');
    res.json({ success: true, data: null });
  } catch (err) {
    console.error('Error in pending issues route:', {
      error: err,
      sqlState: err.sqlState,
      sqlMessage: err.sqlMessage,
      stack: err.stack,
      userId: req.user.id
    });

    // Provide more specific error messages based on the error type
    if (err.sqlState === '42S22') {
      return res.status(400).json({ success: false, message: 'Missing column in database' });
    } else if (err.sqlState === '23000') {
      return res.status(400).json({ success: false, message: 'Data integrity violation' });
    }

    res.status(500).json({ 
      success: false, 
      message: 'Server error',
      error: err.message
    });
  }
});

// Resolve an issue (admin only)
router.put('/:id/resolve', authenticateToken, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ success: false, message: 'Forbidden' });
    }

    const issueId = parseInt(req.params.id, 10);
    const { reply } = req.body;
    if (isNaN(issueId)) {
      return res.status(400).json({ success: false, message: 'Invalid issue ID' });
    }

    // Update the issue status to resolved
    const [result] = await db.pool.execute(
      `UPDATE meeting_issues 
       SET status = 'resolved', admin_reply = ?, updated_at = NOW() 
       WHERE id = ?`,
      [reply || null, issueId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ success: false, message: 'Issue not found' });
    }

    // Get the updated issue to return
    const [updatedIssue] = await db.pool.execute(
      `SELECT mi.*, u.full_name as user_name, u.email as user_email, m.meeting_name
       FROM meeting_issues mi
       LEFT JOIN users u ON mi.user_id = u.id
       LEFT JOIN meetings m ON mi.meeting_id = m.id
       WHERE mi.id = ?`,
      [issueId]
    );

        // Send email to student if reply provided
    try {
      if (reply && updatedIssue.length) {
        const emailService = require('../utils/emailService');
        await emailService.sendEmail({
          to: updatedIssue[0].user_email,
          subject: 'Response to your meeting issue - Allemni online',
          html: `<p>Dear ${updatedIssue[0].user_name},</p><p>${reply}</p><p>Best regards,<br/>Allemni online Admin Team</p>`
        });
      }
    } catch (emailErr) {
      console.error('Error sending issue reply email:', emailErr);
    }

    res.json({ 
      success: true, 
      message: 'Issue resolved successfully',
      data: updatedIssue[0]
    });
  } catch (err) {
    console.error('Error resolving issue:', err);
    res.status(500).json({ success: false, message: 'Server error' });
  }
});

module.exports = router;
