import React, { useState } from 'react';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Typography
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { convertFromDatabaseTime, formatDateInStudentTimezone } from '../utils/timezone';

// Extend dayjs plugins
dayjs.extend(utc);

const MeetingFeedbackDialog = ({ open, meeting, timezone = null, onSubmit, onClose }) => {
  const { t } = useTranslation();
  const [issueType, setIssueType] = useState('');
  const [description, setDescription] = useState('');

  const handleSubmit = () => {
    if (onSubmit && meeting) {
      onSubmit(meeting.meeting_id || meeting.id, {
        booking_id: (typeof meeting.booking_id === 'number' || (typeof meeting.booking_id === 'string' && /^\d+$/.test(meeting.booking_id)) ) ? Number(meeting.booking_id) : null,
        issue_type: issueType,
        description
      });
    }
  };

  const disabled = !issueType;

  return (
    <Dialog
      open={open}
      onClose={(_, reason) => {
        // Prevent closing by backdrop click or escape to force answer
        if (reason === 'backdropClick' || reason === 'escapeKeyDown') return;
        if (onClose) onClose();
      }}
      disableEscapeKeyDown
      fullWidth
      maxWidth="sm"
    >
      <DialogTitle>{t('meetings.feedback.title')}</DialogTitle>
      <DialogContent dividers>
        {meeting && (
          <Typography variant="subtitle1" gutterBottom>
            {t('meetings.feedback.detailsHeader')}
          </Typography>
        )}
        {meeting && (
          <>
            <Typography variant="body2" gutterBottom>
              {t('meetings.teacher')}: {meeting.teacher_name || '—'}
            </Typography>
            <Typography variant="body2" gutterBottom>
              {(() => {
                const dateStr = timezone ? formatDateInStudentTimezone(meeting.datetime, timezone, 'YYYY-MM-DD') : dayjs.utc(meeting.datetime).local().format('YYYY-MM-DD');
                return `${t('meetings.date')}: ${dateStr}`;
              })()}
            </Typography>
          </>
        )}
        {meeting && (
          <Typography variant="body2" gutterBottom>
            {(() => {
              const startStr = timezone ? formatDateInStudentTimezone(meeting.datetime, timezone, 'hh:mm A') : dayjs.utc(meeting.datetime).local().format('hh:mm A');
              const endStr = timezone ? formatDateInStudentTimezone(dayjs(meeting.datetime).add(meeting.duration || 50, 'minute').toDate(), timezone, 'hh:mm A') : dayjs.utc(meeting.datetime).local().add(meeting.duration || 50, 'minute').format('hh:mm A');
              return `${t('meetings.time')}: ${startStr} - ${endStr} (${meeting.duration || 50} ${t('common.minutes')})`;
            })()}
          </Typography>
        )}
        <Typography gutterBottom>
          {t('meetings.feedback.question')}
        </Typography>
        <RadioGroup
          value={issueType}
          onChange={(e) => setIssueType(e.target.value)}
        >
          <FormControlLabel value="no_issue" control={<Radio />} label={t('meetings.feedback.success')} />
          <FormControlLabel value="teacher_absent" control={<Radio />} label={t('meetings.feedback.teacher_absent')} />
          <FormControlLabel value="technical_issue" control={<Radio />} label={t('meetings.feedback.technical_issue')} />
        </RadioGroup>
        {(issueType === 'teacher_absent' || issueType === 'technical_issue') && (
          <TextField
            label={t('meetings.feedback.details')}
            multiline
            minRows={3}
            fullWidth
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            sx={{ mt: 2 }}
          />
        )}
      </DialogContent>
      <DialogActions>
        {/* no cancel button to force selection */}
        <Button disabled={disabled} variant="contained" onClick={handleSubmit}>
          {t('common.submit')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MeetingFeedbackDialog;
