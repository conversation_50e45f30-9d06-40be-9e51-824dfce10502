const emailService = require('../utils/emailService');
const emailTemplates = require('../templates/bookingRescheduleEmails');

/**
 * Parse timezone offset from string format (e.g., "UTC+03:00")
 * @param {string} timezone - Timezone string
 * @returns {number} Offset in minutes
 */
const parseTimezoneOffset = (timezone) => {
  if (!timezone || timezone === 'UTC') return 0;
  
  const match = timezone.match(/UTC([+-])(\d{2}):(\d{2})/);
  if (!match) return 0;
  
  const sign = match[1] === '+' ? 1 : -1;
  const hours = parseInt(match[2]);
  const minutes = parseInt(match[3]);
  
  return sign * (hours * 60 + minutes);
};

/**
 * Convert UTC datetime to user's timezone
 * @param {string} utcDatetime - UTC datetime string
 * @param {string} userTimezone - User's timezone (e.g., "UTC+03:00")
 * @returns {Date} Date object in user's timezone
 */
const convertToUserTimezone = (utcDatetime, userTimezone) => {
  const utcDate = new Date(utcDatetime);
  const offsetMinutes = parseTimezoneOffset(userTimezone);
  
  // Add the offset to UTC time to get user's local time
  return new Date(utcDate.getTime() + (offsetMinutes * 60 * 1000));
};

/**
 * Format date for email display in user's timezone
 * @param {string} datetime - UTC datetime string
 * @param {string} userTimezone - User's timezone (e.g., "UTC+03:00")
 * @returns {string} Formatted date
 */
const formatBookingDate = (datetime, userTimezone = 'UTC') => {
  const userDate = convertToUserTimezone(datetime, userTimezone);
  
  const options = { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    timeZone: 'UTC' // We already converted to user timezone, so display as UTC
  };
  
  const arabicDate = userDate.toLocaleDateString('ar-EG', options);
  const englishDate = userDate.toLocaleDateString('en-US', options);
  
  return `${arabicDate} - ${englishDate}`;
};

/**
 * Format time for email display in user's timezone
 * @param {string} datetime - UTC datetime string
 * @param {string} userTimezone - User's timezone (e.g., "UTC+03:00")
 * @returns {string} Formatted time with timezone
 */
const formatBookingTime = (datetime, userTimezone = 'UTC') => {
  const userDate = convertToUserTimezone(datetime, userTimezone);
  
  const options = { 
    hour: '2-digit', 
    minute: '2-digit',
    timeZone: 'UTC' // We already converted to user timezone, so display as UTC
  };
  
  const arabicTime = userDate.toLocaleTimeString('ar-EG', options);
  const englishTime = userDate.toLocaleTimeString('en-US', options);
  
  // Add timezone info
  const timezoneDisplay = userTimezone === 'UTC' ? 'UTC' : userTimezone;
  
  return `${arabicTime} - ${englishTime} (${timezoneDisplay})`;
};

/**
 * Send reschedule confirmation email to teacher
 * @param {Object} bookingData - Booking information (includes old and new datetime)
 * @param {Object} teacherData - Teacher information (includes timezone)
 * @param {Object} studentData - Student information
 * @param {string} rescheduleReason - Reason for rescheduling (optional)
 * @returns {Promise} Email send result
 */
const sendTeacherRescheduleEmail = async (bookingData, teacherData, studentData, rescheduleReason = null) => {
  try {
    console.log('📧 Sending teacher reschedule confirmation email...');
    
    const templateData = {
      teacherName: teacherData.full_name,
      studentName: studentData.full_name,
      oldBookingDate: formatBookingDate(bookingData.originalDatetime, teacherData.timezone),
      oldBookingTime: formatBookingTime(bookingData.originalDatetime, teacherData.timezone),
      newBookingDate: formatBookingDate(bookingData.newDatetime, teacherData.timezone),
      newBookingTime: formatBookingTime(bookingData.newDatetime, teacherData.timezone),
      duration: bookingData.duration || '50',
      rescheduleReason: rescheduleReason
    };

    const htmlContent = emailTemplates.getTeacherRescheduleTemplate(templateData);

    const result = await emailService.sendEmail({
      to: teacherData.email,
      subject: 'تأكيد تغيير موعد الدرس - Lesson Reschedule Confirmed - Allemni online',
      html: htmlContent
    });

    console.log('✅ Teacher reschedule confirmation email sent successfully');
    return result;
  } catch (error) {
    console.error('❌ Error sending teacher reschedule email:', error);
    throw error;
  }
};

/**
 * Send reschedule notification email to student
 * @param {Object} bookingData - Booking information (includes old and new datetime)
 * @param {Object} teacherData - Teacher information
 * @param {Object} studentData - Student information (includes timezone)
 * @param {string} rescheduleReason - Reason for rescheduling (optional)
 * @returns {Promise} Email send result
 */
const sendStudentRescheduleNotificationEmail = async (bookingData, teacherData, studentData, rescheduleReason = null) => {
  try {
    console.log('📧 Sending student reschedule notification email...');
    
    const templateData = {
      studentName: studentData.full_name,
      teacherName: teacherData.full_name,
      oldBookingDate: formatBookingDate(bookingData.originalDatetime, studentData.timezone),
      oldBookingTime: formatBookingTime(bookingData.originalDatetime, studentData.timezone),
      newBookingDate: formatBookingDate(bookingData.newDatetime, studentData.timezone),
      newBookingTime: formatBookingTime(bookingData.newDatetime, studentData.timezone),
      duration: bookingData.duration || '50',
      rescheduleReason: rescheduleReason
    };

    const htmlContent = emailTemplates.getStudentRescheduleNotificationTemplate(templateData);

    const result = await emailService.sendEmail({
      to: studentData.email,
      subject: 'تم تغيير موعد درسك - Your Lesson Has Been Rescheduled - Allemni online',
      html: htmlContent
    });

    console.log('✅ Student reschedule notification email sent successfully');
    return result;
  } catch (error) {
    console.error('❌ Error sending student reschedule notification email:', error);
    throw error;
  }
};

/**
 * Send reschedule confirmation email to student when they reschedule
 * @param {Object} bookingData - Booking information (includes old and new datetime)
 * @param {Object} teacherData - Teacher information
 * @param {Object} studentData - Student information (includes timezone)
 * @param {string} rescheduleReason - Reason for rescheduling (optional)
 * @returns {Promise} Email send result
 */
const sendStudentRescheduleEmail = async (bookingData, teacherData, studentData, rescheduleReason = null) => {
  try {
    console.log('📧 Sending student reschedule confirmation email...');

    const templateData = {
      studentName: studentData.full_name,
      teacherName: teacherData.full_name,
      oldBookingDate: formatBookingDate(bookingData.originalDatetime, studentData.timezone),
      oldBookingTime: formatBookingTime(bookingData.originalDatetime, studentData.timezone),
      newBookingDate: formatBookingDate(bookingData.newDatetime, studentData.timezone),
      newBookingTime: formatBookingTime(bookingData.newDatetime, studentData.timezone),
      duration: bookingData.duration || '50',
      rescheduleReason: rescheduleReason
    };

    const htmlContent = emailTemplates.getStudentRescheduleTemplate(templateData);

    const result = await emailService.sendEmail({
      to: studentData.email,
      subject: 'تأكيد تغيير موعد الدرس - Lesson Reschedule Confirmed - Allemni online',
      html: htmlContent
    });

    console.log('✅ Student reschedule confirmation email sent successfully');
    return result;
  } catch (error) {
    console.error('❌ Error sending student reschedule email:', error);
    throw error;
  }
};

/**
 * Send reschedule notification email to teacher when student reschedules
 * @param {Object} bookingData - Booking information (includes old and new datetime)
 * @param {Object} teacherData - Teacher information (includes timezone)
 * @param {Object} studentData - Student information
 * @param {string} rescheduleReason - Reason for rescheduling (optional)
 * @returns {Promise} Email send result
 */
const sendTeacherRescheduleNotificationEmail = async (bookingData, teacherData, studentData, rescheduleReason = null) => {
  try {
    console.log('📧 Sending teacher reschedule notification email...');

    const templateData = {
      teacherName: teacherData.full_name,
      studentName: studentData.full_name,
      oldBookingDate: formatBookingDate(bookingData.originalDatetime, teacherData.timezone),
      oldBookingTime: formatBookingTime(bookingData.originalDatetime, teacherData.timezone),
      newBookingDate: formatBookingDate(bookingData.newDatetime, teacherData.timezone),
      newBookingTime: formatBookingTime(bookingData.newDatetime, teacherData.timezone),
      duration: bookingData.duration || '50',
      rescheduleReason: rescheduleReason
    };

    const htmlContent = emailTemplates.getTeacherRescheduleNotificationTemplate(templateData);

    const result = await emailService.sendEmail({
      to: teacherData.email,
      subject: 'الطالب غير موعد الدرس - Student Rescheduled Lesson - Allemni online',
      html: htmlContent
    });

    console.log('✅ Teacher reschedule notification email sent successfully');
    return result;
  } catch (error) {
    console.error('❌ Error sending teacher reschedule notification email:', error);
    throw error;
  }
};

/**
 * Send all appropriate reschedule emails based on who rescheduled
 * @param {Object} bookingData - Booking information (includes old and new datetime)
 * @param {Object} teacherData - Teacher information (includes timezone)
 * @param {Object} studentData - Student information (includes timezone)
 * @param {string} rescheduledBy - Who rescheduled ('teacher' or 'student')
 * @param {string} rescheduleReason - Reason for rescheduling (optional)
 * @returns {Promise} Array of email send results
 */
const sendRescheduleEmails = async (bookingData, teacherData, studentData, rescheduledBy, rescheduleReason = null) => {
  try {
    console.log(`📧 Sending reschedule emails for booking ${bookingData.id}, rescheduled by: ${rescheduledBy}`);

    const emailResults = [];

    if (rescheduledBy === 'teacher') {
      // Teacher rescheduled: send confirmation to teacher, notification to student
      const teacherEmailPromise = sendTeacherRescheduleEmail(bookingData, teacherData, studentData, rescheduleReason);
      const studentEmailPromise = sendStudentRescheduleNotificationEmail(bookingData, teacherData, studentData, rescheduleReason);

      const results = await Promise.allSettled([teacherEmailPromise, studentEmailPromise]);
      emailResults.push(...results);

    } else if (rescheduledBy === 'student') {
      // Student rescheduled: send confirmation to student, notification to teacher
      const studentEmailPromise = sendStudentRescheduleEmail(bookingData, teacherData, studentData, rescheduleReason);
      const teacherEmailPromise = sendTeacherRescheduleNotificationEmail(bookingData, teacherData, studentData, rescheduleReason);

      const results = await Promise.allSettled([studentEmailPromise, teacherEmailPromise]);
      emailResults.push(...results);
    }

    // Log results
    emailResults.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        console.log(`✅ Reschedule email ${index + 1} sent successfully`);
      } else {
        console.error(`❌ Reschedule email ${index + 1} failed:`, result.reason);
      }
    });

    return emailResults;
  } catch (error) {
    console.error('❌ Error in sendRescheduleEmails:', error);
    throw error;
  }
};

module.exports = {
  sendTeacherRescheduleEmail,
  sendStudentRescheduleNotificationEmail,
  sendStudentRescheduleEmail,
  sendTeacherRescheduleNotificationEmail,
  sendRescheduleEmails,
  formatBookingDate,
  formatBookingTime,
  parseTimezoneOffset,
  convertToUserTimezone
};
