import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Container,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Grid,
  CircularProgress,
  Alert
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import Layout from '../../components/Layout';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import PendingIcon from '@mui/icons-material/Pending';
import CancelIcon from '@mui/icons-material/Cancel';
import { useAuth } from '../../contexts/AuthContext';
import { format } from 'date-fns';
import axios from '../../utils/axios';

const Wallet = () => {
  const { t } = useTranslation();
  const { currentUser, token } = useAuth();
  const [balance, setBalance] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  useEffect(() => {
    if (currentUser && token) {
      fetchBalance();
      fetchTransactions();
    }
  }, [currentUser, token, page, rowsPerPage]);

  const fetchBalance = async () => {
    try {
      const { data } = await axios.get('/wallet/balance', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (data.success) {
        setBalance(data.balance);
      }
    } catch (error) {
      console.error('Error fetching balance:', error);
    }
  };

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const { data } = await axios.get('/wallet/transactions', {
        params: {
          page: page + 1,
          limit: rowsPerPage
        },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setTransactions(data.data.transactions);
        setTotalCount(data.data.pagination.total);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError(t('wallet.errorFetchingTransactions'));
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getStatusChip = (status, type) => {
    if (type === 'credit') {
      return (
        <Chip
          icon={<ArrowDownwardIcon />}
          label={t('wallet.credit')}
          color="success"
          size="small"
          variant="outlined"
        />
      );
    } else if (type === 'cancelled') {
      return (
        <Chip
          icon={<CancelIcon />}
          label={t('wallet.cancelled')}
          color="error"
          size="small"
          variant="outlined"
        />
      );
    } else {
      return (
        <Chip
          icon={<PendingIcon />}
          label={t('wallet.pending')}
          color="warning"
          size="small"
          variant="outlined"
        />
      );
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg">
        <Grid container spacing={3}>
          {/* Balance Card */}
          <Grid item xs={12} md={4}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                height: '100%',
                textAlign: 'center',
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                color: 'white'
              }}
            >
              <AccountBalanceWalletIcon sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h4" gutterBottom>
                {t('wallet.title')}
              </Typography>
              <Box sx={{ mt: 3 }}>
                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                  ${balance !== null ? balance : '0.00'}
                </Typography>
                <Typography variant="subtitle1" sx={{ mt: 1 }}>
                  {t('wallet.currentBalance')}
                </Typography>
              </Box>
            </Paper>
          </Grid>

          {/* Transactions Table */}
          <Grid item xs={12} md={8}>
            <Paper elevation={3} sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom>
                {t('wallet.transactionHistory')}
              </Typography>

              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : error ? (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              ) : transactions.length === 0 ? (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    {t('wallet.noTransactions')}
                  </Typography>
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>{t('wallet.date')}</TableCell>
                          <TableCell>{t('wallet.description')}</TableCell>
                          <TableCell align="right">{t('wallet.amount')}</TableCell>
                          <TableCell align="center">{t('wallet.status')}</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {transactions.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>
                              {format(new Date(transaction.created_at), 'yyyy-MM-dd HH:mm')}
                            </TableCell>
                            <TableCell>
                              {transaction.student_name ?
                                t('wallet.lessonFrom', { student: transaction.student_name }) :
                                t('wallet.payment')}
                            </TableCell>
                            <TableCell align="right">
                              <Typography
                                color={transaction.transaction_type === 'credit' ? 'success.main' :
                                       transaction.transaction_type === 'cancelled' ? 'error.main' :
                                       'text.secondary'}
                                fontWeight="bold"
                              >
                                {transaction.transaction_type === 'credit' ? '+' : ''}
                                ${transaction.amount}
                              </Typography>
                            </TableCell>
                            <TableCell align="center">
                              {getStatusChip(transaction.status, transaction.transaction_type)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <TablePagination
                    component="div"
                    count={totalCount}
                    page={page}
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    rowsPerPageOptions={[5, 10, 25]}
                    labelRowsPerPage={t('common.rowsPerPage')}
                  />
                </>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default Wallet;
