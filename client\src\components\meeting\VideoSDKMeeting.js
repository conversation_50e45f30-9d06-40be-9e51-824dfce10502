import React, { useState, useEffect, useRef, useMemo } from "react";
import {
  MeetingProvider,
  MeetingConsumer,
  useMeeting,
  useParticipant,
} from "@videosdk.live/react-sdk";
import "./index.css";
import { getToken, authToken, createMeeting, createMeetingWithCustomId, validateMeeting } from "./api";
import JoiningScreen from "./components/screens/JoiningScreen";
import { MeetingAppProvider } from "./MeetingAppContext";
import MeetingContainer from "./MeetingContainer";
import LeaveScreen from "./components/screens/LeaveScreen";
import WaitingToJoinScreen from "./components/screens/WaitingToJoinScreen";
import {
  createCameraVideoTrack,
  createMicrophoneAudioTrack,
} from "@videosdk.live/react-sdk";
import { useAuth } from "../../contexts/AuthContext";

function VideoSDKMeeting({ roomId, meetingId, meetingData, onClose }) {
  const { currentUser } = useAuth();
  const [token, setToken] = useState("");
  const [meetingIdState, setMeetingId] = useState(roomId || "");
  const [participantName, setParticipantName] = useState("");
  const [micOn, setMicOn] = useState(true);
  const [webcamOn, setWebcamOn] = useState(true);
  const [selectedMic, setSelectedMic] = useState({ id: null });
  const [selectedWebcam, setSelectedWebcam] = useState({ id: null });
  const [selectWebcamDeviceId, setSelectWebcamDeviceId] = useState(
    selectedWebcam.id
  );
  const [selectMicDeviceId, setSelectMicDeviceId] = useState(selectedMic.id);
  const [isMeetingStarted, setMeetingStarted] = useState(false);
  const [isMeetingLeft, setIsMeetingLeft] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const isMobile = window.matchMedia(
    "only screen and (max-width: 768px)"
  ).matches;

  useEffect(() => {
    if (isMobile) {
      window.onbeforeunload = () => {
        return "Are you sure you want to exit?";
      };
    }
  }, [isMobile]);

  // تهيئة الميتنج تلقائياً عند تحميل المكون
  useEffect(() => {
    const initializeMeeting = async () => {
      if (roomId && !isInitialized) {
        try {
          const token = await getToken();
          console.log('Initializing meeting with roomId:', roomId);
          console.log('Meeting data:', meetingData);

          // استخدام videosdk_meeting_id إذا كان متوفراً، وإلا إنشاء غرفة جديدة
          let finalMeetingId = roomId;

          if (meetingData && meetingData.videosdk_meeting_id) {
            finalMeetingId = meetingData.videosdk_meeting_id;
            console.log('Using existing VideoSDK meeting ID:', finalMeetingId);
          } else {
            console.log('No VideoSDK meeting ID found, using room_name:', roomId);
            // يمكن إنشاء meeting جديد هنا إذا لزم الأمر
          }

          if (!token) {
            console.error('VideoSDK token is empty. Ensure REACT_APP_VIDEOSDK_TOKEN or AUTH_URL is configured.');
            return;
          }
          setToken(token);
          setMeetingId(finalMeetingId);
          setParticipantName(currentUser?.full_name || "User");
          setIsInitialized(true);
          setMeetingStarted(true);

          console.log('Meeting initialized successfully with:', {
            originalRoomId: roomId,
            finalMeetingId: finalMeetingId,
            participantName: currentUser?.full_name || "User"
          });
        } catch (error) {
          console.error('Error initializing meeting:', error);
        }
      }
    };

    initializeMeeting();
  }, [roomId, isInitialized, currentUser, meetingData]);

  const getMeetingAndToken = async (id) => {
    const token = authToken;
    const meetingId = id == null ? await createMeeting({ token }) : id;
    setToken(token);
    setMeetingId(meetingId);
    return { meetingId, token };
  };

  const onMeetingJoined = () => {
    // Meeting joined callback
  };

  return (
    <>
      <MeetingAppProvider
        selectedMic={selectedMic}
        selectedWebcam={selectedWebcam}
        setSelectedMic={setSelectedMic}
        setSelectedWebcam={setSelectedWebcam}
      >
        {isMeetingStarted ? (
          <MeetingProvider
            config={{
              meetingId: meetingIdState,
              micEnabled: micOn,
              webcamEnabled: webcamOn,
              name: participantName ? participantName : "TestUser",
              multiStream: true,
              customCameraVideoTrack: selectWebcamDeviceId
                ? createCameraVideoTrack({
                    cameraId: selectWebcamDeviceId,
                    optimizationMode: "motion",
                    encoderConfig: "h540p_w960p",
                    facingMode: "environment",
                  })
                : undefined,
              customMicrophoneAudioTrack: selectMicDeviceId
                ? createMicrophoneAudioTrack({
                    microphoneId: selectMicDeviceId,
                    echoCancellation: false,
                    noiseSuppression: true,
                  })
                : undefined,
            }}
            token={token}
            reinitialiseMeetingOnConfigChange={true}
            joinWithoutUserInteraction={true}
          >
            <MeetingContainer
              meetingData={meetingData}
              onMeetingLeave={() => {
                setToken("");
                setMeetingId("");
                setParticipantName("");
                setWebcamOn(false);
                setMicOn(false);
                setMeetingStarted(false);
                if (onClose) onClose();
              }}
              setIsMeetingLeft={setIsMeetingLeft}
              selectedMic={selectedMic}
              selectedWebcam={selectedWebcam}
              selectWebcamDeviceId={selectWebcamDeviceId}
              setSelectWebcamDeviceId={setSelectWebcamDeviceId}
              selectMicDeviceId={selectMicDeviceId}
              setSelectMicDeviceId={setSelectMicDeviceId}
              micEnabled={micOn}
              webcamEnabled={webcamOn}
              onClose={onClose}
            />
          </MeetingProvider>
        ) : isMeetingLeft ? (
          <LeaveScreen
            setIsMeetingLeft={setIsMeetingLeft}
            onClose={onClose}
          />
        ) : !roomId ? (
          <JoiningScreen
            participantName={participantName}
            setParticipantName={setParticipantName}
            setMeetingId={setMeetingId}
            setToken={setToken}
            setMicOn={setMicOn}
            micEnabled={micOn}
            webcamEnabled={webcamOn}
            setSelectedMic={setSelectedMic}
            setSelectedWebcam={setSelectedWebcam}
            setWebcamOn={setWebcamOn}
            onClickStartMeeting={() => {
              setMeetingStarted(true);
            }}
            startMeeting={isMeetingStarted}
            setIsMeetingLeft={setIsMeetingLeft}
            meetingId={meetingIdState}
            token={token}
            getMeetingAndToken={getMeetingAndToken}
            onMeetingJoined={onMeetingJoined}
          />
        ) : (
          <div className="flex items-center justify-center h-screen bg-gray-900">
            <div className="text-white text-lg">جاري تحضير الميتنج...</div>
          </div>
        )}
      </MeetingAppProvider>
    </>
  );
}

export default VideoSDKMeeting;
