import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import {
  Box,
  Button,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Card,
  CardMedia,
  CardContent,
  CardActions
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import VideoFileIcon from '@mui/icons-material/VideoFile';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Layout from '../../components/Layout';
import { useAuth } from '../../contexts/AuthContext';

const EditVideoUpload = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const [videoFile, setVideoFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');
  const [uploadProgress, setUploadProgress] = useState(0);

  // تحميل الفيديو الحالي
  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
    } else {
      // التحقق من وجود فيديو مؤقت في localStorage أولاً
      const savedVideoUrl = localStorage.getItem('teacherVideoUrl');
      if (savedVideoUrl) {
        setVideoUrl(savedVideoUrl);
        setSuccess(true);
      } else {
        // إذا لم يوجد فيديو مؤقت، تحميل من قاعدة البيانات
        const loadCurrentVideo = async () => {
          try {
            const response = await axios.get('/api/teacher/profile');
            if (response.data && response.data.profile && response.data.profile.intro_video_url) {
              const videoPath = response.data.profile.intro_video_url;
              const fullVideoUrl = videoPath;

              setVideoUrl(fullVideoUrl);
              setSuccess(true);

              // حفظ في localStorage للاستخدام المستقبلي
              localStorage.setItem('teacherVideoUrl', fullVideoUrl);
              localStorage.setItem('teacherVideoRelativePath', videoPath);
            }
          } catch (error) {
            console.error('Error loading current video:', error);
          }
        };

        loadCurrentVideo();
      }
    }
  }, [currentUser, navigate]);

  const handleVideoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // التحقق من نوع الملف
      const validTypes = ['video/mp4', 'video/webm', 'video/ogg'];
      if (!validTypes.includes(file.type)) {
        setError(t('teacher.invalidVideoFormat') || 'نوع الفيديو غير مدعوم');
        return;
      }

      // التحقق من حجم الملف (1MB - 100MB)
      if (file.size < 1 * 1024 * 1024) {
        setError(t('teacher.videoTooSmall') || 'حجم الفيديو يجب أن يكون على الأقل 1MB');
        return;
      }
      if (file.size > 100 * 1024 * 1024) {
        setError(t('teacher.videoTooLarge') || 'حجم الفيديو كبير جداً (الحد الأقصى 100MB)');
        return;
      }

      setVideoFile(file);
      setError('');
    }
  };

  const handleUpload = async () => {
    if (!videoFile) {
      setError(t('teacher.noVideoSelected') || 'لم يتم اختيار فيديو');
      return;
    }

    setLoading(true);
    setError('');
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('introVideo', videoFile);

    try {
      const response = await axios.post('/api/teacher/upload-video', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      if (response.data && response.data.videoUrl) {
        const videoPath = response.data.videoUrl;
        const fullVideoUrl = videoPath;

        setVideoUrl(fullVideoUrl);
        setSuccess(true);
        setVideoFile(null);

        // حفظ في localStorage مثل صفحة upload-video
        localStorage.setItem('teacherVideoUrl', fullVideoUrl);
        localStorage.setItem('teacherVideoRelativePath', videoPath);

        // إضافة علامة لتحديث البيانات في صفحة تعديل التطبيق
        localStorage.setItem('videoUpdated', 'true');
      }
    } catch (err) {
      console.error('Video upload error:', err);
      setError(err.response?.data?.message || t('teacher.videoUploadError') || 'حدث خطأ أثناء رفع الفيديو');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAndReturn = () => {
    // إضافة علامة لتحديث البيانات في صفحة تعديل التطبيق
    localStorage.setItem('videoUpdated', 'true');
    navigate('/teacher/edit-application');
  };

  const handleDeleteVideo = async () => {
    try {
      setLoading(true);

      // الحصول على المسار النسبي من localStorage
      const relativePath = localStorage.getItem('teacherVideoRelativePath');
      const deleteUrl = relativePath || videoUrl;

      if (deleteUrl) {
        await axios.delete(`/api/teacher/delete-video?videoUrl=${encodeURIComponent(deleteUrl)}`);
      }

      // تحديث الواجهة
      setVideoUrl('');
      setSuccess(false);
      setVideoFile(null);

      // حذف من localStorage
      localStorage.removeItem('teacherVideoUrl');
      localStorage.removeItem('teacherVideoRelativePath');

      // إضافة علامة لتحديث البيانات في صفحة تعديل التطبيق
      localStorage.setItem('videoUpdated', 'true');
    } catch (err) {
      console.error('Error deleting video:', err);
      setError(err.response?.data?.message || t('teacher.videoDeleteError') || 'حدث خطأ أثناء حذف الفيديو');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout title={t('teacher.editVideoUpload.title') || 'تعديل الفيديو التعريفي'}>
      <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
        {/* زر العودة */}
        <Box sx={{ mb: 3 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => {
              // إضافة علامة لتحديث البيانات
              localStorage.setItem('videoUpdated', 'true');
              navigate('/teacher/edit-application');
            }}
            disabled={loading}
          >
            {t('common.back') || 'العودة'}
          </Button>
        </Box>

        <Paper elevation={3} sx={{ p: 4 }}>
          <Typography variant="h5" component="h1" gutterBottom>
            {t('teacher.editVideoUpload.title') || 'تعديل الفيديو التعريفي'}
          </Typography>

          <Typography variant="body1" paragraph>
            {t('teacher.editVideoUpload.description') || 'يمكنك تغيير الفيديو التعريفي الخاص بك هنا. الفيديو الجديد سيحل محل الفيديو الحالي.'}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && videoUrl ? (
            <Box sx={{ mb: 4 }}>
              <Alert severity="success" sx={{ mb: 2 }}>
                {t('teacher.editVideoUpload.success') || 'تم رفع الفيديو بنجاح!'}
              </Alert>

              <Card>
                <CardMedia
                  component="video"
                  controls
                  src={videoUrl.startsWith('/') ? videoUrl : `/${videoUrl}`}
                  sx={{ height: 300 }}
                />
                <CardContent>
                  <Typography variant="body2" color="text.secondary">
                    {t('teacher.editVideoUpload.videoReady') || 'الفيديو جاهز ومحفوظ'}
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button
                    size="small"
                    color="primary"
                    variant="contained"
                    onClick={handleSaveAndReturn}
                  >
                    {t('teacher.editVideoUpload.saveAndReturn') || 'حفظ والعودة'}
                  </Button>
                  <Button
                    size="small"
                    color="error"
                    onClick={handleDeleteVideo}
                    disabled={loading}
                  >
                    {t('teacher.editVideoUpload.delete') || 'حذف الفيديو'}
                  </Button>
                </CardActions>
              </Card>
            </Box>
          ) : (
            <Box>
              <Box sx={{ mb: 3, p: 2, border: '1px dashed grey', borderRadius: 1 }}>
                <Typography variant="subtitle1" gutterBottom>
                  {t('teacher.editVideoUpload.requirements') || 'متطلبات الفيديو'}:
                </Typography>
                <Typography variant="body2">
                  • {t('teacher.editVideoUpload.formatRequirement') || 'الصيغة'}: MP4, WebM, OGG
                </Typography>
                <Typography variant="body2">
                  • {t('teacher.editVideoUpload.sizeRequirement') || 'الحجم'}: 1MB - 100MB
                </Typography>
                <Typography variant="body2">
                  • {t('teacher.editVideoUpload.lengthRequirement') || 'المدة'}: 2-5 {t('teacher.editVideoUpload.minutes') || 'دقائق'}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                <input
                  type="file"
                  accept="video/mp4,video/webm,video/ogg"
                  style={{ display: 'none' }}
                  id="edit-video-upload-input"
                  onChange={handleVideoChange}
                  disabled={loading}
                />
                <label htmlFor="edit-video-upload-input">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<CloudUploadIcon />}
                    disabled={loading}
                    sx={{ mb: 2 }}
                  >
                    {t('teacher.editVideoUpload.selectVideo') || 'اختر فيديو جديد'}
                  </Button>
                </label>

                {videoFile && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <VideoFileIcon sx={{ mr: 1 }} />
                    <Typography variant="body2">
                      {videoFile.name} ({Math.round(videoFile.size / (1024 * 1024))} MB)
                    </Typography>
                  </Box>
                )}

                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleUpload}
                  disabled={!videoFile || loading}
                  startIcon={loading ? <CircularProgress size={20} /> : null}
                >
                  {loading ? (t('teacher.editVideoUpload.uploading') || 'جاري الرفع...') : (t('teacher.editVideoUpload.upload') || 'رفع الفيديو')}
                </Button>

                {loading && (
                  <Box sx={{ width: '100%', mt: 2 }}>
                    <Typography variant="body2" align="center">
                      {uploadProgress}%
                    </Typography>
                    <Box
                      sx={{
                        height: 10,
                        bgcolor: '#e0e0e0',
                        borderRadius: 5,
                        mt: 1
                      }}
                    >
                      <Box
                        sx={{
                          height: '100%',
                          width: `${uploadProgress}%`,
                          bgcolor: 'primary.main',
                          borderRadius: 5
                        }}
                      />
                    </Box>
                  </Box>
                )}
              </Box>
            </Box>
          )}
        </Paper>
      </Box>
    </Layout>
  );
};

export default EditVideoUpload;
