# ميزة سبب إعادة الجدولة مع الإيميلات - Reschedule Reason with Email Notifications

## نظرة عامة - Overview

تم إضافة ميزة شاملة لإعادة جدولة الدروس مع إمكانية كتابة سبب إعادة الجدولة وإرسال إيميلات تلقائية للمعلم والطالب.

This feature allows teachers to reschedule lessons with a reason and automatically sends email notifications to both teacher and student.

## الميزات الجديدة - New Features

### 📝 **سبب إعادة الجدولة:**
- **حقل اختياري** لكتابة سبب إعادة الجدولة
- **يظهر في نافذة إعادة الجدولة** للمعلم والطالب
- **يحفظ في قاعدة البيانات** مع معلومات إضافية

### 👥 **إعادة الجدولة للجميع:**
- **المعلم يمكنه إعادة الجدولة**: كما كان من قبل
- **الطالب يمكنه إعادة الجدولة**: ميزة جديدة مع مراعاة القيود
- **مراعاة الأوقات المتاحة**: للمعلم وحجوزات الطالب الأخرى

### 📧 **إيميلات تلقائية ذكية:**
#### عند إعادة جدولة المعلم:
- **إيميل للمعلم**: تأكيد إعادة الجدولة مع التفاصيل
- **إيميل للطالب**: إشعار بتغيير الموعد مع التفاصيل الجديدة

#### عند إعادة جدولة الطالب:
- **إيميل للطالب**: تأكيد إعادة الجدولة مع التفاصيل
- **إيميل للمعلم**: إشعار بتغيير الطالب للموعد مع التفاصيل

### ⏰ **دعم المناطق الزمنية:**
- **كل مستخدم يرى الأوقات بمنطقته الزمنية**
- **عرض الموعد القديم والجديد** بوضوح
- **تحويل تلقائي** من UTC إلى المنطقة الزمنية للمستخدم

## التغييرات المطلوبة - Required Changes

### 1. قاعدة البيانات - Database Changes

```sql
-- إضافة أعمدة جديدة لجدول bookings
ALTER TABLE `bookings` 
ADD COLUMN `reschedule_reason` TEXT NULL AFTER `cancelled_at`;

ALTER TABLE `bookings` 
ADD COLUMN `rescheduled_by` ENUM('student', 'teacher') NULL AFTER `reschedule_reason`;

ALTER TABLE `bookings` 
ADD COLUMN `rescheduled_at` TIMESTAMP NULL AFTER `rescheduled_by`;

ALTER TABLE `bookings` 
ADD COLUMN `original_datetime` DATETIME NULL AFTER `rescheduled_at`;
```

### 2. الملفات المحدثة - Updated Files

#### Backend:
- `server/routes/bookings.routes.js` - تحديث API endpoint لإعادة الجدولة
- `server/templates/bookingRescheduleEmails.js` - قوالب الإيميلات الجديدة
- `server/services/bookingRescheduleEmailService.js` - خدمة إرسال الإيميلات
- `server/migrations/add_reschedule_reason.sql` - ملف migration

#### Frontend:
- `client/src/pages/teacher/Bookings.js` - إضافة حقل سبب إعادة الجدولة (محدث)
- `client/src/pages/student/Bookings.js` - إضافة واجهة إعادة الجدولة للطلاب (جديد)
- `client/src/i18n/i18n.js` - إضافة النصوص للترجمة (محدث)

## كيفية عمل الميزة - How It Works

### 1. عندما يقوم المعلم بإعادة جدولة درس:

1. **يفتح نافذة إعادة الجدولة** من صفحة الحجوزات
2. **يختار الموعد الجديد** من الأوقات المتاحة
3. **يكتب سبب إعادة الجدولة** (اختياري)
4. **يؤكد إعادة الجدولة**

### 2. عندما يقوم الطالب بإعادة جدولة درس:

1. **يفتح نافذة إعادة الجدولة** من صفحة الحجوزات
2. **يرى الأوقات المتاحة للمعلم** (مع مراعاة حجوزاته الأخرى)
3. **يختار الموعد الجديد** من الأوقات المتاحة
4. **يكتب سبب إعادة الجدولة** (اختياري)
5. **يؤكد إعادة الجدولة**

### 3. ما يحدث في النظام:

1. **تحديث قاعدة البيانات**:
   - حفظ الموعد الجديد
   - حفظ الموعد القديم في `original_datetime`
   - حفظ سبب إعادة الجدولة
   - حفظ من قام بإعادة الجدولة (`teacher`)
   - حفظ وقت إعادة الجدولة

2. **إرسال الإيميلات**:
   - إيميل تأكيد للمعلم
   - إيميل إشعار للطالب

## تصميم الإيميلات - Email Design

### 🎨 **المميزات:**
- **ثنائي اللغة**: عربي وإنجليزي في نفس الإيميل
- **تصميم احترافي**: ألوان متناسقة (أزرق للإعادة الجدولة)
- **مقارنة واضحة**: الموعد القديم مقابل الموعد الجديد
- **سبب إعادة الجدولة**: يظهر إذا تم كتابته
- **أزرار عمل**: روابط لعرض الجدول أو الحجوزات

### 📧 **محتوى الإيميلات:**

#### للمعلم:
```
الموضوع: تأكيد تغيير موعد الدرس - Lesson Reschedule Confirmed

المحتوى:
- تأكيد إعادة الجدولة
- تفاصيل الموعد القديم (بمنطقته الزمنية)
- تفاصيل الموعد الجديد (بمنطقته الزمنية)
- سبب إعادة الجدولة (إن وُجد)
- رابط لعرض الجدول
```

#### للطالب:
```
الموضوع: تم تغيير موعد درسك - Your Lesson Has Been Rescheduled

المحتوى:
- إشعار بتغيير الموعد
- تفاصيل الموعد القديم (بمنطقته الزمنية)
- تفاصيل الموعد الجديد (بمنطقته الزمنية)
- سبب إعادة الجدولة (إن وُجد)
- ملاحظة مهمة لتحديث التقويم
- رابط لعرض الحجوزات
```

## هيكل قاعدة البيانات الجديد - New Database Structure

```sql
CREATE TABLE `bookings` (
  `id` int NOT NULL,
  `teacher_profile_id` int NOT NULL,
  `student_id` int NOT NULL,
  `datetime` datetime NOT NULL,
  `status` enum('scheduled','completed','cancelled','issue_reported','ongoing') NOT NULL DEFAULT 'scheduled',
  `cancellation_reason` TEXT NULL,
  `cancelled_by` ENUM('student', 'teacher') NULL,
  `cancelled_at` TIMESTAMP NULL,
  `reschedule_reason` TEXT NULL,                      -- جديد
  `rescheduled_by` ENUM('student', 'teacher') NULL,   -- جديد
  `rescheduled_at` TIMESTAMP NULL,                    -- جديد
  `original_datetime` DATETIME NULL,                  -- جديد
  `duration` varchar(10) DEFAULT '50',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## خطوات التطبيق - Implementation Steps

### 1. قاعدة البيانات:
```bash
mysql -u username -p teach_me_islam_arabic < apply_reschedule_reason_migration.sql
```

### 2. إعادة تشغيل الخادم:
```bash
npm restart
```

### 3. اختبار الميزة:

#### للمعلمين:
1. سجل دخول كمعلم
2. اذهب إلى `/teacher/bookings`
3. اضغط "إعادة جدولة" على أي حجز
4. اختر موعد جديد واكتب سبب إعادة الجدولة
5. تحقق من وصول الإيميلات

#### للطلاب:
1. سجل دخول كطالب
2. اذهب إلى `/student/bookings`
3. اضغط "تفاصيل" على أي حجز مجدول
4. اضغط "إعادة جدولة"
5. اختر موعد جديد واكتب سبب إعادة الجدولة
6. تحقق من وصول الإيميلات

## أمثلة على المناطق الزمنية - Timezone Examples

```
الوقت في قاعدة البيانات (UTC):
- الموعد القديم: 2024-01-15 14:30:00
- الموعد الجديد: 2024-01-16 16:00:00

المعلم (UTC+03:00 - القاهرة):
- الموعد القديم: ٠٥:٣٠ م - 05:30 PM (UTC+03:00)
- الموعد الجديد: ٠٧:٠٠ م - 07:00 PM (UTC+03:00)

الطالب (UTC-05:00 - نيويورك):
- الموعد القديم: ٠٩:٣٠ ص - 09:30 AM (UTC-05:00)
- الموعد الجديد: ١١:٠٠ ص - 11:00 AM (UTC-05:00)
```

## ملاحظات مهمة - Important Notes

- **سبب إعادة الجدولة اختياري** ولا يمنع العملية إذا لم يتم كتابته
- **الإيميلات ترسل بشكل غير متزامن** لعدم تأخير استجابة API
- **نظام مقاوم للأخطاء**: فشل الإيميل لا يؤثر على إعادة الجدولة
- **دعم كامل للمناطق الزمنية**: كل مستخدم يرى الوقت بمنطقته
- **حفظ التاريخ**: يتم حفظ الموعد الأصلي للمراجعة لاحقاً

## الاختبار - Testing

تم اختبار الميزة بنجاح:
- ✅ تنسيق المناطق الزمنية (للمعلم والطالب)
- ✅ إرسال الإيميلات (جميع السيناريوهات)
- ✅ حفظ البيانات في قاعدة البيانات
- ✅ واجهة المستخدم (المعلم والطالب)
- ✅ التحقق من تضارب الأوقات
- ✅ مراعاة الأوقات المتاحة للمعلم
- ✅ مراعاة حجوزات الطالب الأخرى

### 📧 **الإيميلات المرسلة في الاختبار:**
- **12 إيميل** تم إرسالها بنجاح إلى `<EMAIL>`
- **4 سيناريوهات مختلفة**: إعادة جدولة المعلم والطالب
- **مناطق زمنية مختلفة**: UTC+03:00 للمعلم، UTC-05:00 للطالب

**الميزة مكتملة وجاهزة للاستخدام! 🚀**
