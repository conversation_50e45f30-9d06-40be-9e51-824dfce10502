{"name": "gauge", "version": "3.0.2", "description": "A terminal based horizontal guage", "main": "index.js", "scripts": {"test": "standard && tap test/*.js --coverage"}, "repository": {"type": "git", "url": "https://github.com/iarna/gauge"}, "keywords": ["progressbar", "progress", "gauge"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/npm/gauge/issues"}, "homepage": "https://github.com/npm/gauge", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.2", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.1", "object-assign": "^4.1.1", "signal-exit": "^3.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.2"}, "devDependencies": {"readable-stream": "^2.0.6", "require-inject": "^1.4.0", "standard": "^11.0.1", "tap": "^12.0.1", "through2": "^2.0.0"}, "files": ["base-theme.js", "CHANGELOG.md", "error.js", "has-color.js", "index.js", "LICENSE", "package.json", "plumbing.js", "process.js", "progress-bar.js", "README.md", "render-template.js", "set-immediate.js", "set-interval.js", "spin.js", "template-item.js", "theme-set.js", "themes.js", "wide-truncate.js"], "engines": {"node": ">=10"}}