import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Container,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Grid,
  CircularProgress,
  Alert,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Tabs,
  Tab
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import Layout from '../../components/Layout';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import PendingIcon from '@mui/icons-material/Pending';
import CancelIcon from '@mui/icons-material/Cancel';
import { useAuth } from '../../contexts/AuthContext';
import { format } from 'date-fns';
import axios from '../../utils/axios';
import { PayPalScriptProvider, PayPalButtons } from "@paypal/react-paypal-js";
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import StripePayment from '../../components/StripePayment';

// Initialize Stripe
const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY);

const Wallet = () => {
  const { t } = useTranslation();
  const { currentUser, token } = useAuth();
  const [balance, setBalance] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [isAddMoneyDialogOpen, setIsAddMoneyDialogOpen] = useState(false);
  const [amountToAdd, setAmountToAdd] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('paypal'); // 'paypal' or 'stripe'

  // PayPal configuration with environment variables
  const paypalOptions = {
    "client-id": process.env.REACT_APP_PAYPAL_CLIENT_ID,
    currency: "USD",
    intent: "capture"
  };

  useEffect(() => {
    if (currentUser && token) {
      fetchBalance();
      fetchTransactions();
    }
  }, [currentUser, token, page, rowsPerPage]);

  const fetchBalance = async () => {
    try {
      const { data } = await axios.get('/wallet/balance', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      if (data.success) {
        setBalance(data.balance);
      }
    } catch (error) {
      console.error('Error fetching balance:', error);
    }
  };

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const { data } = await axios.get('/wallet/transactions', {
        params: {
          page: page + 1,
          limit: rowsPerPage
        },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setTransactions(data.data.transactions);
        setTotalCount(data.data.pagination.total);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError(t('wallet.errorFetchingTransactions'));
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleAddMoneyClick = () => {
    setIsAddMoneyDialogOpen(true);
  };

  const handleCloseAddMoneyDialog = () => {
    setIsAddMoneyDialogOpen(false);
    setAmountToAdd('');
  };

  const handleAmountChange = (event) => {
    const value = event.target.value;
    if (/^\d*\.?\d{0,2}$/.test(value) || value === '') {
      setAmountToAdd(value);
    }
  };

  const createPayPalOrder = async (data, actions) => {
    return actions.order.create({
      purchase_units: [
        {
          amount: {
            value: amountToAdd,
            currency_code: "USD"
          },
          description: "Add funds to wallet"
        }
      ]
    });
  };

  const onPayPalApprove = async (data, actions) => {
    try {
      // Don't capture here - just send the order ID to backend
      // The backend will handle the capture
      const response = await axios.post('/wallet/deposit', {
        paymentId: data.orderID,
        amount: parseFloat(amountToAdd),
        status: 'COMPLETED'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        setSuccessMessage(t('wallet.depositSuccess'));
        // Refresh balance and transactions
        fetchBalance();
        fetchTransactions();
        handleCloseAddMoneyDialog();
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      setError(t('wallet.errorProcessingPayment'));
    }
  };

  const handleCloseSuccessMessage = () => {
    setSuccessMessage('');
  };

  const handleStripeSuccess = (data) => {
    setSuccessMessage(t('wallet.depositSuccess'));
    // Refresh balance and transactions
    fetchBalance();
    fetchTransactions();
    handleCloseAddMoneyDialog();
  };

  const handleStripeError = (error) => {
    setError(error);
  };

  const getStatusChip = (status, type) => {
    if (type === 'debit') {
      return (
        <Chip
          icon={<ArrowUpwardIcon />}
          label={t('wallet.debit')}
          color="error"
          size="small"
          variant="outlined"
        />
      );
    } else if (type === 'credit') {
      return (
        <Chip
          icon={<ArrowDownwardIcon />}
          label={t('wallet.credit')}
          color="success"
          size="small"
          variant="outlined"
        />
      );
    } else if (status === 'pending') {
      return (
        <Chip
          icon={<PendingIcon />}
          label={t('wallet.pending')}
          color="warning"
          size="small"
          variant="outlined"
        />
      );
    } else {
      return (
        <Chip
          icon={<CancelIcon />}
          label={t('wallet.cancelled')}
          color="default"
          size="small"
          variant="outlined"
        />
      );
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg">
        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
        <Grid container spacing={3}>
          {/* Balance Card */}
          <Grid item xs={12} md={4}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                height: '100%',
                textAlign: 'center',
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                color: 'white'
              }}
            >
              <AccountBalanceWalletIcon sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h4" gutterBottom>
                {t('wallet.title')}
              </Typography>
              <Box sx={{ mt: 3 }}>
                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                  ${balance !== null ? balance : '0.00'}
                </Typography>
                <Typography variant="subtitle1" sx={{ mt: 1 }}>
                  {t('wallet.currentBalance')}
                </Typography>
              </Box>
              <Button
                variant="contained"
                color="primary"
                sx={{ 
                  mt: 3,
                  bgcolor: 'white',
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                  }
                }}
                onClick={handleAddMoneyClick}
              >
                {t('wallet.addMoney')}
              </Button>
            </Paper>
          </Grid>

          {/* Transactions Table */}
          <Grid item xs={12} md={8}>
            <Paper elevation={3} sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom>
                {t('wallet.transactionHistory')}
              </Typography>

              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : error ? (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              ) : transactions.length === 0 ? (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    {t('wallet.noTransactions')}
                  </Typography>
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>{t('wallet.date')}</TableCell>
                          <TableCell>{t('wallet.description')}</TableCell>
                          <TableCell align="right">{t('wallet.amount')}</TableCell>
                          <TableCell align="center">{t('wallet.status')}</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {transactions.map((transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>
                              {format(new Date(transaction.created_at), 'yyyy-MM-dd HH:mm')}
                            </TableCell>
                            <TableCell>
                              {transaction.type === 'deposit' ?
                                transaction.teacher_name :
                                transaction.teacher_name ?
                                  t('wallet.lessonWith', { teacher: transaction.teacher_name }) :
                                  t('wallet.payment')}
                            </TableCell>
                            <TableCell align="right">
                              <Typography
                                color={transaction.transaction_type === 'debit' ? 'error.main' :
                                       transaction.transaction_type === 'credit' ? 'success.main' :
                                       'text.secondary'}
                                fontWeight="bold"
                              >
                                {transaction.transaction_type === 'debit' ? '-' :
                                 transaction.transaction_type === 'credit' ? '+' : ''}
                                ${transaction.amount}
                              </Typography>
                            </TableCell>
                            <TableCell align="center">
                              {getStatusChip(transaction.status, transaction.transaction_type)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <TablePagination
                    component="div"
                    count={totalCount}
                    page={page}
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    rowsPerPageOptions={[5, 10, 25]}
                    labelRowsPerPage={t('common.rowsPerPage')}
                  />
                </>
              )}
            </Paper>
          </Grid>
        </Grid>
        </ProfileCompletionAlert>

        {/* Add Money Dialog */}
        <Dialog 
          open={isAddMoneyDialogOpen} 
          onClose={handleCloseAddMoneyDialog}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Typography variant="h6" component="div">
              {t('wallet.addMoney')}
            </Typography>
          </DialogTitle>
          <DialogContent>
            <Box sx={{ p: 2 }}>
              <TextField
                fullWidth
                label={t('wallet.amount')}
                value={amountToAdd}
                onChange={handleAmountChange}
                type="text"
                inputProps={{
                  inputMode: 'decimal',
                  pattern: '[0-9]*',
                }}
                sx={{ mb: 3 }}
                error={!!error}
                helperText={error}
              />

              {amountToAdd && parseFloat(amountToAdd) > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Tabs
                    value={paymentMethod}
                    onChange={(e, newValue) => setPaymentMethod(newValue)}
                    sx={{ mb: 2 }}
                  >
                    <Tab label="PayPal" value="paypal" />
                    <Tab label="Credit Card (Stripe)" value="stripe" />
                  </Tabs>

                  {paymentMethod === 'paypal' && (
                    <PayPalScriptProvider options={paypalOptions}>
                      <PayPalButtons
                        createOrder={createPayPalOrder}
                        onApprove={onPayPalApprove}
                        style={{
                          layout: "vertical",
                          color: "blue",
                          shape: "rect",
                          label: "paypal"
                        }}
                      />
                    </PayPalScriptProvider>
                  )}

                  {paymentMethod === 'stripe' && (
                    <Elements stripe={stripePromise}>
                      <StripePayment
                        amount={amountToAdd}
                        onSuccess={handleStripeSuccess}
                        onError={handleStripeError}
                      />
                    </Elements>
                  )}
                </Box>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseAddMoneyDialog} color="primary">
              {t('common.cancel')}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Success Message */}
        <Snackbar
          open={!!successMessage}
          autoHideDuration={6000}
          onClose={handleCloseSuccessMessage}
          message={successMessage}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        />
      </Container>
    </Layout>
  );
};

export default Wallet;
