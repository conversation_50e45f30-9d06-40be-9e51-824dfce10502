import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';
import axios from 'axios';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  IconButton,
  InputAdornment,
  useTheme,
  alpha,
  Divider,
  Stack,
  Fade,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  ArrowForward as ArrowForwardIcon,
  ArrowBack as ArrowBackIcon,
  Google as GoogleIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';

const Login = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { login, googleLogin } = useAuth();
  const isRtl = i18n.language === 'ar';

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [accountStatusMessage, setAccountStatusMessage] = useState('');

  // التحقق من رسائل حالة الحساب من navigation state أو localStorage
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Login page: Checking for account status messages');
      console.log('Location state:', location.state);
    }

    // Test: إضافة رسالة تجريبية للاختبار (تم حذفها بعد التأكد من عمل النظام)

    // التحقق من navigation state أولاً
    if (location.state && location.state.message) {
      console.log('Found message in location state:', location.state.message);
      setAccountStatusMessage(location.state.message);
      setError(location.state.message);
      return;
    }

    // التحقق من localStorage للرسائل المحفوظة
    const savedMessage = localStorage.getItem('accountStatusMessage');
    if (process.env.NODE_ENV === 'development') {
      console.log('Saved message in localStorage:', savedMessage);
    }

    if (savedMessage) {
      try {
        const messageData = JSON.parse(savedMessage);

        // استخدام الرسالة المترجمة حسب اللغة الحالية
        const displayMessage = i18n.language === 'ar' ? messageData.message : messageData.message_en;

        setAccountStatusMessage(displayMessage);
        setError(displayMessage);

        // تحديث location.state للتوافق مع باقي الكود
        location.state = {
          message: displayMessage,
          accountStatus: messageData.accountStatus,
          deleteScheduledAt: messageData.deleteScheduledAt
        };

        // حذف الرسالة من localStorage بعد عرضها
        localStorage.removeItem('accountStatusMessage');

        if (process.env.NODE_ENV === 'development') {
          console.log('Displaying account status message:', {
            accountStatus: messageData.accountStatus,
            message: displayMessage,
            language: i18n.language
          });
        }
      } catch (e) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error parsing saved account status message:', e);
        }
        localStorage.removeItem('accountStatusMessage');
      }
    } else if (process.env.NODE_ENV === 'development') {
      console.log('No account status message found');
    }
  }, [location.state, i18n.language]); // إضافة i18n.language للتحديث عند تغيير اللغة



  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setError('');
  };

  const handleGoogleSuccess = async (credentialResponse) => {
    setError('');
    setLoading(true);
    try {
      const result = await googleLogin(credentialResponse);
      if (!result.success) {
        throw new Error(result.message || t('auth.googleSignInError'));
      }
      if (result.success && result.user) {
        const { role } = result.user;

        if (!role) {
          throw new Error(t('auth.invalidRole'));
        }

        switch (role) {
          case 'admin':
            navigate('/admin/dashboard');
            break;
          case 'platform_teacher':
            navigate('/teacher/dashboard');
            break;
          case 'new_teacher':
            navigate('/teacher/application');
            break;
          case 'student':
            navigate('/student/dashboard');
            break;
          default:
            navigate('/');
        }
      }
    } catch (error) {
      console.error('Google login error:', error);
      setError(error.message || t('auth.googleSignInError'));
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleError = () => {
    setError(t('auth.googleSignInError'));
    setLoading(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    // Basic validation
    if (!formData.email || !formData.password) {
      setError(t('auth.fillAllFields'));
      setLoading(false);
      return;
    }

    // Clear error only after validation passes
    setError('');

    try {
      // أولاً، تحقق من حالة المستخدم قبل محاولة تسجيل الدخول
      const statusResponse = await axios.post('/api/auth/check-user-status', {
        email: formData.email
      });

      // إذا كان المستخدم محذوف أو مجدول للحذف
      if (!statusResponse.data.success && statusResponse.data.accountStatus) {
        const displayMessage = i18n.language === 'ar'
          ? statusResponse.data.message
          : statusResponse.data.message_en;

        setAccountStatusMessage(displayMessage);
        setError(displayMessage);

        // حفظ الرسالة في localStorage للمرات القادمة
        localStorage.setItem('accountStatusMessage', JSON.stringify({
          message: statusResponse.data.message,
          message_en: statusResponse.data.message_en,
          accountStatus: statusResponse.data.accountStatus,
          deleteScheduledAt: statusResponse.data.deleteScheduledAt
        }));

        setLoading(false);
        return;
      }

      // إذا كان المستخدم نشط، تابع عملية تسجيل الدخول العادية
      await login(formData.email, formData.password);
      navigate('/');
    } catch (error) {
      console.error('Login error details:', {
        error: error,
        response: error.response,
        data: error.response?.data,
        status: error.response?.status,
        message: error.message
      });

      // إذا كان الخطأ من التحقق من حالة المستخدم (المستخدم غير موجود)
      if (error.response && error.response.status === 404) {
        // تابع عملية تسجيل الدخول العادية لعرض رسالة الخطأ المناسبة
        try {
          await login(formData.email, formData.password);
          navigate('/');
        } catch (loginError) {
          const errorData = loginError.response?.data;
          if (errorData?.requiresVerification) {
            navigate('/verify-email', { state: { email: formData.email } });
          } else {
            let errorMessage;
            if (errorData?.errorType === 'EMAIL_NOT_FOUND') {
              errorMessage = t('auth.emailNotFound');
            } else if (errorData?.errorType === 'WRONG_PASSWORD') {
              errorMessage = t('auth.wrongPassword');
            } else if (errorData?.errorType === 'ACCOUNT_DELETED') {
              errorMessage = t('auth.accountDeleted', 'تم حذف هذا الحساب ولا يمكن تسجيل الدخول');
            } else if (errorData?.message) {
              errorMessage = errorData.message;
            } else if (loginError.message) {
              errorMessage = loginError.message;
            } else if (loginError.response?.status === 401) {
              errorMessage = t('auth.invalidCredentials');
            } else {
              errorMessage = t('auth.loginFailed');
            }
            setError(errorMessage);
          }
        }
      } else {
        // خطأ آخر في تسجيل الدخول
        const errorData = error.response?.data;
        if (errorData?.requiresVerification) {
          navigate('/verify-email', { state: { email: formData.email } });
        } else {
          let errorMessage;
          if (errorData?.errorType === 'EMAIL_NOT_FOUND') {
            errorMessage = t('auth.emailNotFound');
          } else if (errorData?.errorType === 'WRONG_PASSWORD') {
            errorMessage = t('auth.wrongPassword');
          } else if (errorData?.errorType === 'ACCOUNT_DELETED') {
            errorMessage = t('auth.accountDeleted', 'تم حذف هذا الحساب ولا يمكن تسجيل الدخول');
          } else if (errorData?.message) {
            errorMessage = errorData.message;
          } else if (error.message) {
            errorMessage = error.message;
          } else if (error.response?.status === 401) {
            errorMessage = t('auth.invalidCredentials');
          } else {
            errorMessage = t('auth.loginFailed');
          }
          setError(errorMessage);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        background: `linear-gradient(to bottom, ${alpha(theme.palette.primary.dark, 0.9)}, ${alpha('#000', 0.7)})`,
        py: 8,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: 'url("https://png.pngtree.com/background/20230216/original/pngtree-islamic-background-picture-image_2027687.jpg")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          zIndex: -1
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `radial-gradient(circle at center, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.dark, 0.4)})`,
          zIndex: -1
        }
      }}
    >
      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 1 }}>
        <Fade in timeout={1000}>
          <Paper
            elevation={24}
            sx={{
              p: { xs: 3, sm: 6 },
              borderRadius: 4,
              backdropFilter: 'blur(20px)',
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              boxShadow: `0 8px 32px ${alpha(theme.palette.primary.dark, 0.2)}`,
              position: 'relative',
              overflow: 'hidden',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: 4,
                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              }
            }}
          >
            <Box component="form" onSubmit={handleSubmit}>
              <Typography
                variant="h4"
                align="center"
                gutterBottom
                sx={{
                  fontWeight: 700,
                  mb: 4,
                  background: `linear-gradient(120deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  textShadow: '0 2px 4px rgba(0,0,0,0.1)',
                  position: 'relative',
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    bottom: -8,
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: 60,
                    height: 2,
                    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
                  }
                }}
              >
                {t('auth.welcomeBack')}
              </Typography>

              {/* رسائل حالة الحساب */}
              {accountStatusMessage && (
                <Alert
                  severity={location.state?.accountStatus === 'deleted' ? 'error' : 'warning'}
                  sx={{ mb: 3, borderRadius: 2 }}
                >
                  {accountStatusMessage}
                  {location.state?.accountStatus === 'pending_deletion' && (
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {i18n.language === 'ar'
                        ? 'يمكنك تسجيل الدخول وإلغاء عملية الحذف من صفحة الملف الشخصي.'
                        : 'You can log in and cancel the deletion from your profile page.'
                      }
                    </Typography>
                  )}
                </Alert>
              )}

              {/* رسائل الخطأ العادية */}
              {error && !accountStatusMessage && (
                <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
                  {error}
                </Alert>
              )}



              <Stack spacing={3}>
                <TextField
                  fullWidth
                  name="email"
                  label={t('common.email')}
                  value={formData.email}
                  onChange={handleChange}
                  error={Boolean(error)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Email />
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  fullWidth
                  name="password"
                  label={t('common.password')}
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleChange}
                  error={Boolean(error)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Lock />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowPassword(!showPassword)}
                          edge="end"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                  <Link
                    to="/forgot-password"
                    style={{
                      color: theme.palette.primary.main,
                      textDecoration: 'none',
                      fontSize: '0.875rem',
                    }}
                  >
                    {t('auth.forgotPassword')}
                  </Link>
                </Box>

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  endIcon={!isRtl ? <ArrowForwardIcon /> : <ArrowBackIcon />}
                  sx={{
                    mt: 2,
                    height: 48,
                    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    '&:hover': {
                      background: `linear-gradient(90deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
                    },
                  }}
                >
                  {loading ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    t('auth.signIn')
                  )}
                </Button>

                <Box sx={{ position: 'relative', my: 2 }}>
                  <Divider>
                    <Typography variant="body2" color="text.secondary">
                      {t('auth.or')}
                    </Typography>
                  </Divider>
                </Box>

                <GoogleOAuthProvider clientId="52320482193-ig6u5a5r3hi0gu65g683c34t5efc2b6s.apps.googleusercontent.com">
                  <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                    <GoogleLogin
                      onSuccess={handleGoogleSuccess}
                      onError={handleGoogleError}
                      type="standard"
                      theme="outline"
                      size="large"
                      width="400"
                      text="continue_with"
                      shape="rectangular"
                      logo_alignment="left"
                      useOneTap={false}
                      auto_select={false}
                    />
                  </Box>
                </GoogleOAuthProvider>

                <Box sx={{ textAlign: 'center', mt: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    {t('auth.noAccount')}{' '}
                    <Link
                      to="/register"
                      style={{
                        color: theme.palette.primary.main,
                        textDecoration: 'none',
                        fontWeight: 600,
                      }}
                    >
                      {t('auth.signUp')}
                    </Link>
                  </Typography>
                </Box>
              </Stack>
            </Box>
          </Paper>
        </Fade>
      </Container>
    </Box>
  );
};

export default Login;
