import React, { useEffect, useState } from 'react';
import {
  Box,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography
} from '@mui/material';
import ChatIcon from '@mui/icons-material/Chat';
import moment from 'moment-timezone';
import { formatDateInStudentTimezone } from '../../utils/timezone';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import i18n from '../../i18n/i18n';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';

const MyLessons = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [summaries, setSummaries] = useState([]);
  const [bookings, setBookings] = useState([]);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [teacherProfile, setTeacherProfile] = useState({});
  const [selectedBookings, setSelectedBookings] = useState([]);
  const [selectedStudentName, setSelectedStudentName] = useState('');
  // 1. أضف state لإدارة Dialog الشكوى
  const [complaintDialogOpen, setComplaintDialogOpen] = useState(false);
  const [complaintBooking, setComplaintBooking] = useState(null);
  const [complaintType, setComplaintType] = useState('');
  const [complaintDescription, setComplaintDescription] = useState('');
  const [submittingComplaint, setSubmittingComplaint] = useState(false);
  // 1. أضف state لتخزين الشكاوى لكل booking
  const [issues, setIssues] = useState([]);

  useEffect(() => {
    const fetchBookings = async () => {
      try {
        const response = await axios.get('/api/bookings/teacher');
        let bookings = [];
        if (response.data?.success && Array.isArray(response.data.data)) {
          bookings = response.data.data;
        } else if (Array.isArray(response.data)) {
          bookings = response.data; // fallback
        }

        // Aggregate lessons per student
        const summaryMap = {};
        bookings.forEach((booking) => {
          const studentId = booking.student_id;
          const studentName = booking.student_name || booking.student_full_name || t('common.unknown');

          if (!summaryMap[studentId]) {
            summaryMap[studentId] = {
              studentId,
              studentName,
              total: 0
            };
          }

          summaryMap[studentId].total += 1;
        });

        setSummaries(Object.values(summaryMap));
        setBookings(bookings);
        if (response.data.teacherTimezone) {
          setTeacherProfile({ timezone: response.data.teacherTimezone });
        }
      } catch (error) {
        console.error('Error fetching meetings:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchBookings();
  }, [t]);

  // 2. عند فتح تفاصيل الطالب، اجلب الشكاوى المرتبطة بكل booking
  const handleOpenDetails = async (studentId, studentName) => {
    setSelectedStudentName(studentName);
    const studentBookings = bookings.filter(b => b.student_id === studentId);
    setSelectedBookings(studentBookings);
    setDetailsOpen(true);
    // اجلب الشكاوى لهذه الحجوزات
    if (studentBookings.length) {
      try {
        const bookingIds = studentBookings.map(b => b.id);
        const { data } = await axios.get('/meeting-issues', { params: { bookingIds: bookingIds.join(',') } });
        if (data.success && Array.isArray(data.data)) {
          setIssues(data.data);
        } else {
          setIssues([]);
        }
      } catch (err) {
        setIssues([]);
      }
    } else {
      setIssues([]);
    }
  };

  const handleCloseDetails = () => setDetailsOpen(false);

  // 3. دالة مساعدة لإيجاد الشكوى الخاصة بكل booking
  const getBookingIssue = (bookingId) => {
    return issues.find(issue => issue.booking_id === bookingId && ['student_attended_no_commission','student_absent'].includes(issue.issue_type));
  };

  // 2. دالة فتح Dialog الشكوى
  const handleOpenComplaint = (booking) => {
    setComplaintBooking(booking);
    setComplaintType('');
    setComplaintDescription('');
    setComplaintDialogOpen(true);
  };

  // 3. دالة إرسال الشكوى
  const handleSubmitComplaint = async () => {
    if (!complaintType) return;
    setSubmittingComplaint(true);
    try {
      await axios.post('/meeting-issues', {
        booking_id: complaintBooking.id,
        meeting_id: complaintBooking.meeting_id || complaintBooking.id,
        issue_type: complaintType,
        description: complaintDescription
      });
      setComplaintDialogOpen(false);
      setComplaintBooking(null);
    } catch (err) {
      alert('حدث خطأ أثناء إرسال الشكوى');
    } finally {
      setSubmittingComplaint(false);
    }
  };

  if (loading) {
    return (
      <Layout title={t('teacher.myLessons')}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress />
        </Box>
      </Layout>
    );
  }

  return (
    <Layout title={t('teacher.myLessons')}>
      <TableContainer component={Paper} sx={{ maxWidth: '100%', overflowX: 'auto' }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('teacher.studentName')}</TableCell>
              <TableCell align="center">{t('teacher.totalLessons')}</TableCell>
              <TableCell align="center">{t('chat.chat')}</TableCell>
              <TableCell align="center">{t('common.details')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {summaries.map((row) => (
              <TableRow key={row.studentId} hover>
                <TableCell component="th" scope="row">
                  {row.studentName}
                </TableCell>
                <TableCell align="center">{row.total}</TableCell>
                <TableCell align="center">
                  <IconButton color="primary" onClick={() => navigate('/teacher/chat', { state: { studentId: row.studentId, studentName: row.studentName } })}>
                    <ChatIcon />
                  </IconButton>
                </TableCell>
                <TableCell align="center">
                  <Button variant="outlined" size="small" onClick={() => handleOpenDetails(row.studentId, row.studentName)}>
                    {t('common.view')}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {summaries.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  {t('teacher.noLessonsFound')}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Details Dialog */}
      <Dialog open={detailsOpen} onClose={handleCloseDetails} maxWidth="sm" fullWidth>
        <DialogTitle>{selectedStudentName}</DialogTitle>
        <DialogContent dividers>
          {selectedBookings.length === 0 ? (
            <Typography variant="body2" align="center">{t('teacher.noLessonsFound')}</Typography>
          ) : (
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>{t('bookings.date')}</TableCell>
                  <TableCell>{t('bookings.time')}</TableCell>
                  <TableCell>{t('bookings.price')}</TableCell>
                  <TableCell>{t('bookings.status')}</TableCell>
                  <TableCell>{t('teacher.complaintStatus', 'Complaint Status')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {selectedBookings.map((b, idx) => {
                  let dateObj = new Date(b.datetime || b.start_time || b.date_time);
                  if (teacherProfile.timezone) {
                    const formattedDateTime = formatDateInStudentTimezone(dateObj, teacherProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
                    dateObj = moment(formattedDateTime, 'YYYY-MM-DD HH:mm:ss').toDate();
                  }
                  return (
                    <TableRow key={idx}>
                      <TableCell>{moment(dateObj).format('MMMM D, YYYY')}</TableCell>
                      <TableCell>{moment(dateObj).format('h:mm A')}</TableCell>
                      <TableCell>{(b.price_paid !== null && !isNaN(b.price_paid)) ? `${parseFloat(b.price_paid).toFixed(2)} ${t('common.currency')}` : '-'}</TableCell>
                      <TableCell>{t(`bookings.statuses.${b.status}`, { defaultValue: b.status.charAt(0).toUpperCase() + b.status.slice(1) })}</TableCell>
                      <TableCell>
                        {(() => {
                          const issue = getBookingIssue(b.id);
                          if (issue) {
                            return t(`teacher.complaintStatusValues.${issue.status}`, issue.status);
                          }
                          return b.status === 'issue_reported' ? (
                            <Button
                              variant="outlined"
                              color="warning"
                              size="small"
                              startIcon={<ReportProblemIcon />}
                              sx={{ ml: 1, fontWeight: 'bold', borderRadius: 2, px: 2, py: 0.5, textTransform: 'none' }}
                              onClick={() => handleOpenComplaint(b)}
                            >
                              {t('teacher.submitComplaint')}
                            </Button>
                          ) : '-';
                        })()}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDetails}>{t('common.close')}</Button>
        </DialogActions>
      </Dialog>

      {/* أضف Dialog الشكوى في نهاية الصفحة */}
      <Dialog open={complaintDialogOpen} onClose={() => setComplaintDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>{t('teacher.submitComplaint')}</DialogTitle>
        <DialogContent dividers>
          <Typography gutterBottom>{t('teacher.complaintReason')}</Typography>
          <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
            <Button
              variant={complaintType === 'student_attended_no_commission' ? 'contained' : 'outlined'}
              color="primary"
              sx={{ flex: 1, fontWeight: 'bold', borderRadius: 2, py: 1, textTransform: 'none' }}
              onClick={() => setComplaintType('student_attended_no_commission')}
            >
              {t('teacher.complaintType1')}
            </Button>
            <Button
              variant={complaintType === 'student_absent' ? 'contained' : 'outlined'}
              color="primary"
              sx={{ flex: 1, fontWeight: 'bold', borderRadius: 2, py: 1, textTransform: 'none' }}
              onClick={() => setComplaintType('student_absent')}
            >
              {t('teacher.complaintType2')}
            </Button>
          </Box>
          <Typography gutterBottom>{t('teacher.complaintDetails')}</Typography>
          <textarea
            rows={3}
            style={{ width: '100%', padding: 8, fontSize: 16, borderRadius: 8, border: '1px solid #ccc', marginTop: 4 }}
            value={complaintDescription}
            onChange={e => setComplaintDescription(e.target.value)}
            placeholder={t('teacher.complaintDetailsPlaceholder')}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setComplaintDialogOpen(false)}>{t('common.cancel')}</Button>
          <Button
            onClick={handleSubmitComplaint}
            variant="contained"
            color="primary"
            disabled={!complaintType || submittingComplaint}
            sx={{ fontWeight: 'bold', borderRadius: 2, px: 3, py: 1 }}
          >
            {submittingComplaint ? t('common.sending') : t('common.submit')}
          </Button>
        </DialogActions>
      </Dialog>
    </Layout>
  );
};

export default MyLessons;
