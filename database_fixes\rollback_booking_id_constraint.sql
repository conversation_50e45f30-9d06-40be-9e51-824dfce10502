-- Rollback script to undo the NOT NULL constraint if needed
-- Use this only if you need to revert the changes

-- 1. Drop foreign key constraint
ALTER TABLE meeting_issues 
DROP FOREIGN KEY IF EXISTS fk_meeting_issues_booking_id;

-- 2. Drop index
DROP INDEX IF EXISTS idx_meeting_issues_booking_id ON meeting_issues;

-- 3. Make booking_id nullable again
ALTER TABLE meeting_issues 
MODIFY COLUMN booking_id INT NULL;

-- 4. Verify the rollback
DESCRIBE meeting_issues;

-- 5. Show current state
SELECT 
    'Total meeting_issues records' as metric,
    COUNT(*) as count
FROM meeting_issues
UNION ALL
SELECT 
    'Records with NULL booking_id' as metric,
    COUNT(*) as count
FROM meeting_issues 
WHERE booking_id IS NULL
UNION ALL
SELECT 
    'Records with valid booking_id' as metric,
    COUNT(*) as count
FROM meeting_issues 
WHERE booking_id IS NOT NULL;
