import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Schedule as PendingIcon,
  Error as ErrorIcon,
  Payment as PaymentIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';

const WithdrawalManagement = () => {
  const { t } = useTranslation();
  const { token } = useAuth();
  const [withdrawals, setWithdrawals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedWithdrawal, setSelectedWithdrawal] = useState(null);
  const [isProcessDialogOpen, setIsProcessDialogOpen] = useState(false);
  const [action, setAction] = useState('');
  const [notes, setNotes] = useState('');
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    fetchWithdrawals();
  }, [page, rowsPerPage, statusFilter]);

  const fetchWithdrawals = async () => {
    setLoading(true);
    try {
      const { data } = await axios.get('/admin/withdrawals', {
        params: {
          page,
          limit: rowsPerPage,
          status: statusFilter
        },
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setWithdrawals(data.data.withdrawals);
        setTotalCount(data.data.pagination.total);
      }
    } catch (error) {
      console.error('Error fetching withdrawals:', error);
      setError(t('admin.withdrawalManagement.errorFetching'));
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleStatusFilterChange = (event) => {
    setStatusFilter(event.target.value);
    setPage(0);
  };

  const handleProcessClick = (withdrawal, actionType) => {
    setSelectedWithdrawal(withdrawal);
    setAction(actionType);
    setIsProcessDialogOpen(true);
    setNotes('');
  };

  const handleCloseProcessDialog = () => {
    setIsProcessDialogOpen(false);
    setSelectedWithdrawal(null);
    setAction('');
    setNotes('');
  };

  const handleProcessWithdrawal = async () => {
    if (!selectedWithdrawal || !action) return;

    setProcessing(true);
    try {
      const { data } = await axios.post('/admin/withdrawals/process', {
        withdrawalId: selectedWithdrawal.id,
        action,
        notes
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setSuccessMessage(data.message);
        fetchWithdrawals();
        handleCloseProcessDialog();
      }
    } catch (error) {
      console.error('Error processing withdrawal:', error);
      setError(error.response?.data?.error || t('admin.withdrawalManagement.errorProcessing'));
    } finally {
      setProcessing(false);
    }
  };

  const getStatusChip = (status) => {
    const statusConfig = {
      pending: { color: 'warning', icon: <PendingIcon />, label: t('withdrawal.pending') },
      processing: { color: 'info', icon: <PaymentIcon />, label: t('withdrawal.processing') },
      completed: { color: 'success', icon: <ApproveIcon />, label: t('withdrawal.completed') },
      failed: { color: 'error', icon: <ErrorIcon />, label: t('withdrawal.failed') },
      cancelled: { color: 'default', icon: <RejectIcon />, label: t('withdrawal.cancelled') }
    };

    const config = statusConfig[status] || statusConfig.pending;

    return (
      <Chip
        icon={config.icon}
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  return (
    <Layout>
      <Container maxWidth="xl">
        <Typography variant="h4" gutterBottom>
          {t('admin.withdrawalManagement.title')}
        </Typography>

        {/* Filters */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>{t('admin.withdrawalManagement.statusFilter')}</InputLabel>
            <Select
              value={statusFilter}
              onChange={handleStatusFilterChange}
              label={t('admin.withdrawalManagement.statusFilter')}
            >
              <MenuItem value="all">{t('admin.withdrawalManagement.all')}</MenuItem>
              <MenuItem value="pending">{t('withdrawal.pending')}</MenuItem>
              <MenuItem value="processing">{t('withdrawal.processing')}</MenuItem>
              <MenuItem value="completed">{t('withdrawal.completed')}</MenuItem>
              <MenuItem value="failed">{t('withdrawal.failed')}</MenuItem>
              <MenuItem value="cancelled">{t('withdrawal.cancelled')}</MenuItem>
            </Select>
          </FormControl>
        </Paper>

        {/* Withdrawals Table */}
        <Paper elevation={3}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ m: 2 }}>
              {error}
            </Alert>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('admin.withdrawalManagement.date')}</TableCell>
                      <TableCell>{t('admin.withdrawalManagement.teacher')}</TableCell>
                      <TableCell>{t('admin.withdrawalManagement.amount')}</TableCell>
                      <TableCell>{t('admin.withdrawalManagement.paypalEmail')}</TableCell>
                      <TableCell align="center">{t('admin.withdrawalManagement.status')}</TableCell>
                      <TableCell>{t('admin.withdrawalManagement.notes')}</TableCell>
                      <TableCell align="center">{t('admin.withdrawalManagement.actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {withdrawals.map((withdrawal) => (
                      <TableRow key={withdrawal.id}>
                        <TableCell>
                          {format(new Date(withdrawal.created_at), 'yyyy-MM-dd HH:mm')}
                        </TableCell>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {withdrawal.teacher_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {withdrawal.teacher_email}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography fontWeight="bold" color="primary">
                            ${withdrawal.amount}
                          </Typography>
                        </TableCell>
                        <TableCell>{withdrawal.paypal_email}</TableCell>
                        <TableCell align="center">
                          {getStatusChip(withdrawal.status)}
                        </TableCell>
                        <TableCell>
                          <Typography variant="caption">
                            {withdrawal.admin_notes || '-'}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          {withdrawal.status === 'pending' && (
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Button
                                size="small"
                                variant="contained"
                                color="success"
                                onClick={() => handleProcessClick(withdrawal, 'approve')}
                              >
                                {t('admin.withdrawalManagement.approve')}
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                color="error"
                                onClick={() => handleProcessClick(withdrawal, 'reject')}
                              >
                                {t('admin.withdrawalManagement.reject')}
                              </Button>
                            </Box>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              <TablePagination
                component="div"
                count={totalCount}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[5, 10, 25]}
                labelRowsPerPage={t('common.rowsPerPage')}
              />
            </>
          )}
        </Paper>

        {/* Process Dialog */}
        <Dialog open={isProcessDialogOpen} onClose={handleCloseProcessDialog} maxWidth="sm" fullWidth>
          <DialogTitle>
            {action === 'approve' ? t('admin.withdrawalManagement.approveWithdrawal') : t('admin.withdrawalManagement.rejectWithdrawal')}
          </DialogTitle>
          <DialogContent>
            {selectedWithdrawal && (
              <Box sx={{ pt: 2 }}>
                <Typography variant="body1" gutterBottom>
                  <strong>{t('admin.withdrawalManagement.teacher')}:</strong> {selectedWithdrawal.teacher_name}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>{t('admin.withdrawalManagement.amount')}:</strong> ${selectedWithdrawal.amount}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  <strong>{t('admin.withdrawalManagement.paypalEmail')}:</strong> {selectedWithdrawal.paypal_email}
                </Typography>

                <TextField
                  fullWidth
                  label={t('admin.withdrawalManagement.notesOptional')}
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  multiline
                  rows={3}
                  sx={{ mt: 2 }}
                />

                {action === 'approve' && (
                  <Alert severity="info" sx={{ mt: 2 }}>
                    {t('admin.withdrawalManagement.processingInfo')}
                  </Alert>
                )}
              </Box>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseProcessDialog}>{t('common.cancel')}</Button>
            <Button
              onClick={handleProcessWithdrawal}
              variant="contained"
              color={action === 'approve' ? 'success' : 'error'}
              disabled={processing}
            >
              {processing ? <CircularProgress size={20} /> :
               action === 'approve' ? t('admin.withdrawalManagement.approveAndProcess') : t('admin.withdrawalManagement.reject')}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Success Message */}
        {successMessage && (
          <Alert severity="success" sx={{ mt: 2 }} onClose={() => setSuccessMessage('')}>
            {successMessage}
          </Alert>
        )}
      </Container>
    </Layout>
  );
};

export default WithdrawalManagement;
