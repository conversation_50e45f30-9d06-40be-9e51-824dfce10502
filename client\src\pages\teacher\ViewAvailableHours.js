import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import {
  Box,
  Button,
  Typography,
  Paper,
  Alert,
  CircularProgress,
  Tooltip,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import Fab from '@mui/material/Fab';
import Layout from '../../components/Layout';
import { useAuth } from '../../contexts/AuthContext';

const ViewAvailableHours = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [availableHours, setAvailableHours] = useState({
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: []
  });

  // Define time slots - 30-minute intervals from 00:00 to 23:30
  const timeSlots = [
    { key: "00:00-00:30", label: "00:00", time: "00:00", hour: 0, minute: 0 },
    { key: "00:30-01:00", label: "00:30", time: "00:30", hour: 0, minute: 30 },
    { key: "01:00-01:30", label: "01:00", time: "01:00", hour: 1, minute: 0 },
    { key: "01:30-02:00", label: "01:30", time: "01:30", hour: 1, minute: 30 },
    { key: "02:00-02:30", label: "02:00", time: "02:00", hour: 2, minute: 0 },
    { key: "02:30-03:00", label: "02:30", time: "02:30", hour: 2, minute: 30 },
    { key: "03:00-03:30", label: "03:00", time: "03:00", hour: 3, minute: 0 },
    { key: "03:30-04:00", label: "03:30", time: "03:30", hour: 3, minute: 30 },
    { key: "04:00-04:30", label: "04:00", time: "04:00", hour: 4, minute: 0 },
    { key: "04:30-05:00", label: "04:30", time: "04:30", hour: 4, minute: 30 },
    { key: "05:00-05:30", label: "05:00", time: "05:00", hour: 5, minute: 0 },
    { key: "05:30-06:00", label: "05:30", time: "05:30", hour: 5, minute: 30 },
    { key: "06:00-06:30", label: "06:00", time: "06:00", hour: 6, minute: 0 },
    { key: "06:30-07:00", label: "06:30", time: "06:30", hour: 6, minute: 30 },
    { key: "07:00-07:30", label: "07:00", time: "07:00", hour: 7, minute: 0 },
    { key: "07:30-08:00", label: "07:30", time: "07:30", hour: 7, minute: 30 },
    { key: "08:00-08:30", label: "08:00", time: "08:00", hour: 8, minute: 0 },
    { key: "08:30-09:00", label: "08:30", time: "08:30", hour: 8, minute: 30 },
    { key: "09:00-09:30", label: "09:00", time: "09:00", hour: 9, minute: 0 },
    { key: "09:30-10:00", label: "09:30", time: "09:30", hour: 9, minute: 30 },
    { key: "10:00-10:30", label: "10:00", time: "10:00", hour: 10, minute: 0 },
    { key: "10:30-11:00", label: "10:30", time: "10:30", hour: 10, minute: 30 },
    { key: "11:00-11:30", label: "11:00", time: "11:00", hour: 11, minute: 0 },
    { key: "11:30-12:00", label: "11:30", time: "11:30", hour: 11, minute: 30 },
    { key: "12:00-12:30", label: "12:00", time: "12:00", hour: 12, minute: 0 },
    { key: "12:30-13:00", label: "12:30", time: "12:30", hour: 12, minute: 30 },
    { key: "13:00-13:30", label: "13:00", time: "13:00", hour: 13, minute: 0 },
    { key: "13:30-14:00", label: "13:30", time: "13:30", hour: 13, minute: 30 },
    { key: "14:00-14:30", label: "14:00", time: "14:00", hour: 14, minute: 0 },
    { key: "14:30-15:00", label: "14:30", time: "14:30", hour: 14, minute: 30 },
    { key: "15:00-15:30", label: "15:00", time: "15:00", hour: 15, minute: 0 },
    { key: "15:30-16:00", label: "15:30", time: "15:30", hour: 15, minute: 30 },
    { key: "16:00-16:30", label: "16:00", time: "16:00", hour: 16, minute: 0 },
    { key: "16:30-17:00", label: "16:30", time: "16:30", hour: 16, minute: 30 },
    { key: "17:00-17:30", label: "17:00", time: "17:00", hour: 17, minute: 0 },
    { key: "17:30-18:00", label: "17:30", time: "17:30", hour: 17, minute: 30 },
    { key: "18:00-18:30", label: "18:00", time: "18:00", hour: 18, minute: 0 },
    { key: "18:30-19:00", label: "18:30", time: "18:30", hour: 18, minute: 30 },
    { key: "19:00-19:30", label: "19:00", time: "19:00", hour: 19, minute: 0 },
    { key: "19:30-20:00", label: "19:30", time: "19:30", hour: 19, minute: 30 },
    { key: "20:00-20:30", label: "20:00", time: "20:00", hour: 20, minute: 0 },
    { key: "20:30-21:00", label: "20:30", time: "20:30", hour: 20, minute: 30 },
    { key: "21:00-21:30", label: "21:00", time: "21:00", hour: 21, minute: 0 },
    { key: "21:30-22:00", label: "21:30", time: "21:30", hour: 21, minute: 30 },
    { key: "22:00-22:30", label: "22:00", time: "22:00", hour: 22, minute: 0 },
    { key: "22:30-23:00", label: "22:30", time: "22:30", hour: 22, minute: 30 },
    { key: "23:00-23:30", label: "23:00", time: "23:00", hour: 23, minute: 0 },
    { key: "23:30-00:00", label: "23:30", time: "23:30", hour: 23, minute: 30 }
  ];

  // Define days of the week
  const daysOfWeek = [
    { key: 'monday', label: t('days.monday') },
    { key: 'tuesday', label: t('days.tuesday') },
    { key: 'wednesday', label: t('days.wednesday') },
    { key: 'thursday', label: t('days.thursday') },
    { key: 'friday', label: t('days.friday') },
    { key: 'saturday', label: t('days.saturday') },
    { key: 'sunday', label: t('days.sunday') }
  ];

  // Check if user is already logged in and load available hours
  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
      return;
    }

    // Verify user is a teacher
    if (currentUser.role !== 'teacher' && currentUser.role !== 'platform_teacher' && currentUser.role !== 'new_teacher') {
      navigate('/dashboard');
      return;
    }

    const loadAvailableHours = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/api/teacher/profile');
        if (response.data.success && response.data.profile) {
          const profile = response.data.profile;
          if (profile.available_hours) {
            try {
              // Si es un string, intentar parsearlo
              if (typeof profile.available_hours === 'string') {
                const parsedHours = JSON.parse(profile.available_hours);
                // Ensure all days are arrays
                const normalizedHours = {
                  monday: Array.isArray(parsedHours.monday) ? parsedHours.monday : [],
                  tuesday: Array.isArray(parsedHours.tuesday) ? parsedHours.tuesday : [],
                  wednesday: Array.isArray(parsedHours.wednesday) ? parsedHours.wednesday : [],
                  thursday: Array.isArray(parsedHours.thursday) ? parsedHours.thursday : [],
                  friday: Array.isArray(parsedHours.friday) ? parsedHours.friday : [],
                  saturday: Array.isArray(parsedHours.saturday) ? parsedHours.saturday : [],
                  sunday: Array.isArray(parsedHours.sunday) ? parsedHours.sunday : []
                };
                setAvailableHours(normalizedHours);
                console.log('Parsed available hours from string:', normalizedHours);
              }
              // Si ya es un objeto, usarlo directamente
              else if (typeof profile.available_hours === 'object') {
                // Ensure all days are arrays
                const normalizedHours = {
                  monday: Array.isArray(profile.available_hours.monday) ? profile.available_hours.monday : [],
                  tuesday: Array.isArray(profile.available_hours.tuesday) ? profile.available_hours.tuesday : [],
                  wednesday: Array.isArray(profile.available_hours.wednesday) ? profile.available_hours.wednesday : [],
                  thursday: Array.isArray(profile.available_hours.thursday) ? profile.available_hours.thursday : [],
                  friday: Array.isArray(profile.available_hours.friday) ? profile.available_hours.friday : [],
                  saturday: Array.isArray(profile.available_hours.saturday) ? profile.available_hours.saturday : [],
                  sunday: Array.isArray(profile.available_hours.sunday) ? profile.available_hours.sunday : []
                };
                setAvailableHours(normalizedHours);
                console.log('Available hours already an object:', normalizedHours);
              }
            } catch (parseError) {
              console.error('Error parsing available hours:', parseError);
              setError(t('teacher.errorParsingHours'));
            }
          }
        }
      } catch (error) {
        console.error('Error loading available hours:', error);
        setError(t('teacher.errorLoadingHours'));
      } finally {
        setLoading(false);
      }
    };

    loadAvailableHours();
  }, [currentUser, navigate, t]);

  // Get total selected hours
  const getTotalSelectedHours = () => {
    return Object.values(availableHours).reduce((total, daySlots) => total + (daySlots ? daySlots.length : 0), 0);
  };

  // Get abbreviated day names for mobile
  const getAbbreviatedDayName = (dayKey) => {
    const abbreviations = {
      monday: t('days.mondayShort') || 'Mon',
      tuesday: t('days.tuesdayShort') || 'Tue',
      wednesday: t('days.wednesdayShort') || 'Wed',
      thursday: t('days.thursdayShort') || 'Thu',
      friday: t('days.fridayShort') || 'Fri',
      saturday: t('days.saturdayShort') || 'Sat',
      sunday: t('days.sundayShort') || 'Sun'
    };
    return abbreviations[dayKey] || dayKey.substring(0, 3);
  };



  // Handle edit button click
  const handleEditHours = () => {
    navigate('/teacher/manage-hours');
  };

  // Handle back button click
  const handleBack = () => {
    navigate('/teacher/profile');
  };

  return (
    <Layout title={t('teacher.viewAvailableHours')}>
      <Box sx={{
        width: '100%',
        maxWidth: '100%',
        mx: 'auto',
        p: { xs: 0.5, sm: 1 },
        position: 'relative',
        // Ensure proper spacing on very small screens
        minHeight: '100vh',
        overflow: 'visible' // Prevent any container scrolling
      }}>
        <Paper elevation={3} sx={{
          p: { xs: 0.5, sm: 1, md: 2 },
          borderRadius: { xs: 1, sm: 2, md: 3 },
          // Reduce elevation on mobile for better performance
          boxShadow: {
            xs: '0 1px 3px rgba(0,0,0,0.12)',
            sm: '0 3px 6px rgba(0,0,0,0.16)',
            md: '0 10px 20px rgba(0,0,0,0.19)'
          },
          width: '100%',
          overflow: 'visible' // Prevent paper scrolling
        }}>
          {/* Floating Edit Button - Only for platform teachers */}
          {currentUser && currentUser.role === 'platform_teacher' && (
            <Fab
              color="primary"
              aria-label={t('teacher.editAvailableHours')}
              size={window.innerWidth < 600 ? 'medium' : 'large'}
              sx={{
                position: 'fixed',
                bottom: { xs: 20, sm: 30 },
                right: { xs: 20, sm: 30 },
                zIndex: 1000,
                boxShadow: { xs: 2, sm: 3 },
                '&:hover': {
                  transform: { xs: 'none', sm: 'scale(1.1)' }
                }
              }}
              onClick={handleEditHours}
            >
              <EditIcon sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
            </Fab>
          )}
          {/* Header */}
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            mb: { xs: 2, sm: 3 },
            gap: { xs: 2, sm: 0 }
          }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h5" component="h1" gutterBottom sx={{
                fontSize: { xs: '1.25rem', sm: '1.5rem', md: '1.75rem' }
              }}>
                {t('teacher.viewAvailableHours')}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{
                fontSize: { xs: '0.8rem', sm: '0.875rem' },
                display: { xs: 'none', sm: 'block' } // Hide description on mobile to save space
              }}>
                {t('teacher.viewAvailableHoursDescription')}
              </Typography>
            </Box>
            <Box sx={{
              mt: { xs: 0, sm: 0 },
              display: 'flex',
              gap: { xs: 1, sm: 2 },
              width: { xs: '100%', sm: 'auto' },
              justifyContent: { xs: 'center', sm: 'flex-end' }
            }}>
              <Chip
                icon={<EventAvailableIcon sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }} />}
                label={`${getTotalSelectedHours()} ${t('teacher.timeSlots')}`}
                color="primary"
                variant="filled"
                sx={{
                  fontWeight: 'bold',
                  fontSize: { xs: '0.75rem', sm: '0.9rem' },
                  px: { xs: 1.5, sm: 2 },
                  py: { xs: 0.5, sm: 1 },
                  background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                  '&:hover': {
                    transform: { xs: 'none', sm: 'translateY(-1px)' },
                    boxShadow: '0 6px 16px rgba(0,0,0,0.2)'
                  },
                  flex: { xs: 1, sm: 'none' },
                  maxWidth: { xs: '200px', sm: 'none' }
                }}
              />
            </Box>
          </Box>

          {/* Alerts */}
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Quick Stats */}
          {getTotalSelectedHours() > 0 && !loading && (
            <Paper elevation={2} sx={{
              p: 3,
              mb: 3,
              borderRadius: 3,
              background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.05)} 0%, ${alpha(theme.palette.primary.main, 0.05)} 100%)`,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
            }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: theme.palette.primary.main }}>
                📊 {t('teacher.quickStats')}
              </Typography>
              <Box sx={{
                display: 'grid',
                gridTemplateColumns: {
                  xs: '1fr', // Mobile: single column
                  sm: 'repeat(2, 1fr)', // Tablet: 2 columns
                  md: 'repeat(3, 1fr)' // Desktop: 3 columns
                },
                gap: { xs: 1.5, sm: 2 }
              }}>
                <Box sx={{
                  textAlign: 'center',
                  p: { xs: 1.5, sm: 2 },
                  borderRadius: 2,
                  bgcolor: alpha(theme.palette.success.main, 0.1)
                }}>
                  <Typography variant="h4" sx={{
                    fontWeight: 'bold',
                    color: theme.palette.success.main,
                    fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' }
                  }}>
                    {getTotalSelectedHours()}
                  </Typography>
                  <Typography variant="body2" sx={{
                    color: theme.palette.text.secondary,
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}>
                    {t('teacher.totalSlots')}
                  </Typography>
                </Box>
                <Box sx={{
                  textAlign: 'center',
                  p: { xs: 1.5, sm: 2 },
                  borderRadius: 2,
                  bgcolor: alpha(theme.palette.info.main, 0.1)
                }}>
                  <Typography variant="h4" sx={{
                    fontWeight: 'bold',
                    color: theme.palette.info.main,
                    fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' }
                  }}>
                    {Math.round(getTotalSelectedHours() / 2 * 10) / 10}
                  </Typography>
                  <Typography variant="body2" sx={{
                    color: theme.palette.text.secondary,
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}>
                    {t('teacher.hoursPerWeek')}
                  </Typography>
                </Box>
                <Box sx={{
                  textAlign: 'center',
                  p: { xs: 1.5, sm: 2 },
                  borderRadius: 2,
                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                  gridColumn: { xs: '1', sm: 'span 2', md: '3' } // Full width on mobile, span 2 on tablet, normal on desktop
                }}>
                  <Typography variant="h4" sx={{
                    fontWeight: 'bold',
                    color: theme.palette.warning.main,
                    fontSize: { xs: '1.5rem', sm: '2rem', md: '2.125rem' }
                  }}>
                    {Object.values(availableHours).filter(daySlots => daySlots && daySlots.length > 0).length}
                  </Typography>
                  <Typography variant="body2" sx={{
                    color: theme.palette.text.secondary,
                    fontSize: { xs: '0.75rem', sm: '0.875rem' }
                  }}>
                    {t('teacher.activeDays')}
                  </Typography>
                </Box>
              </Box>
            </Paper>
          )}

          {/* No available hours message */}
          {getTotalSelectedHours() === 0 && !loading && (
            <Alert
              severity="info"
              sx={{
                mb: 3,
                borderRadius: 3,
                border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                '& .MuiAlert-icon': {
                  fontSize: '1.5rem'
                }
              }}
            >
              <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                {t('teacher.noAvailableHours')}
              </Typography>
            </Alert>
          )}

          {/* Loading indicator */}
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            /* Professional Calendar View */
            <Box sx={{
              mb: { xs: 2, sm: 3, md: 4 },
              borderRadius: { xs: 2, sm: 3 },
              overflow: 'visible', // Remove overflow hidden to prevent scrolling
              boxShadow: {
                xs: '0 2px 8px rgba(0,0,0,0.1)',
                sm: '0 4px 16px rgba(0,0,0,0.1)',
                md: '0 8px 32px rgba(0,0,0,0.1)'
              },
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              width: '100%', // Ensure full width
              position: 'relative' // Ensure proper positioning
            }}>
              {/* Calendar Header */}
              <Box sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                p: { xs: 2, sm: 2.5, md: 3 },
                color: 'white'
              }}>
                <Typography variant="h5" sx={{
                  fontWeight: 'bold',
                  mb: { xs: 0.5, sm: 1 },
                  fontSize: { xs: '1.1rem', sm: '1.25rem', md: '1.5rem' }
                }}>
                  <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>📅 </Box>
                  {t('teacher.weeklySchedule')}
                </Typography>
                <Typography variant="body2" sx={{
                  opacity: 0.9,
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                  display: { xs: 'none', sm: 'block' } // Hide description on mobile
                }}>
                  {t('teacher.scheduleDescription')}
                </Typography>
              </Box>

              {/* Days Header */}
              <Box sx={{
                display: 'grid',
                gridTemplateColumns: {
                  xs: '50px repeat(7, 1fr)', // Mobile: much smaller time column
                  sm: '80px repeat(7, 1fr)',  // Tablet: smaller time column
                  md: '120px repeat(7, 1fr)', // Desktop: full time column
                },
                bgcolor: alpha(theme.palette.primary.main, 0.05),
                borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                width: '100%', // Full width
                position: 'sticky', // Make header sticky
                top: 0,
                zIndex: 10
              }}>
                <Box sx={{
                  p: { xs: 0.5, sm: 1.2, md: 2 },
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
                }}>
                  <Typography variant="body2" sx={{
                    fontWeight: 'bold',
                    color: theme.palette.text.secondary,
                    fontSize: { xs: '0.6rem', sm: '0.75rem', md: '0.875rem' },
                    textAlign: 'center'
                  }}>
                    <Box component="span" sx={{ display: { xs: 'none', sm: 'inline' } }}>⏰ </Box>
                    {t('teacher.time')}
                  </Typography>
                </Box>
                {daysOfWeek.map((day) => (
                  <Box
                    key={day.key}
                    sx={{
                      p: { xs: 1, sm: 1.5, md: 2 },
                      textAlign: 'center',
                      borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                      '&:last-child': { borderRight: 'none' }
                    }}
                  >
                    <Typography variant="h6" sx={{
                      fontWeight: 'bold',
                      color: theme.palette.primary.main,
                      fontSize: { xs: '0.6rem', sm: '0.8rem', md: '1.1rem' },
                      lineHeight: 1.1,
                      textAlign: 'center'
                    }}>
                      {/* Show different formats based on screen size */}
                      <Box component="span" sx={{ display: { xs: 'none', md: 'block' } }}>
                        {day.label}
                      </Box>
                      <Box component="span" sx={{ display: { xs: 'none', sm: 'block', md: 'none' } }}>
                        {day.label.length > 6 ? day.label.substring(0, 6) : day.label}
                      </Box>
                      <Box component="span" sx={{ display: { xs: 'block', sm: 'none' } }}>
                        {getAbbreviatedDayName(day.key)}
                      </Box>
                    </Typography>
                  </Box>
                ))}
              </Box>

              {/* Time Slots Grid */}
              <Box sx={{
                width: '100%',
                overflow: 'visible', // Remove scrolling
                position: 'relative'
              }}>
                {timeSlots.map((timeSlot, index) => {
                  const isHourStart = timeSlot.minute === 0;
                  return (
                    <Box
                      key={timeSlot.key}
                      sx={{
                        display: 'grid',
                        gridTemplateColumns: {
                          xs: '50px repeat(7, 1fr)', // Mobile: much smaller time column
                          sm: '80px repeat(7, 1fr)',  // Tablet: smaller time column
                          md: '120px repeat(7, 1fr)', // Desktop: full time column
                        },
                        borderBottom: `1px solid ${alpha(theme.palette.divider, isHourStart ? 0.3 : 0.1)}`,
                        bgcolor: index % 4 < 2 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.05),
                          transition: 'all 0.2s ease'
                        },
                        width: '100%' // Ensure full width
                      }}
                    >
                      {/* Time Label */}
                      <Box sx={{
                        p: { xs: 0.2, sm: 0.8, md: 1.5 },
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        bgcolor: isHourStart ? alpha(theme.palette.primary.main, 0.05) : 'transparent',
                        minWidth: { xs: '50px', sm: '80px', md: '120px' }
                      }}>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: isHourStart ? 'bold' : 'medium',
                            color: isHourStart ? theme.palette.primary.main : theme.palette.text.secondary,
                            fontSize: {
                              xs: isHourStart ? '0.6rem' : '0.55rem', // Much smaller on mobile
                              sm: isHourStart ? '0.75rem' : '0.65rem', // Smaller on tablet
                              md: isHourStart ? '0.9rem' : '0.8rem'    // Normal on desktop
                            },
                            textAlign: 'center',
                            lineHeight: 1.1,
                            whiteSpace: 'nowrap' // Prevent text wrapping
                          }}
                        >
                          {timeSlot.label}
                        </Typography>
                      </Box>

                      {/* Day Cells */}
                      {daysOfWeek.map((day) => {
                        const isSelected = availableHours[day.key] && availableHours[day.key].includes(timeSlot.key);
                        return (
                          <Box
                            key={`${day.key}-${timeSlot.key}`}
                            sx={{
                              p: { xs: 0.1, sm: 0.5, md: 1 },
                              minHeight: { xs: '28px', sm: '36px', md: '45px' },
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                              position: 'relative',
                              cursor: 'default',
                              '&:last-child': { borderRight: 'none' },
                              '&:hover': {
                                bgcolor: alpha(theme.palette.primary.main, 0.08)
                              },
                              flex: 1, // Ensure cells expand to fill available space
                              width: '100%'
                            }}
                          >
                            {isSelected ? (
                              <Tooltip title={`${t('teacher.available')} - ${timeSlot.label}`} arrow>
                                <Box sx={{
                                  width: '100%',
                                  height: { xs: '20px', sm: '26px', md: '32px' },
                                  borderRadius: { xs: 1, md: 2 },
                                  background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.8)} 0%, ${alpha(theme.palette.success.dark, 0.9)} 100%)`,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  boxShadow: {
                                    xs: '0 1px 3px rgba(76, 175, 80, 0.3)',
                                    md: '0 2px 8px rgba(76, 175, 80, 0.3)'
                                  },
                                  border: {
                                    xs: `1px solid ${alpha(theme.palette.success.main, 0.3)}`,
                                    md: `2px solid ${alpha(theme.palette.success.main, 0.3)}`
                                  },
                                  transition: 'all 0.3s ease',
                                  '&:hover': {
                                    transform: { xs: 'scale(1.02)', md: 'scale(1.05)' },
                                    boxShadow: {
                                      xs: '0 2px 6px rgba(76, 175, 80, 0.4)',
                                      md: '0 4px 16px rgba(76, 175, 80, 0.4)'
                                    }
                                  }
                                }}>
                                  <CheckCircleIcon
                                    sx={{
                                      color: 'white',
                                      fontSize: { xs: '0.7rem', sm: '0.9rem', md: '1.2rem' },
                                      filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))'
                                    }}
                                  />
                                </Box>
                              </Tooltip>
                            ) : (
                              <Tooltip title={`${t('teacher.unavailable')} - ${timeSlot.label}`} arrow>
                                <Box sx={{
                                  width: '100%',
                                  height: { xs: '20px', sm: '26px', md: '32px' },
                                  borderRadius: { xs: 1, md: 2 },
                                  border: {
                                    xs: `1px dashed ${alpha(theme.palette.grey[400], 0.5)}`,
                                    md: `2px dashed ${alpha(theme.palette.grey[400], 0.5)}`
                                  },
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  bgcolor: alpha(theme.palette.grey[100], 0.3),
                                  transition: 'all 0.3s ease',
                                  '&:hover': {
                                    border: {
                                      xs: `1px dashed ${alpha(theme.palette.grey[400], 0.8)}`,
                                      md: `2px dashed ${alpha(theme.palette.grey[400], 0.8)}`
                                    },
                                    bgcolor: alpha(theme.palette.grey[100], 0.5)
                                  }
                                }}>
                                  <RadioButtonUncheckedIcon
                                    sx={{
                                      color: alpha(theme.palette.grey[400], 0.7),
                                      fontSize: { xs: '0.6rem', sm: '0.8rem', md: '1.1rem' }
                                    }}
                                  />
                                </Box>
                              </Tooltip>
                            )}
                          </Box>
                        );
                      })}
                    </Box>
                  );
                })}
              </Box>
            </Box>
          )}



          {/* Navigation Buttons */}
          <Box sx={{ display: 'flex', justifyContent: 'flex-start', mt: 3 }}>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={handleBack}
              disabled={loading}
            >
              {t('common.back')}
            </Button>
          </Box>
        </Paper>
      </Box>
    </Layout>
  );
};

export default ViewAvailableHours;
