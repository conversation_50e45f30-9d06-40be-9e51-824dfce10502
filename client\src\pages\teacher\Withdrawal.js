import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Grid,
  CircularProgress,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Snackbar
} from '@mui/material';
import {
  AccountBalanceWallet as WalletIcon,
  PaymentOutlined as PaymentIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';

const Withdrawal = () => {
  const { t } = useTranslation();
  const { currentUser, token } = useAuth();
  const [balance, setBalance] = useState(0);
  const [withdrawals, setWithdrawals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [isWithdrawDialogOpen, setIsWithdrawDialogOpen] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [paypalEmail, setPaypalEmail] = useState('');
  const [settings, setSettings] = useState({});
  const [isOTPDialogOpen, setIsOTPDialogOpen] = useState(false);
  const [otpCode, setOtpCode] = useState('');
  const [currentWithdrawalId, setCurrentWithdrawalId] = useState(null);

  useEffect(() => {
    if (currentUser && token) {
      fetchBalance();
      fetchWithdrawals();
      fetchSettings();
    }
  }, [currentUser, token, page, rowsPerPage]);

  const fetchBalance = async () => {
    try {
      const { data } = await axios.get('/wallet/balance', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setBalance(data.data.balance);
      }
    } catch (error) {
      console.error('Error fetching balance:', error);
    }
  };

  const fetchWithdrawals = async () => {
    setLoading(true);
    try {
      const { data } = await axios.get(`/withdrawal?page=${page}&limit=${rowsPerPage}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setWithdrawals(data.data.withdrawals);
        setTotalCount(data.data.pagination.total);
      }
    } catch (error) {
      console.error('Error fetching withdrawals:', error);
      setError(t('withdrawal.errorFetchingWithdrawals'));
    } finally {
      setLoading(false);
    }
  };

  const fetchSettings = async () => {
    try {
      const { data } = await axios.get('/withdrawal/settings', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setSettings(data.data);
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleWithdrawClick = () => {
    setIsWithdrawDialogOpen(true);
  };

  const handleCloseWithdrawDialog = () => {
    setIsWithdrawDialogOpen(false);
    setWithdrawAmount('');
    setPaypalEmail('');
    setError('');
  };

  const handleAmountChange = (event) => {
    const value = event.target.value;
    if (/^\d*\.?\d{0,2}$/.test(value) || value === '') {
      setWithdrawAmount(value);
    }
  };

  const handleSubmitWithdrawal = async () => {
    try {
      setError('');
      
      if (!withdrawAmount || !paypalEmail) {
        setError(t('withdrawal.fillAllFields'));
        return;
      }

      const amount = parseFloat(withdrawAmount);
      const minAmount = parseFloat(settings.min_withdrawal_amount || 10);

      if (amount < minAmount) {
        setError(t('withdrawal.minimumAmount', { amount: minAmount }));
        return;
      }

      if (amount > balance) {
        setError(t('withdrawal.insufficientBalance'));
        return;
      }

      const { data } = await axios.post('/withdrawal/request', {
        amount,
        paypalEmail
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setCurrentWithdrawalId(data.withdrawalId);
        setIsWithdrawDialogOpen(false);
        setIsOTPDialogOpen(true);
      }
    } catch (error) {
      console.error('Error submitting withdrawal:', error);
      setError(error.response?.data?.error || t('withdrawal.errorSubmitting'));
    }
  };

  const handleVerifyOTP = async () => {
    try {
      setError('');
      
      if (!otpCode) {
        setError(t('withdrawal.enterOTP'));
        return;
      }

      const { data } = await axios.post('/withdrawal/verify-otp', {
        withdrawalId: currentWithdrawalId,
        otpCode
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setSuccessMessage(t('withdrawal.requestCompleted'));
        setIsOTPDialogOpen(false);
        setOtpCode('');
        setCurrentWithdrawalId(null);
        fetchBalance();
        fetchWithdrawals();
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      setError(error.response?.data?.error || t('withdrawal.errorVerifyingOTP'));
    }
  };

  const handleCloseOTPDialog = async () => {
    try {
      // Cancel the withdrawal request
      const { data } = await axios.put(`/withdrawal/${currentWithdrawalId}/cancel`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setSuccessMessage(t('withdrawal.withdrawalCancelled'));
        setIsOTPDialogOpen(false);
        setOtpCode('');
        setCurrentWithdrawalId(null);
        fetchBalance();
        fetchWithdrawals();
      }
    } catch (error) {
      console.error('Error cancelling withdrawal:', error);
      setError(error.response?.data?.error || t('withdrawal.errorCancelling'));
    }
  };

  const handleCancelWithdrawal = async (withdrawalId) => {
    try {
      const { data } = await axios.put(`/withdrawal/${withdrawalId}/cancel`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        setSuccessMessage(t('withdrawal.requestCancelled'));
        fetchBalance();
        fetchWithdrawals();
      }
    } catch (error) {
      console.error('Error cancelling withdrawal:', error);
      setError(error.response?.data?.error || t('withdrawal.errorCancelling'));
    }
  };

  const getStatusChip = (status) => {
    const statusConfig = {
      pending: { color: 'warning', icon: <ScheduleIcon />, label: t('withdrawal.pending') },
      processing: { color: 'info', icon: <PaymentIcon />, label: t('withdrawal.processing') },
      completed: { color: 'success', icon: <CheckCircleIcon />, label: t('withdrawal.completed') },
      failed: { color: 'error', icon: <ErrorIcon />, label: t('withdrawal.failed') },
      cancelled: { color: 'default', icon: <CancelIcon />, label: t('withdrawal.cancelled') }
    };

    const config = statusConfig[status] || statusConfig.pending;

    return (
      <Chip
        icon={config.icon}
        label={config.label}
        color={config.color}
        size="small"
        variant="outlined"
      />
    );
  };

  const handleCloseSuccessMessage = () => {
    setSuccessMessage('');
  };

  return (
    <Layout>
      <Container maxWidth="lg">
        <Grid container spacing={3}>
          {/* Balance Card */}
          <Grid item xs={12} md={4}>
            <Paper
              elevation={3}
              sx={{
                p: 4,
                height: '100%',
                textAlign: 'center',
                background: 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                color: 'white'
              }}
            >
              <WalletIcon sx={{ fontSize: 60, mb: 2 }} />
              <Typography variant="h4" gutterBottom>
                {t('withdrawal.title')}
              </Typography>
              <Box sx={{ mt: 3 }}>
                <Typography variant="h3" component="div" sx={{ fontWeight: 'bold' }}>
                  ${balance !== null ? balance.toFixed(2) : '0.00'}
                </Typography>
                <Typography variant="subtitle1" sx={{ mt: 1 }}>
                  {t('withdrawal.availableBalance')}
                </Typography>
              </Box>
              <Button
                variant="contained"
                color="primary"
                sx={{ 
                  mt: 3,
                  bgcolor: 'white',
                  color: 'primary.main',
                  '&:hover': {
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                  }
                }}
                onClick={handleWithdrawClick}
                disabled={balance <= 0}
              >
                {t('withdrawal.requestWithdrawal')}
              </Button>
              {settings.min_withdrawal_amount && (
                <Typography variant="caption" display="block" sx={{ mt: 2 }}>
                  {t('withdrawal.minimumWithdrawal')}: ${settings.min_withdrawal_amount}
                </Typography>
              )}
            </Paper>
          </Grid>

          {/* Withdrawals Table */}
          <Grid item xs={12} md={8}>
            <Paper elevation={3} sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom>
                {t('withdrawal.withdrawalHistory')}
              </Typography>

              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress />
                </Box>
              ) : error ? (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              ) : withdrawals.length === 0 ? (
                <Box sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    {t('withdrawal.noWithdrawals')}
                  </Typography>
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>{t('withdrawal.date')}</TableCell>
                          <TableCell>{t('withdrawal.amount')}</TableCell>
                          <TableCell>{t('withdrawal.paypalEmail')}</TableCell>
                          <TableCell align="center">{t('withdrawal.status')}</TableCell>
                          <TableCell align="center">{t('withdrawal.actions')}</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {withdrawals.map((withdrawal) => (
                          <TableRow key={withdrawal.id}>
                            <TableCell>
                              {format(new Date(withdrawal.created_at), 'yyyy-MM-dd HH:mm')}
                            </TableCell>
                            <TableCell>
                              <Typography fontWeight="bold">
                                ${withdrawal.amount}
                              </Typography>
                            </TableCell>
                            <TableCell>{withdrawal.paypal_email}</TableCell>
                            <TableCell align="center">
                              {getStatusChip(withdrawal.status)}
                            </TableCell>
                            <TableCell align="center">
                              {withdrawal.status === 'pending' && (
                                <Button
                                  size="small"
                                  color="error"
                                  onClick={() => handleCancelWithdrawal(withdrawal.id)}
                                >
                                  {t('withdrawal.cancel')}
                                </Button>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  <TablePagination
                    component="div"
                    count={totalCount}
                    page={page}
                    onPageChange={handleChangePage}
                    rowsPerPage={rowsPerPage}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    rowsPerPageOptions={[5, 10, 25]}
                    labelRowsPerPage={t('common.rowsPerPage')}
                  />
                </>
              )}
            </Paper>
          </Grid>
        </Grid>

        {/* Withdrawal Request Dialog */}
        <Dialog 
          open={isWithdrawDialogOpen} 
          onClose={handleCloseWithdrawDialog}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Typography variant="h6" component="div">
              {t('withdrawal.requestWithdrawal')}
            </Typography>
          </DialogTitle>
          <DialogContent>
            <Box sx={{ p: 2 }}>
              <TextField
                fullWidth
                label={t('withdrawal.amount')}
                value={withdrawAmount}
                onChange={handleAmountChange}
                type="text"
                inputProps={{
                  inputMode: 'decimal',
                  pattern: '[0-9]*',
                }}
                sx={{ mb: 3 }}
                helperText={settings.min_withdrawal_amount && 
                  t('withdrawal.minimumAmount', { amount: settings.min_withdrawal_amount })}
              />

              <TextField
                fullWidth
                label={t('withdrawal.paypalEmail')}
                value={paypalEmail}
                onChange={(e) => setPaypalEmail(e.target.value)}
                type="email"
                sx={{ mb: 2 }}
                helperText={t('withdrawal.paypalEmailHelp')}
              />

              {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              )}

              {settings.withdrawal_processing_days && (
                <Alert severity="info" sx={{ mt: 2 }}>
                  {t('withdrawal.processingTime', { days: settings.withdrawal_processing_days })}
                </Alert>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseWithdrawDialog} color="primary">
              {t('common.cancel')}
            </Button>
            <Button 
              onClick={handleSubmitWithdrawal} 
              color="primary" 
              variant="contained"
              disabled={!withdrawAmount || !paypalEmail}
            >
              {t('withdrawal.submit')}
            </Button>
          </DialogActions>
        </Dialog>

        {/* OTP Dialog */}
        <Dialog open={isOTPDialogOpen} onClose={handleCloseOTPDialog}>
          <DialogTitle>{t('withdrawal.enterOTPTitle')}</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                {t('withdrawal.otpInstructions')}
              </Typography>
              <TextField
                autoFocus
                margin="dense"
                label={t('withdrawal.otpCode')}
                type="text"
                fullWidth
                value={otpCode}
                onChange={(e) => setOtpCode(e.target.value)}
                inputProps={{ maxLength: 6 }}
              />
              {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {error}
                </Alert>
              )}
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseOTPDialog}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleVerifyOTP} variant="contained" color="primary">
              {t('withdrawal.verify')}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Success Message */}
        <Snackbar
          open={!!successMessage}
          autoHideDuration={6000}
          onClose={handleCloseSuccessMessage}
          message={successMessage}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        />
      </Container>
    </Layout>
  );
};

export default Withdrawal;
