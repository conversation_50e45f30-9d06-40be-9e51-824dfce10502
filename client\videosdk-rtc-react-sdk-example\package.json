{"name": "react-sdk-example", "version": "0.1.0", "private": true, "homepage": "https://lab.videosdk.live/react-rtc-demo/", "dependencies": {"@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@popperjs/core": "^2.11.8", "@videosdk.live/react-sdk": "^0.2.1", "lottie-react": "^2.4.1", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^8.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-player": "^2.16.0", "react-responsive": "^10.0.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "web-vitals": "^4.2.4"}, "overrides": {"nth-check": "2.1.1", "postcss": "^8.4.31"}, "devDependencies": {"autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5"}, "scripts": {"start": "react-scripts start", "kill-start": "sudo kill -9 `sudo lsof -t -i:3000` && yarn start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}