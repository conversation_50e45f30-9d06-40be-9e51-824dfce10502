const db = require('../config/db');

/**
 * Middleware للتحقق من حالة المستخدم
 * يتحقق من أن المستخدم ليس محذوف أو مجدول للحذف
 */
const checkUserStatus = async (req, res, next) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // جلب حالة المستخدم الحالية من قاعدة البيانات
    const [rows] = await db.pool.execute(
      'SELECT id, status, delete_scheduled_at, deleted_at, deletion_reason FROM users WHERE id = ?',
      [req.user.id]
    );

    if (!rows || rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = rows[0];

    // التحقق من حالة الحذف
    if (user.status === 'deleted') {
      return res.status(401).json({
        success: false,
        message: 'Account has been deleted',
        accountStatus: 'deleted',
        requiresLogout: true
      });
    }

    if (user.status === 'pending_deletion') {
      return res.status(401).json({
        success: false,
        message: 'Account is scheduled for deletion',
        accountStatus: 'pending_deletion',
        deleteScheduledAt: user.delete_scheduled_at,
        requiresLogout: true
      });
    }

    // تحديث بيانات المستخدم في الطلب
    req.user.status = user.status;
    req.user.delete_scheduled_at = user.delete_scheduled_at;

    next();
  } catch (error) {
    console.error('Error checking user status:', error);
    return res.status(500).json({
      success: false,
      message: 'Error checking user status'
    });
  }
};

/**
 * Middleware للسماح بالوصول للمستخدمين المجدولين للحذف
 * يستخدم في صفحات الملف الشخصي وإلغاء الحذف
 */
const allowPendingDeletion = async (req, res, next) => {
  try {
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // جلب حالة المستخدم الحالية من قاعدة البيانات
    const [rows] = await db.pool.execute(
      'SELECT id, status, delete_scheduled_at FROM users WHERE id = ?',
      [req.user.id]
    );

    if (!rows || rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = rows[0];

    // منع الوصول للحسابات المحذوفة ناعم
    if (user.deleted_at) {
      return res.status(401).json({
        success: false,
        message: 'Account has been deleted',
        accountStatus: 'deleted',
        requiresLogout: true,
        deletedAt: user.deleted_at,
        deletionReason: user.deletion_reason
      });
    }

    // منع الوصول للحسابات المحذوفة (النظام القديم)
    if (user.status === 'deleted') {
      return res.status(401).json({
        success: false,
        message: 'Account has been deleted',
        accountStatus: 'deleted',
        requiresLogout: true
      });
    }

    // السماح بالوصول للحسابات النشطة والمجدولة للحذف
    req.user.status = user.status;
    req.user.delete_scheduled_at = user.delete_scheduled_at;

    next();
  } catch (error) {
    console.error('Error checking user status:', error);
    return res.status(500).json({
      success: false,
      message: 'Error checking user status'
    });
  }
};

module.exports = {
  checkUserStatus,
  allowPendingDeletion
};
