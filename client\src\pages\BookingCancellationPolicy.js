import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Fade,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  useTheme,
  alpha,
  Chip,
  Alert,
  Grid,
  Card,
  CardContent,
  Stack,
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Warning as WarningIcon,
  AccessTime as AccessTimeIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Info as InfoIcon,
  Timer as TimerIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';

const BookingCancellationPolicy = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';
  const policy = t('policies.bookingCancellation', { returnObjects: true });

  const SectionCard = ({ title, children, bg }) => (
    <Card
      elevation={2}
      sx={{
        mb: 4,
        borderRadius: 3,
        ...(isRtl
        ? { borderRight: `6px solid ${theme.palette.primary.main}` }
        : { borderLeft: `6px solid ${theme.palette.primary.main}` }),
        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),
      }}
    >
      <CardContent>
        <Typography
          variant="h5"
          fontWeight={600}
          color={theme.palette.primary.main}
          mb={2}
          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
        >
          {title}
        </Typography>
        {children}
      </CardContent>
    </Card>
  );

  const renderSubSection = (subSection) => (
    <Box sx={{ mb: 3 }}>
      <Typography
        variant="h6"
        sx={{
          fontWeight: 600,
          color: theme.palette.primary.main,
          mb: 1,
          fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',
        }}
      >
        {subSection.title}
      </Typography>
      {subSection.points && (
        <List>
          {subSection.points.map((item, idx) => (
            <ListItem key={idx} sx={{
              pl: isRtl ? 0 : 2,
              pr: isRtl ? 2 : 0,
              flexDirection: 'row',
              textAlign: isRtl ? 'right' : 'left',
            }}>
              <ListItemIcon sx={{
              minWidth: 'auto',
              mx: isRtl ? 0 : 1,
              ml: isRtl ? 1 : 0,
            }}><CheckCircleIcon color="success" /></ListItemIcon>
              <ListItemText primary={item} />
            </ListItem>
          ))}
        </List>
      )}
    </Box>
  );

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,
        pt: 4,
        pb: 8,
      }}
    >
      <Container maxWidth="lg">
        <Fade in timeout={800}>
          <Box sx={{ p: { xs: 2, md: 4 }, direction: isRtl ? 'rtl' : 'ltr' }}>
        <Typography variant="h4" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
          {policy.title}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" mb={3}>
          {policy.subtitle}
        </Typography>
        <Divider sx={{ mb: 3 }} />
        {/* Student Policy */}
        <SectionCard title={policy.studentPolicy.title}>
          {renderSubSection(policy.studentPolicy.booking)}
          {renderSubSection(policy.studentPolicy.cancellation)}
          {renderSubSection(policy.studentPolicy.rescheduling)}
          {renderSubSection(policy.studentPolicy.lateArrival)}
        </SectionCard>
        {/* Tutor Policy */}
        <SectionCard title={policy.tutorPolicy.title} bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}>
          {renderSubSection(policy.tutorPolicy.availability)}
          {renderSubSection(policy.tutorPolicy.cancellation)}
          {renderSubSection(policy.tutorPolicy.rescheduling)}
          {renderSubSection(policy.tutorPolicy.lateArrival)}
        </SectionCard>
        {/* General Notes */}
        <SectionCard title={policy.generalNotes.title}>
          <List>
            {policy.generalNotes.points.map((item, idx) => (
              <ListItem key={idx} sx={{
              pl: isRtl ? 0 : 2,
              pr: isRtl ? 2 : 0,
              flexDirection: 'row',
              textAlign: isRtl ? 'right' : 'left',
            }}>
                <ListItemIcon sx={{
              minWidth: 'auto',
              mx: isRtl ? 0 : 1,
              ml: isRtl ? 1 : 0,
            }}><InfoIcon color="info" /></ListItemIcon>
                <ListItemText primary={item} />
              </ListItem>
            ))}
          </List>
        </SectionCard>
        {/* Summary */}
        <Divider sx={{ my: 3 }} />
        <Grid container spacing={2} mb={3}>
          <Grid item xs={12} md={6}>
            <Card sx={{ bgcolor: alpha(theme.palette.success.light, 0.2) }}>
              <CardContent>
                <Typography variant="h6" fontWeight={600} color={theme.palette.success.main} mb={1}>{policy.summary.forStudents}</Typography>
                <List>
                  <ListItem><ListItemIcon><CheckCircleIcon color="success" /></ListItemIcon><ListItemText primary={policy.summary.freeCancellation} /></ListItem>
                  <ListItem><ListItemIcon><AccessTimeIcon color="info" /></ListItemIcon><ListItemText primary={policy.summary.gracePeriod} /></ListItem>
                  <ListItem><ListItemIcon><TimerIcon color="info" /></ListItemIcon><ListItemText primary={policy.summary.oneReschedule} /></ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card sx={{ bgcolor: alpha(theme.palette.info.light, 0.2) }}>
              <CardContent>
                <Typography variant="h6" fontWeight={600} color={theme.palette.info.main} mb={1}>{policy.summary.forTutors}</Typography>
                <List>
                  <ListItem><ListItemIcon><CancelIcon color="error" /></ListItemIcon><ListItemText primary={policy.summary.cancellationBefore} /></ListItem>
                  <ListItem><ListItemIcon><AccessTimeIcon color="info" /></ListItemIcon><ListItemText primary={policy.summary.delayAllowance} /></ListItem>
                  <ListItem><ListItemIcon><NotificationsIcon color="info" /></ListItemIcon><ListItemText primary={policy.summary.immediateNotification} /></ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        <Alert icon={<InfoIcon />} severity="info" sx={{ direction: isRtl ? 'rtl' : 'ltr' }}>{policy.summary.importantNote}</Alert>
      </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default BookingCancellationPolicy; 