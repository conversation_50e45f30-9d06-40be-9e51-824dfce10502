import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Paper,
  Divider,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Button,
  Chip,
  IconButton,
  useTheme,
  alpha,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  Person as PersonIcon,
  School as SchoolIcon,
  Assignment as AssignmentIcon,
  Category as CategoryIcon,
  TrendingUp as TrendingUpIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  AccessTime as AccessTimeIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Visibility as VisibilityIcon,
  Language as LanguageIcon,
  AccountBalanceWallet as AccountBalanceWalletIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';

// Removed recharts import as it's not installed

const Dashboard = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [stats, setStats] = useState({
    totalTeachers: 0,
    pendingApplications: 0,
    totalStudents: 0,
    totalCourseCategories: 0,
    totalLanguages: 0,
    totalBookings: 0,
    totalRevenue: 0
  });
  const [recentApplications, setRecentApplications] = useState([]);
  const [recentStudents, setRecentStudents] = useState([]);
  const [teachersByLanguage, setTeachersByLanguage] = useState([]);
  const [studentsByCountry, setStudentsByCountry] = useState([]);
  const [bookingStats, setBookingStats] = useState([]);

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
    alpha(theme.palette.primary.main, 0.7),
    alpha(theme.palette.secondary.main, 0.7),
    alpha(theme.palette.success.main, 0.7),
    alpha(theme.palette.warning.main, 0.7)
  ];

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch all data in parallel for better performance
        const [
          statsResponse,
          applicationsResponse,
          studentsResponse,
          teachersByLanguageResponse,
          studentsByCountryResponse,
          bookingStatsResponse
        ] = await Promise.all([
          axios.get('/admin/stats'),
          axios.get('/admin/recent-applications'),
          axios.get('/admin/recent-students'),
          axios.get('/admin/teachers-by-language'),
          axios.get('/admin/students-by-country'),
          axios.get('/admin/booking-stats')
        ]);

        // Set state with real data from API
        setStats(statsResponse.data);
        setRecentApplications(applicationsResponse.data);
        setRecentStudents(studentsResponse.data);
        setTeachersByLanguage(teachersByLanguageResponse.data);
        setStudentsByCountry(studentsByCountryResponse.data);
        setBookingStats(bookingStatsResponse.data);

        // No fallback data - only use real data from API

        setError('');
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError(t('admin.dashboard.fetchError'));
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [t]);

  const formatDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return format(date, 'PPP', { locale: i18n.language === 'ar' ? ar : enUS });
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return theme.palette.success.main;
      case 'rejected':
        return theme.palette.error.main;
      case 'pending':
      default:
        return theme.palette.warning.main;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'approved':
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case 'rejected':
        return <CancelIcon sx={{ color: theme.palette.error.main }} />;
      case 'pending':
      default:
        return <AccessTimeIcon sx={{ color: theme.palette.warning.main }} />;
    }
  };

  if (loading) {
    return (
      <Layout title={t('admin.dashboard.title')}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout title={t('admin.dashboard.title')}>
        <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
      </Layout>
    );
  }

  return (
    <Layout title={t('admin.dashboard.title')}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
          {t('admin.dashboard.welcomeMessage')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('admin.dashboard.overview')}
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderRadius: 2,
              boxShadow: 3,
              height: '100%',
              transition: 'transform 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: 6
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: theme.palette.primary.main }}>
                  <SchoolIcon />
                </Avatar>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                {stats.totalTeachers}
              </Typography>
              <Typography color="textSecondary" variant="body2">
                {t('admin.dashboard.totalTeachers')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderRadius: 2,
              boxShadow: 3,
              height: '100%',
              transition: 'transform 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: 6
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: theme.palette.warning.main }}>
                  <AssignmentIcon />
                </Avatar>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                {stats.pendingApplications}
              </Typography>
              <Typography color="textSecondary" variant="body2">
                {t('admin.dashboard.pendingApplications')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderRadius: 2,
              boxShadow: 3,
              height: '100%',
              transition: 'transform 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: 6
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar sx={{ bgcolor: alpha(theme.palette.secondary.main, 0.1), color: theme.palette.secondary.main }}>
                  <PersonIcon />
                </Avatar>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                {stats.totalStudents}
              </Typography>
              <Typography color="textSecondary" variant="body2">
                {t('admin.dashboard.totalStudents')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderRadius: 2,
              boxShadow: 3,
              height: '100%',
              transition: 'transform 0.3s',
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: 6
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: theme.palette.success.main }}>
                  <AccountBalanceWalletIcon />
                </Avatar>
              </Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
                {stats.totalRevenue}
              </Typography>
              <Typography color="textSecondary" variant="body2">
                {t('admin.dashboard.totalRevenue')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Secondary Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={4}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              borderRadius: 2,
              bgcolor: alpha(theme.palette.primary.main, 0.05)
            }}
          >
            <Avatar sx={{ bgcolor: theme.palette.primary.main, mr: 2 }}>
              <CategoryIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {stats.totalCourseCategories || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {t('admin.dashboard.totalCourseCategories')}
              </Typography>
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              borderRadius: 2,
              bgcolor: alpha(theme.palette.secondary.main, 0.05)
            }}
          >
            <Avatar sx={{ bgcolor: theme.palette.secondary.main, mr: 2 }}>
              <LanguageIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {stats.totalLanguages || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {t('admin.dashboard.totalLanguages')}
              </Typography>
            </Box>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Paper
            elevation={2}
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              borderRadius: 2,
              bgcolor: alpha(theme.palette.success.main, 0.05)
            }}
          >
            <Avatar sx={{ bgcolor: theme.palette.success.main, mr: 2 }}>
              <TrendingUpIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {stats.totalBookings || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {t('admin.dashboard.totalBookings')}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Charts and Tables */}
      <Grid container spacing={3}>
        {/* Booking Stats Chart */}
        <Grid item xs={12} md={8}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              height: '100%',
              borderRadius: 2,
              boxShadow: 3
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {t('admin.dashboard.bookingStats')}
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => navigate('/admin/bookings')}
              >
                {t('admin.dashboard.viewAll')}
              </Button>
            </Box>
            <Box sx={{ height: 300, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
                  <Box sx={{ width: 16, height: 16, bgcolor: theme.palette.success.main, mr: 1, borderRadius: 1 }}></Box>
                  <Typography variant="body2">{t('admin.dashboard.completed')}</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
                  <Box sx={{ width: 16, height: 16, bgcolor: theme.palette.error.main, mr: 1, borderRadius: 1 }}></Box>
                  <Typography variant="body2">{t('admin.dashboard.cancelled')}</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{ width: 16, height: 16, bgcolor: theme.palette.warning.main, mr: 1, borderRadius: 1 }}></Box>
                  <Typography variant="body2">{t('admin.dashboard.pending')}</Typography>
                </Box>
              </Box>

              {bookingStats.length > 0 ? (
                <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                  {bookingStats.map((stat, index) => {
                    const total = stat.completed + stat.cancelled + stat.pending;
                    return (
                      <Box key={index} sx={{ mb: 2 }}>
                        <Typography variant="body2" sx={{ mb: 0.5 }}>{stat.name}</Typography>
                        <Box sx={{ display: 'flex', height: 24 }}>
                          {total > 0 ? (
                            <>
                              <Box
                                sx={{
                                  width: `${(stat.completed / total) * 100}%`,
                                  bgcolor: theme.palette.success.main,
                                  mr: '1px'
                                }}
                              />
                              <Box
                                sx={{
                                  width: `${(stat.cancelled / total) * 100}%`,
                                  bgcolor: theme.palette.error.main,
                                  mr: '1px'
                                }}
                              />
                              <Box
                                sx={{
                                  width: `${(stat.pending / total) * 100}%`,
                                  bgcolor: theme.palette.warning.main
                                }}
                              />
                            </>
                          ) : (
                            <Box sx={{ width: '100%', height: '100%', bgcolor: alpha(theme.palette.text.disabled, 0.1) }} />
                          )}
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                          <Typography variant="caption">{stat.completed} {t('admin.dashboard.completed')}</Typography>
                          <Typography variant="caption">{stat.cancelled} {t('admin.dashboard.cancelled')}</Typography>
                          <Typography variant="caption">{stat.pending} {t('admin.dashboard.pending')}</Typography>
                        </Box>
                      </Box>
                    );
                  })}
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center', py: 3, flexGrow: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    {t('admin.dashboard.noBookingStats')}
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>

        {/* Teachers by Language */}
        <Grid item xs={12} md={4}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              height: '100%',
              borderRadius: 2,
              boxShadow: 3
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {t('admin.dashboard.teachersByLanguage')}
              </Typography>
            </Box>
            <Box sx={{ height: 300, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
              {teachersByLanguage.length > 0 ? (
                teachersByLanguage.map((entry, index) => {
                  const total = teachersByLanguage.reduce((sum, item) => sum + item.value, 0);
                  const percentage = ((entry.value / total) * 100).toFixed(0);

                  return (
                    <Box key={index} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              bgcolor: COLORS[index % COLORS.length],
                              mr: 1,
                              borderRadius: '50%'
                            }}
                          />
                          <Typography variant="body2">{entry.name}</Typography>
                        </Box>
                        <Typography variant="body2" fontWeight="bold">{entry.value} ({percentage}%)</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={parseInt(percentage)}
                        sx={{
                          height: 8,
                          borderRadius: 5,
                          bgcolor: alpha(COLORS[index % COLORS.length], 0.2),
                          '& .MuiLinearProgress-bar': {
                            bgcolor: COLORS[index % COLORS.length]
                          }
                        }}
                      />
                    </Box>
                  );
                })
              ) : (
                <Box sx={{ textAlign: 'center', py: 3 }}>
                  <Typography variant="body1" color="text.secondary">
                    {t('admin.dashboard.noTeachersByLanguage')}
                  </Typography>
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>

        {/* Recent Applications */}
        <Grid item xs={12} md={6}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: 3
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {t('admin.dashboard.recentApplications')}
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => navigate('/admin/applications')}
              >
                {t('admin.dashboard.viewAll')}
              </Button>
            </Box>
            {recentApplications.length > 0 ? (
              <List sx={{ width: '100%' }}>
                {recentApplications.map((application, index) => (
                  <React.Fragment key={application.id || index}>
                    <ListItem
                      alignItems="flex-start"
                      sx={{
                        px: 2,
                        borderRadius: 1,
                        '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.05) }
                      }}
                      secondaryAction={
                        <Tooltip title={t('admin.dashboard.viewDetails')}>
                          <IconButton
                            edge="end"
                            aria-label="view"
                            onClick={() => navigate(`/admin/applications/${application.id}`)}
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                      }
                    >
                      <ListItemAvatar>
                        <Avatar alt={application.full_name}>
                          {application.full_name?.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                              {application.full_name}
                            </Typography>
                            <Chip
                              icon={getStatusIcon(application.status)}
                              label={t(`admin.applications.status.${application.status || 'pending'}`, { defaultValue: application.status || 'Pending' })}
                              size="small"
                              sx={{
                                ml: 1,
                                bgcolor: alpha(getStatusColor(application.status), 0.1),
                                color: getStatusColor(application.status),
                                borderColor: getStatusColor(application.status)
                              }}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <>
                            <Typography component="span" variant="body2" color="text.primary">
                              {application.email}
                            </Typography>
                            <Typography component="span" variant="body2" color="text.secondary" sx={{ display: 'block' }}>
                              {formatDate(application.created_at)}
                            </Typography>
                          </>
                        }
                      />
                    </ListItem>
                    {index < recentApplications.length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="body1" color="text.secondary">
                  {t('admin.dashboard.noApplications')}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Recent Students */}
        <Grid item xs={12} md={6}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: 3
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {t('admin.dashboard.recentStudents')}
              </Typography>
              <Button
                variant="outlined"
                size="small"
                onClick={() => navigate('/admin/students')}
              >
                {t('admin.dashboard.viewAll')}
              </Button>
            </Box>
            {recentStudents.length > 0 ? (
              <List sx={{ width: '100%' }}>
                {recentStudents.map((student, index) => (
                  <React.Fragment key={student.id}>
                    <ListItem
                      alignItems="flex-start"
                      sx={{
                        px: 2,
                        borderRadius: 1,
                        '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.05) }
                      }}
                      secondaryAction={
                        <Tooltip title={t('admin.dashboard.viewDetails')}>
                          <IconButton
                            edge="end"
                            aria-label="view"
                            onClick={() => navigate(`/admin/students/${student.id}`)}
                          >
                            <VisibilityIcon />
                          </IconButton>
                        </Tooltip>
                      }
                    >
                      <ListItemAvatar>
                        <Avatar alt={student.full_name}>
                          {student.full_name?.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                            {student.full_name}
                          </Typography>
                        }
                        secondary={
                          <>
                            <Typography component="span" variant="body2" color="text.primary">
                              {student.email}
                            </Typography>
                            <Typography component="span" variant="body2" color="text.secondary" sx={{ display: 'block' }}>
                              {student.country} • {formatDate(student.created_at)}
                            </Typography>
                          </>
                        }
                      />
                    </ListItem>
                    {index < recentStudents.length - 1 && <Divider variant="inset" component="li" />}
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="body1" color="text.secondary">
                  {t('admin.dashboard.noStudents')}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Students by Country */}
        <Grid item xs={12}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: 3
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {t('admin.dashboard.studentsByCountry')}
              </Typography>
            </Box>
            {studentsByCountry.length > 0 ? (
              <Grid container spacing={2}>
                {studentsByCountry.map((country, index) => (
                  <Grid item xs={12} sm={6} md={4} lg={2.4} key={index}>
                    <Box sx={{ mb: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                        <Typography variant="body2">{country.name}</Typography>
                        <Typography variant="body2" fontWeight="bold">{country.value}</Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={(country.value / Math.max(...studentsByCountry.map(c => c.value))) * 100}
                        sx={{
                          height: 8,
                          borderRadius: 5,
                          bgcolor: alpha(COLORS[index % COLORS.length], 0.2),
                          '& .MuiLinearProgress-bar': {
                            bgcolor: COLORS[index % COLORS.length]
                          }
                        }}
                      />
                    </Box>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="body1" color="text.secondary">
                  {t('admin.dashboard.noStudentsByCountry')}
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Layout>
  );
};

export default Dashboard;
