import React from 'react';
import { <PERSON>, Container, Stack, <PERSON><PERSON><PERSON>, IconButton, Link as <PERSON><PERSON><PERSON><PERSON>, Divider, useTheme, useMediaQuery } from '@mui/material';
import { Facebook, Twitter, Instagram, YouTube } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { Link as RouterLink } from 'react-router-dom';

const Footer = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isRtl = i18n.language === 'ar';

  return (
    <Box
      component="footer"
      sx={{
        bgcolor: 'primary.main',
        color: 'white',
        py: { xs: 2, sm: 3, md: 4 },
        mt: 'auto',
        direction: isRtl ? 'rtl' : 'ltr'
      }}
    >
      <Container maxWidth="lg" sx={{ px: { xs: 2, sm: 3, md: 4 } }}>
        <Stack
          direction={{ xs: 'column', sm: 'column', md: 'row' }}
          spacing={{ xs: 2, sm: 3, md: 4 }}
          justifyContent="space-between"
          alignItems={{ xs: 'center', sm: 'center', md: 'flex-start' }}
          sx={{ py: { xs: 1, sm: 2, md: 3 } }}
        >
          <Stack
            spacing={{ xs: 1, sm: 1.5, md: 2 }}
            sx={{
              textAlign: { xs: 'center', sm: 'center', md: isRtl ? 'right' : 'left' },
              maxWidth: { xs: '100%', md: '60%' }
            }}
          >
            <Typography
              variant="body1"
              fontWeight="bold"
              sx={{
                fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
                fontFamily: 'Tajawal, sans-serif'
              }}
            >
              {t('footer.platformName')}
            </Typography>
            <Typography
              variant="caption"
              sx={{
                opacity: 0.8,
                fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' },
                lineHeight: 1.4
              }}
            >
              {t('footer.designedBy')}
            </Typography>
            <Stack
              direction="row"
              spacing={{ xs: 1, sm: 1.5, md: 2 }}
              sx={{
                mt: { xs: 1, sm: 1.5, md: 2 },
                justifyContent: { xs: 'center', sm: 'center', md: isRtl ? 'flex-end' : 'flex-start' }
              }}
            >
              <IconButton
                component="a"
                href="https://www.facebook.com/profile.php?id=61577664671968"
                target="_blank"
                rel="noopener noreferrer"
                color="inherit"
                size={isMobile ? "small" : "medium"}
                aria-label="Facebook"
                sx={{
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.1)',
                    transform: 'scale(1.1)'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                <Facebook fontSize={isMobile ? "small" : "medium"} />
              </IconButton>
              <IconButton
                component="a"
                href="https://x.com/allemnionl40266"
                target="_blank"
                rel="noopener noreferrer"
                color="inherit"
                size={isMobile ? "small" : "medium"}
                aria-label="Twitter"
                sx={{
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.1)',
                    transform: 'scale(1.1)'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                <Twitter fontSize={isMobile ? "small" : "medium"} />
              </IconButton>
              <IconButton
                component="a"
                href="https://www.instagram.com/allemnionline/"
                target="_blank"
                rel="noopener noreferrer"
                color="inherit"
                size={isMobile ? "small" : "medium"}
                aria-label="Instagram"
                sx={{
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.1)',
                    transform: 'scale(1.1)'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                <Instagram fontSize={isMobile ? "small" : "medium"} />
              </IconButton>
              <IconButton
                component="a"
                href="https://www.youtube.com/@allemnionline"
                target="_blank"
                rel="noopener noreferrer"
                color="inherit"
                size={isMobile ? "small" : "medium"}
                aria-label="YouTube"
                sx={{
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.1)',
                    transform: 'scale(1.1)'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                <YouTube fontSize={isMobile ? "small" : "medium"} />
              </IconButton>
            </Stack>
          </Stack>

          <Stack
            spacing={{ xs: 1, sm: 1.5, md: 2 }}
            sx={{
              textAlign: { xs: 'center', sm: 'center', md: isRtl ? 'right' : 'left' },
              maxWidth: { xs: '100%', md: '35%' }
            }}
          >
            <Typography
              variant="body2"
              fontWeight="bold"
              sx={{
                fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                fontFamily: 'Tajawal, sans-serif'
              }}
            >
              {t('footer.quickLinks')}
            </Typography>
            <Stack spacing={{ xs: 0.5, sm: 1, md: 1.5 }}>
              <MuiLink
                component={RouterLink}
                to="/about-us"
                color="inherit"
                underline="hover"
                variant="body2"
                sx={{
                  opacity: 0.8,
                  fontSize: { xs: '0.8rem', sm: '0.85rem', md: '0.9rem' },
                  '&:hover': {
                    opacity: 1,
                    transform: 'translateX(5px)',
                    transition: 'all 0.2s ease-in-out'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                {t('footer.about')}
              </MuiLink>
              <MuiLink
                component={RouterLink}
                to="/privacy-policy"
                color="inherit"
                underline="hover"
                variant="body2"
                sx={{
                  opacity: 0.8,
                  fontSize: { xs: '0.8rem', sm: '0.85rem', md: '0.9rem' },
                  '&:hover': {
                    opacity: 1,
                    transform: 'translateX(5px)',
                    transition: 'all 0.2s ease-in-out'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                {t('footer.privacy')}
              </MuiLink>
            </Stack>
          </Stack>
        </Stack>

        <Divider
          sx={{
            borderColor: 'rgba(255,255,255,0.1)',
            my: { xs: 2, sm: 3, md: 4 },
            mx: { xs: -2, sm: -3, md: 0 }
          }}
        />

        <Typography
          variant="body2"
          align="center"
          sx={{
            pb: { xs: 1, sm: 1.5, md: 2 },
            fontSize: { xs: '0.75rem', sm: '0.8rem', md: '0.85rem' },
            opacity: 0.9,
            fontFamily: 'Tajawal, sans-serif'
          }}
        >
          {t('footer.copyright')}
        </Typography>
      </Container>
    </Box>
  );
};

export default Footer;
