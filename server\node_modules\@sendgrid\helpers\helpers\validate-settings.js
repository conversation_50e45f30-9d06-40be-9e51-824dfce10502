'use strict';

const validate = (parent, parentName, childName, childType) => {
  if (typeof parent === 'undefined' || typeof parent[childName] === 'undefined') {
    return;
  }
  if (typeof parent[childName] !== childType) {
    throw new Error(`${childType} expected for \`${parentName}.${childName}\``)
  }
};

module.exports = {
  validateMailSettings(settings) {
    if (typeof settings !== 'object') {
      throw new Error('Object expected for `mailSettings`');
    }
    const {
      bcc,
      bypassListManagement,
      bypassSpamManagement,
      bypassBounceManagement,
      bypassUnsubscribeManagement,
      footer,
      sandboxMode,
      spamCheck,
    } = settings;
    validate(bcc, 'bcc', 'enable', 'boolean');
    validate(bcc, 'bcc', 'email', 'string');
    validate(bypassListManagement, 'bypassListManagement', 'enable', 'boolean');
    validate(bypassSpamManagement, 'bypassSpamManagement', 'enable', 'boolean');
    validate(bypassBounceManagement, 'bypassBounceManagement', 'enable', 'boolean');
    validate(bypassUnsubscribeManagement, 'bypassUnsubscribeManagement', 'enable', 'boolean');
    validate(footer, 'footer', 'enable', 'boolean');
    validate(footer, 'footer', 'text', 'string');
    validate(footer, 'footer', 'html', 'string');
    validate(sandboxMode, 'sandboxMode', 'enable', 'boolean');
    validate(spamCheck, 'spamCheck', 'enable', 'boolean');
    validate(spamCheck, 'spamCheck', 'threshold', 'number');
    validate(spamCheck, 'spamCheck', 'postToUrl', 'string');
  },

  validateTrackingSettings(settings) {
    if (typeof settings !== 'object') {
      throw new Error('Object expected for `trackingSettings`');
    }
    const {
      clickTracking,
      openTracking,
      subscriptionTracking,
      ganalytics,
    } = settings;
    validate(clickTracking, 'clickTracking', 'enable', 'boolean');
    validate(clickTracking, 'clickTracking', 'enableText', 'boolean');
    validate(openTracking, 'openTracking', 'enable', 'boolean');
    validate(openTracking, 'openTracking', 'substitutionTag', 'string');
    validate(subscriptionTracking, 'subscriptionTracking', 'enable', 'boolean');
    validate(subscriptionTracking, 'subscriptionTracking', 'text', 'string');
    validate(subscriptionTracking, 'subscriptionTracking', 'html', 'string');
    validate(subscriptionTracking, 'subscriptionTracking', 'substitutionTag', 'string');
    validate(ganalytics, 'ganalytics', 'enable', 'boolean');
    validate(ganalytics, 'ganalytics', 'utm_source', 'string');
    validate(ganalytics, 'ganalytics', 'utm_medium', 'string');
    validate(ganalytics, 'ganalytics', 'utm_term', 'string');
    validate(ganalytics, 'ganalytics', 'utm_content', 'string');
    validate(ganalytics, 'ganalytics', 'utm_campaign', 'string');
  },
};
