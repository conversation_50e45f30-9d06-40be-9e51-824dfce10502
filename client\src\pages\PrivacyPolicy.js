import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Fade,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  useTheme,
  alpha,
  Alert
} from '@mui/material';
import {
  Info as InfoIcon,
  Email as EmailIcon,
} from '@mui/icons-material';

const PrivacyPolicy = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';

  const SectionCard = ({ children, bg }) => (
    <Card
      elevation={2}
      sx={{
        mb: 4,
        borderRadius: 3,
        ...(isRtl
          ? { borderRight: `6px solid ${theme.palette.primary.main}` }
          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),
        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),
      }}
    >
      <CardContent>{children}</CardContent>
    </Card>
  );

  /**
   * Safely fetch a translation; returns empty string if key is missing.
   */
  const safeT = (key) => {
    const val = t(key);
    return val && !val.includes(key) ? val : '';
  };

  /**
   * Render numbered section (1-9) – title, subtitle/content, bullet items.
   */
  const renderStandardSection = (num) => {
    const subtitle = safeT(`privacy.section${num}.subtitle`);
    const content = safeT(`privacy.section${num}.content`);

    // collect bullet items (item1-item10)
    const bullets = [];
    for (let i = 1; i <= 10; i++) {
      const item = safeT(`privacy.section${num}.item${i}`);
      if (item) bullets.push(item);
    }

    return (
      <SectionCard key={num}>
        <Typography
          variant="h5"
          fontWeight={600}
          color={theme.palette.primary.main}
          mb={2}
          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
        >
          {t(`privacy.section${num}.title`)}
        </Typography>
        {subtitle && <Typography mb={1}>{subtitle}</Typography>}
        {content && <Typography mb={1}>{content}</Typography>}
        {bullets.length > 0 && (
          <List>
            {bullets.map((item, idx) => (
              <ListItem
                key={idx}
                sx={{
                  pl: isRtl ? 0 : 2,
                  pr: isRtl ? 2 : 0,
                  flexDirection: 'row',
                  textAlign: isRtl ? 'right' : 'left',
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 'auto',
                    mx: isRtl ? 0 : 1,
                    ml: isRtl ? 1 : 0,
                  }}
                >
                  <InfoIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary={item}
                  sx={{ textAlign: isRtl ? 'right' : 'left' }}
                />
              </ListItem>
            ))}
          </List>
        )}
      </SectionCard>
    );
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,
        pt: 4,
        pb: 8,
      }}
    >
      <Container maxWidth="lg">
        <Fade in timeout={800}>
          <Box sx={{ p: { xs: 2, md: 4 }, direction: isRtl ? 'rtl' : 'ltr' }}>
            {/* Title & Intro */}
            <Typography
              variant="h4"
              fontWeight={700}
              mb={2}
              sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
            >
              {t('privacy.title')}
            </Typography>
            <Typography variant="subtitle1" color="text.secondary" mb={3}>
              {t('privacy.intro')}
            </Typography>
            <Divider sx={{ mb: 3 }} />

            {/* Sections 1-9 */}
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => renderStandardSection(num))}

            {/* Section 10 – Contact */}
            <SectionCard>
              <Typography
                variant="h5"
                fontWeight={600}
                color={theme.palette.primary.main}
                mb={2}
                sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
              >
                {t('privacy.section10.title')}
              </Typography>
              <Typography mb={2}>{t('privacy.section10.content')}</Typography>
              <Alert
                icon={<EmailIcon />}
                severity="info"
                sx={{ direction: isRtl ? 'rtl' : 'ltr' }}
              >
                {t('privacy.section10.email')}
              </Alert>
            </SectionCard>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default PrivacyPolicy;
