import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  MenuItem,
  Button,
  Avatar,
  Pagination,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  AccessTime as TimeIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';

const MeetingSessions = () => {
  const { t, i18n } = useTranslation();
  const [sessions, setSessions] = useState([]);
  const [stats, setStats] = useState({});
  const [recentSessions, setRecentSessions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    userRole: '',
    page: 1,
    limit: 10
  });
  const [pagination, setPagination] = useState({});

  const formatDuration = (seconds) => {
    if (!seconds) return '0:00';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    const locale = i18n.language === 'ar' ? ar : enUS;
    return format(date, 'dd/MM/yyyy HH:mm', { locale });
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/admin/meeting-time-stats');
      if (response.data.success) {
        setStats(response.data.stats);
        setRecentSessions(response.data.recentSessions);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const fetchSessions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      Object.keys(filters).forEach(key => {
        if (filters[key]) {
          params.append(key, filters[key]);
        }
      });

      const response = await axios.get(`/api/admin/meeting-sessions?${params}`);
      if (response.data.success) {
        setSessions(response.data.sessions);
        setPagination(response.data.pagination);
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
      setError('فشل في تحميل جلسات الاجتماعات');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    fetchSessions();
  }, [filters]);

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value,
      page: 1 // Reset to first page when filtering
    }));
  };

  const handlePageChange = (event, newPage) => {
    setFilters(prev => ({
      ...prev,
      page: newPage
    }));
  };

  if (error) {
    return (
      <Layout title={t('admin.meetingSessions.title')}>
        <Alert severity="error">{error}</Alert>
      </Layout>
    );
  }

  return (
    <Layout title={t('admin.meetingSessions.title')}>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
          {t('admin.meetingSessions.title')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          {t('admin.meetingSessions.overview')}
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main' }}>
                  <AnalyticsIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {stats.total_meetings || 0}
                  </Typography>
                  <Typography color="textSecondary">
                    {t('admin.meetingSessions.totalMeetings')}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main' }}>
                  <TimeIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatDuration(stats.total_duration_seconds)}
                  </Typography>
                  <Typography color="textSecondary">
                    {t('admin.meetingSessions.totalTime')}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main' }}>
                  <SchoolIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatDuration(stats.teacher_total_time)}
                  </Typography>
                  <Typography color="textSecondary">
                    {t('admin.meetingSessions.teacherTime')}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main' }}>
                  <PersonIcon />
                </Avatar>
                <Box>
                  <Typography variant="h4" fontWeight="bold">
                    {formatDuration(stats.student_total_time)}
                  </Typography>
                  <Typography color="textSecondary">
                    {t('admin.meetingSessions.studentTime')}
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('admin.meetingSessions.filterResults')}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                select
                fullWidth
                label={t('admin.meetingSessions.userType')}
                value={filters.userRole}
                onChange={(e) => handleFilterChange('userRole', e.target.value)}
              >
                <MenuItem value="">{t('admin.meetingSessions.all')}</MenuItem>
                <MenuItem value="teacher">{t('admin.meetingSessions.teacher')}</MenuItem>
                <MenuItem value="student">{t('admin.meetingSessions.student')}</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="outlined"
                onClick={() => setFilters({ userRole: '', page: 1, limit: 10 })}
              >
                {t('admin.meetingSessions.reset')}
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Sessions Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {t('admin.meetingSessions.sessions')}
          </Typography>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TableContainer component={Paper} sx={{ mt: 2 }}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>{t('admin.meetingSessions.user')}</TableCell>
                      <TableCell>{t('admin.meetingSessions.type')}</TableCell>
                      <TableCell>{t('admin.meetingSessions.meeting')}</TableCell>
                      <TableCell>{t('admin.meetingSessions.joinTime')}</TableCell>
                      <TableCell>{t('admin.meetingSessions.leaveTime')}</TableCell>
                      <TableCell>{t('admin.meetingSessions.duration')}</TableCell>
                      <TableCell>{t('admin.meetingSessions.status')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {sessions.map((session) => (
                      <TableRow key={session.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2" fontWeight="bold">
                              {session.full_name}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              ({session.email})
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={session.user_role === 'teacher' ? t('admin.meetingSessions.teacher') : t('admin.meetingSessions.student')}
                            color={session.user_role === 'teacher' ? 'primary' : 'secondary'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {session.meeting_name}
                          </Typography>
                        </TableCell>
                        <TableCell>{formatDateTime(session.join_time)}</TableCell>
                        <TableCell>{formatDateTime(session.leave_time)}</TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight="bold">
                            {formatDuration(session.duration_seconds)}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={session.session_status === 'active' ? t('admin.meetingSessions.active') : t('admin.meetingSessions.ended')}
                            color={session.session_status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              {pagination.pages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                  <Pagination
                    count={pagination.pages}
                    page={pagination.page}
                    onChange={handlePageChange}
                    color="primary"
                  />
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </Layout>
  );
};

export default MeetingSessions;
