import { io } from 'socket.io-client';

let socket = null;

export const initSocket = () => {
  const token = localStorage.getItem('token');

  if (!token) {
    console.log('No token available for socket connection');
    return null;
  }

  if (!socket) {
    const socketUrl = process.env.REACT_APP_API_URL || '';
    console.log('Initializing socket connection to:', socketUrl);

    socket = io(socketUrl, {
      auth: {
        token
      },
      transports: ['websocket'],
      withCredentials: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    socket.on('connect', () => {
      console.log('Socket connected successfully with ID:', socket.id);
    });

    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error.message);
      if (error.message.includes('Authentication error')) {
        disconnectSocket();
      }
    });

    socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
    });

    socket.on('reconnect', (attemptNumber) => {
      console.log('Socket reconnected after', attemptNumber, 'attempts');
    });

    socket.on('reconnect_error', (error) => {
      console.error('Socket reconnection error:', error);
    });

    socket.on('error', (error) => {
      console.error('Socket error:', error);
    });

    // Add event listener for application status updates
    socket.on('application_status_changed', (data) => {
      console.log('Received application status update:', data);
    });
  }

  return socket;
};

export const getSocket = () => socket;

export const connectSocket = (userId) => {
  if (userId) {
    console.log('Joining teacher room for user:', userId);
    socket.emit('join_teacher_room', userId);
  }
};

export const disconnectSocket = () => {
  if (socket) {
    console.log('Disconnecting socket:', socket.id);
    socket.disconnect();
    socket = null;
  }
};

export default getSocket();
