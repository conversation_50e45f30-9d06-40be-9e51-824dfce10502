import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  Checkbox,
  Typography,
  Box,
  Link as MuiLink,
} from '@mui/material';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const GenderDialog = ({ open, onClose, onSubmit, isRtl }) => {
  const { t } = useTranslation();
  const [gender, setGender] = useState('male');
  const [privacyPolicy, setPrivacyPolicy] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = () => {
    if (!privacyPolicy) {
      setError(t('privacyPolicy.agreement.required'));
      return;
    }
    onSubmit(gender);
  };

  return (
    <Dialog open={open} onClose={onClose} dir={isRtl ? 'rtl' : 'ltr'}>
      <DialogTitle>{t('auth.selectGender')}</DialogTitle>
      <DialogContent>
        <FormControl component="fieldset" fullWidth>
          <FormLabel component="legend">{t('auth.gender')}</FormLabel>
          <RadioGroup
            name="gender"
            value={gender}
            onChange={(e) => setGender(e.target.value)}
          >
            <FormControlLabel
              value="male"
              control={<Radio />}
              label={t('auth.male')}
            />
            <FormControlLabel
              value="female"
              control={<Radio />}
              label={t('auth.female')}
            />
          </RadioGroup>
        </FormControl>

        <Box sx={{ mt: 3 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={privacyPolicy}
                onChange={(e) => {
                  setPrivacyPolicy(e.target.checked);
                  if (e.target.checked) setError('');
                }}
                name="privacyPolicy"
              />
            }
            label={
              <Typography variant="body2">
                {t('privacyPolicy.agreement.checkbox')}{' '}
                <MuiLink
                  component={Link}
                  to="/privacy-policy"
                  target="_blank"
                  rel="noopener"
                  sx={{ fontWeight: 600 }}
                >
                  {t('footer.privacy')}
                </MuiLink>
              </Typography>
            }
          />
          {error && (
            <Typography color="error" variant="caption" sx={{ display: 'block', mt: 0.5 }}>
              {error}
            </Typography>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>{t('common.cancel')}</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {t('common.submit')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GenderDialog;
