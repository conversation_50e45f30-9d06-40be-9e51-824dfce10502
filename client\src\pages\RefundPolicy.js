import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Fade,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  useTheme,
  alpha,
  Alert,
} from '@mui/material';
import {
  Email as EmailIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';

const RefundPolicy = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';
  const refund = t('policies.refund', { returnObjects: true });

  const SectionCard = ({ children, bg }) => (
    <Card
      elevation={2}
      sx={{
        mb: 4,
        borderRadius: 3,
        ...(isRtl
          ? { borderRight: `6px solid ${theme.palette.primary.main}` }
          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),
        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),
      }}
    >
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,
        pt: 4,
        pb: 8,
      }}
    >
      <Container maxWidth="lg">
        <Fade in timeout={800}>
          <Box sx={{ p: { xs: 2, md: 4 }, direction: isRtl ? 'rtl' : 'ltr' }}>
            <Typography variant="h4" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
              {refund.title}
            </Typography>
            <Divider sx={{ mb: 3 }} />

            {/* القسم الأول */}
            <SectionCard>
              <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
                {refund.section1.title}
              </Typography>
              <Typography mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
                {refund.section1.description}
              </Typography>
              <List>
                {refund.section1.items.map((item, idx) => (
                  <ListItem key={idx} sx={{
                    pl: isRtl ? 0 : 2,
                    pr: isRtl ? 2 : 0,
                    flexDirection: 'row',
                    textAlign: isRtl ? 'right' : 'left',
                  }}>
                    <ListItemIcon sx={{
                      minWidth: 'auto',
                      mx: isRtl ? 0 : 1,
                      ml: isRtl ? 1 : 0,
                    }}>
                      <CheckCircleIcon color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary={item}
                      sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
                    />
                  </ListItem>
                ))}
              </List>
            </SectionCard>

            {/* القسم الثاني */}
            <SectionCard>
              <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
                {refund.section2.title}
              </Typography>
              <Typography mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
                {refund.section2.description}
              </Typography>
              <List>
                {refund.section2.items.map((item, idx) => (
                  <ListItem key={idx} sx={{
                    pl: isRtl ? 0 : 2,
                    pr: isRtl ? 2 : 0,
                    flexDirection: 'row',
                    textAlign: isRtl ? 'right' : 'left',
                  }}>
                    <ListItemIcon sx={{
                      minWidth: 'auto',
                      mx: isRtl ? 0 : 1,
                      ml: isRtl ? 1 : 0,
                    }}>
                      <CancelIcon color="error" />
                    </ListItemIcon>
                    <ListItemText
                      primary={item}
                      sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
                    />
                  </ListItem>
                ))}
              </List>
            </SectionCard>

            {/* القسم الثالث */}
            <SectionCard>
              <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
                {refund.section3.title}
              </Typography>
              <Typography sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
                {refund.section3.description}
              </Typography>
            </SectionCard>

            {/* معلومات التواصل */}
            <Divider sx={{ my: 3 }} />
            <Box>
              <Typography variant="h6" fontWeight={600} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
                {refund.contact.title}
              </Typography>
              <Alert
                icon={<EmailIcon />}
                severity="info"
                sx={{
                  direction: isRtl ? 'rtl' : 'ltr',
                  fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif'
                }}
              >
                {refund.contact.email}
              </Alert>
            </Box>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default RefundPolicy;