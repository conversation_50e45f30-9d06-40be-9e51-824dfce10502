-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.1deb5ubuntu1
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: 27 يوليو 2025 الساعة 16:20
-- إصدار الخادم: 8.0.42-0ubuntu0.22.04.1
-- PHP Version: 8.1.2-1ubuntu2.22

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `teach_me_islam_arabic`
--

-- --------------------------------------------------------

--
-- بنية الجدول `admin_earnings`
--

CREATE TABLE `admin_earnings` (
  `id` int NOT NULL,
  `meeting_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `teacher_id` int NOT NULL,
  `student_id` int NOT NULL,
  `lesson_amount` decimal(10,2) NOT NULL,
  `commission_rate` decimal(5,4) NOT NULL,
  `commission_amount` decimal(10,2) NOT NULL,
  `teacher_earnings` decimal(10,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `bookings`
--

CREATE TABLE `bookings` (
  `id` int NOT NULL,
  `teacher_profile_id` int NOT NULL,
  `student_id` int NOT NULL,
  `datetime` datetime NOT NULL,
  `status` enum('scheduled','completed','cancelled','issue_reported','ongoing') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'scheduled',
  `cancellation_reason` text COLLATE utf8mb4_general_ci,
  `cancelled_by` enum('student','teacher') COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cancelled_at` timestamp NULL DEFAULT NULL,
  `reschedule_reason` text COLLATE utf8mb4_general_ci,
  `rescheduled_by` enum('student','teacher') COLLATE utf8mb4_general_ci DEFAULT NULL,
  `rescheduled_at` timestamp NULL DEFAULT NULL,
  `original_datetime` datetime DEFAULT NULL,
  `duration` varchar(10) COLLATE utf8mb4_general_ci DEFAULT '50',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `bookings`
--

INSERT INTO `bookings` (`id`, `teacher_profile_id`, `student_id`, `datetime`, `status`, `cancellation_reason`, `cancelled_by`, `cancelled_at`, `reschedule_reason`, `rescheduled_by`, `rescheduled_at`, `original_datetime`, `duration`, `created_at`, `updated_at`) VALUES
(28, 32, 41, '2025-06-11 22:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-11 21:51:33', '2025-06-11 22:26:40'),
(29, 32, 41, '2025-06-11 22:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-11 22:27:28', '2025-06-11 22:58:19'),
(30, 31, 31, '2025-06-12 10:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-12 09:54:05', '2025-07-13 07:05:30'),
(31, 32, 41, '2025-06-12 15:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-12 10:16:55', '2025-06-12 19:50:44'),
(32, 32, 41, '2025-06-12 12:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-12 12:30:03', '2025-06-12 13:48:47'),
(33, 32, 41, '2025-06-12 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-12 13:53:03', '2025-06-12 19:50:44'),
(34, 31, 31, '2025-06-12 19:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-12 15:50:45', '2025-07-13 07:05:30'),
(35, 31, 31, '2025-06-12 20:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-12 16:07:26', '2025-07-13 07:05:30'),
(36, 32, 41, '2025-06-12 21:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-12 20:38:18', '2025-06-12 21:32:46'),
(37, 32, 41, '2025-06-12 21:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-12 21:27:37', '2025-06-12 21:56:16'),
(38, 32, 41, '2025-06-12 22:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-12 22:08:27', '2025-06-12 22:56:34'),
(39, 32, 41, '2025-06-12 23:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-12 22:12:29', '2025-06-13 01:26:45'),
(40, 31, 31, '2025-06-13 07:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-13 06:51:58', '2025-07-13 07:05:30'),
(41, 31, 31, '2025-06-13 19:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-13 19:11:01', '2025-07-13 07:05:30'),
(42, 31, 31, '2025-06-13 20:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-13 19:11:38', '2025-07-13 07:05:30'),
(43, 31, 41, '2025-06-13 21:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-13 20:33:04', '2025-06-13 21:58:57'),
(44, 32, 41, '2025-06-13 23:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-13 23:19:35', '2025-06-14 00:54:28'),
(45, 31, 31, '2025-06-14 19:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-14 08:30:43', '2025-07-13 07:05:30'),
(46, 31, 31, '2025-06-14 19:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-14 09:25:24', '2025-07-13 07:05:30'),
(47, 31, 31, '2025-06-14 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-14 10:16:22', '2025-07-13 07:05:30'),
(48, 31, 31, '2025-06-14 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-14 10:19:19', '2025-07-19 20:46:59'),
(49, 32, 41, '2025-06-14 12:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-14 12:27:01', '2025-06-14 12:58:02'),
(50, 31, 31, '2025-06-14 14:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-14 14:04:01', '2025-07-13 07:05:30'),
(51, 32, 41, '2025-06-14 18:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-14 17:44:15', '2025-06-14 18:52:40'),
(52, 32, 41, '2025-06-14 18:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-14 17:44:37', '2025-06-14 21:44:57'),
(53, 32, 41, '2025-06-14 19:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-14 17:50:59', '2025-06-14 21:44:57'),
(54, 32, 41, '2025-06-14 22:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-14 22:26:53', '2025-06-14 23:02:35'),
(55, 32, 41, '2025-06-16 15:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-14 23:32:36', '2025-06-16 20:02:13'),
(56, 32, 41, '2025-06-15 01:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-15 00:36:13', '2025-06-15 02:31:28'),
(57, 32, 41, '2025-06-15 03:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-15 02:32:14', '2025-06-15 03:29:29'),
(58, 32, 41, '2025-06-15 03:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-15 02:32:52', '2025-06-15 14:53:57'),
(59, 32, 41, '2025-06-15 04:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-15 02:33:47', '2025-06-15 14:53:57'),
(60, 31, 31, '2025-06-15 19:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-15 06:43:20', '2025-07-13 07:05:30'),
(61, 31, 31, '2025-06-15 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-15 13:50:09', '2025-07-13 07:05:30'),
(62, 32, 41, '2025-06-15 16:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-15 15:15:52', '2025-06-15 16:27:36'),
(63, 32, 41, '2025-06-15 16:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-15 15:16:26', '2025-07-12 16:58:32'),
(64, 31, 31, '2025-06-16 20:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-16 19:35:15', '2025-07-13 07:05:30'),
(65, 32, 41, '2025-06-16 20:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-16 20:05:52', '2025-06-16 21:54:06'),
(66, 32, 41, '2025-06-16 22:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-16 21:54:04', '2025-06-16 23:06:16'),
(67, 32, 41, '2025-06-16 23:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-16 22:39:47', '2025-06-17 10:51:23'),
(68, 32, 41, '2025-06-16 23:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-16 22:45:59', '2025-06-16 23:29:35'),
(69, 31, 31, '2025-06-17 06:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-17 06:23:10', '2025-07-13 07:05:30'),
(70, 32, 41, '2025-06-17 11:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-17 10:52:15', '2025-06-17 11:54:25'),
(71, 32, 41, '2025-06-17 12:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-17 11:55:28', '2025-06-17 13:10:59'),
(72, 32, 41, '2025-06-17 13:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-17 13:11:56', '2025-06-17 14:05:34'),
(73, 32, 41, '2025-06-17 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-17 14:06:52', '2025-06-17 15:06:55'),
(74, 32, 41, '2025-06-17 15:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-17 15:07:56', '2025-06-17 15:50:14'),
(75, 32, 41, '2025-06-17 16:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-17 15:51:53', '2025-06-17 17:37:38'),
(76, 32, 41, '2025-06-17 17:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-17 17:38:48', '2025-06-17 17:52:14'),
(77, 32, 41, '2025-06-17 18:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-17 17:52:56', '2025-06-17 18:54:36'),
(78, 32, 41, '2025-06-17 19:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-17 18:54:29', '2025-06-18 00:42:22'),
(79, 32, 41, '2025-06-18 00:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-18 00:40:57', '2025-06-18 01:23:48'),
(83, 32, 41, '2025-06-18 01:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 01:45:03', '2025-06-18 02:00:20'),
(84, 32, 41, '2025-06-18 02:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-18 02:00:45', '2025-06-18 05:53:19'),
(85, 32, 41, '2025-06-18 06:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 05:54:12', '2025-06-18 06:26:47'),
(86, 32, 41, '2025-06-18 06:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 06:27:06', '2025-06-18 07:12:53'),
(87, 32, 41, '2025-06-18 07:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-18 07:13:21', '2025-06-18 07:53:48'),
(88, 31, 31, '2025-06-18 08:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-18 07:41:14', '2025-07-13 07:05:30'),
(89, 32, 41, '2025-06-18 08:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-18 07:54:32', '2025-06-18 08:54:03'),
(90, 32, 41, '2025-06-18 09:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 08:54:51', '2025-06-18 09:32:43'),
(91, 32, 41, '2025-06-18 09:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 09:32:41', '2025-06-18 09:57:52'),
(92, 32, 41, '2025-06-18 10:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 09:57:50', '2025-06-18 10:58:15'),
(93, 31, 31, '2025-06-18 10:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 10:24:48', '2025-07-13 07:05:30'),
(94, 31, 31, '2025-06-18 11:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-18 10:25:23', '2025-07-13 07:05:30'),
(95, 32, 41, '2025-06-18 11:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 10:58:42', '2025-06-18 11:27:45'),
(96, 32, 41, '2025-06-18 11:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 11:27:43', '2025-06-18 13:21:06'),
(97, 31, 31, '2025-06-18 12:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 12:20:50', '2025-07-13 07:05:30'),
(98, 32, 41, '2025-06-18 13:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 13:21:28', '2025-06-18 13:59:30'),
(99, 31, 31, '2025-06-18 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-18 13:47:48', '2025-07-13 07:05:30'),
(100, 32, 41, '2025-06-18 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 13:59:28', '2025-06-18 14:35:25'),
(101, 32, 41, '2025-06-18 15:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 14:39:56', '2025-06-18 16:14:51'),
(102, 31, 31, '2025-06-18 15:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-18 14:51:30', '2025-07-13 07:05:30'),
(103, 31, 31, '2025-06-18 16:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 15:51:21', '2025-07-13 07:05:30'),
(104, 32, 41, '2025-06-18 17:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 16:57:13', '2025-06-18 17:26:37'),
(105, 32, 41, '2025-06-18 17:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-18 17:27:11', '2025-06-19 04:13:13'),
(106, 32, 41, '2025-06-19 04:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-19 04:13:49', '2025-06-19 04:25:34'),
(107, 32, 41, '2025-06-19 04:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-19 04:25:32', '2025-06-20 12:48:34'),
(108, 31, 31, '2025-06-19 10:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-19 10:08:54', '2025-07-13 07:05:30'),
(109, 31, 31, '2025-06-19 11:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-19 10:09:24', '2025-07-13 07:05:30'),
(110, 31, 31, '2025-06-19 15:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-19 14:59:00', '2025-07-13 07:05:30'),
(111, 31, 31, '2025-06-20 08:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-20 07:57:33', '2025-07-13 07:05:30'),
(112, 31, 31, '2025-06-20 08:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-20 07:58:39', '2025-07-19 20:46:59'),
(113, 31, 31, '2025-06-20 08:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-20 08:02:04', '2025-07-13 07:05:30'),
(114, 32, 41, '2025-06-20 13:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-20 12:48:31', '2025-06-20 13:29:19'),
(115, 32, 41, '2025-06-20 13:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-20 13:29:05', '2025-06-20 14:14:21'),
(116, 32, 41, '2025-06-20 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-20 13:55:32', '2025-06-20 14:41:00'),
(117, 32, 41, '2025-06-20 14:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-20 14:42:08', '2025-06-20 15:22:26'),
(118, 32, 41, '2025-06-20 15:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-20 15:23:01', '2025-06-20 15:55:56'),
(119, 32, 41, '2025-06-20 16:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-20 15:55:44', '2025-06-21 05:54:16'),
(120, 31, 31, '2025-06-21 07:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-20 16:21:24', '2025-07-13 07:05:30'),
(121, 31, 31, '2025-06-20 19:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-20 16:22:27', '2025-07-13 07:05:30'),
(122, 32, 41, '2025-06-21 06:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-21 05:54:14', '2025-06-21 06:27:10'),
(123, 32, 41, '2025-06-21 06:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-21 06:27:29', '2025-06-21 06:58:50'),
(124, 32, 41, '2025-06-21 07:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-21 06:59:11', '2025-06-21 07:57:53'),
(125, 32, 41, '2025-06-21 08:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '61', '2025-06-21 07:58:21', '2025-06-21 09:09:39'),
(126, 31, 31, '2025-06-21 09:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-21 09:28:24', '2025-07-13 07:05:30'),
(127, 31, 31, '2025-06-21 10:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-21 09:56:50', '2025-07-13 07:05:30'),
(128, 31, 31, '2025-06-21 18:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-21 10:17:05', '2025-07-13 07:05:30'),
(129, 32, 41, '2025-06-21 10:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-21 10:17:34', '2025-06-21 15:24:51'),
(130, 31, 31, '2025-06-22 14:00:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-21 10:19:04', '2025-07-16 19:31:53'),
(131, 31, 31, '2025-06-21 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-21 13:57:02', '2025-07-13 07:05:30'),
(132, 31, 31, '2025-06-21 14:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-21 14:24:44', '2025-07-13 07:05:30'),
(133, 31, 31, '2025-06-21 16:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-21 14:30:53', '2025-07-13 07:05:30'),
(134, 32, 41, '2025-06-21 15:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-21 15:23:30', '2025-06-21 16:10:47'),
(135, 32, 41, '2025-06-21 16:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-21 16:13:14', '2025-06-21 18:17:51'),
(136, 32, 41, '2025-06-21 21:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-21 17:38:42', '2025-06-22 04:31:22'),
(137, 32, 41, '2025-06-21 21:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-21 17:51:29', '2025-06-22 04:31:22'),
(138, 32, 41, '2025-06-21 22:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-21 17:52:04', '2025-06-22 04:31:22'),
(139, 32, 41, '2025-06-22 08:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-22 05:25:57', '2025-06-22 11:08:41'),
(140, 32, 41, '2025-06-22 09:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-22 05:31:13', '2025-06-22 11:08:41'),
(141, 32, 41, '2025-06-22 08:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-22 07:57:10', '2025-06-22 11:08:41'),
(142, 31, 31, '2025-06-22 08:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-22 07:57:59', '2025-07-13 07:05:30'),
(143, 31, 31, '2025-06-22 08:30:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-22 07:58:15', '2025-07-16 19:31:53'),
(144, 31, 31, '2025-06-22 08:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-22 07:59:05', '2025-07-19 20:46:59'),
(145, 31, 31, '2025-06-22 08:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-22 08:00:00', '2025-07-19 20:46:59'),
(146, 31, 31, '2025-06-22 09:00:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-22 08:01:46', '2025-07-16 19:31:53'),
(149, 32, 41, '2025-06-24 19:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-24 19:29:33', '2025-07-12 16:58:32'),
(150, 32, 41, '2025-06-24 20:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-24 19:58:29', '2025-07-12 16:58:32'),
(151, 32, 41, '2025-06-24 20:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-24 20:00:26', '2025-07-19 19:01:19'),
(152, 32, 41, '2025-06-24 22:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-24 21:43:00', '2025-06-25 08:50:08'),
(153, 31, 31, '2025-06-25 10:30:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-25 10:28:00', '2025-07-16 19:31:53'),
(154, 31, 31, '2025-06-26 17:30:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-26 17:02:03', '2025-07-16 19:31:53'),
(155, 31, 31, '2025-06-26 20:30:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-26 20:05:17', '2025-07-16 19:31:53'),
(156, 31, 31, '2025-06-26 21:00:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-26 20:57:41', '2025-07-16 19:31:53'),
(157, 32, 41, '2025-06-27 15:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-27 15:22:59', '2025-06-27 20:56:33'),
(158, 31, 31, '2025-06-27 16:00:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-27 15:34:20', '2025-07-16 19:31:53'),
(159, 32, 41, '2025-06-27 21:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-27 20:57:11', '2025-07-02 14:48:15'),
(160, 31, 31, '2025-06-28 08:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-28 07:57:12', '2025-07-13 07:05:30'),
(161, 31, 31, '2025-06-28 09:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-28 09:26:03', '2025-07-13 07:05:30'),
(162, 31, 31, '2025-06-30 12:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-06-30 12:05:37', '2025-07-13 07:05:30'),
(163, 31, 31, '2025-06-30 16:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-06-30 16:28:12', '2025-07-13 07:05:30'),
(165, 32, 75, '2025-07-02 22:00:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-02 21:33:50', '2025-07-16 19:36:53'),
(166, 32, 41, '2025-07-11 20:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '30', '2025-07-11 20:20:00', '2025-07-11 21:00:24'),
(167, 32, 41, '2025-07-11 21:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-11 20:58:16', '2025-07-11 21:34:26'),
(168, 32, 41, '2025-07-11 21:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-11 21:34:56', '2025-07-11 21:59:29'),
(170, 32, 41, '2025-07-11 23:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '43', '2025-07-11 23:07:59', '2025-07-12 00:07:46'),
(171, 32, 41, '2025-07-12 00:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2', '2025-07-12 00:25:50', '2025-07-12 00:32:12'),
(172, 32, 41, '2025-07-12 10:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '30', '2025-07-12 10:57:33', '2025-07-12 11:05:00'),
(173, 32, 41, '2025-07-12 12:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-12 12:52:12', '2025-07-16 14:42:01'),
(174, 32, 41, '2025-07-12 13:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '37', '2025-07-12 13:21:23', '2025-07-12 13:40:19'),
(175, 32, 41, '2025-07-12 13:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-12 13:40:44', '2025-07-12 13:55:21'),
(177, 32, 41, '2025-07-12 15:00:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-12 15:22:27', '2025-07-16 19:36:53'),
(178, 32, 41, '2025-07-12 17:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '11', '2025-07-12 17:05:06', '2025-07-16 14:42:01'),
(179, 31, 31, '2025-07-13 07:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-07-13 07:06:08', '2025-07-13 15:24:59'),
(180, 31, 31, '2025-07-13 15:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-13 15:25:12', '2025-07-13 16:53:26'),
(181, 32, 41, '2025-07-16 14:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-07-16 14:48:56', '2025-07-19 19:01:19'),
(182, 31, 31, '2025-07-19 20:00:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-07-19 20:47:13', '2025-07-19 20:51:01'),
(183, 31, 31, '2025-07-20 08:30:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-20 08:00:22', '2025-07-20 08:55:14'),
(187, 32, 41, '2025-07-23 17:00:00', 'cancelled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-23 16:17:50', '2025-07-23 16:43:16'),
(188, 32, 41, '2025-07-23 17:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-23 16:44:31', '2025-07-23 17:26:05'),
(189, 32, 41, '2025-07-23 20:00:00', 'cancelled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-23 17:14:00', '2025-07-23 17:25:32'),
(190, 32, 41, '2025-07-23 17:30:00', 'cancelled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-23 17:26:27', '2025-07-23 17:28:07'),
(191, 32, 41, '2025-07-23 18:00:00', 'cancelled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-23 17:28:41', '2025-07-23 17:28:58'),
(192, 32, 41, '2025-07-23 18:00:00', 'cancelled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-23 17:30:31', '2025-07-23 17:37:41'),
(193, 32, 41, '2025-07-23 18:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-23 17:42:15', '2025-07-24 10:53:22'),
(194, 32, 41, '2025-07-24 00:30:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-23 18:05:09', '2025-07-27 13:15:47'),
(195, 32, 41, '2025-07-24 11:00:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-24 10:01:14', '2025-07-27 13:15:47'),
(196, 32, 41, '2025-07-26 00:30:00', 'cancelled', 'jjudsiu ', 'teacher', '2025-07-24 20:31:47', NULL, NULL, NULL, NULL, '25', '2025-07-24 10:53:42', '2025-07-24 20:31:47'),
(197, 31, 31, '2025-07-24 13:30:00', 'cancelled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-24 13:07:35', '2025-07-24 13:07:49'),
(198, 31, 31, '2025-07-24 13:30:00', 'cancelled', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-24 13:08:13', '2025-07-24 13:10:10'),
(199, 31, 31, '2025-07-24 13:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-24 13:10:22', '2025-07-24 16:42:26'),
(200, 31, 31, '2025-07-24 17:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '50', '2025-07-24 16:42:37', '2025-07-24 21:19:26'),
(201, 32, 41, '2025-07-24 21:00:00', 'cancelled', 'jsjkjJK', 'student', '2025-07-24 20:46:39', NULL, NULL, NULL, NULL, '25', '2025-07-24 20:46:12', '2025-07-24 20:46:39'),
(202, 32, 41, '2025-07-25 00:30:00', 'completed', NULL, NULL, NULL, 'cnkjnj', 'teacher', '2025-07-24 21:08:43', '2025-07-25 15:00:00', '25', '2025-07-24 21:07:09', '2025-07-25 19:41:26'),
(203, 31, 31, '2025-07-24 21:30:00', 'cancelled', 'نعم أنا مشغول', 'student', '2025-07-24 21:20:44', NULL, NULL, NULL, NULL, '25', '2025-07-24 21:19:38', '2025-07-24 21:20:44'),
(204, 31, 31, '2025-07-25 14:30:00', 'issue_reported', NULL, NULL, NULL, NULL, 'teacher', '2025-07-24 21:26:17', '2025-07-24 21:30:00', '25', '2025-07-24 21:22:04', '2025-07-27 11:49:40'),
(205, 31, 31, '2025-07-25 21:30:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-24 21:23:11', '2025-07-25 21:55:22'),
(206, 31, 31, '2025-07-25 11:30:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-25 11:10:59', '2025-07-25 11:55:46'),
(207, 32, 41, '2025-07-26 20:00:00', 'issue_reported', NULL, NULL, NULL, NULL, 'teacher', '2025-07-25 21:53:58', '2025-07-26 03:00:00', '25', '2025-07-25 11:37:28', '2025-07-27 13:15:47'),
(208, 32, 41, '2025-07-26 18:00:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-25 21:26:33', '2025-07-27 10:09:45'),
(209, 31, 31, '2025-07-28 10:00:00', 'scheduled', NULL, NULL, NULL, NULL, 'teacher', '2025-07-26 11:47:26', '2025-07-26 12:30:00', '25', '2025-07-26 11:44:45', '2025-07-26 11:47:26'),
(210, 32, 41, '2025-07-27 10:30:00', 'completed', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-27 10:10:30', '2025-07-27 11:16:57'),
(211, 31, 31, '2025-07-27 12:00:00', 'issue_reported', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '25', '2025-07-27 11:49:20', '2025-07-27 12:25:47');

-- --------------------------------------------------------

--
-- بنية الجدول `categories`
--

CREATE TABLE `categories` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `description` text COLLATE utf8mb4_general_ci,
  `created_by` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `categories`
--

INSERT INTO `categories` (`id`, `name`, `description`, `created_by`, `created_at`, `updated_at`) VALUES
(11, '', '', 1, '2025-04-16 11:35:22', '2025-06-30 17:26:47'),
(12, 'العقيدة والتوحيد – Creed and Monotheism (ʿAqīdah and Tawḥīd)', '', 1, '2025-04-16 11:35:33', '2025-06-09 17:03:09'),
(13, 'أصول الفقه – Principles of Islamic Jurisprudence (Uṣūl al-Fiqh)', '', 1, '2025-06-09 17:03:48', '2025-06-09 17:03:48'),
(14, 'الحديث وعلومه – Hadith and its Sciences', '', 1, '2025-06-09 17:04:03', '2025-06-09 17:04:03'),
(15, 'علوم القرآن – Quranic Sciences', '', 1, '2025-06-09 17:04:17', '2025-06-09 17:04:17'),
(16, 'السيرة النبوية والتاريخ الإسلامي – Prophetic Biography and Islamic History', '', 1, '2025-06-09 17:04:31', '2025-06-09 17:04:31'),
(17, 'الفرائض (علم المواريث) – Inheritance Law (ʿIlm al-Farāʾiḍ)', '', 1, '2025-06-09 17:04:42', '2025-06-09 17:04:42'),
(18, 'مقاصد الشريعة – Objectives of Islamic Law (Maqāṣid al-Sharīʿah)', '', 1, '2025-06-09 17:04:54', '2025-06-09 17:04:54'),
(19, 'الآداب الشرعية والتزكية – Islamic Etiquette and Spiritual Purification', '', 1, '2025-06-09 17:05:07', '2025-06-09 17:05:07'),
(20, 'القواعد الفقهية – Legal Maxims (Qawāʿid Fiqhiyyah)', '', 1, '2025-06-09 17:05:17', '2025-06-09 17:05:17'),
(21, 'الاجتهاد والفتوى – Ijtihad and Fatwa', '', 1, '2025-06-09 17:05:35', '2025-06-09 17:05:35'),
(22, 'المنطق وآداب البحث والمناظرة – Logic and the Ethics of Inquiry and Debate', '', 1, '2025-06-09 17:05:57', '2025-06-09 17:05:57'),
(23, 'النحو – Grammar (Nahw)', '', 1, '2025-06-09 17:34:32', '2025-06-09 17:34:32'),
(24, 'الصرف – Morphology (Sarf)', '', 1, '2025-06-09 17:34:49', '2025-06-09 17:34:49'),
(25, 'البلاغة – Rhetoric (Balāghah)', '', 1, '2025-06-09 17:35:01', '2025-06-09 17:35:01'),
(26, 'العروض والقافية – Prosody and Rhyme (ʿArūḍ and Qāfiyah)', '', 1, '2025-06-09 17:35:14', '2025-06-09 17:35:14'),
(27, 'اللغة والمعجم – Lexicology and Semantics', '', 1, '2025-06-09 17:35:25', '2025-06-09 17:35:25'),
(28, 'الخط والإملاء – Calligraphy and Orthography', '', 1, '2025-06-09 17:35:40', '2025-06-09 17:35:40'),
(29, 'الإنشاء والتعبير – Composition and Expression', '', 1, '2025-06-09 17:36:05', '2025-06-09 17:36:05'),
(30, 'الأدب العربي – Arabic Literature', '', 1, '2025-06-09 17:36:15', '2025-06-09 17:36:15'),
(31, 'القراءة والكتابة – Reading and Writing', '', 1, '2025-06-09 17:36:26', '2025-06-09 17:36:26'),
(32, 'الاستماع والمحادثة – Listening and Speaking', '', 1, '2025-06-09 17:36:37', '2025-06-09 17:36:37');

-- --------------------------------------------------------

--
-- بنية الجدول `contact_messages`
--

CREATE TABLE `contact_messages` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `user_role` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('pending','answered') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `reply` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL,
  `answered_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `conversations`
--

CREATE TABLE `conversations` (
  `id` int NOT NULL,
  `student_id` int NOT NULL,
  `teacher_id` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `conversations`
--

INSERT INTO `conversations` (`id`, `student_id`, `teacher_id`, `created_at`, `updated_at`) VALUES
(8, 31, 30, '2025-06-09 12:50:29', '2025-06-09 12:50:29'),
(9, 41, 40, '2025-06-10 20:25:33', '2025-06-10 20:25:33');

-- --------------------------------------------------------

--
-- بنية الجدول `languages`
--

CREATE TABLE `languages` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `created_by` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `languages`
--

INSERT INTO `languages` (`id`, `name`, `created_by`, `created_at`, `updated_at`) VALUES
(11, 'العربية (Arabic)', 1, '2025-06-09 16:12:39', '2025-06-09 16:55:00'),
(12, 'الإنجليزية (English)', 1, '2025-06-09 16:55:17', '2025-06-09 16:55:17'),
(13, 'الفرنسية (Français)', 1, '2025-06-09 16:55:32', '2025-06-09 16:55:32'),
(14, 'الإسبانية (Español)', 1, '2025-06-09 16:55:46', '2025-06-09 16:55:46'),
(15, 'التركية (Türkçe)', 1, '2025-06-09 16:55:57', '2025-06-09 16:55:57'),
(16, 'الأوردو (اردو)', 1, '2025-06-09 16:56:19', '2025-06-09 16:56:19'),
(17, 'الإندونيسية (Bahasa Indonesia)', 1, '2025-06-09 16:56:35', '2025-06-09 16:56:35'),
(18, 'الماليزية (Bahasa Melayu)', 1, '2025-06-09 16:56:46', '2025-06-09 16:56:46'),
(19, 'الألمانية (Deutsch)', 1, '2025-06-09 16:57:05', '2025-06-09 16:57:05'),
(20, 'الروسية (Русский)', 1, '2025-06-09 16:57:28', '2025-06-09 16:57:28'),
(21, 'الصينية (中文)', 1, '2025-06-09 16:57:49', '2025-06-09 16:57:49'),
(22, 'البنغالية (বাংলা)', 1, '2025-06-09 16:58:08', '2025-06-09 16:58:08'),
(23, 'الهوسا (Hausa)', 1, '2025-06-09 16:58:19', '2025-06-09 16:58:19'),
(24, 'السواحيلية (Kiswahili)', 1, '2025-06-09 16:58:35', '2025-06-09 16:58:35'),
(25, 'الفارسية (فارسی)', 1, '2025-06-09 16:58:45', '2025-06-09 16:58:45'),
(26, 'الأمهرية (Amharic)', 1, '2025-06-09 16:58:58', '2025-06-09 16:58:58'),
(27, 'الفيتنامية (Tiếng Việt)', 1, '2025-06-09 16:59:32', '2025-06-09 16:59:32'),
(28, 'التايلاندية (ภาษาไทย)', 1, '2025-06-09 16:59:54', '2025-06-09 16:59:54'),
(29, 'الكورية (한국어)', 1, '2025-06-09 17:00:09', '2025-06-09 17:00:09'),
(30, 'اليابانية (日本語)', 1, '2025-06-09 17:00:18', '2025-06-09 17:00:18'),
(31, 'البوسنية (Bosanski)', 1, '2025-06-09 17:00:31', '2025-06-09 17:00:31');

-- --------------------------------------------------------

--
-- بنية الجدول `meetings`
--

CREATE TABLE `meetings` (
  `id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `teacher_id` int NOT NULL,
  `student_id` int DEFAULT NULL,
  `meeting_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `room_name` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `videosdk_meeting_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `meeting_date` datetime NOT NULL,
  `duration` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('scheduled','completed','cancelled','issue_reported','ongoing') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'scheduled',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `teacher_total_time_seconds` int DEFAULT '0',
  `student_total_time_seconds` int DEFAULT '0',
  `session_start_time` timestamp NULL DEFAULT NULL,
  `session_end_time` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `meetings`
--

INSERT INTO `meetings` (`id`, `teacher_id`, `student_id`, `meeting_name`, `room_name`, `videosdk_meeting_id`, `meeting_date`, `duration`, `amount`, `status`, `created_at`, `updated_at`, `teacher_total_time_seconds`, `student_total_time_seconds`, `session_start_time`, `session_end_time`) VALUES
('039f8187-3107-4fad-980e-a91e5d3e5b3e', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 11:30 AM', '6a685487-e819-4f97-83ce-fb13f15cc342', 'zbow-81nv-ow3k', '2025-06-18 11:30:00', 25, '9.50', 'completed', '2025-06-18 11:27:43', '2025-07-12 16:33:20', 0, 0, NULL, NULL),
('044c35c1-cbae-47f9-ac54-e7722091d55c', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 20, 2025 08:30 AM', '93b46979-d5ef-4913-8765-e0e1d5fefaf5', 'wbic-1py7-zjai', '2025-07-20 08:30:00', 25, '2.00', 'issue_reported', '2025-07-20 08:00:23', '2025-07-20 08:56:14', 0, 0, NULL, NULL),
('08dc5909-7ac0-4249-8bb9-790c66a4bb80', 40, 41, 'jsjjsna & Mostafa - Jun 19, 2025 05:00 AM', 'a83722a2-42fb-4140-bf7e-4b0ba2b6e114', '9w8r-79ir-f4r8', '2025-06-19 04:00:00', 25, '9.50', 'completed', '2025-06-19 04:13:50', '2025-07-12 16:33:20', 0, 0, NULL, NULL),
('0be242e2-b90c-4dac-b0d7-0bfd77e20943', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 03:00 AM', 'ffb3ce6e-cbf6-4058-938f-6c5882886716', '5bu6-vzsv-hg59', '2025-06-18 02:00:00', 50, '19.00', 'completed', '2025-06-18 02:00:46', '2025-07-12 16:33:20', 0, 0, NULL, NULL),
('0cde89f9-4d1f-465a-8053-2a015d4b6f03', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 18, 2025 11:00 AM', 'a8073b8a-9392-479d-a593-6627e18da236', '3rnr-a3tm-7auj', '2025-06-18 11:00:00', 50, '4.00', 'completed', '2025-06-18 10:25:24', '2025-07-12 16:33:20', 0, 0, NULL, NULL),
('0e6cc984-b2e0-4250-b1f5-cae35731323b', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 12, 2025 10:00 AM', '6363e845-37ff-456b-b1e7-37df54c9a9f4', NULL, '2025-06-12 10:00:00', 25, '2.00', 'completed', '2025-06-12 09:54:05', '2025-06-13 07:05:22', 1566, 0, '2025-06-12 09:56:07', '2025-06-12 10:22:13'),
('0f75bcef-8c71-4b12-962b-03b0548ebfc6', 40, 41, 'jsjjsna & Mostafa - Jun 22, 2025 08:00 AM', '601d9be2-baf9-4cc9-98da-828f98c77446', '92z3-x6yr-ncg4', '2025-06-22 08:00:00', 25, '9.50', 'completed', '2025-06-22 07:57:10', '2025-07-12 16:33:20', 0, 0, NULL, NULL),
('103ae59d-3b49-489f-b450-6b41f66e4abe', 40, 41, 'jsjjsna & Mostafa - Jul 12, 2025 02:30 PM', 'bd92f447-6d2e-4436-8e82-3b3fbb466e70', '5h9q-dhdl-gbzr', '2025-07-12 13:30:00', 25, '7.50', 'completed', '2025-07-12 13:40:44', '2025-07-12 16:33:20', 0, 0, NULL, NULL),
('108702b3-a93f-43c5-b438-54909b328e99', 40, 41, 'jsjjsna & Mostafa - Jun 20, 2025 03:30 PM', '53038c20-02dd-4715-9707-ce43523faae3', '34jv-ntpx-lkay', '2025-06-20 15:30:00', 25, '9.50', 'completed', '2025-06-20 15:23:01', '2025-07-12 16:33:20', 0, 0, NULL, NULL),
('121811da-ec9f-412c-8402-985c0b74194b', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 30, 2025 04:30 PM', 'b9bcb8aa-6789-4b25-bd92-404c0daa4fa9', '9wzt-cyju-4tit', '2025-06-30 16:30:00', 50, '4.00', 'completed', '2025-06-30 16:28:13', '2025-07-12 16:33:20', 0, 0, NULL, NULL),
('145f280f-b839-48e8-93c4-4d05eedfc37a', 40, 41, 'jsjjsna & Mostafa - Jul 26, 2025 06:00 PM', 'c63162a1-754a-48b5-b30e-b06f9a237167', 'rhji-lh9u-1x81', '2025-07-26 18:00:00', 25, '7.50', 'completed', '2025-07-25 21:26:33', '2025-07-27 10:10:31', 0, 0, NULL, NULL),
('14dceb0f-2609-49d5-9cda-09eb74f397cb', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 10:30 AM', 'f1d4f30b-740e-4565-9f2a-c3c5323621ef', 'r3x7-aegd-0mv7', '2025-06-21 10:30:00', 25, '9.50', 'completed', '2025-06-21 10:17:34', '2025-07-12 16:33:20', 0, 0, NULL, NULL),
('15789fbb-21a1-4c8b-a04f-e29e40aa6480', 40, 41, 'jsjjsna & Mostafa - Jul 12, 2025 01:30 PM', 'b554bb02-ebbf-443f-8f2f-512d2a7e7ab0', 'h26r-rx1j-cqm6', '2025-07-12 12:30:00', 25, '7.50', 'completed', '2025-07-12 12:52:12', '2025-07-16 11:45:11', 0, 0, NULL, NULL),
('15effeee-a1b4-4a80-975b-1a236e96eaf8', 40, 41, 'jsjjsna & Mostafa - Jul 23, 2025 05:00 PM', 'c27aaa9d-ccec-43c2-9045-60762c589e9c', '50wq-wp1q-0to8', '2025-07-23 17:00:00', 25, '7.50', 'cancelled', '2025-07-23 16:17:51', '2025-07-23 16:18:47', 0, 0, NULL, NULL),
('16cdc33d-739e-419a-846c-9b55437370ca', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 06:30 AM', 'bb5a1212-d5ef-450d-b27e-9bde57541d6c', 'sny8-hpwa-n9h8', '2025-06-21 06:30:00', 25, '9.50', 'completed', '2025-06-21 06:27:30', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('1719abf1-c143-4f2c-b9da-325fa5594a3f', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 25, 2025 11:30 AM', '767fb219-b24f-49b8-871e-17bcb324e57c', 'kw4m-wee5-vh72', '2025-07-25 11:30:00', 25, '2.00', 'issue_reported', '2025-07-25 11:11:00', '2025-07-25 11:56:46', 0, 0, NULL, NULL),
('17aa90bb-e02f-4ac9-b016-742f30ab39b4', 40, 41, 'jsjjsna & Mostafa - Jun 14, 2025 10:30 PM', '522c5a5e-9a6e-41a9-b2bf-6ea039df1905', NULL, '2025-06-14 22:30:00', 25, '9.50', 'completed', '2025-06-14 22:26:53', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('1813b300-e5e1-40cc-9ab2-49c914815b74', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 01:00 AM', 'ee75ed99-c9ec-49e2-a92d-4c2a659faef5', NULL, '2025-06-18 00:00:00', 50, '19.00', 'completed', '2025-06-18 00:40:57', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('1a6128bb-573e-4033-a492-f05d1ca6f00e', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 10:30 AM', 'b8aef54e-2290-459b-9e54-e424ee6d1b92', 'cu7f-nuy6-cu5e', '2025-06-18 09:30:00', 25, '9.50', 'completed', '2025-06-18 09:32:41', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('1bb68a82-9213-47aa-ab4e-fb854a042054', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 21, 2025 06:30 PM', '041a57f3-3086-41a5-b9eb-78066170f419', '54qy-ku7v-6wwu', '2025-06-21 18:30:00', 50, '4.00', 'completed', '2025-06-21 10:17:05', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('1cbd806c-247f-48b7-9d5c-195cf90434ce', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 01:30 PM', 'f1d7aebe-05fc-4563-9b77-afe15b9cc1bf', 'j76f-1690-4gu0', '2025-06-18 13:30:00', 25, '9.50', 'completed', '2025-06-18 13:21:28', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('20f3a82c-79f3-4611-b621-8d8213de05d7', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 10:00 AM', '343116ec-484c-475c-a307-ca320f8c8d7a', 'l2m6-6wqu-cfth', '2025-06-18 10:00:00', 25, '9.50', 'completed', '2025-06-18 09:57:51', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('216ead0a-3914-4407-a075-283674655c07', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 24, 2025 01:30 PM', 'f9614335-ab72-4929-ae70-252ec5a8065f', 've8k-qre0-wrr6', '2025-07-24 13:30:00', 25, '2.00', 'cancelled', '2025-07-24 13:07:36', '2025-07-24 13:07:49', 0, 0, NULL, NULL),
('23ad5d51-88af-4570-9213-47912cb6e775', 40, 41, 'jsjjsna & Mostafa - Jun 20, 2025 01:00 PM', '84a800df-38e5-446f-8c22-40dc8c22e0a2', 'bukl-v25k-p0pk', '2025-06-20 13:00:00', 25, '9.50', 'completed', '2025-06-20 12:48:32', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('248f8e5c-50fc-4384-923b-e7438406a9c6', 40, 41, 'jsjjsna & Mostafa - Jun 14, 2025 06:00 PM', '89a2b7d5-1374-44e2-b34c-1bd00e909fb8', NULL, '2025-06-14 18:00:00', 50, '19.00', 'completed', '2025-06-14 17:44:15', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('25a5bd96-f2c4-403d-b35b-6ae20ee8d2af', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 22, 2025 08:30 AM', '28980f48-9864-4ea0-8b8b-fc115df01ce2', '2843-kke1-uzh4', '2025-06-22 08:30:00', 25, '2.00', 'cancelled', '2025-06-22 07:58:15', '2025-06-22 07:58:48', 0, 0, NULL, NULL),
('25f291f9-4d2f-4b10-b3ca-b30dc380d347', 40, 41, 'jsjjsna & Mostafa - Jun 17, 2025 04:00 PM', '4b6c96c1-1cde-46a7-bb51-c4e966887f88', NULL, '2025-06-17 16:00:00', 50, '19.00', 'completed', '2025-06-17 15:51:53', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('26721faf-b560-47f2-88cb-a3b39c5fd9e5', 40, 41, 'jsjjsna & Mostafa - Jun 17, 2025 04:00 PM', '35bc74a8-fda9-4150-8c52-29f4f1b28165', NULL, '2025-06-17 15:00:00', 50, '19.00', 'completed', '2025-06-17 15:07:56', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('27ea4b74-63ba-4857-a259-ffe8d221289e', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 21, 2025 02:00 PM', 'fb409547-fff5-46e3-b758-137d494f0159', 'c8bq-obs7-si1s', '2025-06-21 14:00:00', 50, '4.00', 'completed', '2025-06-21 13:57:02', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('29183d80-e9c2-4389-b5bc-b46c608e5bce', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 04:30 PM', '27c0c38b-9060-41d4-9963-5aeb4e5ac8f6', 'vxs4-9lyx-rbch', '2025-06-21 16:30:00', 50, '19.00', 'completed', '2025-06-21 16:13:14', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('2e88e480-4348-43c3-9d2c-dc665da0c5dd', 40, 41, 'jsjjsna & Mostafa - Jul 23, 2025 06:00 PM', '26b26891-91fa-4146-997a-4b7ea0d9b397', 'tj7l-0mmw-0rly', '2025-07-23 18:00:00', 25, '7.50', 'cancelled', '2025-07-23 17:28:41', '2025-07-23 17:28:58', 0, 0, NULL, NULL),
('2f5fd66c-f259-4ece-ba96-c2181ef4c6e6', 30, 41, 'jsjjsna & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 13, 2025 09:00 PM', '1c9a76ec-61fe-427f-9350-1c09e6564049', NULL, '2025-06-13 21:00:00', 50, '4.00', 'completed', '2025-06-13 20:33:04', '2025-06-13 21:51:21', 2901, 2994, '2025-06-13 21:02:33', '2025-06-13 21:51:21'),
('31145510-e6c4-4861-9f0a-f64fc92b3c8f', 40, 41, 'jsjjsna & Mostafa - Jun 15, 2025 03:30 AM', '3ca08a65-e5f5-4fe6-9f99-5adbc33960c9', NULL, '2025-06-15 03:30:00', 50, '19.00', 'completed', '2025-06-15 02:32:52', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('31267967-4f12-4a02-8899-8cc4a1541a15', 40, 41, 'jsjjsna & Mostafa - Jun 16, 2025 11:30 PM', '67d17a0d-d64b-4c8c-a023-da45e4640e0f', NULL, '2025-06-16 23:30:00', 50, '19.00', 'completed', '2025-06-16 22:39:47', '2025-07-12 17:05:07', 81, 0, '2025-06-16 23:30:12', '2025-06-16 23:31:33'),
('32835e48-33a3-4b8c-9d2a-3974d73141e9', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 24, 2025 01:30 PM', '6e1b9946-009d-45b4-9760-ab4a6b428ab6', 'w7tc-wkly-0r90', '2025-07-24 13:30:00', 25, '2.00', 'completed', '2025-07-24 13:10:22', '2025-07-24 13:55:48', 0, 0, NULL, NULL),
('32b7ee7c-b4f5-433c-a2e1-4eca3dbf50c7', 40, 41, 'jsjjsna & Mostafa - Jul 23, 2025 05:30 PM', '9c50633b-2de4-46a7-b004-d8ad6151c848', 'ty5s-xmlr-mpyx', '2025-07-23 17:30:00', 25, '7.50', 'cancelled', '2025-07-23 17:26:28', '2025-07-23 17:28:07', 0, 0, NULL, NULL),
('3402c1e3-57e2-4723-b43e-03b72c53f119', 40, 41, 'jsjjsna & Mostafa - Jul 26, 2025 12:30 AM', '0d12ecd9-35ee-424a-9ea1-ad4676e25291', '2vs2-vle5-39hw', '2025-07-26 00:30:00', 25, '7.50', 'cancelled', '2025-07-24 10:53:42', '2025-07-24 20:31:47', 0, 0, NULL, NULL),
('344ba562-3339-4421-b5ec-546184cdc4fa', 40, 41, 'jsjjsna & Mostafa - Jul 24, 2025 09:00 PM', 'd44a6ca7-bd2b-4f4e-9db3-7334d03cd948', 'xhfg-l0pl-70s3', '2025-07-24 21:00:00', 25, '7.50', 'cancelled', '2025-07-24 20:46:13', '2025-07-24 20:46:39', 0, 0, NULL, NULL),
('34f38403-0f0d-4d99-b0c3-a46b28d4dea7', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 21, 2025 04:00 PM', 'c6cbd785-5331-481a-95f3-a9f4af1dc192', 'azf3-9bl0-o9z4', '2025-06-21 16:00:00', 25, '2.00', 'completed', '2025-06-21 14:30:53', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('359822c2-8136-4cd8-80e6-2d0f06cf4cc9', 40, 75, 'jskbsb & Mostafa - Jul 2, 2025 10:00 PM', 'caf825e7-ea93-4f85-8306-61c851e6a65e', 'gy5t-1fsg-gsps', '2025-07-02 22:00:00', 25, '3.00', 'issue_reported', '2025-07-02 21:33:51', '2025-07-16 19:32:53', 0, 0, NULL, NULL),
('3630ed72-5f33-49d5-b9c2-9d8ac73f28a3', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 06:30 AM', 'e852a06c-4809-4608-9c06-c39209986953', 'v6d0-9tn5-yulu', '2025-06-18 06:30:00', 25, '9.50', 'completed', '2025-06-18 06:27:06', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('37040831-41ad-4022-8039-94df77dd66de', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 08:00 AM', 'cdec9d32-af46-47fb-a91a-d54a2e04c12a', 'h178-uaf1-jjpb', '2025-06-21 08:00:00', 25, '19.00', 'completed', '2025-06-21 07:58:21', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('370e06fb-7e32-4b5c-85eb-e72882413ba1', 40, 41, 'jsjjsna & Mostafa - Jul 16, 2025 03:00 PM', '99515a10-b8ff-4131-bdd0-22b00a272cec', 'igxl-w3vn-rkoq', '2025-07-16 14:00:00', 50, '15.00', 'completed', '2025-07-16 14:48:56', '2025-07-23 11:24:43', 0, 0, NULL, NULL),
('38043bf3-1bcc-4533-ab7c-ad11af6c1ea6', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 22, 2025 08:00 AM', '6cbe4ddb-a107-47b9-aca0-51d0184896cb', '62hy-5ew8-phzy', '2025-06-22 08:00:00', 25, '2.00', 'completed', '2025-06-22 07:59:05', '2025-07-19 20:47:14', 0, 0, NULL, NULL),
('380c4080-677e-482f-9515-3d76289fea36', 40, 41, 'jsjjsna & Mostafa - Jul 26, 2025 12:00 PM', '77690231-5a47-41db-9c1b-cccb709a6346', 'yplu-anxj-zgyg', '2025-07-26 12:00:00', 25, '7.50', 'completed', '2025-07-24 10:01:15', '2025-07-26 12:25:40', 0, 0, NULL, NULL),
('39d6ca17-3995-44eb-9403-74b97fda644f', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 19, 2025 09:00 PM', '4e64bb64-5f7a-4dcd-85db-0bde4f3ec469', 'txm4-pk54-y03l', '2025-07-19 20:00:00', 50, '2.00', 'issue_reported', '2025-07-19 20:47:14', '2025-07-19 20:52:00', 0, 0, NULL, NULL),
('3b0ba30d-2638-4e1e-b052-31f36b83a7dc', 40, 41, 'jsjjsna & Mostafa - Jul 11, 2025 09:00 PM', '55c60926-d559-400a-be7f-ae1584f3a518', 'dk0t-ydt8-midf', '2025-07-11 21:00:00', 25, '7.50', 'completed', '2025-07-11 20:58:16', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('3b6e9223-34e9-429a-8da9-dd0cbc75e171', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 15, 2025 07:30 PM', 'b0e80206-3a36-4943-ab08-b185c3e84977', NULL, '2025-06-15 19:30:00', 50, '4.00', 'completed', '2025-06-15 06:43:20', '2025-07-13 07:06:09', 0, 1644, '2025-06-15 19:40:16', NULL),
('3be590b6-51b7-4776-acf1-66e88448bb46', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 14, 2025 07:00 PM', 'dfc0b83e-ad6a-47a5-9120-38ca91147a92', NULL, '2025-06-14 19:00:00', 25, '2.00', 'cancelled', '2025-06-14 08:30:43', '2025-06-14 08:31:11', 0, 0, NULL, NULL),
('3c372c51-fa52-4819-b9d9-d92397df0d10', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 13, 2025 07:30 PM', 'e2ae6228-a860-4882-87f1-a2d5035a5109', NULL, '2025-06-13 19:30:00', 25, '2.00', 'completed', '2025-06-13 19:11:01', '2025-06-13 19:55:10', 1403, 1284, '2025-06-13 19:31:47', '2025-06-13 19:55:10'),
('3c44872d-c79d-4f55-b300-23c6b288d2fe', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 08:00 AM', 'ca4cd4a5-0867-4d5f-9c76-d44d5ff1cc52', 'swr0-ln1u-c4ok', '2025-06-18 07:00:00', 50, '19.00', 'completed', '2025-06-18 07:13:21', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('3ca264de-81a7-4b02-af11-5cb2509921a0', 40, 41, 'jsjjsna & Mostafa - Jul 11, 2025 10:30 PM', '855c52c9-9feb-4e1f-a7fb-9e0990582167', 'qewh-lqtt-c981', '2025-07-11 21:30:00', 25, '7.50', 'completed', '2025-07-11 21:34:56', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('4246d516-7b72-4e4a-ace2-789eb2802d90', 40, 41, 'jsjjsna & Mostafa - Jun 17, 2025 11:00 AM', '02ddd1dd-3835-47e9-acb0-64e9ee861ade', NULL, '2025-06-17 11:00:00', 50, '19.00', 'completed', '2025-06-17 10:52:15', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('43938612-b0cd-4dff-bb24-5bc18141213d', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 02:00 PM', 'f77c78dc-0a12-4c81-acdf-a495dd80b352', '7jy5-ym4l-x6nv', '2025-06-18 14:00:00', 25, '9.50', 'completed', '2025-06-18 13:59:28', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('44134c96-97d9-43e6-80cf-2584c8a18f9f', 40, 41, 'jsjjsna & Mostafa - Jun 20, 2025 02:00 PM', 'e72d22c2-3dce-4ce3-b7d5-a6b237ca8f7f', 'suxd-d1a0-pv5c', '2025-06-20 14:00:00', 25, '9.50', 'completed', '2025-06-20 13:55:32', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('45010fcf-39c6-44f0-a1f4-8f0475b02937', 40, 41, 'jsjjsna & Mostafa - Jul 25, 2025 03:00 PM', 'acc7586b-c1bf-4beb-9730-c6a195cdbad4', 'nbfg-v58e-g8nl', '2025-07-25 15:00:00', 25, '7.50', 'completed', '2025-07-24 21:07:10', '2025-07-25 21:26:33', 0, 0, NULL, NULL),
('46a43f67-51db-44c4-a73d-e973637ccda4', 40, 41, 'jsjjsna & Mostafa - Jun 16, 2025 09:00 PM', 'ef02884d-336b-4b1d-84d6-2661dc5f6e2a', NULL, '2025-06-16 20:00:00', 25, '9.50', 'completed', '2025-06-16 20:05:52', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('47079ab5-ba7c-4369-a488-42cbc07d6909', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 19, 2025 11:00 AM', 'b18af995-f6c9-4dca-b680-d7791a7a23c9', '9exw-ivla-2u9q', '2025-06-19 11:00:00', 25, '2.00', 'completed', '2025-06-19 10:09:24', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('49840017-e843-41c6-ae56-3ae0051d5f30', 40, 41, 'jsjjsna & Mostafa - Jul 27, 2025 11:00 AM', '7c4e96bc-c278-4e64-bec9-313835c33282', '5473-jqsa-lx7m', '2025-07-27 10:30:00', 25, '7.50', 'issue_reported', '2025-07-27 10:10:31', '2025-07-27 10:35:19', 0, 0, NULL, NULL),
('4b8cdd6c-a11f-41ec-8ec0-216a75892a86', 40, 41, 'jsjjsna & Mostafa - Jun 12, 2025 09:30 PM', 'e1087b75-6640-4043-8716-9556e61f00f6', NULL, '2025-06-12 21:30:00', 25, '9.50', 'completed', '2025-06-12 21:27:37', '2025-06-12 21:56:10', 0, 698, '2025-06-12 21:34:10', NULL),
('4d6f7803-3ccc-46f2-806f-8a3e21b0feca', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 06:00 AM', '302f56dc-03ac-4e67-a644-902ac5ba26e5', 'y7z6-ws6z-kwpd', '2025-06-21 06:00:00', 25, '9.50', 'completed', '2025-06-21 05:54:15', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('52054c22-0482-4911-8a3b-7f6a4f87df0f', 40, 41, 'jsjjsna & Mostafa - Jun 20, 2025 04:00 PM', '418ef532-5182-4489-b579-9dfef784b7aa', 'zf65-h11g-zd5t', '2025-06-20 16:00:00', 25, '9.50', 'completed', '2025-06-20 15:55:44', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('522107ec-c4fa-4743-9b62-a77a5e5ce233', 40, 41, 'jsjjsna & Mostafa - Jul 12, 2025 11:30 AM', 'c4d3abdd-d457-4bc5-b6e5-26f158bd496c', '2p6n-8nej-5pz5', '2025-07-12 10:30:00', 30, '7.50', 'completed', '2025-07-12 10:57:34', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('5276f7a8-e8f2-42c9-b25e-329c41ed9687', 40, 41, 'jsjjsna & Mostafa - Jun 17, 2025 06:00 PM', '4a63af79-8590-4365-9d54-e8589a7a8ce8', NULL, '2025-06-17 17:00:00', 50, '19.00', 'completed', '2025-06-17 17:38:48', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('5321d012-2f9e-43ee-a9bf-4f8f65a4df99', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 22, 2025 02:00 PM', '2154e8f7-c4e6-4e93-ae30-29c555193646', 'dby9-6b6b-5tes', '2025-06-22 14:00:00', 50, '4.00', 'issue_reported', '2025-06-21 10:19:04', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('545f793a-9cfd-4038-9c41-8eed63789d85', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 09:30 PM', 'c301b429-e609-43d7-bf47-5547ccea0d8a', 'yf9q-tb23-pzsf', '2025-06-21 21:30:00', 25, '9.50', 'completed', '2025-06-21 17:51:29', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('587d69e0-9c27-40ff-8ca8-cf0698de5185', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 14, 2025 07:00 PM', '96918935-7152-4ae3-b235-5fcf3316b6ba', NULL, '2025-06-14 19:30:00', 25, '2.00', 'completed', '2025-06-14 09:25:24', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('58e8f593-1673-4445-a151-e953f692e753', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 13, 2025 08:00 PM', '3ea9dd9f-8eda-45cd-8098-fc6ddac67db7', NULL, '2025-06-13 20:00:00', 25, '2.00', 'completed', '2025-06-13 19:11:38', '2025-06-13 21:01:44', 0, 0, NULL, NULL),
('59e37bbe-230f-417b-9a78-04f09a487c3e', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 18, 2025 03:00 PM', '2fb3b984-dbcb-41dd-9926-c31523b35691', 'a622-mmgn-cmgl', '2025-06-18 15:00:00', 50, '4.00', 'completed', '2025-06-18 14:51:30', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('5bd0d3b6-e4d0-4b69-87b7-eb320d348dc4', 40, 41, 'jsjjsna & Mostafa - Jun 15, 2025 03:00 AM', 'a4a83fe2-9187-4849-9254-f2afc59b73f4', NULL, '2025-06-15 03:00:00', 25, '9.50', 'completed', '2025-06-15 02:32:14', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('5de6d695-b508-4091-9536-d425dc4f3f3e', 40, 41, 'jsjjsna & Mostafa - Jun 22, 2025 09:00 AM', '9ded40d0-d192-4a93-84cf-6bc7ec6ae99a', 'lvxb-f49z-afzu', '2025-06-22 09:00:00', 25, '9.50', 'completed', '2025-06-22 05:31:13', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('5f958b08-31be-4761-a24c-015ada1d7ced', 40, 41, 'jsjjsna & Mostafa - Jun 14, 2025 06:30 PM', '15a6ddb6-dfe0-4dd0-b420-9957e8953b04', NULL, '2025-06-14 18:30:00', 25, '9.50', 'completed', '2025-06-14 17:44:37', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('6003fd27-733f-4a87-bf2a-f36c62601678', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 21, 2025 07:00 AM', '62b56e50-e065-412c-afd5-2b37d9fd46c6', 'rzmp-mbp3-xj2i', '2025-06-21 07:00:00', 50, '4.00', 'completed', '2025-06-20 16:21:24', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('651797e3-0b83-4e0f-9cb4-bb5e79f47f20', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 13, 2025 03:30 PM', 'ea8d5bec-7b75-462b-9122-c9df1ecf058d', 'mnfk-d0g1-fj3m', '2025-07-13 15:30:00', 25, '2.00', 'completed', '2025-07-13 15:25:13', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('65aed109-ee0d-4b3e-b104-0388104ab9f1', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 14, 2025 02:00 PM', 'cabf9431-7aa3-4f90-9796-89bd4082567c', NULL, '2025-06-14 14:00:00', 25, '2.00', 'completed', '2025-06-14 10:19:19', '2025-07-19 20:47:14', 0, 0, NULL, NULL),
('662a9454-62a3-4e84-beff-65e01ba2a2ce', 40, 41, 'jsjjsna & Mostafa - Jun 24, 2025 08:00 PM', '3850b985-5cf8-445b-b781-bdb3c4f73bcc', 'kaxr-olg7-weba', '2025-06-24 20:00:00', 25, '9.50', 'completed', '2025-06-24 20:00:26', '2025-07-23 11:24:43', 0, 0, NULL, NULL),
('698976df-4e65-40af-956e-ec5e8ed17eaf', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 14, 2025 02:30 PM', '90456aad-c575-417d-8172-ff8298f95703', NULL, '2025-06-14 14:30:00', 50, '4.00', 'completed', '2025-06-14 14:04:01', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('6b9b2cf5-28a6-479d-94e8-3bc32c01f782', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 24, 2025 11:00 PM', 'fe77e015-27c8-45fa-bd74-5db533d71b67', 'ku9w-j8vn-dyx1', '2025-06-24 23:00:00', 25, '2.00', 'issue_reported', '2025-06-24 17:28:39', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('6ba7be9e-ac17-4af5-b7a2-e592ad71e1fe', 40, 41, 'jsjjsna & Mostafa - Jun 27, 2025 09:00 PM', '87d310fc-8033-4403-9c6e-5eaebd17d547', '0onr-c6wj-r6qb', '2025-06-27 21:00:00', 25, '9.50', 'completed', '2025-06-27 20:57:12', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('6ee93a9c-0d2c-4f80-8db1-67943645124a', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 18, 2025 08:00 AM', 'eed0e05c-dc89-4a5a-8dac-1af494648e51', 'pjpf-lwtk-hlpw', '2025-06-18 08:00:00', 50, '4.00', 'completed', '2025-06-18 07:41:15', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('6fc89b65-68c2-4384-84c9-58811022d3d7', 40, 41, 'jsjjsna & Mostafa - Jul 12, 2025 04:00 PM', '89f132f7-f26e-40eb-8ff3-165ca5f3c594', 'nhav-n2uq-ftxn', '2025-07-12 15:00:00', 25, '7.50', 'issue_reported', '2025-07-12 15:22:27', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('70cd4620-726f-46c1-8a07-6fbc4ecc8c0d', 40, 41, 'jsjjsna & Mostafa - Jun 11, 2025 10:00 PM', '2f486972-0560-45b6-9cf4-273d89305c3a', NULL, '2025-06-11 21:00:00', 25, '9.50', 'completed', '2025-06-11 21:51:33', '2025-06-21 15:24:18', 5, 40, '2025-06-11 22:00:07', '2025-06-11 22:00:47'),
('71922ae9-d073-4914-8399-ff23ac2098b1', 40, 41, 'jsjjsna & Mostafa - Jun 11, 2025 10:30 PM', '85490016-2a23-4f08-940a-0de01c8336da', NULL, '2025-06-11 22:30:00', 25, '9.50', 'completed', '2025-06-11 22:27:28', '2025-06-11 22:55:45', 0, 0, NULL, NULL),
('72cb4593-1fdf-4922-8300-6bc1858703db', 40, 41, 'jsjjsna & Mostafa - Jul 12, 2025 12:00 AM', 'e6b6fcc3-341e-465d-95fe-7b01d3c530e7', 'tdo0-0vu7-tf93', '2025-07-11 23:00:00', 14, '7.50', 'completed', '2025-07-11 23:07:59', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('7339bc3c-58fb-45bf-91fc-ab7bfb4681df', 40, 41, 'jsjjsna & Mostafa - Jun 16, 2025 11:00 PM', '4c3a4c6f-4a47-4d4e-8c1b-ad31e8638fc2', NULL, '2025-06-16 23:00:00', 25, '9.50', 'completed', '2025-06-16 22:45:59', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('734d4c2f-fb56-4540-b7a3-04fd604c9f96', 40, 41, 'jsjjsna & Mostafa - Jun 14, 2025 07:00 PM', 'c06f1df4-4632-43cf-bd90-82c57ce44277', NULL, '2025-06-14 19:00:00', 50, '19.00', 'completed', '2025-06-14 17:50:59', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('7374bdbd-4b85-4497-82f5-a57a01a94821', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 26, 2025 05:30 PM', '0e799b11-5a6b-4f2f-ae37-3f2c168fcd53', '5c5g-u4sa-u5au', '2025-06-26 17:30:00', 25, '2.00', 'issue_reported', '2025-06-26 17:02:04', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('73f9635c-93d7-4087-90f2-cda60060491e', 40, 41, 'jsjjsna & Mostafa - Jun 13, 2025 11:30 PM', 'bfe7bd1c-4455-4676-a253-8f1c2cf91db0', NULL, '2025-06-13 23:30:00', 25, '9.50', 'completed', '2025-06-13 23:19:35', '2025-06-13 23:55:32', 1717, 595, '2025-06-13 23:55:31', '2025-06-13 23:55:32'),
('762ddc0b-f71d-45f5-ab36-e2a4ad460369', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 18, 2025 02:00 PM', '2ab46629-f8e8-4bb9-b5c0-f1e9766ed504', 'z1mp-a52n-n15x', '2025-06-18 14:00:00', 50, '4.00', 'completed', '2025-06-18 13:47:49', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('780baa66-d436-4292-9237-5605c6006550', 40, 41, 'jsjjsna & Mostafa - Jun 17, 2025 02:00 PM', '9dbcdaa1-f6c6-4cff-b13a-a7f8735f7d9f', NULL, '2025-06-17 13:00:00', 50, '19.00', 'completed', '2025-06-17 13:11:56', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('794bca08-6b9b-4ba8-a29a-af1e43e2cf3a', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 26, 2025 09:00 PM', 'c9266abb-0377-4b26-b3aa-b603ed18a616', 'sicn-s1nu-utp8', '2025-06-26 21:00:00', 25, '2.00', 'issue_reported', '2025-06-26 20:57:42', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('7a5b27cb-7716-414f-bfa9-21d39ae392bd', 40, 41, 'jsjjsna & Mostafa - Jun 12, 2025 09:00 PM', '246b411f-a71c-4c20-9ad5-96bbdd26feeb', NULL, '2025-06-12 21:00:00', 25, '9.50', 'completed', '2025-06-12 20:38:18', '2025-06-12 21:34:05', 1174, 0, '2025-06-12 21:14:59', '2025-06-12 21:34:05'),
('7dc757c4-e3e9-44d4-8e52-dd396483e724', 40, 41, 'jsjjsna & Mostafa - Jun 24, 2025 10:30 PM', '329813db-9399-4918-99f2-650f18b1c7fc', 'ixu0-bepm-xenm', '2025-06-24 22:30:00', 25, '9.50', 'completed', '2025-06-24 21:43:00', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('7ded9eaf-84ee-46ff-aa04-2b96f606a185', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 17, 2025 06:30 AM', '75a1a74d-1144-4765-871a-adfdeebff551', NULL, '2025-06-17 06:30:00', 25, '2.00', 'completed', '2025-06-17 06:23:10', '2025-07-13 07:06:09', 0, 0, '2025-06-17 06:30:30', NULL),
('7e1cc25c-ee44-4603-9a2a-e08e2e655be4', 40, 41, 'jsjjsna & Mostafa - Jun 15, 2025 04:30 AM', '4fd14534-a22b-4c26-8f9b-0a143ad1df54', NULL, '2025-06-15 04:30:00', 25, '9.50', 'completed', '2025-06-15 02:33:47', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('7e28fe4b-5fd3-4d35-975e-57aedb69f969', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 18, 2025 10:30 AM', '761d9eb2-7c5a-4e59-8bad-20986f1fa1fe', '0ivx-2qb4-diu9', '2025-06-18 10:30:00', 25, '2.00', 'completed', '2025-06-18 10:24:49', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('7efe8719-6645-4f19-8dae-1640eca9ed98', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 16, 2025 08:00 PM', '93ecd64e-cb2c-4d00-9a44-852e0fca4a78', NULL, '2025-06-16 20:00:00', 25, '2.00', 'completed', '2025-06-16 19:35:16', '2025-07-13 07:06:09', 0, 0, '2025-06-16 20:00:06', NULL),
('82646c83-4adc-47b7-a17a-a2d1506d8770', 40, 41, 'jsjjsna & Mostafa - Jun 12, 2025 03:00 PM', '24272af1-9737-49c6-b235-292f6503aeac', NULL, '2025-06-12 15:00:00', 25, '9.50', 'completed', '2025-06-12 10:16:55', '2025-06-12 19:52:49', 0, 0, NULL, NULL),
('82b9f8bf-968a-41b2-ac03-4ccb8c9d68fa', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 19, 2025 03:00 PM', '5edb5849-3460-41d0-a8d7-abf844f83512', 'lu4b-5n8y-8fph', '2025-06-19 15:00:00', 50, '4.00', 'completed', '2025-06-19 14:59:00', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('8418a631-9a3a-4bd8-a997-9b5a0350bb8a', 40, 41, 'jsjjsna & Mostafa - Jul 12, 2025 12:30 AM', '472eef27-579a-4ba2-856d-b2d0df16f06a', 'vtkq-vr1m-2ba2', '2025-07-12 00:30:00', 2, '7.50', 'completed', '2025-07-12 00:25:51', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('84ddd16e-8918-4371-9c54-bb6a30cc4be0', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 02:30 AM', '21e48891-eb34-4279-8b58-fa09cd2e4a29', 'mvaw-2h9n-8nbr', '2025-06-18 01:30:00', 25, '9.50', 'completed', '2025-06-18 01:45:03', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('854606f3-36bd-40c5-be40-456b439c9f7a', 40, 41, 'jsjjsna & Mostafa - Jul 23, 2025 05:00 PM', '59b44dd5-8395-47e2-a23b-fce8f8c371fb', 'kwn9-5asc-d9jp', '2025-07-23 17:00:00', 25, '7.50', 'completed', '2025-07-23 16:44:31', '2025-07-23 17:25:27', 0, 0, NULL, NULL),
('85ca85a0-3625-4852-959e-7040a63f2920', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 26, 2025 08:30 PM', '4d37aaf6-a739-4de8-9ec8-19b182175379', 'zqa8-ytsy-uotc', '2025-06-26 20:30:00', 25, '2.00', 'issue_reported', '2025-06-26 20:05:18', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('892f8bcc-0370-4c34-8cfa-18d6ae910b37', 40, 41, 'jsjjsna & Mostafa - Jul 25, 2025 07:00 PM', '76f9ac10-4de7-4e34-9a11-7781f075a0c4', 'w4fe-yil6-b930', '2025-07-25 19:00:00', 25, '7.50', 'completed', '2025-07-23 18:05:10', '2025-07-25 21:26:33', 0, 0, NULL, NULL),
('893bd2bd-94be-4ed2-a3ac-44546ea9d9a4', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 13, 2025 07:30 AM', '5a9f9beb-2b29-4798-9d13-b8ec86af26f3', 'hh13-mq85-8a7b', '2025-07-13 07:30:00', 50, '4.00', 'completed', '2025-07-13 07:06:09', '2025-07-13 15:25:13', 0, 0, NULL, NULL),
('8944cc66-fda5-46e5-86e7-53f6c27fe5f8', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 04:00 PM', '1c799438-93eb-4842-b6fc-a612a7c4a0d6', 'ehhh-zdib-n7gj', '2025-06-21 15:00:00', 50, '19.00', 'completed', '2025-06-21 15:23:30', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('8aed7e78-b10b-4838-b980-0d7862ca4383', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 21, 2025 10:00 AM', '1f249dd0-1fe9-4cf1-9d43-c7bac2409e41', 'jrys-66di-edc1', '2025-06-21 10:00:00', 50, '4.00', 'completed', '2025-06-21 09:56:50', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('8c96769b-eed4-4e06-9f8c-f77fe0907291', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 10:00 PM', 'a5fc666f-2591-4a66-9e07-40dfed12dbb5', 'qe5p-s1n0-32e7', '2025-06-21 22:00:00', 25, '9.50', 'completed', '2025-06-21 17:52:04', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('8e8bdf00-9cf5-4cb1-a70e-2cbee7742d78', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 12, 2025 07:00 PM', '9142d283-b712-4f7c-a6b5-e22dc4edcbc7', NULL, '2025-06-12 19:00:00', 50, '4.00', 'completed', '2025-06-12 15:50:45', '2025-06-13 07:05:22', 0, 0, NULL, NULL),
('91a5df98-2b25-4b24-8636-99755e6e609e', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 03:00 PM', '3c0e606e-3ec7-40cd-a8d9-6fb420539393', '5820-q6xy-4j0u', '2025-06-18 15:00:00', 25, '9.50', 'completed', '2025-06-18 14:39:56', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('95e96f52-0562-4c3d-a868-aaef9b1f1ff5', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 27, 2025 04:00 PM', 'a5cb8d39-1449-4f18-98af-df1df8d7aca4', 'vny0-y97h-t3cq', '2025-06-27 16:00:00', 50, '4.00', 'issue_reported', '2025-06-27 15:34:20', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('96a7c19f-1bee-4996-94b8-060ea86639b7', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 12, 2025 08:00 PM', '3225db0c-11d9-4849-bc20-2ec767322622', NULL, '2025-06-12 20:00:00', 25, '2.00', 'completed', '2025-06-12 16:07:26', '2025-06-13 07:05:21', 0, 0, NULL, NULL),
('994ec425-1962-4995-829c-0ccf81b895cb', 40, 41, 'jsjjsna & Mostafa - Jun 17, 2025 03:00 PM', 'f1861d49-f783-488b-826a-3ca6999f2d50', NULL, '2025-06-17 14:00:00', 50, '19.00', 'completed', '2025-06-17 14:06:52', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('998d0cda-185b-4897-8622-8ef2b09410ac', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 18, 2025 12:30 PM', '1e9f20a2-cde0-4ae2-9356-49d38b302e08', 'htz0-70me-xtul', '2025-06-18 12:30:00', 25, '2.00', 'completed', '2025-06-18 12:20:51', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('99b08d5c-d2ca-4c9a-a20a-a14f25eac13d', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 14, 2025 02:00 PM', 'ca2e6bea-e903-4fd5-8b23-4b20f171fd56', NULL, '2025-06-14 14:00:00', 25, '2.00', 'cancelled', '2025-06-14 10:16:22', '2025-06-14 10:16:51', 0, 0, NULL, NULL),
('9be790e2-2dd0-4830-a283-ec6d768fb7c5', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 09:00 PM', 'c5a3e0cd-3549-4758-bb26-91b73bc2a2ee', 'oe6h-skq3-lu6p', '2025-06-21 21:00:00', 25, '9.50', 'completed', '2025-06-21 17:38:42', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('9cbeb49d-7ba7-4b09-b9a8-ae2c065ff93e', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 19, 2025 10:30 AM', 'e5bfed94-308d-40c1-9a79-6860ef5a7c44', 'of1j-tbwc-8i8c', '2025-06-19 10:30:00', 25, '2.00', 'completed', '2025-06-19 10:08:54', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('9cc9a29b-4ac1-4b31-9c55-2efaba3814d8', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 24, 2025 03:00 PM', '15a9eaaa-9840-4f57-9f08-3b65ebd5a1a6', '5rjb-3ux5-opct', '2025-06-24 15:00:00', 25, '2.00', 'issue_reported', '2025-06-24 14:45:26', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('9f812ef2-8543-4773-a6d3-72e311aa335d', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 24, 2025 09:30 PM', '10d03f65-2886-4587-a354-e8469d78ec8e', 'vkj4-k0bh-xjb2', '2025-07-24 21:30:00', 25, '2.00', 'cancelled', '2025-07-24 21:19:38', '2025-07-24 21:20:44', 0, 0, NULL, NULL),
('9fba2e5d-928c-4e37-8694-3805c6fb0904', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 15, 2025 02:00 PM', '43859c64-f6b6-4d82-962b-22315bcd613b', NULL, '2025-06-15 14:00:00', 50, '4.00', 'completed', '2025-06-15 13:50:09', '2025-07-13 07:06:09', 0, 0, '2025-06-15 14:06:20', NULL),
('a0792c29-ce89-420a-978e-0dbdf502a30c', 40, 41, 'jsjjsna & Mostafa - Jun 12, 2025 10:30 PM', 'c2dc3a7a-97e7-4505-86ed-2af2cba13108', NULL, '2025-06-12 22:30:00', 25, '9.50', 'completed', '2025-06-12 22:08:27', '2025-06-12 22:55:27', 1469, 1478, '2025-06-12 22:55:25', '2025-06-12 22:55:27'),
('a2772b7e-b4cf-455a-b17e-9cc32ec4f92b', 40, 41, 'jsjjsna & Mostafa - Jun 19, 2025 04:30 AM', '2ccf4573-1ad5-4931-92cd-47802cbe8e95', 'hnap-4p5f-zg9w', '2025-06-19 04:30:00', 25, '9.50', 'completed', '2025-06-19 04:25:32', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('a2cde03a-5529-4478-8ab2-64c6109d8592', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 21, 2025 02:30 PM', '03ba43b3-157e-45f8-bd07-09d505664c70', 's921-yeje-em9b', '2025-06-21 14:30:00', 25, '2.00', 'completed', '2025-06-21 14:24:44', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('a610a2c9-2b65-47ca-a744-0975df4135ae', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 05:30 PM', '5f8b1384-1ee2-450b-aa22-0726e6a93809', 'ezac-syyg-am97', '2025-06-18 17:30:00', 25, '9.50', 'completed', '2025-06-18 17:27:12', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('a977ed6f-2579-4be1-b4ba-ff753c5ba5bb', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 20, 2025 07:30 PM', '5c630409-8567-4719-9f22-8e42f5e840de', 'kprx-qs2e-043a', '2025-06-20 19:30:00', 50, '4.00', 'completed', '2025-06-20 16:22:27', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('aa2881b2-c4cc-4bab-b3fa-e581aeaaa543', 40, 41, 'jsjjsna & Mostafa - Jun 24, 2025 07:30 PM', '19d97eac-da9f-4f38-bb8d-4c0c4d7562a7', 'jzuk-1idf-w2lm', '2025-06-24 19:30:00', 25, '9.50', 'cancelled', '2025-06-24 19:29:34', '2025-06-24 19:40:49', 0, 0, NULL, NULL),
('ab53c480-78f0-4996-b0ce-4ee9ef482c13', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 20, 2025 08:30 AM', '2a94bc1e-4b32-4fd9-9f0f-cb7ef3dabeda', '7t7y-ka7b-3c2c', '2025-06-20 08:30:00', 25, '2.00', 'completed', '2025-06-20 08:02:04', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('ada947e7-201a-433e-84e4-fe61bdda99a6', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 28, 2025 08:00 AM', '5a2d28a1-db88-4792-87b2-e6bccf485f58', '7c68-2ue3-dk4s', '2025-06-28 08:00:00', 50, '4.00', 'completed', '2025-06-28 07:57:12', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('ae8904b8-cfbf-440a-b9b7-46d7eca19dff', 30, 31, 'Ahmed & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 13, 2025 07:00 AM', '085256ab-59b2-4733-b312-342b1b4aa678', NULL, '2025-06-13 07:00:00', 50, '4.00', 'completed', '2025-06-13 06:51:58', '2025-06-13 07:50:24', 2667, 2728, '2025-06-13 07:04:46', '2025-06-13 07:50:24'),
('b1b2b20c-2e54-4314-8dbe-22367123d591', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 25, 2025 09:30 PM', 'cd262e44-75e6-4096-b27c-0a752b767750', 'bu4h-krtd-e85f', '2025-07-25 21:30:00', 25, '2.00', 'issue_reported', '2025-07-24 21:23:11', '2025-07-25 21:56:22', 0, 0, NULL, NULL),
('b32c10c6-e429-4504-9b9b-410c80e01a9a', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 08:00 AM', '3b755adc-2e49-4e78-bff2-4ab4cdd907b5', '05qr-pclt-eckj', '2025-06-18 08:00:00', 50, '19.00', 'completed', '2025-06-18 07:54:32', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('b3e8acbe-e520-404d-b1a4-f4eecfe9d9b6', 40, 41, 'jsjjsna & Mostafa - Jul 23, 2025 06:00 PM', 'b7422957-59ce-403d-afbe-c08a88bd098c', 'ezuu-7vzw-9pgr', '2025-07-23 18:00:00', 25, '7.50', 'cancelled', '2025-07-23 17:30:31', '2025-07-23 17:37:41', 0, 0, NULL, NULL),
('b500aba9-e194-494a-880d-861fac03d50f', 40, 41, 'jsjjsna & Mostafa - Jun 15, 2025 04:30 PM', 'e220bd7f-169c-4922-a901-abb2bd64a8f9', NULL, '2025-06-15 16:30:00', 50, '19.00', 'cancelled', '2025-06-15 15:16:26', '2025-06-15 16:03:57', 0, 0, NULL, NULL),
('b55e7765-2955-47fd-bef8-68475bf1c4d5', 40, 41, 'jsjjsna & Mostafa - Jul 26, 2025 12:00 AM', 'a53d85c0-47d9-4094-b859-2f3b58a412b8', '3tqb-el4m-bpsg', '2025-07-26 00:00:00', 25, '7.50', 'completed', '2025-07-25 11:37:29', '2025-07-26 00:25:25', 0, 0, NULL, NULL),
('b654cf20-511d-41eb-b87d-fe2d3d0dccc1', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 24, 2025 09:30 PM', '4ca52102-70cf-44b3-b12b-a4fa5824bc87', 'utp9-fzv6-8z8l', '2025-07-24 21:30:00', 25, '2.00', 'issue_reported', '2025-07-24 21:22:04', '2025-07-25 07:35:49', 0, 0, NULL, NULL),
('b71b366b-078e-4aa6-bf82-b806f739d285', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 22, 2025 08:30 AM', '1d72aec9-84ba-4f2f-9c23-51d98fb8c272', '6yjr-ac8e-o0g1', '2025-06-22 08:30:00', 25, '2.00', 'completed', '2025-06-22 08:00:00', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('b95f238d-834e-42af-81ed-757e15980b8b', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 06:00 AM', '52403f48-34e9-425c-a5c2-865df24634b4', 'e4q4-qx8w-03of', '2025-06-18 06:00:00', 25, '9.50', 'completed', '2025-06-18 05:54:12', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('b9d8aa10-7023-4641-b475-05a2ca73c2c0', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 30, 2025 12:30 PM', 'eb7d003d-81d1-463f-a7ab-6f52e520744d', '8fuw-404o-i4k3', '2025-06-30 12:30:00', 25, '2.00', 'completed', '2025-06-30 12:05:38', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('ba521216-927a-4586-8f81-d270d253cc16', 40, 41, 'jsjjsna & Mostafa - Jul 11, 2025 08:30 PM', '9083212c-2291-4f25-8974-5b1dbfa12721', '7o7j-v2nr-evdc', '2025-07-11 20:30:00', 25, '7.50', 'completed', '2025-07-11 20:20:01', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('bd03252d-ebbe-4363-984b-92a0aa51eeaf', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 28, 2025 09:30 AM', 'a7f3f36b-15b9-434f-811d-4924f196598b', '6mtr-mk1x-xgv9', '2025-06-28 09:30:00', 50, '4.00', 'completed', '2025-06-28 09:26:04', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('c3d08e1f-e464-458b-a516-489c9906df84', 40, 41, 'jsjjsna & Mostafa - Jun 20, 2025 01:30 PM', 'd3fc2cc2-2f53-4173-8a7b-b05562b62313', 'uke1-6rym-es0t', '2025-06-20 13:30:00', 25, '9.50', 'completed', '2025-06-20 13:29:05', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('c42bc100-011a-48a7-a03b-089aacb7ea6d', 40, 41, 'jsjjsna & Mostafa - Jun 22, 2025 08:30 AM', 'b0ef37ce-6fb6-4e7f-96b6-35d023436ba9', 'nhsn-l6se-8e10', '2025-06-22 08:30:00', 25, '9.50', 'completed', '2025-06-22 05:25:58', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('c5c49c19-5563-4f54-ae15-ff7f04a63dda', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 25, 2025 10:30 AM', '4dfee638-c0a5-4814-a650-633e695002f5', '1hks-64xt-5wr5', '2025-06-25 10:30:00', 25, '2.00', 'issue_reported', '2025-06-25 10:28:01', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('c65d7b88-decb-4346-87d3-94bbddece7e0', 40, 41, 'jsjjsna & Mostafa - Jun 12, 2025 11:00 PM', 'c40ddbe8-8971-440d-bbe2-5e20b26e10f0', NULL, '2025-06-12 23:00:00', 50, '19.00', 'completed', '2025-06-12 22:12:29', '2025-06-13 23:26:24', 27, 0, '2025-06-12 23:07:59', '2025-06-12 23:08:07'),
('c6e868f4-e675-4b75-8756-06294db9ca76', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 27, 2025 12:00 PM', '78d673eb-9bf4-4301-98b2-076ea972aeef', 'kcwm-67sr-695j', '2025-07-27 12:00:00', 25, '2.00', 'issue_reported', '2025-07-27 11:49:21', '2025-07-27 12:26:47', 0, 0, NULL, NULL),
('c7350840-4b6c-4ed9-9467-82e208b10dc1', 40, 41, 'jsjjsna & Mostafa - Jul 12, 2025 06:00 PM', '8a4ac903-a32e-4b1c-ba80-bd62274f537b', '79t0-hl95-ofb0', '2025-07-12 17:00:00', 11, '7.50', 'completed', '2025-07-12 17:05:07', '2025-07-16 14:48:56', 0, 0, NULL, NULL),
('c7b14fa3-c43e-493a-a271-a38deabacb3b', 40, 41, 'jsjjsna & Mostafa - Jun 14, 2025 12:30 PM', 'f434ff34-b533-4bc6-94d9-e29e9f053b9e', NULL, '2025-06-14 12:30:00', 25, '9.50', 'completed', '2025-06-14 12:27:01', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('c93449d8-e03f-48da-b221-267425a1d440', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 18, 2025 04:00 PM', '8a54eea7-27dc-4516-8f6f-0ceb2e115406', 'blqw-p6tb-auwh', '2025-06-18 16:00:00', 25, '2.00', 'completed', '2025-06-18 15:51:22', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('caca1ce2-f507-4a9d-b11a-38a47f0e7cd5', 40, 41, 'jsjjsna & Mostafa - Jul 12, 2025 02:00 PM', 'ed13f592-067a-4087-acb6-0ebd1cf341b3', 'fyre-j804-n6tj', '2025-07-12 13:00:00', 37, '7.50', 'completed', '2025-07-12 13:21:23', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('cc11b9a3-0c1a-414a-9cc7-4373898aeaf1', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 20, 2025 08:00 AM', '06f3807e-72e0-479b-b1f8-044660502672', '5tva-t4qv-0hzu', '2025-06-20 08:00:00', 25, '2.00', 'completed', '2025-06-20 07:58:39', '2025-07-19 20:47:14', 0, 0, NULL, NULL),
('ce7e5e16-cacb-414a-ba76-4b71d876a834', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 11:00 AM', 'c3778f2b-ff71-4964-8728-55ff932f0d86', '4xgs-r8jv-zvbm', '2025-06-18 11:00:00', 25, '9.50', 'completed', '2025-06-18 10:58:43', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('cedf1a15-377f-4dea-be8f-0b9493bd1f24', 40, 41, 'jsjjsna & Mostafa - Jun 27, 2025 03:30 PM', 'dae800b2-ce4b-4d17-a8a9-67dc2bcc17dd', 'd7gf-9z6t-4mut', '2025-06-27 15:30:00', 25, '9.50', 'completed', '2025-06-27 15:22:59', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('d2f136e4-1540-4552-9f35-66875d9e55f1', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 21, 2025 09:30 AM', '970d713b-d674-456a-8e51-c886c38d8c31', 'b6rb-6wci-tl6i', '2025-06-21 09:30:00', 25, '2.00', 'completed', '2025-06-21 09:28:25', '2025-07-13 07:06:09', 0, 0, NULL, NULL),
('d75ff244-d16c-4cbc-9984-a7958c2759af', 40, 41, 'jsjjsna & Mostafa - Jun 20, 2025 03:30 PM', 'ed7cfeed-efd8-4d93-800c-a200f8226589', '7bwl-udds-iohf', '2025-06-20 14:30:00', 50, '19.00', 'completed', '2025-06-20 14:42:09', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('d7e4b29d-e3b9-48da-811d-1ebcb5b76a9d', 40, 41, 'jsjjsna & Mostafa - Jun 24, 2025 08:00 PM', 'd66b59e6-5468-4fed-a856-8d4985fd12e5', '2ri6-pp5i-fla5', '2025-06-24 20:00:00', 25, '9.50', 'cancelled', '2025-06-24 19:58:29', '2025-06-24 19:59:02', 0, 0, NULL, NULL),
('d88f5cec-dc8c-4afb-b454-cf7149d1f79e', 40, 41, 'jsjjsna & Mostafa - Jun 16, 2025 10:00 PM', '2aca5a62-359f-4489-99ef-abedf92f23fa', NULL, '2025-06-16 22:30:00', 25, '9.50', 'completed', '2025-06-16 21:54:04', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('da704cf9-067a-4631-8a1c-25a537730954', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 05:00 PM', 'e8301d90-960a-489d-be65-4f96ff530d49', 'cwl9-4m5a-mw2p', '2025-06-18 17:00:00', 25, '9.50', 'completed', '2025-06-18 16:57:14', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('dae4fa6c-59f6-4eae-b4d0-2e1ced2f75d5', 40, 41, 'jsjjsna & Mostafa - Jun 17, 2025 07:00 PM', '37345a1f-a23f-4cdf-83a4-bf2860431bd8', NULL, '2025-06-17 19:00:00', 50, '19.00', 'completed', '2025-06-17 18:54:29', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('dcb3c3ea-d5a6-44c4-91a8-4cb3f76d6925', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 22, 2025 09:00 AM', '7adf1439-11b6-48db-ab93-dd508ef5d522', 's80j-mice-mbnd', '2025-06-22 09:00:00', 25, '2.00', 'issue_reported', '2025-06-22 08:01:46', '2025-07-16 19:31:53', 0, 0, NULL, NULL),
('dcf3b3f5-1de0-4c1f-8c75-b5ceb3b200c7', 40, 41, 'jsjjsna & Mostafa - Jun 12, 2025 02:00 PM', '2ae962ac-072d-4fa1-8287-4290a2e7c886', NULL, '2025-06-12 14:00:00', 25, '9.50', 'completed', '2025-06-12 13:53:03', '2025-06-12 19:52:49', 0, 0, NULL, NULL),
('dda277be-e4a5-4d3c-82c6-eefcbbdf4cad', 40, 41, 'jsjjsna & Mostafa - Jul 23, 2025 08:00 PM', '1d816ffe-26fe-4c60-88ca-db330de7733b', 'o3s7-08s1-1tn6', '2025-07-23 20:00:00', 25, '7.50', 'cancelled', '2025-07-23 17:14:00', '2025-07-23 17:25:32', 0, 0, NULL, NULL),
('dfe9f691-7313-494d-8cfe-48c9b157ce3f', 40, 41, 'jsjjsna & Mostafa - Jun 17, 2025 06:00 PM', '27e621af-249b-409f-ab1a-6759cf30a34b', NULL, '2025-06-17 18:00:00', 50, '19.00', 'completed', '2025-06-17 17:52:56', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('ea9b02a5-4737-4b27-8517-e87ae17f9a21', 40, 41, 'jsjjsna & Mostafa - Jul 23, 2025 06:00 PM', '9f190572-093a-42eb-adb1-7e6d49464221', '5pco-iaq2-89iv', '2025-07-23 18:00:00', 25, '7.50', 'completed', '2025-07-23 17:42:16', '2025-07-24 10:01:15', 0, 0, NULL, NULL),
('eb4f4cde-cb8d-4cb0-b9fe-88ae603baa69', 40, 41, 'jsjjsna & Mostafa - Jun 21, 2025 07:00 AM', 'e5f3e52a-0659-4a65-874c-cc2a89562029', 'lups-w4hq-xyc9', '2025-06-21 07:00:00', 50, '19.00', 'completed', '2025-06-21 06:59:12', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('ef093701-25f9-47b2-8721-48e34499accc', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 22, 2025 08:00 AM', '5bfd5f88-d2df-46e7-8d2e-2b7476638876', 'foe4-6flr-nrtn', '2025-06-22 08:00:00', 25, '2.00', 'cancelled', '2025-06-22 07:57:59', '2025-06-22 07:58:40', 0, 0, NULL, NULL),
('efe935d0-8e7f-4e0c-be69-4f3ab4ad5ef5', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jun 20, 2025 08:00 AM', 'b28cb3b6-0a41-4e7f-9a9b-c7526d6d8775', 'rxsk-r8j2-sx7y', '2025-06-20 08:00:00', 25, '2.00', 'cancelled', '2025-06-20 07:57:33', '2025-06-20 07:58:06', 0, 0, NULL, NULL),
('f08a3759-ad4a-486b-a950-0fa8264e94e5', 40, 41, 'jsjjsna & Mostafa - Jun 16, 2025 03:30 PM', '265655d3-af02-453f-9aad-31540c654091', NULL, '2025-06-16 15:30:00', 50, '19.00', 'completed', '2025-06-14 23:32:36', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('f27bc940-aab6-472f-adba-680484d85b93', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 24, 2025 01:30 PM', 'd3689e2c-af44-44e6-8992-e152b15730db', 'b15o-kag6-n8uk', '2025-07-24 13:30:00', 25, '2.00', 'cancelled', '2025-07-24 13:08:13', '2025-07-24 13:10:10', 0, 0, NULL, NULL),
('f3399194-f8a4-4078-9abc-c7812f475de4', 40, 41, 'jsjjsna & Mostafa - Jun 17, 2025 12:00 PM', '559650f9-cc5e-49d6-9814-c88b32eb299f', NULL, '2025-06-17 12:00:00', 50, '19.00', 'completed', '2025-06-17 11:55:28', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('f5269dbb-2d03-4b42-b388-c542faea5cc7', 40, 41, 'jsjjsna & Mostafa - Jun 15, 2025 01:30 AM', 'c5cef682-382c-4ce4-b38d-362299453dd9', NULL, '2025-06-15 01:30:00', 50, '19.00', 'completed', '2025-06-15 00:36:13', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('f62b9df9-6378-43bd-8c5b-69923e35c46b', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 24, 2025 05:00 PM', '9b0369a9-6aac-4839-9211-60171740b2f0', 'ykj3-89lf-fe6b', '2025-07-24 17:00:00', 50, '4.00', 'completed', '2025-07-24 16:42:37', '2025-07-24 21:19:39', 0, 0, NULL, NULL),
('f90d1674-0518-476f-9fe9-fac668982c1a', 40, 41, 'jsjjsna & Mostafa - Jun 12, 2025 12:30 PM', '8f48a9f1-4770-4fd2-90e7-872b77e8a4c9', NULL, '2025-06-12 12:30:00', 25, '9.50', 'completed', '2025-06-12 12:30:03', '2025-06-12 12:55:38', 0, 0, '2025-06-12 12:32:59', NULL),
('f90f4a84-433e-4b47-a349-ffcb57b3cb6a', 40, 41, 'jsjjsna & Mostafa - Jun 18, 2025 09:00 AM', '99914859-ba41-4ce3-953c-734f5e527344', 'l75j-pj3e-jye6', '2025-06-18 09:00:00', 25, '9.50', 'completed', '2025-06-18 08:54:52', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('fdf2e1c1-5722-4720-a7c6-cf63bd9a6f09', 40, 41, 'jsjjsna & Mostafa - Jun 15, 2025 04:00 PM', 'ce7db6f0-f69b-4b11-9dfb-3c70e5302fef', NULL, '2025-06-15 16:00:00', 25, '9.50', 'completed', '2025-06-15 15:15:52', '2025-07-12 17:05:07', 0, 0, NULL, NULL),
('ff7c5e64-5cc6-490a-8232-bb5db9c08a45', 30, 31, 'تخزين 1 تخزين 1 & د. فؤاد بن أحمد عطاء الله عطاء الله - Jul 26, 2025 12:00 PM', '9f319f0b-b101-4b51-9bf1-8446a7a2c11e', '8f7d-c5uz-cayj', '2025-07-26 12:00:00', 25, '2.00', 'completed', '2025-07-26 11:44:46', '2025-07-26 12:25:40', 0, 0, NULL, NULL);

-- --------------------------------------------------------

--
-- بنية الجدول `meeting_issues`
--

CREATE TABLE `meeting_issues` (
  `id` int NOT NULL,
  `meeting_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `booking_id` int DEFAULT NULL,
  `user_id` int NOT NULL,
  `issue_type` enum('pending','no_issue','teacher_absent','technical_issue','student_attended_no_commission','student_absent') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `description` text COLLATE utf8mb4_general_ci,
  `admin_reply` text COLLATE utf8mb4_general_ci,
  `status` enum('pending','resolved','ignored') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `meeting_issues`
--

INSERT INTO `meeting_issues` (`id`, `meeting_id`, `booking_id`, `user_id`, `issue_type`, `description`, `admin_reply`, `status`, `created_at`, `updated_at`) VALUES
(22, '522107ec-c4fa-4743-9b62-a77a5e5ce233', 172, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:27', '2025-07-12 12:50:31'),
(23, '8418a631-9a3a-4bd8-a997-9b5a0350bb8a', 171, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:31', '2025-07-12 12:50:32'),
(24, '72cb4593-1fdf-4922-8300-6bc1858703db', 170, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:32', '2025-07-12 12:50:33'),
(25, '3ca264de-81a7-4b02-af11-5cb2509921a0', 168, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:33', '2025-07-12 12:50:33'),
(26, '3b0ba30d-2638-4e1e-b052-31f36b83a7dc', 167, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:34', '2025-07-12 12:50:34'),
(27, 'ba521216-927a-4586-8f81-d270d253cc16', 166, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:34', '2025-07-12 12:50:34'),
(28, '6ba7be9e-ac17-4af5-b7a2-e592ad71e1fe', 159, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:35', '2025-07-12 12:50:35'),
(29, 'cedf1a15-377f-4dea-be8f-0b9493bd1f24', 157, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:35', '2025-07-12 12:50:35'),
(30, '7dc757c4-e3e9-44d4-8e52-dd396483e724', 152, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:36', '2025-07-12 12:50:36'),
(31, 'd7e4b29d-e3b9-48da-811d-1ebcb5b76a9d', 150, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:36', '2025-07-12 12:50:37'),
(32, '662a9454-62a3-4e84-beff-65e01ba2a2ce', 150, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:37', '2025-07-12 12:50:37'),
(33, 'aa2881b2-c4cc-4bab-b3fa-e581aeaaa543', 149, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:37', '2025-07-12 12:50:38'),
(34, '5de6d695-b508-4091-9536-d425dc4f3f3e', 140, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:38', '2025-07-12 12:50:38'),
(35, 'c42bc100-011a-48a7-a03b-089aacb7ea6d', 139, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:38', '2025-07-12 12:50:38'),
(36, '0f75bcef-8c71-4b12-962b-03b0548ebfc6', 141, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:39', '2025-07-12 12:50:39'),
(37, '8c96769b-eed4-4e06-9f8c-f77fe0907291', 138, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:39', '2025-07-12 12:50:39'),
(38, '545f793a-9cfd-4038-9c41-8eed63789d85', 137, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:39', '2025-07-12 12:50:39'),
(39, '9be790e2-2dd0-4830-a283-ec6d768fb7c5', 136, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:40', '2025-07-12 12:50:40'),
(40, '29183d80-e9c2-4389-b5bc-b46c608e5bce', 135, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:40', '2025-07-12 12:50:40'),
(41, '8944cc66-fda5-46e5-86e7-53f6c27fe5f8', 134, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:40', '2025-07-12 12:50:41'),
(42, '14dceb0f-2609-49d5-9cda-09eb74f397cb', 129, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:41', '2025-07-12 12:50:41'),
(43, '37040831-41ad-4022-8039-94df77dd66de', 125, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:41', '2025-07-12 12:50:41'),
(44, 'eb4f4cde-cb8d-4cb0-b9fe-88ae603baa69', 124, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:41', '2025-07-12 12:50:42'),
(45, '16cdc33d-739e-419a-846c-9b55437370ca', 123, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:42', '2025-07-12 12:50:42'),
(46, '4d6f7803-3ccc-46f2-806f-8a3e21b0feca', 122, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:42', '2025-07-12 12:50:43'),
(47, '52054c22-0482-4911-8a3b-7f6a4f87df0f', 119, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:43', '2025-07-12 12:50:43'),
(48, '108702b3-a93f-43c5-b438-54909b328e99', 118, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:44', '2025-07-12 12:50:44'),
(49, 'd75ff244-d16c-4cbc-9984-a7958c2759af', 117, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:44', '2025-07-12 12:50:45'),
(50, '44134c96-97d9-43e6-80cf-2584c8a18f9f', 116, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:45', '2025-07-12 12:50:46'),
(51, 'c3d08e1f-e464-458b-a516-489c9906df84', 115, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:46', '2025-07-12 12:50:46'),
(52, '23ad5d51-88af-4570-9213-47912cb6e775', 114, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:47', '2025-07-12 12:50:47'),
(53, 'a2772b7e-b4cf-455a-b17e-9cc32ec4f92b', 107, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:47', '2025-07-12 12:50:47'),
(54, '08dc5909-7ac0-4249-8bb9-790c66a4bb80', 106, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:48', '2025-07-12 12:50:48'),
(55, 'a610a2c9-2b65-47ca-a744-0975df4135ae', 105, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:48', '2025-07-12 12:50:48'),
(56, 'da704cf9-067a-4631-8a1c-25a537730954', 104, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:48', '2025-07-12 12:50:49'),
(57, '91a5df98-2b25-4b24-8636-99755e6e609e', 101, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:49', '2025-07-12 12:50:49'),
(58, '43938612-b0cd-4dff-bb24-5bc18141213d', 100, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:49', '2025-07-12 12:50:49'),
(59, '1cbd806c-247f-48b7-9d5c-195cf90434ce', 98, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:49', '2025-07-12 12:50:50'),
(60, '039f8187-3107-4fad-980e-a91e5d3e5b3e', 96, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:50', '2025-07-12 12:50:50'),
(61, 'ce7e5e16-cacb-414a-ba76-4b71d876a834', 95, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:50', '2025-07-12 12:50:51'),
(62, '20f3a82c-79f3-4611-b621-8d8213de05d7', 92, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:51', '2025-07-12 12:50:51'),
(63, '1a6128bb-573e-4033-a492-f05d1ca6f00e', 91, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:51', '2025-07-12 12:50:52'),
(64, 'f90f4a84-433e-4b47-a349-ffcb57b3cb6a', 90, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:52', '2025-07-12 12:50:52'),
(65, 'b32c10c6-e429-4504-9b9b-410c80e01a9a', 89, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:52', '2025-07-12 12:50:52'),
(66, '3c44872d-c79d-4f55-b300-23c6b288d2fe', 87, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:53', '2025-07-12 12:50:53'),
(67, '3630ed72-5f33-49d5-b9c2-9d8ac73f28a3', 86, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:53', '2025-07-12 12:50:54'),
(68, 'b95f238d-834e-42af-81ed-757e15980b8b', 85, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:54', '2025-07-12 12:50:54'),
(69, '0be242e2-b90c-4dac-b0d7-0bfd77e20943', 84, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:54', '2025-07-12 12:50:55'),
(70, '84ddd16e-8918-4371-9c54-bb6a30cc4be0', 83, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:55', '2025-07-12 12:50:55'),
(71, '1813b300-e5e1-40cc-9ab2-49c914815b74', 79, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:56', '2025-07-12 12:50:56'),
(72, 'dae4fa6c-59f6-4eae-b4d0-2e1ced2f75d5', 78, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:56', '2025-07-12 12:50:56'),
(73, 'dfe9f691-7313-494d-8cfe-48c9b157ce3f', 77, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:57', '2025-07-12 12:50:57'),
(74, '5276f7a8-e8f2-42c9-b25e-329c41ed9687', 76, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:57', '2025-07-12 12:50:58'),
(75, '25f291f9-4d2f-4b10-b3ca-b30dc380d347', 75, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:58', '2025-07-12 12:50:58'),
(76, '26721faf-b560-47f2-88cb-a3b39c5fd9e5', 74, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:50:58', '2025-07-12 12:51:00'),
(77, '994ec425-1962-4995-829c-0ccf81b895cb', 73, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:00', '2025-07-12 12:51:00'),
(78, '780baa66-d436-4292-9237-5605c6006550', 72, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:00', '2025-07-12 12:51:01'),
(79, 'f3399194-f8a4-4078-9abc-c7812f475de4', 71, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:01', '2025-07-12 12:51:02'),
(80, '4246d516-7b72-4e4a-ace2-789eb2802d90', 70, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:02', '2025-07-12 12:51:02'),
(81, '31267967-4f12-4a02-8899-8cc4a1541a15', 67, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:02', '2025-07-12 12:51:03'),
(82, '7339bc3c-58fb-45bf-91fc-ab7bfb4681df', 68, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:03', '2025-07-12 12:51:03'),
(83, 'd88f5cec-dc8c-4afb-b454-cf7149d1f79e', 66, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:04', '2025-07-12 12:51:04'),
(84, '46a43f67-51db-44c4-a73d-e973637ccda4', 65, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:04', '2025-07-12 12:51:05'),
(85, 'f08a3759-ad4a-486b-a950-0fa8264e94e5', 55, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:05', '2025-07-12 12:51:05'),
(86, 'b500aba9-e194-494a-880d-861fac03d50f', 63, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:05', '2025-07-12 12:51:06'),
(87, 'fdf2e1c1-5722-4720-a7c6-cf63bd9a6f09', 62, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:06', '2025-07-12 12:51:06'),
(88, '7e1cc25c-ee44-4603-9a2a-e08e2e655be4', 59, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:06', '2025-07-12 12:51:07'),
(89, '31145510-e6c4-4861-9f0a-f64fc92b3c8f', 58, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:07', '2025-07-12 12:51:08'),
(90, '5bd0d3b6-e4d0-4b69-87b7-eb320d348dc4', 57, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:08', '2025-07-12 12:51:08'),
(91, 'f5269dbb-2d03-4b42-b388-c542faea5cc7', 56, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:08', '2025-07-12 12:51:09'),
(92, '17aa90bb-e02f-4ac9-b016-742f30ab39b4', 54, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:09', '2025-07-12 12:51:10'),
(93, '734d4c2f-fb56-4540-b7a3-04fd604c9f96', 53, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:10', '2025-07-12 12:51:10'),
(94, '5f958b08-31be-4761-a24c-015ada1d7ced', 52, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:10', '2025-07-12 12:51:11'),
(95, '248f8e5c-50fc-4384-923b-e7438406a9c6', 51, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:11', '2025-07-12 12:51:11'),
(96, 'c7b14fa3-c43e-493a-a271-a38deabacb3b', 49, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:12', '2025-07-12 12:51:12'),
(97, '73f9635c-93d7-4087-90f2-cda60060491e', 44, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:12', '2025-07-12 12:51:13'),
(98, '2f5fd66c-f259-4ece-ba96-c2181ef4c6e6', 43, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:13', '2025-07-12 12:51:13'),
(99, 'c65d7b88-decb-4346-87d3-94bbddece7e0', 39, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:13', '2025-07-12 12:51:14'),
(100, 'a0792c29-ce89-420a-978e-0dbdf502a30c', 38, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:14', '2025-07-12 12:51:14'),
(101, '4b8cdd6c-a11f-41ec-8ec0-216a75892a86', 37, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:14', '2025-07-12 12:51:15'),
(102, '7a5b27cb-7716-414f-bfa9-21d39ae392bd', 36, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:15', '2025-07-12 12:51:16'),
(103, '82646c83-4adc-47b7-a17a-a2d1506d8770', 31, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:16', '2025-07-12 12:51:16'),
(104, 'dcf3b3f5-1de0-4c1f-8c75-b5ceb3b200c7', 33, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:16', '2025-07-12 12:51:17'),
(105, 'f90d1674-0518-476f-9fe9-fac668982c1a', 32, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:17', '2025-07-12 12:51:18'),
(106, '71922ae9-d073-4914-8399-ff23ac2098b1', 29, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:18', '2025-07-12 12:51:19'),
(107, '70cd4620-726f-46c1-8a07-6fbc4ecc8c0d', 28, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 12:51:19', '2025-07-12 12:51:19'),
(108, '15789fbb-21a1-4c8b-a04f-e29e40aa6480', 173, 41, 'teacher_absent', 'نةنم', 'اهلا نننن ننن0000', 'resolved', '2025-07-12 12:53:25', '2025-07-16 11:44:23'),
(109, 'caca1ce2-f507-4a9d-b11a-38a47f0e7cd5', 174, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 13:22:24', '2025-07-12 13:54:52'),
(110, '103ae59d-3b49-489f-b450-6b41f66e4abe', 175, 41, 'no_issue', '', NULL, 'resolved', '2025-07-12 13:55:21', '2025-07-12 14:00:56'),
(118, '6fc89b65-68c2-4384-84c9-58811022d3d7', 177, 41, 'teacher_absent', '', NULL, 'pending', '2025-07-12 15:39:42', '2025-07-12 15:39:46'),
(121, 'c7350840-4b6c-4ed9-9467-82e208b10dc1', 178, 41, 'teacher_absent', 'نةءنمةمءؤ', NULL, 'resolved', '2025-07-12 17:10:54', '2025-07-12 18:08:46'),
(122, '121811da-ec9f-412c-8402-985c0b74194b', 163, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:03:46', '2025-07-13 07:04:06'),
(123, 'b9d8aa10-7023-4641-b475-05a2ca73c2c0', 162, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:06', '2025-07-13 07:04:08'),
(124, 'bd03252d-ebbe-4363-984b-92a0aa51eeaf', 161, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:08', '2025-07-13 07:04:10'),
(125, 'ada947e7-201a-433e-84e4-fe61bdda99a6', 160, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:10', '2025-07-13 07:04:11'),
(126, '95e96f52-0562-4c3d-a868-aaef9b1f1ff5', 158, 31, 'teacher_absent', '', NULL, 'pending', '2025-07-13 07:04:11', '2025-07-13 07:04:16'),
(127, '794bca08-6b9b-4ba8-a29a-af1e43e2cf3a', 156, 31, 'teacher_absent', '', NULL, 'pending', '2025-07-13 07:04:16', '2025-07-13 07:04:19'),
(128, '85ca85a0-3625-4852-959e-7040a63f2920', 155, 31, 'technical_issue', '', NULL, 'pending', '2025-07-13 07:04:19', '2025-07-13 07:04:23'),
(129, '7374bdbd-4b85-4497-82f5-a57a01a94821', 154, 31, 'technical_issue', '', NULL, 'pending', '2025-07-13 07:04:23', '2025-07-13 07:04:24'),
(130, 'c5c49c19-5563-4f54-ae15-ff7f04a63dda', 153, 31, 'technical_issue', '', NULL, 'pending', '2025-07-13 07:04:24', '2025-07-13 07:04:25'),
(131, '6b9b2cf5-28a6-479d-94e8-3bc32c01f782', NULL, 31, 'technical_issue', '', NULL, 'pending', '2025-07-13 07:04:25', '2025-07-13 07:04:26'),
(132, '9cc9a29b-4ac1-4b31-9c55-2efaba3814d8', NULL, 31, 'technical_issue', '', NULL, 'pending', '2025-07-13 07:04:26', '2025-07-13 07:04:27'),
(133, '5321d012-2f9e-43ee-a9bf-4f8f65a4df99', 130, 31, 'technical_issue', '', NULL, 'pending', '2025-07-13 07:04:27', '2025-07-13 07:04:27'),
(134, 'dcb3c3ea-d5a6-44c4-91a8-4cb3f76d6925', 146, 31, 'technical_issue', '', NULL, 'pending', '2025-07-13 07:04:28', '2025-07-13 07:04:28'),
(135, '25a5bd96-f2c4-403d-b35b-6ae20ee8d2af', 143, 31, 'technical_issue', '', NULL, 'pending', '2025-07-13 07:04:29', '2025-07-13 07:04:29'),
(136, 'b71b366b-078e-4aa6-bf82-b806f739d285', 143, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:29', '2025-07-13 07:04:31'),
(137, 'ef093701-25f9-47b2-8721-48e34499accc', 142, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:32', '2025-07-13 07:04:32'),
(138, '38043bf3-1bcc-4533-ab7c-ad11af6c1ea6', 142, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:33', '2025-07-13 07:04:33'),
(139, '1bb68a82-9213-47aa-ab4e-fb854a042054', 128, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:34', '2025-07-13 07:04:34'),
(140, '34f38403-0f0d-4d99-b0c3-a46b28d4dea7', 133, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:34', '2025-07-13 07:04:35'),
(141, 'a2cde03a-5529-4478-8ab2-64c6109d8592', 132, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:35', '2025-07-13 07:04:36'),
(142, '27ea4b74-63ba-4857-a259-ffe8d221289e', 131, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:37', '2025-07-13 07:04:37'),
(143, '8aed7e78-b10b-4838-b980-0d7862ca4383', 127, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:38', '2025-07-13 07:04:39'),
(144, 'd2f136e4-1540-4552-9f35-66875d9e55f1', 126, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:40', '2025-07-13 07:04:41'),
(145, '6003fd27-733f-4a87-bf2a-f36c62601678', 120, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:41', '2025-07-13 07:04:51'),
(146, 'a977ed6f-2579-4be1-b4ba-ff753c5ba5bb', 121, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:52', '2025-07-13 07:04:53'),
(147, 'ab53c480-78f0-4996-b0ce-4ee9ef482c13', 113, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:53', '2025-07-13 07:04:55'),
(148, 'efe935d0-8e7f-4e0c-be69-4f3ab4ad5ef5', 111, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:55', '2025-07-13 07:04:56'),
(149, 'cc11b9a3-0c1a-414a-9cc7-4373898aeaf1', 111, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:56', '2025-07-13 07:04:57'),
(150, '82b9f8bf-968a-41b2-ac03-4ccb8c9d68fa', 110, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:57', '2025-07-13 07:04:58'),
(151, '47079ab5-ba7c-4369-a488-42cbc07d6909', 109, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:58', '2025-07-13 07:04:59'),
(152, '9cbeb49d-7ba7-4b09-b9a8-ae2c065ff93e', 108, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:59', '2025-07-13 07:04:59'),
(153, 'c93449d8-e03f-48da-b221-267425a1d440', 103, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:04:59', '2025-07-13 07:05:00'),
(154, 'c93449d8-e03f-48da-b221-267425a1d440', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:00', '2025-07-13 07:05:00'),
(155, '59e37bbe-230f-417b-9a78-04f09a487c3e', 102, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:00', '2025-07-13 07:05:00'),
(156, '762ddc0b-f71d-45f5-ab36-e2a4ad460369', 99, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:01', '2025-07-13 07:05:01'),
(157, '762ddc0b-f71d-45f5-ab36-e2a4ad460369', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:01', '2025-07-13 07:05:01'),
(158, '998d0cda-185b-4897-8622-8ef2b09410ac', 97, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:01', '2025-07-13 07:05:02'),
(159, '0cde89f9-4d1f-465a-8053-2a015d4b6f03', 94, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:02', '2025-07-13 07:05:02'),
(160, '0cde89f9-4d1f-465a-8053-2a015d4b6f03', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:03', '2025-07-13 07:05:03'),
(161, '7e28fe4b-5fd3-4d35-975e-57aedb69f969', 93, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:03', '2025-07-13 07:05:03'),
(162, '7e28fe4b-5fd3-4d35-975e-57aedb69f969', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:03', '2025-07-13 07:05:03'),
(163, '6ee93a9c-0d2c-4f80-8db1-67943645124a', 88, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:03', '2025-07-13 07:05:04'),
(164, '6ee93a9c-0d2c-4f80-8db1-67943645124a', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:04', '2025-07-13 07:05:04'),
(165, '7ded9eaf-84ee-46ff-aa04-2b96f606a185', 69, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:04', '2025-07-13 07:05:04'),
(166, '7ded9eaf-84ee-46ff-aa04-2b96f606a185', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:04', '2025-07-13 07:05:04'),
(167, '7efe8719-6645-4f19-8dae-1640eca9ed98', 64, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:04', '2025-07-13 07:05:05'),
(168, '3b6e9223-34e9-429a-8da9-dd0cbc75e171', 60, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:05', '2025-07-13 07:05:05'),
(169, '9fba2e5d-928c-4e37-8694-3805c6fb0904', 61, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:06', '2025-07-13 07:05:06'),
(170, '587d69e0-9c27-40ff-8ca8-cf0698de5185', 46, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:06', '2025-07-13 07:05:07'),
(171, '587d69e0-9c27-40ff-8ca8-cf0698de5185', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:07', '2025-07-13 07:05:07'),
(172, '3be590b6-51b7-4776-acf1-66e88448bb46', 45, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:07', '2025-07-13 07:05:07'),
(173, '698976df-4e65-40af-956e-ec5e8ed17eaf', 50, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:07', '2025-07-13 07:05:08'),
(174, '698976df-4e65-40af-956e-ec5e8ed17eaf', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:08', '2025-07-13 07:05:08'),
(175, '99b08d5c-d2ca-4c9a-a20a-a14f25eac13d', 47, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:08', '2025-07-13 07:05:08'),
(176, '65aed109-ee0d-4b3e-b104-0388104ab9f1', 47, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:09', '2025-07-13 07:05:09'),
(177, '65aed109-ee0d-4b3e-b104-0388104ab9f1', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:09', '2025-07-13 07:05:09'),
(178, '58e8f593-1673-4445-a151-e953f692e753', 42, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:09', '2025-07-13 07:05:10'),
(179, '58e8f593-1673-4445-a151-e953f692e753', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:10', '2025-07-13 07:05:10'),
(180, '3c372c51-fa52-4819-b9d9-d92397df0d10', 41, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:10', '2025-07-13 07:05:10'),
(181, 'ae8904b8-cfbf-440a-b9b7-46d7eca19dff', 40, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:11', '2025-07-13 07:05:11'),
(182, '96a7c19f-1bee-4996-94b8-060ea86639b7', 35, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:11', '2025-07-13 07:05:15'),
(183, '8e8bdf00-9cf5-4cb1-a70e-2cbee7742d78', 34, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:15', '2025-07-13 07:05:15'),
(184, '0e6cc984-b2e0-4250-b1f5-cae35731323b', 30, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:15', '2025-07-13 07:05:16'),
(185, '0e6cc984-b2e0-4250-b1f5-cae35731323b', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 07:05:16', '2025-07-13 07:05:16'),
(186, '893bd2bd-94be-4ed2-a3ac-44546ea9d9a4', 179, 31, 'no_issue', '', NULL, 'resolved', '2025-07-13 15:24:44', '2025-07-13 15:24:50'),
(187, '651797e3-0b83-4e0f-9cb4-bb5e79f47f20', 180, 31, 'teacher_absent', '', NULL, 'resolved', '2025-07-13 15:30:41', '2025-07-13 16:02:33'),
(188, '370e06fb-7e32-4b5c-85eb-e72882413ba1', 181, 41, 'no_issue', '', 'ةنةمن', 'resolved', '2025-07-16 14:51:05', '2025-07-19 19:01:12'),
(189, '65aed109-ee0d-4b3e-b104-0388104ab9f1', 48, 31, 'no_issue', '', NULL, 'resolved', '2025-07-16 19:31:53', '2025-07-19 20:46:52'),
(190, 'cc11b9a3-0c1a-414a-9cc7-4373898aeaf1', 112, 31, 'no_issue', '', NULL, 'resolved', '2025-07-16 19:31:53', '2025-07-19 20:46:53'),
(191, '38043bf3-1bcc-4533-ab7c-ad11af6c1ea6', 144, 31, 'no_issue', '', NULL, 'resolved', '2025-07-16 19:31:53', '2025-07-19 20:46:50'),
(192, '25a5bd96-f2c4-403d-b35b-6ae20ee8d2af', 145, 31, 'no_issue', '', NULL, 'resolved', '2025-07-16 19:31:53', '2025-07-19 20:46:54'),
(193, '662a9454-62a3-4e84-beff-65e01ba2a2ce', 151, 41, 'no_issue', '', NULL, 'resolved', '2025-07-16 19:31:53', '2025-07-19 19:01:13'),
(194, '359822c2-8136-4cd8-80e6-2d0f06cf4cc9', 165, 75, 'pending', '', NULL, 'pending', '2025-07-16 19:31:53', '2025-07-16 19:31:53'),
(196, '370e06fb-7e32-4b5c-85eb-e72882413ba1', 181, 40, 'student_attended_no_commission', 'ةىتتن', 'njxj9999', 'resolved', '2025-07-16 20:27:23', '2025-07-19 19:32:07'),
(197, '370e06fb-7e32-4b5c-85eb-e72882413ba1', 181, 41, 'no_issue', '', 'jjknsjk ', 'resolved', '2025-07-16 20:49:11', '2025-07-19 19:01:14'),
(198, '370e06fb-7e32-4b5c-85eb-e72882413ba1', 181, 41, 'no_issue', '', NULL, 'resolved', '2025-07-19 19:00:15', '2025-07-19 19:01:16'),
(199, '662a9454-62a3-4e84-beff-65e01ba2a2ce', 151, 41, 'no_issue', '', NULL, 'resolved', '2025-07-19 19:01:15', '2025-07-19 19:01:17'),
(200, '39d6ca17-3995-44eb-9403-74b97fda644f', 182, 31, 'teacher_absent', '', 'نفيدكم بأنه تم التأكد من غياب المدرس، وسيتم إرجاع الدرس لرصيدكم، شكرا على تفهمكم.', 'resolved', '2025-07-19 20:51:01', '2025-07-19 21:09:57'),
(201, '39d6ca17-3995-44eb-9403-74b97fda644f', 182, 31, 'technical_issue', '', NULL, 'pending', '2025-07-19 21:10:01', '2025-07-20 08:00:00'),
(202, '044c35c1-cbae-47f9-ac54-e7722091d55c', 183, 31, 'teacher_absent', '', NULL, 'pending', '2025-07-20 08:55:14', '2025-07-23 13:35:58'),
(203, '044c35c1-cbae-47f9-ac54-e7722091d55c', 183, 30, 'student_absent', '', 'تم حل المشكلة', 'resolved', '2025-07-20 09:20:35', '2025-07-20 09:22:40'),
(204, '39d6ca17-3995-44eb-9403-74b97fda644f', 182, 30, 'student_absent', '', NULL, 'pending', '2025-07-20 09:20:49', '2025-07-20 09:20:49'),
(205, '044c35c1-cbae-47f9-ac54-e7722091d55c', 183, 30, 'student_absent', '', NULL, 'pending', '2025-07-20 09:20:57', '2025-07-20 09:20:57'),
(206, '39d6ca17-3995-44eb-9403-74b97fda644f', 182, 30, 'student_absent', '', NULL, 'pending', '2025-07-20 09:21:03', '2025-07-20 09:21:03'),
(207, '15effeee-a1b4-4a80-975b-1a236e96eaf8', 187, 41, 'no_issue', '', NULL, 'resolved', '2025-07-23 17:13:41', '2025-07-23 17:25:55'),
(208, '15effeee-a1b4-4a80-975b-1a236e96eaf8', 188, 41, 'no_issue', '', NULL, 'resolved', '2025-07-23 17:25:27', '2025-07-23 17:25:57'),
(209, '854606f3-36bd-40c5-be40-456b439c9f7a', 187, 41, 'no_issue', '', NULL, 'resolved', '2025-07-23 17:25:57', '2025-07-23 17:26:00'),
(210, '32b7ee7c-b4f5-433c-a2e1-4eca3dbf50c7', 190, 41, 'no_issue', '', NULL, 'resolved', '2025-07-23 17:30:23', '2025-07-23 18:04:40'),
(211, 'ea9b02a5-4737-4b27-8517-e87ae17f9a21', 191, 41, 'no_issue', '', NULL, 'resolved', '2025-07-23 18:04:41', '2025-07-24 10:00:52'),
(212, '2e88e480-4348-43c3-9d2c-dc665da0c5dd', 193, 41, 'no_issue', '', NULL, 'resolved', '2025-07-23 18:25:07', '2025-07-24 10:00:54'),
(213, 'dda277be-e4a5-4d3c-82c6-eefcbbdf4cad', 189, 41, 'no_issue', '', NULL, 'resolved', '2025-07-24 10:00:54', '2025-07-24 10:00:55'),
(214, 'b3e8acbe-e520-404d-b1a4-f4eecfe9d9b6', 191, 41, 'no_issue', '', NULL, 'resolved', '2025-07-24 10:00:56', '2025-07-24 10:00:56'),
(215, '2e88e480-4348-43c3-9d2c-dc665da0c5dd', 193, 41, 'no_issue', '', NULL, 'resolved', '2025-07-24 10:01:05', '2025-07-24 10:53:17'),
(216, '216ead0a-3914-4407-a075-283674655c07', 199, 31, 'no_issue', '', NULL, 'resolved', '2025-07-24 13:55:48', '2025-07-24 16:42:18'),
(217, 'f27bc940-aab6-472f-adba-680484d85b93', 197, 31, 'no_issue', '', NULL, 'resolved', '2025-07-24 16:42:18', '2025-07-24 16:42:19'),
(218, '32835e48-33a3-4b8c-9d2a-3974d73141e9', 197, 31, 'no_issue', '', NULL, 'resolved', '2025-07-24 16:42:19', '2025-07-24 16:42:20'),
(219, 'f62b9df9-6378-43bd-8c5b-69923e35c46b', 200, 31, 'no_issue', '', NULL, 'resolved', '2025-07-24 17:21:05', '2025-07-24 21:19:21'),
(220, '344ba562-3339-4421-b5ec-546184cdc4fa', 201, 41, 'no_issue', '', NULL, 'resolved', '2025-07-24 21:06:54', '2025-07-24 21:33:03'),
(221, '9f812ef2-8543-4773-a6d3-72e311aa335d', 203, 31, 'technical_issue', '', NULL, 'pending', '2025-07-25 07:35:34', '2025-07-25 07:35:41'),
(222, 'b654cf20-511d-41eb-b87d-fe2d3d0dccc1', 203, 31, 'teacher_absent', '', NULL, 'pending', '2025-07-25 07:35:41', '2025-07-25 07:35:45'),
(223, '1719abf1-c143-4f2c-b9da-325fa5594a3f', 206, 31, 'teacher_absent', '', NULL, 'pending', '2025-07-25 11:55:46', '2025-07-26 06:03:50'),
(224, '45010fcf-39c6-44f0-a1f4-8f0475b02937', 202, 41, 'no_issue', '', NULL, 'resolved', '2025-07-25 15:05:27', '2025-07-25 15:46:10'),
(225, '892f8bcc-0370-4c34-8cfa-18d6ae910b37', 202, 41, 'no_issue', '', NULL, 'resolved', '2025-07-25 19:05:29', '2025-07-25 19:41:06'),
(226, 'b1b2b20c-2e54-4314-8dbe-22367123d591', 205, 31, 'teacher_absent', '', NULL, 'pending', '2025-07-25 21:55:22', '2025-07-26 06:03:51'),
(227, '145f280f-b839-48e8-93c4-4d05eedfc37a', 208, 41, 'no_issue', '', NULL, 'resolved', '2025-07-26 18:25:47', '2025-07-27 02:01:23'),
(228, 'ff7c5e64-5cc6-490a-8232-bb5db9c08a45', NULL, 31, 'no_issue', '', NULL, 'resolved', '2025-07-26 19:52:18', '2025-07-26 19:52:20'),
(229, '380c4080-677e-482f-9515-3d76289fea36', 208, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 02:01:23', '2025-07-27 02:01:25'),
(230, '3402c1e3-57e2-4723-b43e-03b72c53f119', 196, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 02:01:25', '2025-07-27 02:01:27'),
(231, 'b55e7765-2955-47fd-bef8-68475bf1c4d5', 196, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 02:01:27', '2025-07-27 02:01:28'),
(232, '145f280f-b839-48e8-93c4-4d05eedfc37a', 208, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 02:01:56', '2025-07-27 02:09:23'),
(233, '145f280f-b839-48e8-93c4-4d05eedfc37a', 208, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 02:09:57', '2025-07-27 09:56:38'),
(234, '145f280f-b839-48e8-93c4-4d05eedfc37a', 208, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 09:57:06', '2025-07-27 09:57:31'),
(235, '145f280f-b839-48e8-93c4-4d05eedfc37a', 208, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 09:58:06', '2025-07-27 10:01:46'),
(236, '145f280f-b839-48e8-93c4-4d05eedfc37a', 208, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 10:02:06', '2025-07-27 10:05:46'),
(237, '145f280f-b839-48e8-93c4-4d05eedfc37a', 208, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 10:06:06', '2025-07-27 10:09:45'),
(238, '145f280f-b839-48e8-93c4-4d05eedfc37a', NULL, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 10:09:48', '2025-07-27 10:09:48'),
(239, '49840017-e843-41c6-ae56-3ae0051d5f30', 210, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 10:13:51', '2025-07-27 10:28:13'),
(240, '49840017-e843-41c6-ae56-3ae0051d5f30', 210, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 10:29:06', '2025-07-27 10:34:13'),
(241, '49840017-e843-41c6-ae56-3ae0051d5f30', 210, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 10:34:18', '2025-07-27 10:34:18'),
(242, '49840017-e843-41c6-ae56-3ae0051d5f30', 210, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 10:56:00', '2025-07-27 11:16:57'),
(243, '49840017-e843-41c6-ae56-3ae0051d5f30', NULL, 41, 'no_issue', '', NULL, 'resolved', '2025-07-27 11:17:02', '2025-07-27 11:17:02'),
(244, 'c6e868f4-e675-4b75-8756-06294db9ca76', 211, 31, 'no_issue', '', NULL, 'resolved', '2025-07-27 12:25:47', '2025-07-27 16:04:43'),
(245, 'c6e868f4-e675-4b75-8756-06294db9ca76', 211, 31, 'no_issue', '', NULL, 'resolved', '2025-07-27 16:05:35', '2025-07-27 16:10:09'),
(246, 'c6e868f4-e675-4b75-8756-06294db9ca76', 211, 31, 'pending', '', NULL, 'pending', '2025-07-27 16:10:35', '2025-07-27 16:10:35');

-- --------------------------------------------------------

--
-- بنية الجدول `meeting_sessions`
--

CREATE TABLE `meeting_sessions` (
  `id` int NOT NULL,
  `meeting_id` varchar(36) COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` int NOT NULL,
  `user_role` enum('teacher','student') COLLATE utf8mb4_general_ci NOT NULL,
  `join_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `leave_time` timestamp NULL DEFAULT NULL,
  `duration_seconds` int DEFAULT NULL,
  `session_status` enum('active','ended') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `meeting_sessions`
--

INSERT INTO `meeting_sessions` (`id`, `meeting_id`, `user_id`, `user_role`, `join_time`, `leave_time`, `duration_seconds`, `session_status`, `created_at`, `updated_at`) VALUES
(5, '70cd4620-726f-46c1-8a07-6fbc4ecc8c0d', 40, 'teacher', '2025-06-11 21:53:11', '2025-06-11 21:53:16', 5, 'ended', '2025-06-11 21:53:11', '2025-06-11 21:53:16'),
(6, '70cd4620-726f-46c1-8a07-6fbc4ecc8c0d', 41, 'student', '2025-06-11 22:00:07', '2025-06-11 22:00:47', 40, 'ended', '2025-06-11 22:00:07', '2025-06-11 22:00:47'),
(7, '0e6cc984-b2e0-4250-b1f5-cae35731323b', 30, 'teacher', '2025-06-12 09:56:07', '2025-06-12 10:22:13', 1566, 'ended', '2025-06-12 09:56:07', '2025-06-12 10:22:13'),
(8, 'f90d1674-0518-476f-9fe9-fac668982c1a', 40, 'teacher', '2025-06-12 12:32:59', NULL, NULL, 'active', '2025-06-12 12:32:59', '2025-06-12 12:32:59'),
(9, '7a5b27cb-7716-414f-bfa9-21d39ae392bd', 40, 'teacher', '2025-06-12 20:38:56', '2025-06-12 20:39:24', 28, 'ended', '2025-06-12 20:38:56', '2025-06-12 20:39:24'),
(10, '7a5b27cb-7716-414f-bfa9-21d39ae392bd', 40, 'teacher', '2025-06-12 21:14:59', '2025-06-12 21:34:05', 1146, 'ended', '2025-06-12 21:14:59', '2025-06-12 21:34:05'),
(11, '4b8cdd6c-a11f-41ec-8ec0-216a75892a86', 40, 'teacher', '2025-06-12 21:34:10', NULL, NULL, 'active', '2025-06-12 21:34:10', '2025-06-12 21:34:10'),
(12, '4b8cdd6c-a11f-41ec-8ec0-216a75892a86', 41, 'student', '2025-06-12 21:43:35', '2025-06-12 21:55:04', 689, 'ended', '2025-06-12 21:43:35', '2025-06-12 21:55:04'),
(13, '4b8cdd6c-a11f-41ec-8ec0-216a75892a86', 41, 'student', '2025-06-12 21:55:10', '2025-06-12 21:55:15', 5, 'ended', '2025-06-12 21:55:10', '2025-06-12 21:55:15'),
(14, '4b8cdd6c-a11f-41ec-8ec0-216a75892a86', 41, 'student', '2025-06-12 21:56:06', '2025-06-12 21:56:10', 4, 'ended', '2025-06-12 21:56:06', '2025-06-12 21:56:10'),
(15, 'a0792c29-ce89-420a-978e-0dbdf502a30c', 41, 'student', '2025-06-12 22:30:28', '2025-06-12 22:55:04', 1476, 'ended', '2025-06-12 22:30:28', '2025-06-12 22:55:04'),
(16, 'a0792c29-ce89-420a-978e-0dbdf502a30c', 40, 'teacher', '2025-06-12 22:30:41', '2025-06-12 22:55:10', 1469, 'ended', '2025-06-12 22:30:41', '2025-06-12 22:55:10'),
(17, 'a0792c29-ce89-420a-978e-0dbdf502a30c', 41, 'student', '2025-06-12 22:55:25', '2025-06-12 22:55:27', 2, 'ended', '2025-06-12 22:55:25', '2025-06-12 22:55:27'),
(18, 'c65d7b88-decb-4346-87d3-94bbddece7e0', 40, 'teacher', '2025-06-12 23:07:33', '2025-06-12 23:07:52', 19, 'ended', '2025-06-12 23:07:33', '2025-06-12 23:07:52'),
(19, 'c65d7b88-decb-4346-87d3-94bbddece7e0', 40, 'teacher', '2025-06-12 23:07:59', '2025-06-12 23:08:07', 8, 'ended', '2025-06-12 23:07:59', '2025-06-12 23:08:07'),
(20, 'ae8904b8-cfbf-440a-b9b7-46d7eca19dff', 31, 'student', '2025-06-13 07:04:46', '2025-06-13 07:50:14', 2728, 'ended', '2025-06-13 07:04:46', '2025-06-13 07:50:14'),
(21, 'ae8904b8-cfbf-440a-b9b7-46d7eca19dff', 30, 'teacher', '2025-06-13 07:05:57', '2025-06-13 07:50:24', 2667, 'ended', '2025-06-13 07:05:57', '2025-06-13 07:50:24'),
(22, '3c372c51-fa52-4819-b9d9-d92397df0d10', 30, 'teacher', '2025-06-13 19:31:47', '2025-06-13 19:55:10', 1403, 'ended', '2025-06-13 19:31:47', '2025-06-13 19:55:10'),
(23, '3c372c51-fa52-4819-b9d9-d92397df0d10', 31, 'student', '2025-06-13 19:33:39', '2025-06-13 19:55:03', 1284, 'ended', '2025-06-13 19:33:39', '2025-06-13 19:55:03'),
(24, '2f5fd66c-f259-4ece-ba96-c2181ef4c6e6', 41, 'student', '2025-06-13 21:01:11', '2025-06-13 21:02:29', 78, 'ended', '2025-06-13 21:01:11', '2025-06-13 21:02:29'),
(25, '2f5fd66c-f259-4ece-ba96-c2181ef4c6e6', 41, 'student', '2025-06-13 21:02:33', '2025-06-13 21:04:14', 101, 'ended', '2025-06-13 21:02:33', '2025-06-13 21:04:14'),
(26, '2f5fd66c-f259-4ece-ba96-c2181ef4c6e6', 30, 'teacher', '2025-06-13 21:02:58', '2025-06-13 21:51:19', 2901, 'ended', '2025-06-13 21:02:58', '2025-06-13 21:51:19'),
(27, '2f5fd66c-f259-4ece-ba96-c2181ef4c6e6', 41, 'student', '2025-06-13 21:04:26', '2025-06-13 21:51:21', 2815, 'ended', '2025-06-13 21:04:26', '2025-06-13 21:51:21'),
(28, '73f9635c-93d7-4087-90f2-cda60060491e', 40, 'teacher', '2025-06-13 23:26:28', '2025-06-13 23:55:05', 1717, 'ended', '2025-06-13 23:26:28', '2025-06-13 23:55:05'),
(29, '73f9635c-93d7-4087-90f2-cda60060491e', 41, 'student', '2025-06-13 23:45:06', '2025-06-13 23:55:00', 594, 'ended', '2025-06-13 23:45:06', '2025-06-13 23:55:00'),
(30, '73f9635c-93d7-4087-90f2-cda60060491e', 41, 'student', '2025-06-13 23:55:31', '2025-06-13 23:55:32', 1, 'ended', '2025-06-13 23:55:31', '2025-06-13 23:55:32'),
(31, '9fba2e5d-928c-4e37-8694-3805c6fb0904', 31, 'student', '2025-06-15 14:06:20', NULL, NULL, 'active', '2025-06-15 14:06:20', '2025-06-15 14:06:20'),
(32, '9fba2e5d-928c-4e37-8694-3805c6fb0904', 30, 'teacher', '2025-06-15 14:07:04', NULL, NULL, 'active', '2025-06-15 14:07:04', '2025-06-15 14:07:04'),
(33, '3b6e9223-34e9-429a-8da9-dd0cbc75e171', 30, 'teacher', '2025-06-15 19:40:16', NULL, NULL, 'active', '2025-06-15 19:40:16', '2025-06-15 19:40:16'),
(34, '3b6e9223-34e9-429a-8da9-dd0cbc75e171', 31, 'student', '2025-06-15 19:42:05', '2025-06-15 19:49:23', 438, 'ended', '2025-06-15 19:42:05', '2025-06-15 19:49:23'),
(35, '3b6e9223-34e9-429a-8da9-dd0cbc75e171', 31, 'student', '2025-06-15 19:49:26', '2025-06-15 20:09:32', 1206, 'ended', '2025-06-15 19:49:26', '2025-06-15 20:09:32'),
(36, '7efe8719-6645-4f19-8dae-1640eca9ed98', 31, 'student', '2025-06-16 20:00:06', NULL, NULL, 'active', '2025-06-16 20:00:06', '2025-06-16 20:00:06'),
(37, '31267967-4f12-4a02-8899-8cc4a1541a15', 40, 'teacher', '2025-06-16 23:30:12', '2025-06-16 23:31:33', 81, 'ended', '2025-06-16 23:30:12', '2025-06-16 23:31:33'),
(38, '7ded9eaf-84ee-46ff-aa04-2b96f606a185', 31, 'student', '2025-06-17 06:30:30', NULL, NULL, 'active', '2025-06-17 06:30:30', '2025-06-17 06:30:30'),
(39, '7ded9eaf-84ee-46ff-aa04-2b96f606a185', 30, 'teacher', '2025-06-17 06:31:25', NULL, NULL, 'active', '2025-06-17 06:31:25', '2025-06-17 06:31:25');

-- --------------------------------------------------------

--
-- بنية الجدول `messages`
--

CREATE TABLE `messages` (
  `id` int NOT NULL,
  `conversation_id` int NOT NULL,
  `sender_id` int NOT NULL,
  `recipient_id` int NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `messages`
--

INSERT INTO `messages` (`id`, `conversation_id`, `sender_id`, `recipient_id`, `content`, `created_at`, `read_at`) VALUES
(45, 8, 31, 30, 'السلام عليكم ورحمة الله وبركاته.', '2025-06-09 12:50:53', '2025-06-09 12:51:35'),
(46, 8, 31, 30, 'أريد أن أدرس عندك العربية والقرآن الكريم', '2025-06-09 12:51:13', '2025-06-09 12:51:35'),
(47, 8, 30, 31, 'حياك الله مرحبا بك', '2025-06-09 12:51:44', '2025-06-09 12:51:45'),
(48, 8, 30, 31, 'ماذا تريد أن تتعلم', '2025-06-09 12:52:06', '2025-06-09 12:52:07'),
(49, 9, 41, 40, 'اهلا', '2025-06-10 20:25:40', '2025-06-10 20:26:25'),
(50, 9, 41, 40, 'تمام', '2025-06-10 20:26:13', '2025-06-10 20:26:25'),
(52, 9, 40, 41, 'تهخت', '2025-06-10 20:27:59', '2025-06-10 20:37:23'),
(53, 9, 41, 40, 'تىتى', '2025-06-10 20:37:25', '2025-06-10 20:38:16'),
(55, 9, 41, 40, 'تىتىت', '2025-06-10 20:51:29', '2025-06-10 20:51:57'),
(56, 9, 41, 40, 'تىتنىتن', '2025-06-10 20:51:50', '2025-06-10 20:51:57'),
(57, 9, 41, 40, 'تستيخه', '2025-06-10 21:14:00', '2025-06-10 21:14:10'),
(58, 8, 31, 30, 'السلام عليكم ورحمة الله', '2025-06-11 07:27:00', '2025-06-11 07:27:20'),
(59, 8, 31, 30, 'كيف حالك', '2025-06-11 07:27:04', '2025-06-11 07:27:20'),
(60, 8, 31, 30, 'بخير', '2025-06-11 07:27:06', '2025-06-11 07:27:20'),
(61, 8, 30, 31, 'الحمد لله بخير', '2025-06-11 07:27:28', '2025-06-11 07:27:28'),
(62, 9, 40, 41, 'ةنم', '2025-07-19 20:34:01', '2025-07-20 10:56:46');

-- --------------------------------------------------------

--
-- بنية الجدول `notes`
--

CREATE TABLE `notes` (
  `id` int NOT NULL,
  `teacher_id` int NOT NULL,
  `student_id` int NOT NULL,
  `meeting_id` int DEFAULT NULL,
  `content` text,
  `written_by` enum('teacher','student') NOT NULL DEFAULT 'student',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- إرجاع أو استيراد بيانات الجدول `notes`
--

INSERT INTO `notes` (`id`, `teacher_id`, `student_id`, `meeting_id`, `content`, `written_by`, `updated_at`, `created_at`) VALUES
(19, 40, 41, 210, 'mjkasnkj', 'teacher', '2025-07-27 10:42:59', '2025-07-27 10:42:59'),
(22, 40, 41, 210, 'mkm kl', 'student', '2025-07-27 10:45:27', '2025-07-27 10:45:27'),
(23, 31, 31, 211, 'نككمنكم\nط', 'student', '2025-07-27 12:04:40', '2025-07-27 12:04:39');

-- --------------------------------------------------------

--
-- بنية الجدول `payments`
--

CREATE TABLE `payments` (
  `id` int NOT NULL,
  `teacher_profile_id` int DEFAULT NULL,
  `student_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','completed','failed') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `payment_id` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` enum('deposit','booking','refund') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'booking',
  `payment_method` enum('paypal','stripe','wallet') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'wallet',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `payments`
--

INSERT INTO `payments` (`id`, `teacher_profile_id`, `student_id`, `amount`, `status`, `payment_id`, `type`, `payment_method`, `created_at`, `updated_at`) VALUES
(10, 31, 31, '15.00', 'completed', NULL, 'booking', 'wallet', '2025-06-09 12:54:14', '2025-06-09 12:54:14'),
(11, 31, 31, '15.00', 'completed', NULL, 'booking', 'wallet', '2025-06-09 12:58:43', '2025-06-09 12:58:43'),
(12, 31, 31, '15.00', 'completed', NULL, 'booking', 'wallet', '2025-06-09 13:01:47', '2025-06-09 13:01:47'),
(15, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-10 09:17:51', '2025-06-10 09:17:51'),
(16, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-10 10:36:20', '2025-06-10 10:36:20'),
(17, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-10 18:16:04', '2025-06-10 18:16:04'),
(18, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-11 07:35:12', '2025-06-11 07:35:12'),
(19, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-11 08:07:24', '2025-06-11 08:07:24'),
(20, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-11 08:40:10', '2025-06-11 08:40:10'),
(21, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-11 08:41:47', '2025-06-11 08:41:47'),
(22, 32, 31, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-11 15:24:49', '2025-06-11 15:24:49'),
(23, 32, 31, '9.50', 'failed', NULL, 'booking', 'wallet', '2025-06-11 15:25:03', '2025-06-11 15:25:20'),
(24, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-11 19:48:12', '2025-06-11 19:48:12'),
(25, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-11 19:51:30', '2025-06-11 19:51:30'),
(26, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-11 21:37:23', '2025-06-11 21:37:23'),
(27, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-11 21:49:26', '2025-06-11 21:49:26'),
(28, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-11 21:51:33', '2025-06-11 21:51:33'),
(29, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-11 22:27:28', '2025-06-11 22:27:28'),
(30, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-12 09:54:05', '2025-06-12 09:54:05'),
(31, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-12 10:16:55', '2025-06-12 10:16:55'),
(32, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-12 12:30:03', '2025-06-12 12:30:03'),
(33, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-12 13:53:03', '2025-06-12 13:53:03'),
(34, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-12 15:50:45', '2025-06-12 15:50:45'),
(35, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-12 16:07:26', '2025-06-12 16:07:26'),
(36, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-12 20:38:18', '2025-06-12 20:38:18'),
(37, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-12 21:27:37', '2025-06-12 21:27:37'),
(38, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-12 22:08:27', '2025-06-12 22:08:27'),
(39, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-12 22:12:29', '2025-06-12 22:12:29'),
(40, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-13 06:51:58', '2025-06-13 06:51:58'),
(42, NULL, 41, '10.00', 'completed', NULL, 'deposit', 'wallet', '2025-06-13 12:08:14', '2025-06-13 12:37:12'),
(43, NULL, 41, '4.00', 'completed', NULL, 'deposit', 'wallet', '2025-06-13 12:09:12', '2025-06-13 12:37:12'),
(44, NULL, 41, '2.00', 'completed', 'pi_3RZWtpLzlc45IqpN0zbOhNjS', 'deposit', 'stripe', '2025-06-13 12:40:04', '2025-06-13 12:40:04'),
(45, NULL, 41, '10.00', 'completed', '2JE59348BJ2876037', 'deposit', 'paypal', '2025-06-13 13:00:31', '2025-06-13 13:00:31'),
(46, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-13 19:11:01', '2025-06-13 19:11:01'),
(47, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-13 19:11:38', '2025-06-13 19:11:38'),
(48, 31, 41, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-13 20:33:04', '2025-06-13 20:33:04'),
(49, NULL, 41, '100.00', 'completed', 'pi_3RZfFOLzlc45IqpN1qLlDKWh', 'deposit', 'stripe', '2025-06-13 21:34:52', '2025-06-13 21:34:52'),
(50, NULL, 41, '11.00', 'completed', 'pi_3RZgMrLzlc45IqpN1wnqJP5E', 'deposit', 'stripe', '2025-06-13 22:47:01', '2025-06-13 22:47:01'),
(51, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-13 23:19:35', '2025-06-13 23:19:35'),
(52, 31, 31, '2.00', 'failed', NULL, 'booking', 'wallet', '2025-06-14 08:30:43', '2025-06-14 08:31:11'),
(53, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-14 09:25:24', '2025-06-14 09:25:24'),
(54, 31, 31, '2.00', 'failed', NULL, 'booking', 'wallet', '2025-06-14 10:16:22', '2025-06-14 10:16:51'),
(55, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-14 10:19:19', '2025-06-14 10:19:19'),
(56, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-14 12:27:01', '2025-06-14 12:27:01'),
(57, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-14 14:04:01', '2025-06-14 14:04:01'),
(58, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-14 17:44:15', '2025-06-14 17:44:15'),
(59, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-14 17:44:37', '2025-06-14 17:44:37'),
(60, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-14 17:50:59', '2025-06-14 17:50:59'),
(61, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-14 22:26:53', '2025-06-14 22:26:53'),
(62, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-14 23:32:36', '2025-06-14 23:32:36'),
(63, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-15 00:36:13', '2025-06-15 00:36:13'),
(64, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-15 02:32:14', '2025-06-15 02:32:14'),
(65, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-15 02:32:52', '2025-06-15 02:32:52'),
(66, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-15 02:33:47', '2025-06-15 02:33:47'),
(67, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-15 06:43:20', '2025-06-15 06:43:20'),
(68, NULL, 31, '100.00', 'completed', 'pi_3RaAQxLzlc45IqpN1DgOOiN3', 'deposit', 'stripe', '2025-06-15 06:52:54', '2025-06-15 06:52:54'),
(69, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-15 13:50:09', '2025-06-15 13:50:09'),
(70, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-15 15:15:52', '2025-06-15 15:15:52'),
(71, 32, 41, '19.00', 'failed', NULL, 'booking', 'wallet', '2025-06-15 15:16:26', '2025-06-15 16:03:57'),
(72, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-16 19:35:15', '2025-06-16 19:35:15'),
(73, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-16 20:05:52', '2025-06-16 20:05:52'),
(74, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-16 21:54:04', '2025-06-16 21:54:04'),
(75, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-16 22:39:47', '2025-06-16 22:39:47'),
(76, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-16 22:45:59', '2025-06-16 22:45:59'),
(77, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 06:23:10', '2025-06-17 06:23:10'),
(78, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 10:52:15', '2025-06-17 10:52:15'),
(79, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 11:55:28', '2025-06-17 11:55:28'),
(80, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 13:11:56', '2025-06-17 13:11:56'),
(81, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 14:06:52', '2025-06-17 14:06:52'),
(82, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 15:07:56', '2025-06-17 15:07:56'),
(83, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 15:51:53', '2025-06-17 15:51:53'),
(84, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 17:38:48', '2025-06-17 17:38:48'),
(85, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 17:52:56', '2025-06-17 17:52:56'),
(86, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-17 18:54:29', '2025-06-17 18:54:29'),
(87, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 00:40:57', '2025-06-18 00:40:57'),
(88, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 01:23:46', '2025-06-18 01:23:46'),
(89, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 01:38:15', '2025-06-18 01:38:15'),
(90, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 01:42:59', '2025-06-18 01:42:59'),
(91, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 01:45:03', '2025-06-18 01:45:03'),
(92, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 02:00:45', '2025-06-18 02:00:45'),
(93, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 05:54:12', '2025-06-18 05:54:12'),
(94, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 06:27:06', '2025-06-18 06:27:06'),
(95, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 07:13:21', '2025-06-18 07:13:21'),
(96, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 07:41:14', '2025-06-18 07:41:14'),
(97, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 07:54:32', '2025-06-18 07:54:32'),
(98, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 08:54:51', '2025-06-18 08:54:51'),
(99, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 09:32:41', '2025-06-18 09:32:41'),
(100, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 09:57:50', '2025-06-18 09:57:50'),
(101, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 10:24:48', '2025-06-18 10:24:48'),
(102, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 10:25:23', '2025-06-18 10:25:23'),
(103, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 10:58:42', '2025-06-18 10:58:42'),
(104, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 11:27:43', '2025-06-18 11:27:43'),
(105, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 12:20:50', '2025-06-18 12:20:50'),
(106, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 13:21:28', '2025-06-18 13:21:28'),
(107, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 13:47:48', '2025-06-18 13:47:48'),
(108, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 13:59:28', '2025-06-18 13:59:28'),
(109, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 14:39:56', '2025-06-18 14:39:56'),
(110, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 14:51:30', '2025-06-18 14:51:30'),
(111, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-18 15:51:21', '2025-06-18 15:51:21'),
(112, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 16:57:13', '2025-06-18 16:57:13'),
(113, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-18 17:27:11', '2025-06-18 17:27:11'),
(114, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-19 04:13:49', '2025-06-19 04:13:49'),
(115, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-19 04:25:32', '2025-06-19 04:25:32'),
(116, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-19 10:08:54', '2025-06-19 10:08:54'),
(117, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-19 10:09:24', '2025-06-19 10:09:24'),
(118, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-19 14:59:00', '2025-06-19 14:59:00'),
(119, 31, 31, '2.00', 'failed', NULL, 'booking', 'wallet', '2025-06-20 07:57:33', '2025-06-20 07:58:06'),
(120, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-20 07:58:39', '2025-06-20 07:58:39'),
(121, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-20 08:02:04', '2025-06-20 08:02:04'),
(122, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-20 12:48:31', '2025-06-20 12:48:31'),
(123, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-20 13:29:05', '2025-06-20 13:29:05'),
(124, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-20 13:55:32', '2025-06-20 13:55:32'),
(125, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-20 14:42:08', '2025-06-20 14:42:08'),
(126, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-20 15:23:01', '2025-06-20 15:23:01'),
(127, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-20 15:55:44', '2025-06-20 15:55:44'),
(128, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-20 16:21:24', '2025-06-20 16:21:24'),
(129, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-20 16:22:27', '2025-06-20 16:22:27'),
(130, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-21 05:54:14', '2025-06-21 05:54:14'),
(131, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-21 06:27:29', '2025-06-21 06:27:29'),
(132, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 06:59:11', '2025-06-21 06:59:11'),
(133, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 07:58:21', '2025-06-21 07:58:21'),
(134, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 09:28:24', '2025-06-21 09:28:24'),
(135, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 09:56:50', '2025-06-21 09:56:50'),
(136, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 10:17:05', '2025-06-21 10:17:05'),
(137, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-21 10:17:34', '2025-06-21 10:17:34'),
(138, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 10:19:04', '2025-06-21 10:19:04'),
(139, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 13:57:02', '2025-06-21 13:57:02'),
(140, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 14:24:44', '2025-06-21 14:24:44'),
(141, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 14:30:53', '2025-06-21 14:30:53'),
(142, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 15:23:30', '2025-06-21 15:23:30'),
(143, 32, 41, '19.00', 'completed', NULL, 'booking', 'wallet', '2025-06-21 16:13:14', '2025-06-21 16:13:14'),
(144, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-21 17:38:42', '2025-06-21 17:38:42'),
(145, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-21 17:51:29', '2025-06-21 17:51:29'),
(146, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-21 17:52:04', '2025-06-21 17:52:04'),
(147, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-22 05:25:57', '2025-06-22 05:25:57'),
(148, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-22 05:31:13', '2025-06-22 05:31:13'),
(149, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-22 07:57:10', '2025-06-22 07:57:10'),
(150, 31, 31, '2.00', 'failed', NULL, 'booking', 'wallet', '2025-06-22 07:57:59', '2025-06-22 07:58:40'),
(151, 31, 31, '2.00', 'failed', NULL, 'booking', 'wallet', '2025-06-22 07:58:15', '2025-06-22 07:58:48'),
(152, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-22 07:59:05', '2025-06-22 07:59:05'),
(153, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-22 08:00:00', '2025-06-22 08:00:00'),
(154, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-22 08:01:46', '2025-06-22 08:01:46'),
(155, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-24 14:45:25', '2025-06-24 14:45:25'),
(156, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-24 17:28:39', '2025-06-24 17:28:39'),
(157, 32, 41, '9.50', 'failed', NULL, 'booking', 'wallet', '2025-06-24 19:29:33', '2025-06-24 19:40:49'),
(158, 32, 41, '9.50', 'failed', NULL, 'booking', 'wallet', '2025-06-24 19:58:29', '2025-06-24 19:59:02'),
(159, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-24 20:00:26', '2025-06-24 20:00:26'),
(160, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-24 21:43:00', '2025-06-24 21:43:00'),
(161, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-25 10:28:00', '2025-06-25 10:28:00'),
(162, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-26 17:02:03', '2025-06-26 17:02:03'),
(163, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-26 20:05:17', '2025-06-26 20:05:17'),
(164, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-26 20:57:41', '2025-06-26 20:57:41'),
(165, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-27 15:22:59', '2025-06-27 15:22:59'),
(166, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-27 15:34:20', '2025-06-27 15:34:20'),
(167, 32, 41, '9.50', 'completed', NULL, 'booking', 'wallet', '2025-06-27 20:57:11', '2025-06-27 20:57:11'),
(168, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-28 07:57:12', '2025-06-28 07:57:12'),
(169, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-28 09:26:03', '2025-06-28 09:26:03'),
(170, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-06-30 12:05:37', '2025-06-30 12:05:37'),
(171, NULL, 31, '200.00', 'completed', 'pi_3RfkYZLzlc45IqpN0DhTzJOi', 'deposit', 'stripe', '2025-06-30 16:27:49', '2025-06-30 16:27:49'),
(172, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-06-30 16:28:12', '2025-06-30 16:28:12'),
(173, 32, 75, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-02 21:13:33', '2025-07-02 21:13:33'),
(174, 32, 75, '3.00', 'completed', NULL, 'booking', 'wallet', '2025-07-02 21:33:50', '2025-07-02 21:33:50'),
(175, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-11 20:20:00', '2025-07-11 20:20:00'),
(176, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-11 20:58:16', '2025-07-11 20:58:16'),
(177, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-11 21:34:56', '2025-07-11 21:34:56'),
(178, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-11 21:59:37', '2025-07-11 21:59:37'),
(179, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-11 23:07:59', '2025-07-11 23:07:59'),
(180, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-12 00:25:50', '2025-07-12 00:25:50'),
(181, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-12 10:57:33', '2025-07-12 10:57:33'),
(182, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-12 12:52:12', '2025-07-12 12:52:12'),
(183, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-12 13:21:23', '2025-07-12 13:21:23'),
(184, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-12 13:40:44', '2025-07-12 13:40:44'),
(185, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-12 14:01:30', '2025-07-12 14:01:30'),
(186, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-12 15:22:27', '2025-07-12 15:22:27'),
(187, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-12 17:05:06', '2025-07-12 17:05:06'),
(188, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-07-13 07:06:08', '2025-07-13 07:06:08'),
(189, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-07-13 15:25:12', '2025-07-13 15:25:12'),
(190, 32, 41, '15.00', 'completed', NULL, 'booking', 'wallet', '2025-07-16 14:48:56', '2025-07-16 14:48:56'),
(191, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-07-19 20:47:13', '2025-07-19 20:47:13'),
(192, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-07-20 08:00:22', '2025-07-20 08:00:22'),
(193, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-23 11:24:42', '2025-07-23 11:24:42'),
(194, 32, 41, '7.50', 'failed', NULL, 'booking', 'wallet', '2025-07-23 11:52:49', '2025-07-23 15:31:45'),
(195, 31, 31, '2.00', 'failed', NULL, 'booking', 'wallet', '2025-07-23 13:36:19', '2025-07-23 13:36:31'),
(196, 32, 41, '7.50', 'failed', NULL, 'booking', 'wallet', '2025-07-23 16:17:50', '2025-07-23 16:18:47'),
(197, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-23 16:44:31', '2025-07-23 16:44:31'),
(198, 32, 41, '7.50', 'failed', NULL, 'booking', 'wallet', '2025-07-23 17:14:00', '2025-07-23 17:25:32'),
(199, 32, 41, '7.50', 'failed', NULL, 'booking', 'wallet', '2025-07-23 17:26:27', '2025-07-23 17:28:07'),
(200, 32, 41, '7.50', 'failed', NULL, 'booking', 'wallet', '2025-07-23 17:28:41', '2025-07-23 17:28:58'),
(201, 32, 41, '7.50', 'failed', NULL, 'booking', 'wallet', '2025-07-23 17:30:31', '2025-07-23 17:37:41'),
(202, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-23 17:42:15', '2025-07-23 17:42:15'),
(203, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-23 18:05:09', '2025-07-23 18:05:09'),
(204, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-24 10:01:14', '2025-07-24 10:01:14'),
(205, 32, 41, '7.50', 'failed', NULL, 'booking', 'wallet', '2025-07-24 10:53:42', '2025-07-24 20:31:47'),
(206, 31, 31, '2.00', 'failed', NULL, 'booking', 'wallet', '2025-07-24 13:07:35', '2025-07-24 13:07:49'),
(207, 31, 31, '2.00', 'failed', NULL, 'booking', 'wallet', '2025-07-24 13:08:13', '2025-07-24 13:10:10'),
(208, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-07-24 13:10:22', '2025-07-24 13:10:22'),
(209, 31, 31, '4.00', 'completed', NULL, 'booking', 'wallet', '2025-07-24 16:42:37', '2025-07-24 16:42:37'),
(210, 32, 41, '7.50', 'failed', NULL, 'booking', 'wallet', '2025-07-24 20:46:12', '2025-07-24 20:46:39'),
(211, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-24 21:07:09', '2025-07-24 21:07:09'),
(212, 31, 31, '2.00', 'failed', NULL, 'booking', 'wallet', '2025-07-24 21:19:38', '2025-07-24 21:20:44'),
(213, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-07-24 21:22:04', '2025-07-24 21:22:04'),
(214, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-07-24 21:23:11', '2025-07-24 21:23:11'),
(215, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-07-25 11:10:59', '2025-07-25 11:10:59'),
(216, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-25 11:37:28', '2025-07-25 11:37:28'),
(217, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-25 21:26:33', '2025-07-25 21:26:33'),
(218, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-07-26 11:44:45', '2025-07-26 11:44:45'),
(219, 32, 41, '7.50', 'completed', NULL, 'booking', 'wallet', '2025-07-27 10:10:30', '2025-07-27 10:10:30'),
(220, 31, 31, '2.00', 'completed', NULL, 'booking', 'wallet', '2025-07-27 11:49:20', '2025-07-27 11:49:20');

-- --------------------------------------------------------

--
-- بنية الجدول `reviews`
--

CREATE TABLE `reviews` (
  `id` int NOT NULL,
  `teacher_profile_id` int NOT NULL,
  `student_id` int NOT NULL,
  `rating` decimal(2,1) NOT NULL,
  `comment` text COLLATE utf8mb4_general_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `reviews`
--

INSERT INTO `reviews` (`id`, `teacher_profile_id`, `student_id`, `rating`, `comment`, `created_at`, `updated_at`) VALUES
(6, 32, 41, '3.0', 'njnkj', '2025-06-12 10:59:26', '2025-06-12 10:59:26'),
(7, 31, 31, '5.0', 'مدرس ممتاز أنصح به بقوة', '2025-06-15 06:54:13', '2025-06-15 06:54:13');

-- --------------------------------------------------------

--
-- بنية الجدول `review_replies`
--

CREATE TABLE `review_replies` (
  `id` int NOT NULL,
  `review_id` int NOT NULL,
  `teacher_id` int NOT NULL,
  `reply_text` text NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- إرجاع أو استيراد بيانات الجدول `review_replies`
--

INSERT INTO `review_replies` (`id`, `review_id`, `teacher_id`, `reply_text`, `created_at`, `updated_at`) VALUES
(1, 6, 40, 'جزاك الله خيرا', '2025-06-16 02:19:54', '2025-06-16 02:19:54');

-- --------------------------------------------------------

--
-- بنية الجدول `settings`
--

CREATE TABLE `settings` (
  `id` int NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `settings`
--

INSERT INTO `settings` (`id`, `key`, `value`, `description`, `created_at`, `updated_at`) VALUES
(1, 'min_withdrawal_amount', '10.00', 'Minimum amount teachers can withdraw', '2025-06-13 14:40:18', '2025-06-13 14:40:18'),
(2, 'withdrawal_fee_percentage', '0.00', 'Percentage fee for withdrawals (0-100)', '2025-06-13 14:40:18', '2025-06-13 14:40:18'),
(3, 'withdrawal_processing_days', '3-5', 'Expected processing time for withdrawals', '2025-06-13 14:40:18', '2025-06-13 14:40:18');

-- --------------------------------------------------------

--
-- بنية الجدول `student_completion_data`
--

CREATE TABLE `student_completion_data` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `native_language` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `islam_learning_language` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `arabic_learning_language` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `age` int NOT NULL,
  `country` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `timezone` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `arabic_proficiency_level` enum('beginner','intermediate','advanced') COLLATE utf8mb4_general_ci NOT NULL,
  `private_tutoring_preference` tinyint(1) NOT NULL,
  `is_completed` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `student_completion_data`
--

INSERT INTO `student_completion_data` (`id`, `user_id`, `native_language`, `islam_learning_language`, `arabic_learning_language`, `age`, `country`, `timezone`, `arabic_proficiency_level`, `private_tutoring_preference`, `is_completed`, `created_at`, `updated_at`) VALUES
(8, 31, 'العربية', 'الإنجليزية ', 'الإنجليزية', 20, 'middle_east', 'UTC+03:00', 'beginner', 1, 1, '2025-06-09 08:09:09', '2025-06-09 16:56:03'),
(9, 41, 'تىنت', 'يؤ', 'يؤ', 33, 'يسؤيئ', 'UTC+08:00', 'intermediate', 1, 1, '2025-06-09 18:01:57', '2025-07-25 18:38:07'),
(10, 75, 'عربية', 'عربي', 'عربي', 7, 'eh', 'UTC+03:00', 'beginner', 1, 1, '2025-07-02 20:38:50', '2025-07-02 20:38:50');

-- --------------------------------------------------------

--
-- بنية الجدول `teacher_categories`
--

CREATE TABLE `teacher_categories` (
  `id` int NOT NULL,
  `teacher_profile_id` int NOT NULL,
  `category_id` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `teacher_categories`
--

INSERT INTO `teacher_categories` (`id`, `teacher_profile_id`, `category_id`, `created_at`) VALUES
(62, 34, 11, '2025-06-10 09:10:44'),
(63, 34, 12, '2025-06-10 09:10:44'),
(64, 34, 13, '2025-06-10 09:10:44'),
(65, 34, 14, '2025-06-10 09:10:44'),
(66, 34, 15, '2025-06-10 09:10:44'),
(67, 34, 16, '2025-06-10 09:10:44'),
(68, 34, 17, '2025-06-10 09:10:44'),
(69, 34, 18, '2025-06-10 09:10:44'),
(70, 34, 19, '2025-06-10 09:10:44'),
(71, 34, 20, '2025-06-10 09:10:44'),
(72, 34, 21, '2025-06-10 09:10:44'),
(73, 34, 22, '2025-06-10 09:10:44'),
(74, 34, 23, '2025-06-10 09:10:44'),
(75, 34, 24, '2025-06-10 09:10:44'),
(76, 34, 26, '2025-06-10 09:10:44'),
(77, 34, 25, '2025-06-10 09:10:44'),
(78, 34, 27, '2025-06-10 09:10:44'),
(79, 34, 28, '2025-06-10 09:10:44'),
(80, 34, 29, '2025-06-10 09:10:44'),
(81, 34, 30, '2025-06-10 09:10:44'),
(82, 34, 31, '2025-06-10 09:10:44'),
(83, 34, 32, '2025-06-10 09:10:44'),
(311, 35, 15, '2025-06-15 07:13:31'),
(312, 35, 13, '2025-06-15 07:13:31'),
(313, 35, 11, '2025-06-15 07:13:31'),
(332, 36, 32, '2025-07-01 08:18:48'),
(333, 36, 31, '2025-07-01 08:18:48'),
(334, 36, 30, '2025-07-01 08:18:48'),
(335, 36, 29, '2025-07-01 08:18:48'),
(336, 36, 28, '2025-07-01 08:18:48'),
(351, 31, 19, '2025-07-03 06:19:56'),
(352, 31, 16, '2025-07-03 06:19:56'),
(353, 31, 24, '2025-07-03 06:19:56'),
(354, 31, 17, '2025-07-03 06:19:56'),
(355, 31, 23, '2025-07-03 06:19:56'),
(356, 31, 18, '2025-07-03 06:19:56'),
(393, 32, 30, '2025-07-27 16:12:53'),
(394, 32, 21, '2025-07-27 16:12:53'),
(395, 32, 24, '2025-07-27 16:12:53'),
(396, 32, 23, '2025-07-27 16:12:53'),
(397, 32, 15, '2025-07-27 16:12:53'),
(398, 32, 18, '2025-07-27 16:12:53');

-- --------------------------------------------------------

--
-- بنية الجدول `teacher_languages`
--

CREATE TABLE `teacher_languages` (
  `id` int NOT NULL,
  `teacher_profile_id` int NOT NULL,
  `language_id` int NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `teacher_languages`
--

INSERT INTO `teacher_languages` (`id`, `teacher_profile_id`, `language_id`, `created_at`) VALUES
(59, 34, 13, '2025-06-10 09:10:44'),
(60, 34, 11, '2025-06-10 09:10:44'),
(61, 34, 12, '2025-06-10 09:10:44'),
(159, 35, 12, '2025-06-15 07:13:31'),
(160, 35, 11, '2025-06-15 07:13:31'),
(161, 35, 13, '2025-06-15 07:13:31'),
(195, 36, 12, '2025-07-01 08:18:48'),
(220, 31, 11, '2025-07-03 06:19:56'),
(221, 31, 12, '2025-07-03 06:19:56'),
(222, 31, 13, '2025-07-03 06:19:56'),
(289, 32, 17, '2025-07-27 16:12:53'),
(290, 32, 28, '2025-07-27 16:12:53'),
(291, 32, 20, '2025-07-27 16:12:53'),
(292, 32, 13, '2025-07-27 16:12:53'),
(293, 32, 27, '2025-07-27 16:12:53'),
(294, 32, 26, '2025-07-27 16:12:53'),
(295, 32, 29, '2025-07-27 16:12:53'),
(296, 32, 11, '2025-07-27 16:12:53'),
(297, 32, 21, '2025-07-27 16:12:53'),
(298, 32, 24, '2025-07-27 16:12:53'),
(299, 32, 18, '2025-07-27 16:12:53');

-- --------------------------------------------------------

--
-- بنية الجدول `teacher_profiles`
--

CREATE TABLE `teacher_profiles` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `profile_picture_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `commitment_accepted` tinyint(1) DEFAULT '0',
  `country` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `residence` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `native_language` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `teaching_languages` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `course_types` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `qualifications` text COLLATE utf8mb4_general_ci NOT NULL,
  `teaching_experience` text COLLATE utf8mb4_general_ci NOT NULL,
  `intro_video_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cv` text COLLATE utf8mb4_general_ci,
  `available_hours` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `price_per_lesson` decimal(10,2) NOT NULL,
  `trial_lesson_price` decimal(6,2) DEFAULT NULL,
  `timezone` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `payment_method` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `status` enum('pending','approved','rejected') COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `zoom_access_token` text COLLATE utf8mb4_general_ci,
  `zoom_refresh_token` text COLLATE utf8mb4_general_ci
) ;

--
-- إرجاع أو استيراد بيانات الجدول `teacher_profiles`
--

INSERT INTO `teacher_profiles` (`id`, `user_id`, `phone`, `profile_picture_url`, `commitment_accepted`, `country`, `residence`, `native_language`, `teaching_languages`, `course_types`, `qualifications`, `teaching_experience`, `intro_video_url`, `cv`, `available_hours`, `price_per_lesson`, `trial_lesson_price`, `timezone`, `payment_method`, `status`, `created_at`, `updated_at`, `zoom_access_token`, `zoom_refresh_token`) VALUES
(31, 30, '+966557325340', '/uploads/profile-pictures/profilePicture-1751299920337-793135240.jpg', 1, 'Saudi Arabia', 'middle_east', 'Arabic', '[11,12,13]', '[19,16,24,17,23,18]', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\n\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\nإ', '20', '/uploads/videos/video-1751300213012-672117891.mp4', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\n', '{\"monday\":[\"00:00-00:30\",\"00:00-01:00\",\"00:30-01:00\",\"01:00-01:30\",\"01:00-02:00\",\"01:30-02:00\",\"02:00-02:30\",\"02:00-03:00\",\"02:30-03:00\",\"03:00-03:30\",\"03:00-04:00\",\"03:30-04:00\",\"04:00-04:30\",\"04:00-05:00\",\"04:30-05:00\",\"05:00-05:30\",\"05:00-06:00\",\"05:30-06:00\",\"06:00-06:30\",\"06:00-07:00\",\"06:30-07:00\",\"07:00-07:30\",\"07:00-08:00\",\"07:30-08:00\",\"08:00-08:30\",\"08:00-09:00\",\"08:30-09:00\",\"09:00-09:30\",\"09:00-10:00\",\"09:30-10:00\",\"10:00-10:30\",\"10:00-11:00\",\"10:30-11:00\",\"11:00-11:30\",\"11:00-12:00\",\"11:30-12:00\",\"12:00-12:30\",\"12:00-13:00\",\"12:30-13:00\",\"13:00-13:30\",\"13:00-14:00\",\"13:30-14:00\",\"14:00-14:30\",\"14:00-15:00\",\"14:30-15:00\",\"15:00-15:30\",\"15:00-16:00\",\"15:30-16:00\",\"16:00-16:30\",\"16:00-17:00\",\"16:30-17:00\",\"17:00-17:30\",\"17:00-18:00\",\"17:30-18:00\",\"18:00-18:30\",\"18:00-19:00\",\"18:30-19:00\",\"19:00-19:30\",\"19:00-20:00\",\"19:30-20:00\",\"20:00-20:30\",\"20:00-21:00\",\"20:30-21:00\",\"21:00-21:30\",\"21:00-22:00\",\"21:30-22:00\",\"22:00-22:30\",\"22:00-23:00\",\"22:30-23:00\",\"23:00-00:00\",\"23:00-23:30\",\"23:30-00:00\"],\"tuesday\":[\"00:00-00:30\",\"00:00-01:00\",\"00:30-01:00\",\"01:00-01:30\",\"01:00-02:00\",\"01:30-02:00\",\"02:00-02:30\",\"02:00-03:00\",\"02:30-03:00\",\"03:00-03:30\",\"03:00-04:00\",\"03:30-04:00\",\"04:00-04:30\",\"04:00-05:00\",\"04:30-05:00\",\"05:00-05:30\",\"05:00-06:00\",\"05:30-06:00\",\"06:00-06:30\",\"06:00-07:00\",\"06:30-07:00\",\"07:00-07:30\",\"07:00-08:00\",\"07:30-08:00\",\"08:00-08:30\",\"08:00-09:00\",\"08:30-09:00\",\"09:00-09:30\",\"09:00-10:00\",\"09:30-10:00\",\"10:00-10:30\",\"10:00-11:00\",\"10:30-11:00\",\"11:00-11:30\",\"11:00-12:00\",\"11:30-12:00\",\"12:00-12:30\",\"12:00-13:00\",\"12:30-13:00\",\"13:00-13:30\",\"13:00-14:00\",\"13:30-14:00\",\"14:00-14:30\",\"14:00-15:00\",\"14:30-15:00\",\"15:00-15:30\",\"15:00-16:00\",\"15:30-16:00\",\"16:00-16:30\",\"16:00-17:00\",\"16:30-17:00\",\"17:00-17:30\",\"17:00-18:00\",\"17:30-18:00\",\"18:00-18:30\",\"18:00-19:00\",\"18:30-19:00\",\"19:00-19:30\",\"19:00-20:00\",\"19:30-20:00\",\"20:00-20:30\",\"20:00-21:00\",\"20:30-21:00\",\"21:00-21:30\",\"21:00-22:00\",\"21:30-22:00\",\"22:00-22:30\",\"22:00-23:00\",\"22:30-23:00\",\"23:00-00:00\",\"23:00-23:30\",\"23:30-00:00\"],\"wednesday\":[\"00:00-00:30\",\"00:00-01:00\",\"00:30-01:00\",\"01:00-01:30\",\"01:00-02:00\",\"01:30-02:00\",\"02:00-02:30\",\"02:00-03:00\",\"02:30-03:00\",\"03:00-03:30\",\"03:00-04:00\",\"03:30-04:00\",\"04:00-04:30\",\"04:00-05:00\",\"04:30-05:00\",\"05:00-05:30\",\"05:00-06:00\",\"05:30-06:00\",\"06:00-06:30\",\"06:00-07:00\",\"06:30-07:00\",\"07:00-07:30\",\"07:00-08:00\",\"07:30-08:00\",\"08:00-08:30\",\"08:00-09:00\",\"08:30-09:00\",\"09:00-09:30\",\"09:00-10:00\",\"09:30-10:00\",\"10:00-10:30\",\"10:00-11:00\",\"10:30-11:00\",\"11:00-11:30\",\"11:00-12:00\",\"11:30-12:00\",\"12:00-12:30\",\"12:00-13:00\",\"12:30-13:00\",\"13:00-13:30\",\"13:00-14:00\",\"13:30-14:00\",\"14:00-14:30\",\"14:00-15:00\",\"14:30-15:00\",\"15:00-15:30\",\"15:00-16:00\",\"15:30-16:00\",\"16:00-16:30\",\"16:00-17:00\",\"16:30-17:00\",\"17:00-17:30\",\"17:00-18:00\",\"17:30-18:00\",\"18:00-18:30\",\"18:00-19:00\",\"18:30-19:00\",\"19:00-19:30\",\"19:00-20:00\",\"19:30-20:00\",\"20:00-20:30\",\"20:00-21:00\",\"20:30-21:00\",\"21:00-21:30\",\"21:00-22:00\",\"21:30-22:00\",\"22:00-22:30\",\"22:00-23:00\",\"22:30-23:00\",\"23:00-00:00\",\"23:00-23:30\",\"23:30-00:00\"],\"thursday\":[\"00:00-00:30\",\"00:00-01:00\",\"00:30-01:00\",\"01:00-01:30\",\"01:00-02:00\",\"01:30-02:00\",\"02:00-02:30\",\"02:00-03:00\",\"02:30-03:00\",\"03:00-03:30\",\"03:00-04:00\",\"03:30-04:00\",\"04:00-04:30\",\"04:00-05:00\",\"04:30-05:00\",\"05:00-05:30\",\"05:00-06:00\",\"05:30-06:00\",\"06:00-06:30\",\"06:00-07:00\",\"06:30-07:00\",\"07:00-07:30\",\"07:00-08:00\",\"07:30-08:00\",\"08:00-08:30\",\"08:00-09:00\",\"08:30-09:00\",\"09:00-09:30\",\"09:00-10:00\",\"09:30-10:00\",\"10:00-10:30\",\"10:00-11:00\",\"10:30-11:00\",\"11:00-11:30\",\"11:00-12:00\",\"11:30-12:00\",\"12:00-12:30\",\"12:00-13:00\",\"12:30-13:00\",\"13:00-13:30\",\"13:00-14:00\",\"13:30-14:00\",\"14:00-14:30\",\"14:00-15:00\",\"14:30-15:00\",\"15:00-15:30\",\"15:00-16:00\",\"15:30-16:00\",\"16:00-16:30\",\"16:00-17:00\",\"16:30-17:00\",\"17:00-17:30\",\"17:00-18:00\",\"17:30-18:00\",\"18:00-18:30\",\"18:00-19:00\",\"18:30-19:00\",\"19:00-19:30\",\"19:00-20:00\",\"19:30-20:00\",\"20:00-20:30\",\"20:00-21:00\",\"20:30-21:00\",\"21:00-21:30\",\"21:00-22:00\",\"21:30-22:00\",\"22:00-22:30\",\"22:00-23:00\",\"22:30-23:00\",\"23:00-00:00\",\"23:00-23:30\",\"23:30-00:00\"],\"friday\":[\"00:00-00:30\",\"00:00-01:00\",\"00:30-01:00\",\"01:00-01:30\",\"01:00-02:00\",\"01:30-02:00\",\"02:00-02:30\",\"02:00-03:00\",\"02:30-03:00\",\"03:00-03:30\",\"03:00-04:00\",\"03:30-04:00\",\"04:00-04:30\",\"04:00-05:00\",\"04:30-05:00\",\"05:00-05:30\",\"05:00-06:00\",\"05:30-06:00\",\"06:00-06:30\",\"06:00-07:00\",\"06:30-07:00\",\"07:00-07:30\",\"07:00-08:00\",\"07:30-08:00\",\"08:00-08:30\",\"08:00-09:00\",\"08:30-09:00\",\"09:00-09:30\",\"09:00-10:00\",\"09:30-10:00\",\"10:00-10:30\",\"10:00-11:00\",\"10:30-11:00\",\"11:00-11:30\",\"11:00-12:00\",\"11:30-12:00\",\"12:00-12:30\",\"12:00-13:00\",\"12:30-13:00\",\"13:00-13:30\",\"13:00-14:00\",\"13:30-14:00\",\"14:00-14:30\",\"14:00-15:00\",\"14:30-15:00\",\"15:00-15:30\",\"15:00-16:00\",\"15:30-16:00\",\"16:00-16:30\",\"16:00-17:00\",\"16:30-17:00\",\"17:00-17:30\",\"17:00-18:00\",\"17:30-18:00\",\"18:00-18:30\",\"18:00-19:00\",\"18:30-19:00\",\"19:00-19:30\",\"19:00-20:00\",\"19:30-20:00\",\"20:00-20:30\",\"20:00-21:00\",\"20:30-21:00\",\"21:00-21:30\",\"21:00-22:00\",\"21:30-22:00\",\"22:00-22:30\",\"22:00-23:00\",\"22:30-23:00\",\"23:00-00:00\",\"23:00-23:30\",\"23:30-00:00\"],\"saturday\":[\"00:00-00:30\",\"00:00-01:00\",\"00:30-01:00\",\"01:00-01:30\",\"01:00-02:00\",\"01:30-02:00\",\"02:00-02:30\",\"02:00-03:00\",\"02:30-03:00\",\"03:00-03:30\",\"03:00-04:00\",\"03:30-04:00\",\"04:00-04:30\",\"04:00-05:00\",\"04:30-05:00\",\"05:00-05:30\",\"05:00-06:00\",\"05:30-06:00\",\"06:00-06:30\",\"06:00-07:00\",\"06:30-07:00\",\"07:00-07:30\",\"07:00-08:00\",\"07:30-08:00\",\"08:00-08:30\",\"08:00-09:00\",\"08:30-09:00\",\"09:00-09:30\",\"09:00-10:00\",\"09:30-10:00\",\"10:00-10:30\",\"10:00-11:00\",\"10:30-11:00\",\"11:00-11:30\",\"11:00-12:00\",\"11:30-12:00\",\"12:00-12:30\",\"12:00-13:00\",\"12:30-13:00\",\"13:00-13:30\",\"13:00-14:00\",\"13:30-14:00\",\"14:00-14:30\",\"14:00-15:00\",\"14:30-15:00\",\"15:00-15:30\",\"15:00-16:00\",\"15:30-16:00\",\"16:00-16:30\",\"16:00-17:00\",\"16:30-17:00\",\"17:00-17:30\",\"17:00-18:00\",\"17:30-18:00\",\"18:00-18:30\",\"18:00-19:00\",\"18:30-19:00\",\"19:00-19:30\",\"19:00-20:00\",\"19:30-20:00\",\"20:00-20:30\",\"20:00-21:00\",\"20:30-21:00\",\"21:00-21:30\",\"21:00-22:00\",\"21:30-22:00\",\"22:00-22:30\",\"22:00-23:00\",\"22:30-23:00\",\"23:00-00:00\",\"23:00-23:30\",\"23:30-00:00\"],\"sunday\":[\"00:00-00:30\",\"00:00-01:00\",\"00:30-01:00\",\"01:00-01:30\",\"01:00-02:00\",\"01:30-02:00\",\"02:00-02:30\",\"02:00-03:00\",\"02:30-03:00\",\"03:00-03:30\",\"03:00-04:00\",\"03:30-04:00\",\"04:00-04:30\",\"04:00-05:00\",\"04:30-05:00\",\"05:00-05:30\",\"05:00-06:00\",\"05:30-06:00\",\"06:00-06:30\",\"06:00-07:00\",\"06:30-07:00\",\"07:00-07:30\",\"07:00-08:00\",\"07:30-08:00\",\"08:00-08:30\",\"08:00-09:00\",\"08:30-09:00\",\"09:00-09:30\",\"09:00-10:00\",\"09:30-10:00\",\"10:00-10:30\",\"10:00-11:00\",\"10:30-11:00\",\"11:00-11:30\",\"11:00-12:00\",\"11:30-12:00\",\"12:00-12:30\",\"12:00-13:00\",\"12:30-13:00\",\"13:00-13:30\",\"13:00-14:00\",\"13:30-14:00\",\"14:00-14:30\",\"14:00-15:00\",\"14:30-15:00\",\"15:00-15:30\",\"15:00-16:00\",\"15:30-16:00\",\"16:00-16:30\",\"16:00-17:00\",\"16:30-17:00\",\"17:00-17:30\",\"17:00-18:00\",\"17:30-18:00\",\"18:00-18:30\",\"18:00-19:00\",\"18:30-19:00\",\"19:00-19:30\",\"19:00-20:00\",\"19:30-20:00\",\"20:00-20:30\",\"20:00-21:00\",\"20:30-21:00\",\"21:00-21:30\",\"21:00-22:00\",\"21:30-22:00\",\"22:00-22:30\",\"22:00-23:00\",\"22:30-23:00\",\"23:00-00:00\",\"23:00-23:30\",\"23:30-00:00\"]}', '4.00', '3.00', 'UTC+03:00', 'paypal', 'approved', '2025-06-09 08:02:30', '2025-07-03 06:19:56', NULL, NULL),
(32, 40, '', '/uploads/profile-pictures/profilePicture-1751296659474-456427545.jpg', 1, 'kmj88', 'هتهتهخ88', 'تنيىس000uhuihui', '[17,28,20,13,27,26,29,11,21,24,18]', '[30,21,24,23,15,18]', 'تىستشنى88', '28', '/uploads/videos/video-1751296964080-41621749.mp4', 'ءىتنىسن88', '{\"monday\":[\"00:00-00:30\",\"00:30-01:00\",\"01:00-01:30\",\"01:30-02:00\",\"02:00-02:30\",\"02:30-03:00\",\"03:00-03:30\",\"03:30-04:00\",\"04:00-04:30\",\"04:30-05:00\",\"05:00-05:30\",\"05:30-06:00\",\"06:00-06:30\",\"06:30-07:00\",\"07:00-07:30\",\"07:30-08:00\",\"08:00-08:30\",\"08:30-09:00\",\"09:00-09:30\",\"09:30-10:00\",\"10:00-10:30\",\"10:30-11:00\",\"11:00-11:30\",\"11:30-12:00\",\"12:00-12:30\",\"12:30-13:00\",\"13:00-13:30\",\"13:30-14:00\",\"14:00-14:30\",\"14:30-15:00\",\"15:00-15:30\",\"15:30-16:00\",\"16:00-16:30\",\"16:30-17:00\",\"17:00-17:30\",\"17:30-18:00\",\"18:00-18:30\",\"18:30-19:00\",\"19:00-19:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"21:00-21:30\",\"21:30-22:00\",\"22:00-22:30\",\"22:30-23:00\",\"23:00-23:30\",\"23:30-00:00\"],\"tuesday\":[\"00:00-00:30\",\"00:30-01:00\",\"01:00-01:30\",\"01:30-02:00\",\"02:00-02:30\",\"02:30-03:00\",\"03:00-03:30\",\"03:30-04:00\",\"04:00-04:30\",\"04:30-05:00\",\"05:00-05:30\",\"05:30-06:00\",\"06:00-06:30\",\"06:30-07:00\",\"07:00-07:30\",\"07:30-08:00\",\"08:00-08:30\",\"08:30-09:00\",\"09:00-09:30\",\"09:30-10:00\",\"10:00-10:30\",\"10:30-11:00\",\"11:00-11:30\",\"11:30-12:00\",\"12:00-12:30\",\"12:30-13:00\",\"13:00-13:30\",\"13:30-14:00\",\"14:00-14:30\",\"14:30-15:00\",\"15:00-15:30\",\"15:30-16:00\",\"16:00-16:30\",\"16:30-17:00\",\"17:00-17:30\",\"17:30-18:00\",\"18:00-18:30\",\"18:30-19:00\",\"19:00-19:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"21:00-21:30\",\"21:30-22:00\",\"22:00-22:30\",\"22:30-23:00\",\"23:00-23:30\",\"23:30-00:00\"],\"wednesday\":[\"00:00-00:30\",\"00:30-01:00\",\"01:00-01:30\",\"01:30-02:00\",\"02:00-02:30\",\"02:30-03:00\",\"03:00-03:30\",\"03:30-04:00\",\"04:00-04:30\",\"04:30-05:00\",\"05:00-05:30\",\"05:30-06:00\",\"06:00-06:30\",\"06:30-07:00\",\"07:00-07:30\",\"07:30-08:00\",\"08:00-08:30\",\"08:30-09:00\",\"09:00-09:30\",\"09:30-10:00\",\"10:00-10:30\",\"10:30-11:00\",\"11:00-11:30\",\"11:30-12:00\",\"12:00-12:30\",\"12:30-13:00\",\"13:00-13:30\",\"13:30-14:00\",\"14:00-14:30\",\"14:30-15:00\",\"15:00-15:30\",\"15:30-16:00\",\"16:00-16:30\",\"16:30-17:00\",\"17:00-17:30\",\"17:30-18:00\",\"18:00-18:30\",\"18:30-19:00\",\"19:00-19:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"21:00-21:30\",\"21:30-22:00\",\"22:00-22:30\",\"22:30-23:00\",\"23:00-23:30\",\"23:30-00:00\"],\"thursday\":[\"00:00-00:30\",\"00:30-01:00\",\"01:00-01:30\",\"01:30-02:00\",\"02:00-02:30\",\"02:30-03:00\",\"03:00-03:30\",\"03:30-04:00\",\"04:00-04:30\",\"04:30-05:00\",\"05:00-05:30\",\"05:30-06:00\",\"06:00-06:30\",\"06:30-07:00\",\"07:00-07:30\",\"07:30-08:00\",\"08:00-08:30\",\"08:30-09:00\",\"09:00-09:30\",\"09:30-10:00\",\"10:00-10:30\",\"10:30-11:00\",\"11:00-11:30\",\"11:30-12:00\",\"12:00-12:30\",\"12:30-13:00\",\"13:00-13:30\",\"13:30-14:00\",\"14:00-14:30\",\"14:30-15:00\",\"15:00-15:30\",\"15:30-16:00\",\"16:00-16:30\",\"16:30-17:00\",\"17:00-17:30\",\"17:30-18:00\",\"18:00-18:30\",\"18:30-19:00\",\"19:00-19:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"21:00-21:30\",\"21:30-22:00\",\"22:00-22:30\",\"22:30-23:00\",\"23:00-23:30\",\"23:30-00:00\"],\"friday\":[\"00:00-00:30\",\"00:30-01:00\",\"01:00-01:30\",\"01:30-02:00\",\"02:00-02:30\",\"02:30-03:00\",\"03:00-03:30\",\"03:30-04:00\",\"04:00-04:30\",\"04:30-05:00\",\"05:00-05:30\",\"05:30-06:00\",\"06:00-06:30\",\"06:30-07:00\",\"07:00-07:30\",\"07:30-08:00\",\"08:00-08:30\",\"08:30-09:00\",\"09:00-09:30\",\"09:30-10:00\",\"10:00-10:30\",\"10:30-11:00\",\"11:00-11:30\",\"11:30-12:00\",\"12:00-12:30\",\"12:30-13:00\",\"13:00-13:30\",\"13:30-14:00\",\"14:00-14:30\",\"14:30-15:00\",\"15:00-15:30\",\"15:30-16:00\",\"16:00-16:30\",\"16:30-17:00\",\"17:00-17:30\",\"17:30-18:00\",\"18:00-18:30\",\"18:30-19:00\",\"19:00-19:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"21:00-21:30\",\"21:30-22:00\",\"22:00-22:30\",\"22:30-23:00\",\"23:00-23:30\",\"23:30-00:00\"],\"saturday\":[\"00:00-00:30\",\"00:30-01:00\",\"01:00-01:30\",\"01:30-02:00\",\"02:00-02:30\",\"02:30-03:00\",\"03:00-03:30\",\"03:30-04:00\",\"04:00-04:30\",\"04:30-05:00\",\"05:00-05:30\",\"05:30-06:00\",\"06:00-06:30\",\"06:30-07:00\",\"07:00-07:30\",\"07:30-08:00\",\"08:00-08:30\",\"08:30-09:00\",\"09:00-09:30\",\"09:30-10:00\",\"10:00-10:30\",\"10:30-11:00\",\"11:00-11:30\",\"11:30-12:00\",\"12:00-12:30\",\"12:30-13:00\",\"13:00-13:30\",\"13:30-14:00\",\"14:00-14:30\",\"14:30-15:00\",\"15:00-15:30\",\"15:30-16:00\",\"16:00-16:30\",\"16:30-17:00\",\"17:00-17:30\",\"17:30-18:00\",\"18:00-18:30\",\"18:30-19:00\",\"19:00-19:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"21:00-21:30\",\"21:30-22:00\",\"22:00-22:30\",\"22:30-23:00\",\"23:00-23:30\",\"23:30-00:00\"],\"sunday\":[\"03:00-03:30\",\"03:30-04:00\",\"04:00-04:30\",\"04:30-05:00\",\"05:00-05:30\",\"05:30-06:00\",\"06:00-06:30\",\"06:30-07:00\",\"07:00-07:30\",\"07:30-08:00\",\"08:00-08:30\",\"08:30-09:00\",\"09:00-09:30\",\"09:30-10:00\",\"10:00-10:30\",\"10:30-11:00\",\"11:00-11:30\",\"11:30-12:00\",\"12:00-12:30\",\"12:30-13:00\",\"13:00-13:30\",\"13:30-14:00\",\"14:00-14:30\",\"14:30-15:00\",\"15:00-15:30\",\"15:30-16:00\",\"16:00-16:30\",\"16:30-17:00\",\"17:00-17:30\",\"17:30-18:00\",\"18:00-18:30\",\"18:30-19:00\",\"19:00-19:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"21:00-21:30\",\"21:30-22:00\",\"22:00-22:30\",\"22:30-23:00\",\"23:00-23:30\",\"23:30-00:00\"]}', '15.00', '3.00', 'UTC+05:00', 'paypal', 'approved', '2025-06-09 18:15:40', '2025-07-27 16:12:53', NULL, NULL),
(34, 43, '+966557325340', '/uploads/femaleprofile/female.jfif', 1, 'Saudi Arabia', 'Jouf', 'Arabic', '[13,11,12]', '[11,12,13,14,15,16,17,18,19,20,21,22,23,24,26,25,27,28,29,30,31,32]', 'طيب', '2000', '/uploads/videos/video-1749546573550-86502580.mp4', 'طيب', '{\"monday\":[],\"tuesday\":[],\"wednesday\":[],\"thursday\":[],\"friday\":[],\"saturday\":[],\"sunday\":[]}', '5.00', NULL, 'UTC-05:00', 'paypal', 'approved', '2025-06-10 09:10:44', '2025-06-10 09:11:55', NULL, NULL),
(35, 44, '+966557325340', '/uploads/profile-pictures/profilePicture-1749971611858-691469087.JPG', 1, 'Saudi Arabia', 'Jouf', 'Arabic', '[12,11,13]', '[15,13,11]', '\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\n\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\nإخفا', '200000000', '/uploads/videos/video-1749971381778-142497856.mp4', '\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\n\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\nإخفا', '{\"monday\":[\"15:00-15:30\",\"15:30-16:00\",\"16:00-16:30\",\"16:30-17:00\",\"17:00-17:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"22:30-23:00\"],\"tuesday\":[\"15:30-16:00\",\"16:00-16:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"22:30-23:00\",\"23:00-23:30\"],\"wednesday\":[\"15:30-16:00\",\"16:00-16:30\",\"19:30-20:00\",\"20:30-21:00\",\"22:30-23:00\",\"23:00-23:30\",\"23:30-00:00\"],\"thursday\":[\"15:30-16:00\",\"16:00-16:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\",\"23:00-23:30\"],\"friday\":[\"19:00-19:30\",\"19:30-20:00\",\"20:30-21:00\",\"22:30-23:00\",\"23:30-00:00\"],\"saturday\":[\"19:00-19:30\",\"19:30-20:00\",\"20:00-20:30\",\"20:30-21:00\"],\"sunday\":[\"19:00-19:30\",\"19:30-20:00\"]}', '10.00', NULL, 'UTC+03:00', 'paypal', 'approved', '2025-06-15 07:13:31', '2025-06-15 07:15:32', NULL, NULL),
(36, 61, '', '/uploads/profile-pictures/profilePicture-1751357928902-583088474.jpg', 1, 'السعودية', 'مكة المكرمة', 'العربية', '[12]', '[32,31,30,29,28]', 'أستاذة لغة عربية', '10', '/uploads/videos/video-1751357802747-662880735.mp4', 'أستاذ', '{\"monday\":[],\"tuesday\":[],\"wednesday\":[],\"thursday\":[],\"friday\":[],\"saturday\":[],\"sunday\":[]}', '3.00', NULL, 'UTC+03:00', '', 'approved', '2025-07-01 08:18:48', '2025-07-02 06:56:25', NULL, NULL);

-- --------------------------------------------------------

--
-- بنية الجدول `teacher_profile_updates`
--

CREATE TABLE `teacher_profile_updates` (
  `id` int NOT NULL,
  `teacher_profile_id` int NOT NULL,
  `user_id` int NOT NULL,
  `full_name` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `residence` varchar(100) DEFAULT NULL,
  `profile_picture_url` text,
  `intro_video_url` text,
  `cv` text,
  `qualifications` text,
  `teaching_experience` int DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `admin_notes` text,
  `admin_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `reviewed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- إرجاع أو استيراد بيانات الجدول `teacher_profile_updates`
--

INSERT INTO `teacher_profile_updates` (`id`, `teacher_profile_id`, `user_id`, `full_name`, `email`, `country`, `residence`, `profile_picture_url`, `intro_video_url`, `cv`, `qualifications`, `teaching_experience`, `status`, `admin_notes`, `admin_id`, `created_at`, `updated_at`, `reviewed_at`) VALUES
(1, 32, 40, 'يةنت', '<EMAIL>', 'kmj88', 'هتهتهخ88', '/uploads/profile-pictures/profilePicture-1749537563008-336096152.jpg', '/uploads/videos/video-1749504807824-233219254.mp4', 'ءىتنىسن88', 'تىستشنى88', 28, 'approved', 'ىتنيىسنتس99999', 1, '2025-06-10 06:39:23', '2025-06-10 07:37:03', '2025-06-10 07:37:03'),
(2, 32, 40, 'يةنت000', '<EMAIL>', 'kmj88', 'هتهتهخ88', NULL, '/uploads/videos/video-1749504807824-233219254.mp4', 'ءىتنىسن88', 'تىستشنى88', 28, 'approved', '', 1, '2025-06-10 07:59:57', '2025-06-10 08:00:52', '2025-06-10 08:00:52'),
(3, 31, 30, 'د. فؤاد بن أحمد عطاء الله عطاء الله', '<EMAIL>', 'Saudi Arabia', 'middle_east', '/uploads/profile-pictures/profilePicture-1749543164775-666483420.JPG', '/uploads/videos/video-1749455593844-684120377.mp4', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\n', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\n\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\nإ', 20, 'approved', '', 1, '2025-06-10 08:12:44', '2025-06-10 08:25:22', '2025-06-10 08:25:22'),
(4, 32, 40, 'يةنت000', '<EMAIL>', 'kmj88', 'هتهتهخ88', NULL, '/uploads/videos/video-1749504807824-233219254.mp4', 'ءىتنىسن88', 'تىستشنى88', 28, 'approved', '', 1, '2025-06-10 10:38:43', '2025-06-10 10:39:27', '2025-06-10 10:39:27'),
(5, 32, 40, 'Mostafa', '<EMAIL>', 'kmj88', 'هتهتهخ88', '/uploads/profile-pictures/profilePicture-1750508645828-380996452.jpg', '/uploads/videos/video-1749504807824-233219254.mp4', 'ءىتنىسن88', 'تىستشنى88', 28, 'approved', '', 1, '2025-06-21 12:24:05', '2025-06-21 12:25:08', '2025-06-21 12:25:08'),
(6, 31, 30, 'د. فؤاد بن أحمد عطاء الله عطاء الله', '<EMAIL>', 'Saudi Arabia', 'middle_east', '/uploads/profile-pictures/profilePicture-1750579876613-666158695.jpg', '/uploads/videos/video-1749455593844-684120377.mp4', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\n', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\n\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\nإ', 20, 'approved', '', 1, '2025-06-22 08:11:16', '2025-06-22 08:13:07', '2025-06-22 08:13:07'),
(7, 31, 30, 'د. فؤاد بن أحمد عطاء الله عطاء الله', '<EMAIL>', 'Saudi Arabia', 'middle_east', NULL, '/uploads/videos/video-1749455593844-684120377.mp4', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\n', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\n\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\nإ', 20, 'approved', '', 1, '2025-06-28 09:35:06', '2025-06-30 16:09:46', '2025-06-30 16:09:46'),
(8, 32, 40, 'Mostafa', '<EMAIL>', 'kmj88', 'هتهتهخ88', '/uploads/profile-pictures/profilePicture-1751296659474-456427545.jpg', '/uploads/videos/video-1751296637313-963992156.webm', 'ءىتنىسن88', 'تىستشنى88', 28, 'approved', '', 1, '2025-06-30 15:17:39', '2025-06-30 15:18:38', '2025-06-30 15:18:38'),
(9, 32, 40, 'Mostafa', '<EMAIL>', 'kmj88', 'هتهتهخ88', NULL, '/uploads/videos/video-1751296964080-41621749.mp4', 'ءىتنىسن88', 'تىستشنى88', 28, 'approved', '', 1, '2025-06-30 15:22:54', '2025-06-30 15:25:06', '2025-06-30 15:25:06'),
(10, 31, 30, 'د. فؤاد بن أحمد عطاء الله عطاء الله', '<EMAIL>', 'Saudi Arabia', 'middle_east', '/uploads/profile-pictures/profilePicture-1751299920337-793135240.jpg', '/uploads/videos/video-1751299371666-894118536.mp4', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\n', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\n\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\nإ', 20, 'approved', '', 1, '2025-06-30 16:12:00', '2025-06-30 16:12:33', '2025-06-30 16:12:33'),
(11, 31, 30, 'د. فؤاد بن أحمد عطاء الله عطاء الله', '<EMAIL>', 'Saudi Arabia', 'middle_east', NULL, '/uploads/videos/video-1751300213012-672117891.mp4', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\n', 'مرحبا، اسمي فؤاد، أنا من السعودية، أنا حائز على شهادة الدكتوراه والماجستير والبكالوريوس في الشريعة الإسلامية.\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\nأنا حافظ لكتاب الله تعالى، وعندي إجازات في التلاوة يتصل إسناده إلى الرّسول صلى الله عليه وسلم.\r\n\r\nعندي إجازات علمية كثيرة في عدد من الكتب العلمية مثل ثلاثة الأصول، وكتاب التوحيد، والأربعين النووية، والأجرومية، وتحفة الاطفال، والجزرية، وغير ذلك كثير جدا.\r\n\r\nأنا أستطيع أن أدرس جميع المستويات، وجميع الأعمار، المبتدئين، والمتوسطين، والمتقدّمين، للصغار والأطفال والكبار.\r\n\r\nعندي سلاسل كثيرة من الكتب والأدوات التدريسية، فأنا أستطيع أن أدرّس كتاب القاعدة النورانية، والحروف العربية، والقراءة والكتابة والتحدث بالعربية، أو أدرسك كتاب العربية بين يديك، أو كتاب اللغة العربية لطلاب المنح الدوليين في الجامعة الإسلامية في المدينة النبوية.\r\n\r\nوإذا كنت متقدّما ومحترفا، فستدرس عندي كتب التفسير مثل التفسير الميسّر، وكتب العقيدة مثل كتاب التوحيد، والعقيدة الواسطية، وكتب الحديث مثل الأربعين النووية وبلوغ المرام، وكتب الفقه بمذاهبه الأربعة، الحنفي والمالكي والشافعي والحنبلي، وذلك مثل الفقه الميسّر، والملخّص الفقهي، ومنهج السّالكين، وكتب العربية مثل الأجرومية وقطر الندى وألفية ابن مالك، وكذلك الصرف والبلاغة وسائر العلوم العربية الأخرى.\r\n\r\nيمكنني أن أعلّمك العربية، كتابة، وقراءة، ومحادثة، ويمكنني أيضا أن أعلمك تلاوة القرآن الكريم، وكلّ ما يتعلّق بالعربية، مرحبا بك في درسي.\r\n\r\n\r\nأنا الآن أستاذ جامعي في كلية الشريعة منذ عشر سنوات، وأستاذ في اللغة العربية منذ أكثر من عشرين سنة، أتحدث اللغة الإنجليزية بطلاقة، وأتحدث اللغة الفرنسية أيضا.\r\n\r\n\r\n\r\nاحجز درسا معي، لنتحدث سويّا باللغة العربية، وأعدك أنك ستستفيد كثيرا، تعال الآن، ولا تتردّد.\r\n\r\nإ', 20, 'approved', '', 1, '2025-06-30 16:17:33', '2025-06-30 16:18:10', '2025-06-30 16:18:10'),
(12, 32, 40, 'Mostafa', '<EMAIL>', 'kmj88', 'هتهتهخ88', NULL, '/uploads/videos/video-1751296964080-41621749.mp4', 'ءىتنىسن88', 'تىستشنى88', 28, 'approved', '', 1, '2025-07-02 18:15:32', '2025-07-13 16:04:55', '2025-07-13 16:04:55');

-- --------------------------------------------------------

--
-- بنية الجدول `teacher_weekly_breaks`
--

CREATE TABLE `teacher_weekly_breaks` (
  `id` int NOT NULL,
  `teacher_profile_id` int NOT NULL,
  `datetime` datetime NOT NULL COMMENT 'Break date and time in UTC',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `teacher_weekly_breaks`
--

INSERT INTO `teacher_weekly_breaks` (`id`, `teacher_profile_id`, `datetime`, `created_at`) VALUES
(3, 32, '2025-07-22 20:30:00', '2025-07-21 22:34:43'),
(4, 31, '2025-07-23 09:00:00', '2025-07-23 08:47:20'),
(8, 32, '2025-07-24 00:00:00', '2025-07-23 11:52:24'),
(9, 32, '2025-07-25 21:30:00', '2025-07-25 20:51:55'),
(10, 32, '2025-07-26 00:00:00', '2025-07-25 21:25:30');

-- --------------------------------------------------------

--
-- بنية الجدول `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `full_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL,
  `profile_picture_url` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `role` enum('admin','platform_teacher','new_teacher','student') COLLATE utf8mb4_general_ci NOT NULL,
  `gender` enum('male','female') COLLATE utf8mb4_general_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `balance` decimal(10,2) DEFAULT '0.00',
  `reset_code` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `reset_code_expiry` datetime DEFAULT NULL,
  `email_verification_code` varchar(6) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email_verification_expiry` datetime DEFAULT NULL,
  `is_email_verified` tinyint(1) DEFAULT '0',
  `delete_scheduled_at` datetime DEFAULT NULL,
  `status` enum('active','pending_deletion','deleted') COLLATE utf8mb4_general_ci DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `users`
--

INSERT INTO `users` (`id`, `email`, `password`, `full_name`, `profile_picture_url`, `role`, `gender`, `created_at`, `updated_at`, `balance`, `reset_code`, `reset_code_expiry`, `email_verification_code`, `email_verification_expiry`, `is_email_verified`, `delete_scheduled_at`, `status`) VALUES
(1, '<EMAIL>', '$2a$10$DprnlRXxDK3pdmCd3d839OWzTOIStG4NPoUD86f6Hhpc2pMl7Z9f2', 'Admin User', '/uploads/profile-pictures/profile-1742965484801-434729153.jpg', 'admin', 'male', '2025-03-24 14:00:00', '2025-06-30 19:52:29', '0.00', NULL, NULL, NULL, NULL, 1, NULL, 'active'),
(30, '<EMAIL>', '$2a$10$QpKtABsdFJ7OnH2Cro3G6.9qQkVRssi5.HCh4W8PLBfw9bi88zxcW', 'د. فؤاد بن أحمد عطاء الله عطاء الله', 'https://lh3.googleusercontent.com/a/ACg8ocL3nQrktw5n4voNop7TZbc8Mn1e28VdIEfC5UEZhbk0NQ0euHcq=s96-c', 'platform_teacher', 'male', '2025-06-09 07:44:50', '2025-07-01 19:34:51', '0.00', NULL, NULL, NULL, NULL, 1, NULL, 'active'),
(31, '<EMAIL>', '$2a$10$fRxf4U2kehxKInLBZXeHkOrcFFfED4xmRXTzXYR5P1pJ919I3GTcO', 'تخزين 1 تخزين 1', 'https://lh3.googleusercontent.com/a/ACg8ocKOt-fGuZ7rzRWXTEGP8mwMb7vG_huPC-yZ-A3PrvpgNwmfqA=s96-c', 'student', 'male', '2025-06-09 08:07:52', '2025-07-27 11:49:20', '172.00', NULL, NULL, NULL, NULL, 1, NULL, 'active'),
(39, '<EMAIL>', '$2a$10$tT.BtHwzvcKig66rHy5mhORJyTpw4Yzo1HKC4F0YW2zaJt/kdX57u', 'Mostafa Nady', 'https://lh3.googleusercontent.com/a/ACg8ocIWgSSS8aXC-U9HtCN6aTMhYCSE3Co52inBrDP0M55Rv14Dsw=s96-c', 'student', 'male', '2025-06-09 14:48:01', '2025-07-01 19:28:03', '0.00', '376498', '2025-07-01 19:38:03', NULL, NULL, 1, NULL, 'active'),
(40, '<EMAIL>', '$2a$10$xP.fY3jOE3fY8z8c.2BTa.EBKGm6EKu4FOhlgR8WnEcUoKp.110Fm', 'Mostafa', 'https://lh3.googleusercontent.com/a/ACg8ocLFdFabnbIIgjxVB8EWAXjrNArUlJUun4zvlxiGA5N7Vzvobw=s96-c', 'platform_teacher', 'male', '2025-06-09 16:08:50', '2025-07-27 16:12:15', '1000.00', NULL, NULL, NULL, NULL, 1, '2025-07-28 16:12:16', 'pending_deletion'),
(41, '<EMAIL>', '$2a$10$Sgi8y84tcdAhW5i9lrusle4HvJpUGyUOgON8bKKtRQyONQTGWsQWu', 'jsjjsna', 'https://lh3.googleusercontent.com/a/ACg8ocK6-8GKt-CzKSrDYfqm3r2ArPBtMlI1tLxZFfeOwpMD0qsZ_g=s96-c', 'student', 'male', '2025-06-09 17:15:55', '2025-07-27 15:58:07', '9006.00', NULL, NULL, NULL, NULL, 1, '2025-07-28 15:58:08', 'pending_deletion'),
(42, '<EMAIL>', '$2a$10$6lcXr3aQBvWPa1DoepYgleKMmddSVjtkq2bXaHYuMVkozZl2xrP.6', 'nmbohsj', 'https://lh3.googleusercontent.com/a/ACg8ocKndGNpdCOHAOUrrEU1HlwN0P8q7Zt94d7B_hUkTrC6SZ9Kig=s96-c', 'new_teacher', 'female', '2025-06-09 19:24:04', '2025-06-09 19:24:30', '0.00', NULL, NULL, NULL, NULL, 1, NULL, 'active'),
(43, '<EMAIL>', '$2a$10$h.5CYRs.X7mnqVHtwZsm8e4fAEInYjAUkUgG0rqe/VzNA7IAFbaEG', 'asgklll ssgg', 'https://lh3.googleusercontent.com/a/ACg8ocKPAs22gZ2N89BD2G_7IBuOD-8waIukyzq_liJrXUizNgMl9w=s96-c', 'platform_teacher', 'female', '2025-06-10 08:37:50', '2025-06-10 17:39:47', '0.00', NULL, NULL, NULL, NULL, 1, NULL, 'active'),
(44, '<EMAIL>', '$2a$10$YTyNaDLBxbDx5AhlAc9Jr.3Af6kZaRCuzkCYX3s.Ey1/MJrexz0GS', 'أبو عبد الرحمن', '/uploads/profile-pictures/profilePicture-1749971611858-691469087.JPG', 'platform_teacher', 'female', '2025-06-15 07:04:46', '2025-06-15 07:15:32', '0.00', NULL, NULL, NULL, NULL, 1, NULL, 'active'),
(61, '<EMAIL>', '$2a$10$7NN8yEMA9.k.vztXWDGyuubEtkdDtUWupFyX7C4DrQm8APf9Rr1A.', 'إبراهيم فرحاوي', '/uploads/profile-pictures/profilePicture-1751357928902-583088474.jpg', 'platform_teacher', 'female', '2025-06-30 18:06:16', '2025-07-02 06:56:25', '0.00', NULL, NULL, NULL, NULL, 1, NULL, 'active'),
(75, '<EMAIL>', '$2a$10$Wh524LvDw.OrNtDuF2rvkeLft8xafHHoPyLV56g7REHYUYWXDr5/O', 'jskbsb', 'https://lh3.googleusercontent.com/a/ACg8ocLrwO2jk-HsfbPoOYWe0ro2RseWK-xTdIxZM9J3yYfcmyxb4w=s96-c', 'student', 'male', '2025-07-02 20:37:23', '2025-07-02 21:33:50', '97.00', NULL, NULL, NULL, NULL, 1, NULL, 'active'),
(76, '<EMAIL>', '$2a$10$Uj4LKnoROoh3Oy/PraTOTuC72.LHxdSPCHjc8o4iwcHd3Gt35OGsm', 'عبدالرحمن المخلف', 'https://lh3.googleusercontent.com/a/ACg8ocIHkVIC4cDfQk1OnsFZLz1D_ZrMmHh71HEjeU15vTjbgvx1lnUU=s96-c', 'student', 'male', '2025-07-25 19:10:58', '2025-07-25 19:11:19', '0.00', NULL, NULL, NULL, NULL, 1, NULL, 'active');

-- --------------------------------------------------------

--
-- بنية الجدول `user_delete_requests`
--

CREATE TABLE `user_delete_requests` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `delete_code` varchar(6) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `withdrawal_requests`
--

CREATE TABLE `withdrawal_requests` (
  `id` int NOT NULL,
  `teacher_id` int NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `paypal_email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('pending','processing','completed','failed','cancelled','otp_pending') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'otp_pending',
  `payout_batch_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payout_item_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `admin_notes` text COLLATE utf8mb4_unicode_ci,
  `processed_by` int DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `otp_code` varchar(6) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `otp_expiry` datetime DEFAULT NULL,
  `is_otp_verified` tinyint(1) DEFAULT '0',
  `otp_attempts` int DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `withdrawal_requests`
--

INSERT INTO `withdrawal_requests` (`id`, `teacher_id`, `amount`, `paypal_email`, `status`, `payout_batch_id`, `payout_item_id`, `admin_notes`, `processed_by`, `processed_at`, `created_at`, `updated_at`, `otp_code`, `otp_expiry`, `is_otp_verified`, `otp_attempts`) VALUES
(1, 40, '10.00', '<EMAIL>', 'completed', 'BATCH_1749827351791', 'ITEM_1749827351791_1', 'Approved and processed', 1, '2025-06-13 15:09:11', '2025-06-13 15:08:23', '2025-06-13 15:09:11', NULL, NULL, 0, 0),
(2, 40, '10.00', '<EMAIL>', 'cancelled', NULL, NULL, NULL, NULL, NULL, '2025-06-14 15:30:00', '2025-06-14 15:31:50', '684711', '2025-06-14 15:45:00', 1, 0),
(3, 40, '10.00', '<EMAIL>', 'completed', 'BATCH_1749915429303', 'ITEM_1749915429303_3', 'Approved and processed', 1, '2025-06-14 15:37:09', '2025-06-14 15:31:57', '2025-06-14 15:37:09', '673776', '2025-06-14 15:46:57', 1, 0),
(7, 40, '10.00', '<EMAIL>', 'cancelled', NULL, NULL, 'Cancelled by user', NULL, NULL, '2025-06-14 16:05:00', '2025-06-14 16:05:09', '462145', '2025-06-14 16:20:00', 0, 1),
(8, 40, '10.00', '<EMAIL>', 'cancelled', NULL, NULL, 'Cancelled by user', NULL, NULL, '2025-06-14 16:06:18', '2025-06-14 16:06:28', '727086', '2025-06-14 16:21:18', 0, 1),
(9, 40, '100.00', '<EMAIL>', 'cancelled', NULL, NULL, 'Cancelled by user', NULL, NULL, '2025-06-14 16:11:12', '2025-06-14 16:11:21', '663296', '2025-06-14 16:26:13', 0, 1);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_earnings`
--
ALTER TABLE `admin_earnings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_meeting_id` (`meeting_id`),
  ADD KEY `idx_teacher_id` (`teacher_id`),
  ADD KEY `idx_student_id` (`student_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_admin_earnings_date` (`created_at`),
  ADD KEY `idx_admin_earnings_teacher_date` (`teacher_id`,`created_at`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `teacher_profile_id` (`teacher_profile_id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `idx_bookings_status` (`status`),
  ADD KEY `idx_bookings_datetime_status` (`datetime`,`status`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `contact_messages`
--
ALTER TABLE `contact_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_contact_messages_user_id` (`user_id`),
  ADD KEY `idx_contact_messages_status` (`status`),
  ADD KEY `idx_contact_messages_created_at` (`created_at`);

--
-- Indexes for table `conversations`
--
ALTER TABLE `conversations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `teacher_id` (`teacher_id`);

--
-- Indexes for table `languages`
--
ALTER TABLE `languages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `meetings`
--
ALTER TABLE `meetings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `teacher_id` (`teacher_id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `idx_videosdk_meeting_id` (`videosdk_meeting_id`);

--
-- Indexes for table `meeting_issues`
--
ALTER TABLE `meeting_issues`
  ADD PRIMARY KEY (`id`),
  ADD KEY `meeting_id` (`meeting_id`),
  ADD KEY `booking_id` (`booking_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_meeting_booking_user` (`meeting_id`,`booking_id`,`user_id`);

--
-- Indexes for table `meeting_sessions`
--
ALTER TABLE `meeting_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_meeting_id` (`meeting_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_join_time` (`join_time`);

--
-- Indexes for table `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `conversation_id` (`conversation_id`),
  ADD KEY `sender_id` (`sender_id`),
  ADD KEY `recipient_id` (`recipient_id`);

--
-- Indexes for table `notes`
--
ALTER TABLE `notes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_notes_teacher` (`teacher_id`),
  ADD KEY `fk_notes_student` (`student_id`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `teacher_profile_id` (`teacher_profile_id`),
  ADD KEY `student_id` (`student_id`),
  ADD KEY `idx_payments_payment_id` (`payment_id`),
  ADD KEY `idx_payments_type` (`type`),
  ADD KEY `idx_payments_method` (`payment_method`);

--
-- Indexes for table `reviews`
--
ALTER TABLE `reviews`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_review` (`teacher_profile_id`,`student_id`),
  ADD KEY `student_id` (`student_id`);

--
-- Indexes for table `review_replies`
--
ALTER TABLE `review_replies`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_reply` (`review_id`),
  ADD KEY `teacher_id` (`teacher_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `key` (`key`),
  ADD UNIQUE KEY `unique_key` (`key`);

--
-- Indexes for table `student_completion_data`
--
ALTER TABLE `student_completion_data`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indexes for table `teacher_categories`
--
ALTER TABLE `teacher_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `teacher_category_uniq` (`teacher_profile_id`,`category_id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `teacher_languages`
--
ALTER TABLE `teacher_languages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `teacher_language_uniq` (`teacher_profile_id`,`language_id`),
  ADD KEY `language_id` (`language_id`);

--
-- Indexes for table `teacher_profiles`
--
ALTER TABLE `teacher_profiles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indexes for table `teacher_profile_updates`
--
ALTER TABLE `teacher_profile_updates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `idx_teacher_profile_id` (`teacher_profile_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `teacher_weekly_breaks`
--
ALTER TABLE `teacher_weekly_breaks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_teacher_break` (`teacher_profile_id`,`datetime`),
  ADD KEY `idx_teacher_profile_datetime` (`teacher_profile_id`,`datetime`),
  ADD KEY `idx_datetime` (`datetime`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_users_delete_scheduled` (`delete_scheduled_at`),
  ADD KEY `idx_users_status` (`status`);

--
-- Indexes for table `user_delete_requests`
--
ALTER TABLE `user_delete_requests`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_delete` (`user_id`);

--
-- Indexes for table `withdrawal_requests`
--
ALTER TABLE `withdrawal_requests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_teacher_id` (`teacher_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `withdrawal_requests_admin_fk` (`processed_by`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_earnings`
--
ALTER TABLE `admin_earnings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=212;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `contact_messages`
--
ALTER TABLE `contact_messages`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `conversations`
--
ALTER TABLE `conversations`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `languages`
--
ALTER TABLE `languages`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `meeting_issues`
--
ALTER TABLE `meeting_issues`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=247;

--
-- AUTO_INCREMENT for table `meeting_sessions`
--
ALTER TABLE `meeting_sessions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT for table `messages`
--
ALTER TABLE `messages`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=63;

--
-- AUTO_INCREMENT for table `notes`
--
ALTER TABLE `notes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=221;

--
-- AUTO_INCREMENT for table `reviews`
--
ALTER TABLE `reviews`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `review_replies`
--
ALTER TABLE `review_replies`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `student_completion_data`
--
ALTER TABLE `student_completion_data`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `teacher_categories`
--
ALTER TABLE `teacher_categories`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=399;

--
-- AUTO_INCREMENT for table `teacher_languages`
--
ALTER TABLE `teacher_languages`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=300;

--
-- AUTO_INCREMENT for table `teacher_profiles`
--
ALTER TABLE `teacher_profiles`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `teacher_profile_updates`
--
ALTER TABLE `teacher_profile_updates`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `teacher_weekly_breaks`
--
ALTER TABLE `teacher_weekly_breaks`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=77;

--
-- AUTO_INCREMENT for table `user_delete_requests`
--
ALTER TABLE `user_delete_requests`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `withdrawal_requests`
--
ALTER TABLE `withdrawal_requests`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- قيود الجداول المحفوظة
--

--
-- القيود للجدول `admin_earnings`
--
ALTER TABLE `admin_earnings`
  ADD CONSTRAINT `admin_earnings_ibfk_1` FOREIGN KEY (`meeting_id`) REFERENCES `meetings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `admin_earnings_ibfk_2` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `admin_earnings_ibfk_3` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_ibfk_1` FOREIGN KEY (`teacher_profile_id`) REFERENCES `teacher_profiles` (`id`),
  ADD CONSTRAINT `bookings_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`);

--
-- القيود للجدول `categories`
--
ALTER TABLE `categories`
  ADD CONSTRAINT `categories_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- القيود للجدول `contact_messages`
--
ALTER TABLE `contact_messages`
  ADD CONSTRAINT `contact_messages_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `conversations`
--
ALTER TABLE `conversations`
  ADD CONSTRAINT `conversations_student_fk` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `conversations_teacher_fk` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `languages`
--
ALTER TABLE `languages`
  ADD CONSTRAINT `languages_created_by_fk` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- القيود للجدول `meetings`
--
ALTER TABLE `meetings`
  ADD CONSTRAINT `meetings_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `meetings_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- القيود للجدول `meeting_issues`
--
ALTER TABLE `meeting_issues`
  ADD CONSTRAINT `fk_meeting_issues_booking` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `meeting_issues_meeting_id_foreign` FOREIGN KEY (`meeting_id`) REFERENCES `meetings` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `meeting_issues_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- القيود للجدول `meeting_sessions`
--
ALTER TABLE `meeting_sessions`
  ADD CONSTRAINT `meeting_sessions_meeting_fk` FOREIGN KEY (`meeting_id`) REFERENCES `meetings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `meeting_sessions_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `messages`
--
ALTER TABLE `messages`
  ADD CONSTRAINT `messages_conversation_fk` FOREIGN KEY (`conversation_id`) REFERENCES `conversations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `messages_recipient_fk` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `messages_sender_fk` FOREIGN KEY (`sender_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `notes`
--
ALTER TABLE `notes`
  ADD CONSTRAINT `fk_notes_student` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_notes_teacher` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_student_fk` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_teacher_fk` FOREIGN KEY (`teacher_profile_id`) REFERENCES `teacher_profiles` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `reviews`
--
ALTER TABLE `reviews`
  ADD CONSTRAINT `reviews_ibfk_1` FOREIGN KEY (`teacher_profile_id`) REFERENCES `teacher_profiles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `reviews_ibfk_2` FOREIGN KEY (`student_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `review_replies`
--
ALTER TABLE `review_replies`
  ADD CONSTRAINT `review_replies_ibfk_1` FOREIGN KEY (`review_id`) REFERENCES `reviews` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `review_replies_ibfk_2` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `student_completion_data`
--
ALTER TABLE `student_completion_data`
  ADD CONSTRAINT `student_completion_data_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `teacher_categories`
--
ALTER TABLE `teacher_categories`
  ADD CONSTRAINT `teacher_categories_ibfk_1` FOREIGN KEY (`teacher_profile_id`) REFERENCES `teacher_profiles` (`id`),
  ADD CONSTRAINT `teacher_categories_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`);

--
-- القيود للجدول `teacher_languages`
--
ALTER TABLE `teacher_languages`
  ADD CONSTRAINT `teacher_languages_lang_fk` FOREIGN KEY (`language_id`) REFERENCES `languages` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `teacher_languages_teacher_fk` FOREIGN KEY (`teacher_profile_id`) REFERENCES `teacher_profiles` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `teacher_profiles`
--
ALTER TABLE `teacher_profiles`
  ADD CONSTRAINT `teacher_profiles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- القيود للجدول `teacher_profile_updates`
--
ALTER TABLE `teacher_profile_updates`
  ADD CONSTRAINT `teacher_profile_updates_ibfk_1` FOREIGN KEY (`teacher_profile_id`) REFERENCES `teacher_profiles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `teacher_profile_updates_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `teacher_profile_updates_ibfk_3` FOREIGN KEY (`admin_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- القيود للجدول `teacher_weekly_breaks`
--
ALTER TABLE `teacher_weekly_breaks`
  ADD CONSTRAINT `teacher_weekly_breaks_ibfk_1` FOREIGN KEY (`teacher_profile_id`) REFERENCES `teacher_profiles` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `user_delete_requests`
--
ALTER TABLE `user_delete_requests`
  ADD CONSTRAINT `user_delete_requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- القيود للجدول `withdrawal_requests`
--
ALTER TABLE `withdrawal_requests`
  ADD CONSTRAINT `withdrawal_requests_admin_fk` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `withdrawal_requests_teacher_fk` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
