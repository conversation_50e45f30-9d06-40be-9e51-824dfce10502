import React, { useState, useEffect } from 'react';
import {
  Container,
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Avatar,
  useTheme,
  alpha,
  CircularProgress,
  Alert
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import {
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import axios from '../../utils/axios';
import Layout from '../../components/Layout';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';
import { useAuth } from '../../contexts/AuthContext';
import { convertFromDatabaseTime, getCurrentTimeInTimezone, formatDateInStudentTimezone } from '../../utils/timezone';
import moment from 'moment-timezone';

const StudentMeetings = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';
  const [meetings, setMeetings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());
  const { currentUser } = useAuth();
  const [studentProfile, setStudentProfile] = useState(null);

  useEffect(() => {
    if (currentUser) {
      fetchMeetings();
      fetchStudentProfile();

      // Update current time every second
      const timeInterval = setInterval(() => {
        setCurrentTime(new Date());
      }, 1000);

      // Fetch meetings every minute to update statuses
      const meetingsInterval = setInterval(fetchMeetings, 60000);

      return () => {
        clearInterval(timeInterval);
        clearInterval(meetingsInterval);
      };
    }
  }, [currentUser]);

  const fetchStudentProfile = async () => {
    try {
      const { data } = await axios.get('/api/students/profile');
      if (data.success && data.profile) {
        setStudentProfile(data.profile);
      }
    } catch (error) {
      console.error('Error fetching student profile:', error);
    }
  };

  const fetchMeetings = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await axios.get('/meetings/student');
      // Check if response.data is an array or has a meetings property
      if (Array.isArray(response.data)) {
        setMeetings(response.data);
      } else if (response.data && Array.isArray(response.data.meetings)) {
        setMeetings(response.data.meetings);
      } else {
        console.error('Invalid meetings data format:', response.data);
        setMeetings([]);
      }
    } catch (error) {
      console.error('Error fetching meetings:', error);
      setError(error.response?.data?.message || t('meetings.fetchError'));
      setMeetings([]);
    } finally {
      setLoading(false);
    }
  };

  // Format meeting date for display
  const formatMeetingDate = (meetingDate) => {
    if (!studentProfile?.timezone) {
      return format(new Date(meetingDate), 'PPP', {
        locale: isRtl ? ar : enUS
      });
    }
    const formattedDate = formatDateInStudentTimezone(meetingDate, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
    const momentDate = moment(formattedDate, 'YYYY-MM-DD HH:mm:ss');
    return momentDate.format('dddd, MMMM D, YYYY');
  };

  // Format meeting time for display
  const formatMeetingTime = (meetingDate) => {
    if (!studentProfile?.timezone) {
      return format(new Date(meetingDate), 'p', {
        locale: isRtl ? ar : enUS
      });
    }
    const formattedDate = formatDateInStudentTimezone(meetingDate, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
    const momentDate = moment(formattedDate, 'YYYY-MM-DD HH:mm:ss');
    return momentDate.format('h:mm A');
  };

  // Convert meeting date to student's timezone
  const getMeetingDateInStudentTimezone = (meetingDate) => {
    if (!studentProfile || !studentProfile.timezone) {
      return new Date(meetingDate);
    }
    return convertFromDatabaseTime(meetingDate, studentProfile.timezone);
  };

  // Format meeting date directly in student's timezone
  const formatMeetingDateInStudentTimezone = (meetingDate, formatString) => {
    if (!studentProfile || !studentProfile.timezone) {
      return format(new Date(meetingDate), formatString, {
        locale: i18n.language === 'ar' ? ar : enUS
      });
    }

    // Use moment to format directly in student's timezone
    const formattedDate = formatDateInStudentTimezone(meetingDate, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
    const momentDate = moment(formattedDate, 'YYYY-MM-DD HH:mm:ss');

    // Convert moment format to date-fns format
    let dateFnsFormat = formatString;
    if (formatString === 'PPpp') {
      return momentDate.format('dddd, MMMM D, YYYY [at] h:mm A');
    }

    return momentDate.format('MMMM D, YYYY [at] h:mm A');
  };

  // Get current time in student's timezone
  const getCurrentTimeInStudentTimezone = () => {
    if (!studentProfile || !studentProfile.timezone) {
      return new Date();
    }
    return getCurrentTimeInTimezone(studentProfile.timezone);
  };

  const getMeetingStatus = (meeting) => {
    const meetingStartTime = new Date(meeting.meeting_date);
    const meetingEndTime = new Date(meeting.meeting_date);
    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + meeting.duration);
    const now = new Date();

    if (meeting.status === 'cancelled') {
      return 'cancelled';
    }

    if (now >= meetingStartTime && now < meetingEndTime) {
      return 'ongoing';
    }

    if (now >= meetingEndTime) {
      return 'completed';
    }

    return 'scheduled';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ongoing':
        return 'success';
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'error';
      default:
        return 'primary';
    }
  };

  // Get status chip color
  const getStatusChipColor = (status) => {
    switch (status) {
      case 'ongoing':
        return 'success';
      case 'completed':
        return 'default';
      case 'cancelled':
        return 'error';
      default:
        return 'primary';
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
          {/* Header Section */}
          <Paper elevation={3} sx={{ p: 3, mb: 4, bgcolor: 'primary.main', color: 'white' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
              <Box>
                <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                  {t('meetings.myMeetings')}
                </Typography>
                <Typography variant="body1" sx={{ opacity: 0.9 }}>
                  {t('meetings.description')}
                </Typography>
              </Box>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  📅 {studentProfile?.timezone ? (
                    moment(formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('dddd, MMMM D, YYYY')
                  ) : (
                    format(new Date(), 'PPP', {
                      locale: isRtl ? ar : enUS
                    })
                  )}
                </Typography>
                {studentProfile?.timezone && (
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    {studentProfile.timezone}
                  </Typography>
                )}
              </Box>
            </Box>
          </Paper>

          {/* Loading State */}
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ mb: 4 }}>
              {error}
            </Alert>
          ) : (
            /* Meetings Table */
            <TableContainer component={Paper} elevation={2} sx={{ borderRadius: 2, overflow: 'hidden' }}>
              <Table sx={{ minWidth: 650 }}>
                <TableHead>
                  <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1) }}>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <PersonIcon color="primary" />
                        {t('meetings.teacher')}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CalendarIcon color="primary" />
                        {t('meetings.date')}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <TimeIcon color="primary" />
                        {t('meetings.time')}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <TimeIcon color="primary" />
                        {t('meetings.duration')}
                      </Box>
                    </TableCell>
                    <TableCell sx={{ fontWeight: 'bold', fontSize: '1rem' }}>
                      {t('bookings.status')}
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {meetings.map((meeting, index) => (
                    <TableRow
                      key={meeting.id}
                      sx={{
                        '&:nth-of-type(odd)': {
                          bgcolor: alpha(theme.palette.grey[100], 0.5)
                        },
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.05),
                          transform: 'scale(1.001)',
                          transition: 'all 0.2s ease-in-out'
                        },
                        cursor: 'pointer'
                      }}
                    >
                      {/* Teacher Column */}
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar
                            src={meeting.teacher_picture}
                            alt={meeting.teacher_name}
                            sx={{ width: 48, height: 48 }}
                          />
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
                              {meeting.teacher_name}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>

                      {/* Date Column */}
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {formatMeetingDate(meeting.meeting_date)}
                        </Typography>
                      </TableCell>

                      {/* Time Column */}
                      <TableCell>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {formatMeetingTime(meeting.meeting_date)}
                        </Typography>
                      </TableCell>

                      {/* Duration Column */}
                      <TableCell>
                        <Typography variant="body1">
                          {meeting.duration} {t('meetings.minutes')}
                        </Typography>
                      </TableCell>

                      {/* Status Column */}
                      <TableCell>
                        <Chip
                          label={t(`meetings.status.${getMeetingStatus(meeting)}`)}
                          color={getStatusChipColor(getMeetingStatus(meeting))}
                          variant="filled"
                          sx={{ fontWeight: 'medium' }}
                        />
                      </TableCell>
                    </TableRow>
                  ))}

                  {/* Empty State */}
                  {meetings.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={5}>
                        <Box sx={{ textAlign: 'center', py: 6 }}>
                          <Typography variant="h6" color="text.secondary" gutterBottom>
                            📅 {t('meetings.noMeetings')}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {t('meetings.noMeetingsDescription')}
                          </Typography>
                        </Box>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </ProfileCompletionAlert>
      </Container>
    </Layout>
  );
};

export default StudentMeetings;
