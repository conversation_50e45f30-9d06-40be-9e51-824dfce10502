import React from 'react';
import { Box, Container, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

const AuthenticatedFooter = () => {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.language === 'ar';

  return (
    <Box
      component="footer"
      sx={{
        bgcolor: 'primary.dark',
        color: 'white',
        py: { xs: 1, sm: 1.5 },
        mt: 'auto',
        direction: isRtl ? 'rtl' : 'ltr',
        borderTop: '1px solid rgba(255,255,255,0.1)'
      }}
    >
      <Container maxWidth="xl">
        <Typography
          variant="body2"
          align="center"
          sx={{
            fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
            fontFamily: 'Tajawal, sans-serif',
            fontWeight: 500
          }}
        >
          {t('footer.copyright')}
        </Typography>
      </Container>
    </Box>
  );
};

export default AuthenticatedFooter;
