[{"D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js": "1", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js": "2", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js": "3", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js": "4", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js": "5", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js": "6", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js": "7", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js": "8", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js": "9", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js": "10", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js": "11", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js": "12", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js": "13", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js": "14", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js": "15", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js": "16", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js": "17", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js": "18", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js": "19", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js": "20", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js": "21", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js": "22", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js": "23", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js": "24", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js": "25", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js": "26", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js": "27", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js": "28", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js": "29", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js": "30", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js": "31", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js": "32", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js": "33", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js": "34", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js": "35", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js": "36", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js": "37", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js": "38", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js": "39", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js": "40", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js": "41", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js": "42", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js": "43", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js": "44", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js": "45", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js": "46", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js": "47", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js": "48", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js": "49", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js": "50", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js": "51", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js": "52", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js": "53", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js": "54", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js": "55", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js": "56", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js": "57", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js": "58", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js": "59", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js": "60", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js": "61", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js": "62", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js": "63", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js": "64", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js": "65", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js": "66", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js": "67", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js": "68", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js": "69", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js": "70", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js": "71", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js": "72", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js": "73", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js": "74", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js": "75", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js": "76", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js": "77", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js": "78", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js": "79", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js": "80", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js": "81", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js": "82", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js": "83", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js": "84", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js": "85", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js": "86", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js": "87", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js": "88", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js": "89", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js": "90", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js": "91", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js": "92", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js": "93", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js": "94", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js": "95", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js": "96", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js": "97", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js": "98", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js": "99", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js": "100", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js": "101", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js": "102", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js": "103", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js": "104", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js": "105", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js": "106", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js": "107", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js": "108", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js": "109", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js": "110", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js": "111", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js": "112", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js": "113", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx": "114", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx": "115", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js": "116", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js": "117", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js": "118", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js": "119", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js": "120", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js": "121", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js": "122", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js": "123", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js": "124", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js": "125", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js": "126", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js": "127", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js": "128", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx": "129", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx": "130", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx": "131", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx": "132", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx": "133", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx": "134", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx": "135", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx": "136", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx": "137", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx": "138", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx": "139", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx": "140", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js": "141", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js": "142", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js": "143", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js": "144", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js": "145", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js": "146", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js": "147", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js": "148", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js": "149", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js": "150", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js": "151", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js": "152", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js": "153", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js": "154", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\RescheduleDialog.js": "155", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\UserStatusChecker.js": "156", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\ProtectedRoute.js": "157", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\userStatusHandler.js": "158", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axiosConfig.js": "159", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\DeletedUsers.js": "160"}, {"size": 641, "mtime": 1742576862818, "results": "161", "hashOfConfig": "162"}, {"size": 25389, "mtime": 1753990428196, "results": "163", "hashOfConfig": "162"}, {"size": 1456, "mtime": 1746732811228, "results": "164", "hashOfConfig": "162"}, {"size": 235918, "mtime": 1754165360801, "results": "165", "hashOfConfig": "162"}, {"size": 362, "mtime": 1742419325522, "results": "166", "hashOfConfig": "162"}, {"size": 1029, "mtime": 1744808128594, "results": "167", "hashOfConfig": "162"}, {"size": 325, "mtime": 1743221258725, "results": "168", "hashOfConfig": "162"}, {"size": 12269, "mtime": 1753977290977, "results": "169", "hashOfConfig": "162"}, {"size": 7654, "mtime": 1751398254796, "results": "170", "hashOfConfig": "162"}, {"size": 3432, "mtime": 1751285065551, "results": "171", "hashOfConfig": "162"}, {"size": 3326, "mtime": 1751040721930, "results": "172", "hashOfConfig": "162"}, {"size": 943, "mtime": 1749309630963, "results": "173", "hashOfConfig": "162"}, {"size": 24344, "mtime": 1753992000192, "results": "174", "hashOfConfig": "162"}, {"size": 36606, "mtime": 1753386956890, "results": "175", "hashOfConfig": "162"}, {"size": 4768, "mtime": 1751278844207, "results": "176", "hashOfConfig": "162"}, {"size": 10446, "mtime": 1751293932223, "results": "177", "hashOfConfig": "162"}, {"size": 28837, "mtime": 1753731053731, "results": "178", "hashOfConfig": "162"}, {"size": 31044, "mtime": 1753897067901, "results": "179", "hashOfConfig": "162"}, {"size": 15736, "mtime": 1751486831358, "results": "180", "hashOfConfig": "162"}, {"size": 15894, "mtime": 1749479492637, "results": "181", "hashOfConfig": "162"}, {"size": 15959, "mtime": 1749479507046, "results": "182", "hashOfConfig": "162"}, {"size": 18542, "mtime": 1753825742995, "results": "183", "hashOfConfig": "162"}, {"size": 7902, "mtime": 1747334952040, "results": "184", "hashOfConfig": "162"}, {"size": 10135, "mtime": 1747335101462, "results": "185", "hashOfConfig": "162"}, {"size": 7229, "mtime": 1747334912778, "results": "186", "hashOfConfig": "162"}, {"size": 9247, "mtime": 1742785011813, "results": "187", "hashOfConfig": "162"}, {"size": 13648, "mtime": 1751303763967, "results": "188", "hashOfConfig": "162"}, {"size": 29493, "mtime": 1744817248860, "results": "189", "hashOfConfig": "162"}, {"size": 13701, "mtime": 1744022069977, "results": "190", "hashOfConfig": "162"}, {"size": 12085, "mtime": 1746733346280, "results": "191", "hashOfConfig": "162"}, {"size": 11112, "mtime": 1744799843410, "results": "192", "hashOfConfig": "162"}, {"size": 30331, "mtime": 1751480317262, "results": "193", "hashOfConfig": "162"}, {"size": 6641, "mtime": 1742964048040, "results": "194", "hashOfConfig": "162"}, {"size": 6618, "mtime": 1744022276817, "results": "195", "hashOfConfig": "162"}, {"size": 22625, "mtime": 1751041436226, "results": "196", "hashOfConfig": "162"}, {"size": 11838, "mtime": 1749581734964, "results": "197", "hashOfConfig": "162"}, {"size": 7554, "mtime": 1744093753745, "results": "198", "hashOfConfig": "162"}, {"size": 19961, "mtime": 1746026434563, "results": "199", "hashOfConfig": "162"}, {"size": 10379, "mtime": 1749855699542, "results": "200", "hashOfConfig": "162"}, {"size": 8520, "mtime": 1753914584143, "results": "201", "hashOfConfig": "162"}, {"size": 12485, "mtime": 1749828882192, "results": "202", "hashOfConfig": "162"}, {"size": 12535, "mtime": 1749506609302, "results": "203", "hashOfConfig": "162"}, {"size": 24173, "mtime": 1750508377674, "results": "204", "hashOfConfig": "162"}, {"size": 6673, "mtime": 1749331131513, "results": "205", "hashOfConfig": "162"}, {"size": 35597, "mtime": 1749498641458, "results": "206", "hashOfConfig": "162"}, {"size": 32081, "mtime": 1749321424781, "results": "207", "hashOfConfig": "162"}, {"size": 10975, "mtime": 1749337833332, "results": "208", "hashOfConfig": "162"}, {"size": 33527, "mtime": 1749322312984, "results": "209", "hashOfConfig": "162"}, {"size": 15465, "mtime": 1750848677426, "results": "210", "hashOfConfig": "162"}, {"size": 37169, "mtime": 1754093690496, "results": "211", "hashOfConfig": "162"}, {"size": 15303, "mtime": 1750870211486, "results": "212", "hashOfConfig": "162"}, {"size": 66422, "mtime": 1754057882118, "results": "213", "hashOfConfig": "162"}, {"size": 12480, "mtime": 1752698192448, "results": "214", "hashOfConfig": "162"}, {"size": 8011, "mtime": 1744093709651, "results": "215", "hashOfConfig": "162"}, {"size": 4974, "mtime": 1744028374118, "results": "216", "hashOfConfig": "162"}, {"size": 17010, "mtime": 1749916559907, "results": "217", "hashOfConfig": "162"}, {"size": 10864, "mtime": 1746026292144, "results": "218", "hashOfConfig": "162"}, {"size": 21665, "mtime": 1750041191553, "results": "219", "hashOfConfig": "162"}, {"size": 35461, "mtime": 1753977354777, "results": "220", "hashOfConfig": "162"}, {"size": 4214, "mtime": 1742913454633, "results": "221", "hashOfConfig": "162"}, {"size": 8524, "mtime": 1749491950143, "results": "222", "hashOfConfig": "162"}, {"size": 35379, "mtime": 1753897018159, "results": "223", "hashOfConfig": "162"}, {"size": 38672, "mtime": 1754002003436, "results": "224", "hashOfConfig": "162"}, {"size": 5814, "mtime": 1744024131309, "results": "225", "hashOfConfig": "162"}, {"size": 15814, "mtime": 1749589859006, "results": "226", "hashOfConfig": "162"}, {"size": 5024, "mtime": 1746026315740, "results": "227", "hashOfConfig": "162"}, {"size": 11083, "mtime": 1746026269030, "results": "228", "hashOfConfig": "162"}, {"size": 2721, "mtime": 1750156638150, "results": "229", "hashOfConfig": "162"}, {"size": 10320, "mtime": 1754097440961, "results": "230", "hashOfConfig": "162"}, {"size": 13724, "mtime": 1754098168403, "results": "231", "hashOfConfig": "162"}, {"size": 14721, "mtime": 1749819465295, "results": "232", "hashOfConfig": "162"}, {"size": 46095, "mtime": 1754096505139, "results": "233", "hashOfConfig": "162"}, {"size": 49882, "mtime": 1754165977893, "results": "234", "hashOfConfig": "162"}, {"size": 57051, "mtime": 1746744695128, "results": "235", "hashOfConfig": "162"}, {"size": 14257, "mtime": 1749585929387, "results": "236", "hashOfConfig": "162"}, {"size": 27302, "mtime": 1754166418480, "results": "237", "hashOfConfig": "162"}, {"size": 3262, "mtime": 1753034610621, "results": "238", "hashOfConfig": "162"}, {"size": 7726, "mtime": 1753129936379, "results": "239", "hashOfConfig": "162"}, {"size": 56521, "mtime": 1753443150818, "results": "240", "hashOfConfig": "162"}, {"size": 5549, "mtime": 1749490160444, "results": "241", "hashOfConfig": "162"}, {"size": 25593, "mtime": 1753442449956, "results": "242", "hashOfConfig": "162"}, {"size": 3697, "mtime": 1749774472728, "results": "243", "hashOfConfig": "162"}, {"size": 15585, "mtime": 1754005184978, "results": "244", "hashOfConfig": "162"}, {"size": 7971, "mtime": 1746023858919, "results": "245", "hashOfConfig": "162"}, {"size": 2877, "mtime": 1747400985303, "results": "246", "hashOfConfig": "162"}, {"size": 2530, "mtime": 1750507091156, "results": "247", "hashOfConfig": "162"}, {"size": 1488, "mtime": 1742858201413, "results": "248", "hashOfConfig": "162"}, {"size": 42233, "mtime": 1753723325313, "results": "249", "hashOfConfig": "162"}, {"size": 153, "mtime": 1742445554097, "results": "250", "hashOfConfig": "162"}, {"size": 7365, "mtime": 1750226849238, "results": "251", "hashOfConfig": "162"}, {"size": 3181, "mtime": 1744798236742, "results": "252", "hashOfConfig": "162"}, {"size": 2614, "mtime": 1750185563183, "results": "253", "hashOfConfig": "162"}, {"size": 3068, "mtime": 1751036509706, "results": "254", "hashOfConfig": "162"}, {"size": 11091, "mtime": 1752283251033, "results": "255", "hashOfConfig": "162"}, {"size": 20466, "mtime": 1750170525751, "results": "256", "hashOfConfig": "162"}, {"size": 2219, "mtime": 1750163211180, "results": "257", "hashOfConfig": "162"}, {"size": 836, "mtime": 1750184890252, "results": "258", "hashOfConfig": "162"}, {"size": 4725, "mtime": 1750162079006, "results": "259", "hashOfConfig": "162"}, {"size": 11306, "mtime": 1750569740310, "results": "260", "hashOfConfig": "162"}, {"size": 18054, "mtime": 1750163343664, "results": "261", "hashOfConfig": "162"}, {"size": 2950, "mtime": 1750162078997, "results": "262", "hashOfConfig": "162"}, {"size": 938, "mtime": 1750162079095, "results": "263", "hashOfConfig": "162"}, {"size": 211, "mtime": 1750162079017, "results": "264", "hashOfConfig": "162"}, {"size": 6398, "mtime": 1753615402623, "results": "265", "hashOfConfig": "162"}, {"size": 225, "mtime": 1750162079019, "results": "266", "hashOfConfig": "162"}, {"size": 4612, "mtime": 1750486746007, "results": "267", "hashOfConfig": "162"}, {"size": 831, "mtime": 1750162079019, "results": "268", "hashOfConfig": "162"}, {"size": 4793, "mtime": 1750163289799, "results": "269", "hashOfConfig": "162"}, {"size": 12075, "mtime": 1750163272446, "results": "270", "hashOfConfig": "162"}, {"size": 6897, "mtime": 1750163306463, "results": "271", "hashOfConfig": "162"}, {"size": 3348, "mtime": 1750162079002, "results": "272", "hashOfConfig": "162"}, {"size": 1056, "mtime": 1750162079048, "results": "273", "hashOfConfig": "162"}, {"size": 1842, "mtime": 1750162079037, "results": "274", "hashOfConfig": "162"}, {"size": 3656, "mtime": 1750162079039, "results": "275", "hashOfConfig": "162"}, {"size": 4224, "mtime": 1750162079030, "results": "276", "hashOfConfig": "162"}, {"size": 1616, "mtime": 1750162079025, "results": "277", "hashOfConfig": "162"}, {"size": 937, "mtime": 1750162079029, "results": "278", "hashOfConfig": "162"}, {"size": 1738, "mtime": 1750162079038, "results": "279", "hashOfConfig": "162"}, {"size": 662, "mtime": 1750162079047, "results": "280", "hashOfConfig": "162"}, {"size": 550, "mtime": 1750162079020, "results": "281", "hashOfConfig": "162"}, {"size": 519, "mtime": 1750162079047, "results": "282", "hashOfConfig": "162"}, {"size": 1921, "mtime": 1750265054387, "results": "283", "hashOfConfig": "162"}, {"size": 577, "mtime": 1750162079039, "results": "284", "hashOfConfig": "162"}, {"size": 1773, "mtime": 1750162079025, "results": "285", "hashOfConfig": "162"}, {"size": 503, "mtime": 1750523557957, "results": "286", "hashOfConfig": "162"}, {"size": 6750, "mtime": 1750251645533, "results": "287", "hashOfConfig": "162"}, {"size": 4294, "mtime": 1753615413502, "results": "288", "hashOfConfig": "162"}, {"size": 3765, "mtime": 1750163361559, "results": "289", "hashOfConfig": "162"}, {"size": 615, "mtime": 1750162079034, "results": "290", "hashOfConfig": "162"}, {"size": 853, "mtime": 1750162079036, "results": "291", "hashOfConfig": "162"}, {"size": 857, "mtime": 1750162079033, "results": "292", "hashOfConfig": "162"}, {"size": 564, "mtime": 1750162079037, "results": "293", "hashOfConfig": "162"}, {"size": 594, "mtime": 1750162079035, "results": "294", "hashOfConfig": "162"}, {"size": 750, "mtime": 1750162079035, "results": "295", "hashOfConfig": "162"}, {"size": 589, "mtime": 1750162079034, "results": "296", "hashOfConfig": "162"}, {"size": 492, "mtime": 1750162079042, "results": "297", "hashOfConfig": "162"}, {"size": 810, "mtime": 1750162079041, "results": "298", "hashOfConfig": "162"}, {"size": 490, "mtime": 1750162079042, "results": "299", "hashOfConfig": "162"}, {"size": 492, "mtime": 1750162079041, "results": "300", "hashOfConfig": "162"}, {"size": 840, "mtime": 1750162079043, "results": "301", "hashOfConfig": "162"}, {"size": 1776, "mtime": 1750162079045, "results": "302", "hashOfConfig": "162"}, {"size": 896, "mtime": 1750162079044, "results": "303", "hashOfConfig": "162"}, {"size": 548, "mtime": 1750162079044, "results": "304", "hashOfConfig": "162"}, {"size": 825, "mtime": 1750162079046, "results": "305", "hashOfConfig": "162"}, {"size": 1014, "mtime": 1750162079045, "results": "306", "hashOfConfig": "162"}, {"size": 5638, "mtime": 1753617596678, "results": "307", "hashOfConfig": "162"}, {"size": 5701, "mtime": 1753621506072, "results": "308", "hashOfConfig": "162"}, {"size": 6823, "mtime": 1751280498212, "results": "309", "hashOfConfig": "162"}, {"size": 4794, "mtime": 1751272383355, "results": "310", "hashOfConfig": "162"}, {"size": 3832, "mtime": 1751274488484, "results": "311", "hashOfConfig": "162"}, {"size": 6866, "mtime": 1751269538235, "results": "312", "hashOfConfig": "162"}, {"size": 2091, "mtime": 1751304469748, "results": "313", "hashOfConfig": "162"}, {"size": 4201, "mtime": 1754165245297, "results": "314", "hashOfConfig": "162"}, {"size": 21251, "mtime": 1752953246619, "results": "315", "hashOfConfig": "162"}, {"size": 41014, "mtime": 1753477116038, "results": "316", "hashOfConfig": "162"}, {"size": 3682, "mtime": 1753740489711, "results": "317", "hashOfConfig": "162"}, {"size": 28409, "mtime": 1753993217073, "results": "318", "hashOfConfig": "162"}, {"size": 4731, "mtime": 1753898491576, "results": "319", "hashOfConfig": "162"}, {"size": 2037, "mtime": 1753743828980, "results": "320", "hashOfConfig": "162"}, {"size": 19068, "mtime": 1753834528518, "results": "321", "hashOfConfig": "162"}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kyl3u4", {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 50, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js", ["802", "803", "804"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js", ["805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817", "818", "819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841", "842", "843", "844", "845", "846", "847", "848", "849", "850", "851", "852", "853", "854"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js", ["855"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js", ["856"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js", ["857"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js", ["858", "859", "860"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js", ["861", "862", "863", "864", "865", "866", "867", "868", "869", "870", "871", "872", "873", "874"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js", ["875", "876", "877", "878", "879", "880"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js", ["881", "882", "883", "884", "885", "886", "887"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js", ["888", "889", "890", "891"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js", ["892"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js", ["893"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js", ["894", "895", "896"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js", ["897"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js", ["898", "899"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js", ["900", "901", "902"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js", ["903", "904"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js", ["905"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js", ["906", "907", "908", "909", "910"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js", ["911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js", ["923"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js", ["924"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js", ["925", "926"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js", ["927", "928"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js", ["929"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js", ["930", "931"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js", ["932"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js", ["933"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js", ["934"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js", ["935", "936", "937", "938", "939"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js", ["940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js", ["958", "959", "960", "961", "962", "963", "964", "965"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js", ["966", "967", "968", "969", "970", "971", "972", "973", "974", "975", "976", "977", "978", "979", "980", "981", "982"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js", ["983", "984", "985", "986", "987", "988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js", ["999", "1000", "1001", "1002"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js", ["1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js", ["1011"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js", ["1012", "1013"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js", ["1014"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js", ["1015"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js", ["1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js", ["1025"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js", ["1026"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js", ["1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035", "1036", "1037", "1038", "1039", "1040", "1041", "1042"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js", ["1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js", ["1052", "1053"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js", ["1054", "1055", "1056"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js", ["1057"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js", ["1058"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js", ["1059", "1060", "1061", "1062"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js", ["1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js", ["1072"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js", ["1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080", "1081", "1082", "1083", "1084", "1085", "1086", "1087"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js", ["1088", "1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099", "1100"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js", ["1101", "1102", "1103", "1104"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js", ["1105", "1106", "1107", "1108", "1109"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js", ["1110", "1111", "1112", "1113"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js", ["1114", "1115", "1116", "1117", "1118"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js", ["1119", "1120", "1121"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js", ["1122", "1123"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js", ["1124", "1125", "1126", "1127", "1128", "1129", "1130", "1131", "1132", "1133", "1134"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js", ["1135", "1136", "1137", "1138", "1139", "1140", "1141", "1142"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js", ["1143"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js", ["1144"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js", ["1145", "1146", "1147", "1148", "1149"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js", ["1150"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js", ["1151"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js", ["1152"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js", ["1153", "1154", "1155", "1156", "1157", "1158", "1159", "1160"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js", ["1161"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js", ["1162"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js", ["1163", "1164", "1165", "1166", "1167", "1168"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js", ["1169", "1170", "1171", "1172", "1173", "1174"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js", ["1175"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js", ["1176"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\RescheduleDialog.js", ["1177", "1178", "1179", "1180"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\UserStatusChecker.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\ProtectedRoute.js", ["1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1190", "1191", "1192", "1193", "1194"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\userStatusHandler.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axiosConfig.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\DeletedUsers.js", ["1195", "1196", "1197"], [], {"ruleId": "1198", "severity": 1, "message": "1199", "line": 99, "column": 11, "nodeType": "1200", "messageId": "1201", "endLine": 99, "endColumn": 22}, {"ruleId": "1198", "severity": 1, "message": "1202", "line": 99, "column": 24, "nodeType": "1200", "messageId": "1201", "endLine": 99, "endColumn": 39}, {"ruleId": "1198", "severity": 1, "message": "1203", "line": 100, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 100, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1204", "line": 3, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 3, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1205", "line": 4, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 4, "endColumn": 23}, {"ruleId": "1206", "severity": 1, "message": "1207", "line": 63, "column": 24, "nodeType": "1208", "messageId": "1209", "endLine": 63, "endColumn": 66}, {"ruleId": "1210", "severity": 1, "message": "1211", "line": 303, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 303, "endColumn": 16}, {"ruleId": "1210", "severity": 1, "message": "1214", "line": 336, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 336, "endColumn": 17}, {"ruleId": "1210", "severity": 1, "message": "1215", "line": 574, "column": 7, "nodeType": "1212", "messageId": "1213", "endLine": 574, "endColumn": 13}, {"ruleId": "1210", "severity": 1, "message": "1216", "line": 700, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 700, "endColumn": 24}, {"ruleId": "1210", "severity": 1, "message": "1217", "line": 708, "column": 7, "nodeType": "1212", "messageId": "1213", "endLine": 708, "endColumn": 14}, {"ruleId": "1206", "severity": 1, "message": "1207", "line": 740, "column": 30, "nodeType": "1208", "messageId": "1209", "endLine": 740, "endColumn": 107}, {"ruleId": "1210", "severity": 1, "message": "1218", "line": 759, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 759, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1219", "line": 797, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 797, "endColumn": 16}, {"ruleId": "1210", "severity": 1, "message": "1220", "line": 800, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 800, "endColumn": 22}, {"ruleId": "1210", "severity": 1, "message": "1221", "line": 801, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 801, "endColumn": 14}, {"ruleId": "1210", "severity": 1, "message": "1219", "line": 802, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 802, "endColumn": 16}, {"ruleId": "1210", "severity": 1, "message": "1222", "line": 803, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 803, "endColumn": 22}, {"ruleId": "1210", "severity": 1, "message": "1223", "line": 804, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 804, "endColumn": 26}, {"ruleId": "1210", "severity": 1, "message": "1224", "line": 849, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 849, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1225", "line": 971, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 971, "endColumn": 20}, {"ruleId": "1210", "severity": 1, "message": "1226", "line": 972, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 972, "endColumn": 27}, {"ruleId": "1210", "severity": 1, "message": "1227", "line": 1059, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 1059, "endColumn": 22}, {"ruleId": "1210", "severity": 1, "message": "1228", "line": 1210, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 1210, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1229", "line": 1211, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 1211, "endColumn": 26}, {"ruleId": "1210", "severity": 1, "message": "1230", "line": 1213, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 1213, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1231", "line": 1272, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 1272, "endColumn": 22}, {"ruleId": "1210", "severity": 1, "message": "1232", "line": 1279, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 1279, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1233", "line": 1280, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 1280, "endColumn": 20}, {"ruleId": "1210", "severity": 1, "message": "1234", "line": 1405, "column": 7, "nodeType": "1212", "messageId": "1213", "endLine": 1405, "endColumn": 12}, {"ruleId": "1210", "severity": 1, "message": "1235", "line": 2068, "column": 7, "nodeType": "1212", "messageId": "1213", "endLine": 2068, "endColumn": 12}, {"ruleId": "1206", "severity": 1, "message": "1207", "line": 2150, "column": 24, "nodeType": "1208", "messageId": "1209", "endLine": 2150, "endColumn": 58}, {"ruleId": "1210", "severity": 1, "message": "1236", "line": 2168, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 2168, "endColumn": 24}, {"ruleId": "1210", "severity": 1, "message": "1218", "line": 2258, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 2258, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1211", "line": 2399, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 2399, "endColumn": 16}, {"ruleId": "1210", "severity": 1, "message": "1215", "line": 2668, "column": 7, "nodeType": "1212", "messageId": "1213", "endLine": 2668, "endColumn": 13}, {"ruleId": "1210", "severity": 1, "message": "1216", "line": 2794, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 2794, "endColumn": 24}, {"ruleId": "1210", "severity": 1, "message": "1217", "line": 2802, "column": 7, "nodeType": "1212", "messageId": "1213", "endLine": 2802, "endColumn": 14}, {"ruleId": "1210", "severity": 1, "message": "1218", "line": 2854, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 2854, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1219", "line": 2892, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 2892, "endColumn": 16}, {"ruleId": "1210", "severity": 1, "message": "1224", "line": 2939, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 2939, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1225", "line": 3065, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 3065, "endColumn": 20}, {"ruleId": "1210", "severity": 1, "message": "1226", "line": 3066, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 3066, "endColumn": 27}, {"ruleId": "1210", "severity": 1, "message": "1227", "line": 3149, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 3149, "endColumn": 22}, {"ruleId": "1210", "severity": 1, "message": "1228", "line": 3300, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 3300, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1229", "line": 3301, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 3301, "endColumn": 26}, {"ruleId": "1210", "severity": 1, "message": "1230", "line": 3303, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 3303, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1231", "line": 3371, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 3371, "endColumn": 22}, {"ruleId": "1210", "severity": 1, "message": "1232", "line": 3389, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 3389, "endColumn": 23}, {"ruleId": "1210", "severity": 1, "message": "1233", "line": 3390, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 3390, "endColumn": 20}, {"ruleId": "1210", "severity": 1, "message": "1234", "line": 3498, "column": 7, "nodeType": "1212", "messageId": "1213", "endLine": 3498, "endColumn": 12}, {"ruleId": "1210", "severity": 1, "message": "1235", "line": 3982, "column": 7, "nodeType": "1212", "messageId": "1213", "endLine": 3982, "endColumn": 12}, {"ruleId": "1210", "severity": 1, "message": "1237", "line": 4467, "column": 9, "nodeType": "1212", "messageId": "1213", "endLine": 4467, "endColumn": 17}, {"ruleId": "1238", "severity": 1, "message": "1239", "line": 102, "column": 6, "nodeType": "1240", "endLine": 102, "endColumn": 8, "suggestions": "1241"}, {"ruleId": "1238", "severity": 1, "message": "1242", "line": 97, "column": 6, "nodeType": "1240", "endLine": 97, "endColumn": 43, "suggestions": "1243"}, {"ruleId": "1238", "severity": 1, "message": "1244", "line": 101, "column": 6, "nodeType": "1240", "endLine": 101, "endColumn": 40, "suggestions": "1245"}, {"ruleId": "1210", "severity": 1, "message": "1246", "line": 436, "column": 19, "nodeType": "1212", "messageId": "1213", "endLine": 436, "endColumn": 22}, {"ruleId": "1210", "severity": 1, "message": "1246", "line": 458, "column": 19, "nodeType": "1212", "messageId": "1213", "endLine": 458, "endColumn": 22}, {"ruleId": "1210", "severity": 1, "message": "1246", "line": 524, "column": 21, "nodeType": "1212", "messageId": "1213", "endLine": 524, "endColumn": 24}, {"ruleId": "1198", "severity": 1, "message": "1247", "line": 13, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 13, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1248", "line": 14, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 14, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1249", "line": 19, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 19, "endColumn": 8}, {"ruleId": "1198", "severity": 1, "message": "1250", "line": 23, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 23, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1251", "line": 28, "column": 15, "nodeType": "1200", "messageId": "1201", "endLine": 28, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1252", "line": 33, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 33, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1253", "line": 34, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 34, "endColumn": 11}, {"ruleId": "1198", "severity": 1, "message": "1254", "line": 35, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 35, "endColumn": 15}, {"ruleId": "1198", "severity": 1, "message": "1255", "line": 38, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 38, "endColumn": 11}, {"ruleId": "1198", "severity": 1, "message": "1256", "line": 40, "column": 16, "nodeType": "1200", "messageId": "1201", "endLine": 40, "endColumn": 29}, {"ruleId": "1198", "severity": 1, "message": "1257", "line": 42, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 42, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1258", "line": 327, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 327, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1259", "line": 368, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 368, "endColumn": 29}, {"ruleId": "1260", "severity": 1, "message": "1261", "line": 371, "column": 5, "nodeType": "1200", "messageId": "1262", "endLine": 371, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1263", "line": 6, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 6, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1264", "line": 7, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 7, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1251", "line": 17, "column": 15, "nodeType": "1200", "messageId": "1201", "endLine": 17, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1265", "line": 18, "column": 13, "nodeType": "1200", "messageId": "1201", "endLine": 18, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1266", "line": 20, "column": 13, "nodeType": "1200", "messageId": "1201", "endLine": 20, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1267", "line": 21, "column": 15, "nodeType": "1200", "messageId": "1201", "endLine": 21, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1268", "line": 9, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 9, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1269", "line": 26, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 26, "endColumn": 9}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 31, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 31, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1271", "line": 34, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 34, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1272", "line": 40, "column": 11, "nodeType": "1200", "messageId": "1201", "endLine": 40, "endColumn": 19}, {"ruleId": "1238", "severity": 1, "message": "1273", "line": 311, "column": 6, "nodeType": "1240", "endLine": 311, "endColumn": 54, "suggestions": "1274"}, {"ruleId": "1198", "severity": 1, "message": "1275", "line": 368, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 368, "endColumn": 26}, {"ruleId": "1198", "severity": 1, "message": "1276", "line": 40, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 40, "endColumn": 14}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 93, "column": 66, "nodeType": "1208", "messageId": "1279", "endLine": 93, "endColumn": 67, "suggestions": "1280"}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 93, "column": 75, "nodeType": "1208", "messageId": "1279", "endLine": 93, "endColumn": 76, "suggestions": "1281"}, {"ruleId": "1277", "severity": 1, "message": "1282", "line": 93, "column": 77, "nodeType": "1208", "messageId": "1279", "endLine": 93, "endColumn": 78, "suggestions": "1283"}, {"ruleId": "1198", "severity": 1, "message": "1284", "line": 37, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 37, "endColumn": 15}, {"ruleId": "1198", "severity": 1, "message": "1284", "line": 37, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 37, "endColumn": 15}, {"ruleId": "1198", "severity": 1, "message": "1285", "line": 1, "column": 27, "nodeType": "1200", "messageId": "1201", "endLine": 1, "endColumn": 33}, {"ruleId": "1198", "severity": 1, "message": "1286", "line": 30, "column": 13, "nodeType": "1200", "messageId": "1201", "endLine": 30, "endColumn": 23}, {"ruleId": "1238", "severity": 1, "message": "1287", "line": 110, "column": 6, "nodeType": "1240", "endLine": 110, "endColumn": 37, "suggestions": "1288"}, {"ruleId": "1198", "severity": 1, "message": "1289", "line": 2, "column": 23, "nodeType": "1200", "messageId": "1201", "endLine": 2, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1290", "line": 23, "column": 15, "nodeType": "1200", "messageId": "1201", "endLine": 23, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1284", "line": 40, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 40, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1291", "line": 32, "column": 15, "nodeType": "1200", "messageId": "1201", "endLine": 32, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1292", "line": 36, "column": 18, "nodeType": "1200", "messageId": "1201", "endLine": 36, "endColumn": 33}, {"ruleId": "1198", "severity": 1, "message": "1293", "line": 37, "column": 20, "nodeType": "1200", "messageId": "1201", "endLine": 37, "endColumn": 37}, {"ruleId": "1198", "severity": 1, "message": "1294", "line": 27, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 27, "endColumn": 8}, {"ruleId": "1238", "severity": 1, "message": "1295", "line": 82, "column": 6, "nodeType": "1240", "endLine": 82, "endColumn": 41, "suggestions": "1296"}, {"ruleId": "1198", "severity": 1, "message": "1297", "line": 15, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 15, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1298", "line": 26, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 26, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1299", "line": 27, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 27, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1300", "line": 28, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 28, "endColumn": 9}, {"ruleId": "1198", "severity": 1, "message": "1301", "line": 29, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 29, "endColumn": 11}, {"ruleId": "1238", "severity": 1, "message": "1302", "line": 84, "column": 6, "nodeType": "1240", "endLine": 84, "endColumn": 41, "suggestions": "1303"}, {"ruleId": "1198", "severity": 1, "message": "1263", "line": 21, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 21, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1264", "line": 22, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 22, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 32, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 32, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1304", "line": 39, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 39, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1305", "line": 58, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 58, "endColumn": 16}, {"ruleId": "1198", "severity": 1, "message": "1306", "line": 70, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 70, "endColumn": 25}, {"ruleId": "1198", "severity": 1, "message": "1307", "line": 190, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 190, "endColumn": 30}, {"ruleId": "1198", "severity": 1, "message": "1308", "line": 194, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 194, "endColumn": 31}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 203, "column": 66, "nodeType": "1208", "messageId": "1279", "endLine": 203, "endColumn": 67, "suggestions": "1309"}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 203, "column": 75, "nodeType": "1208", "messageId": "1279", "endLine": 203, "endColumn": 76, "suggestions": "1310"}, {"ruleId": "1277", "severity": 1, "message": "1282", "line": 203, "column": 77, "nodeType": "1208", "messageId": "1279", "endLine": 203, "endColumn": 78, "suggestions": "1311"}, {"ruleId": "1198", "severity": 1, "message": "1312", "line": 253, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 253, "endColumn": 22}, {"ruleId": "1238", "severity": 1, "message": "1313", "line": 43, "column": 6, "nodeType": "1240", "endLine": 43, "endColumn": 8, "suggestions": "1314"}, {"ruleId": "1238", "severity": 1, "message": "1315", "line": 43, "column": 6, "nodeType": "1240", "endLine": 43, "endColumn": 8, "suggestions": "1316"}, {"ruleId": "1198", "severity": 1, "message": "1317", "line": 39, "column": 11, "nodeType": "1200", "messageId": "1201", "endLine": 39, "endColumn": 12}, {"ruleId": "1238", "severity": 1, "message": "1318", "line": 88, "column": 6, "nodeType": "1240", "endLine": 88, "endColumn": 39, "suggestions": "1319"}, {"ruleId": "1198", "severity": 1, "message": "1320", "line": 40, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 40, "endColumn": 24}, {"ruleId": "1238", "severity": 1, "message": "1321", "line": 107, "column": 6, "nodeType": "1240", "endLine": 107, "endColumn": 15, "suggestions": "1322"}, {"ruleId": "1238", "severity": 1, "message": "1323", "line": 45, "column": 6, "nodeType": "1240", "endLine": 45, "endColumn": 45, "suggestions": "1324"}, {"ruleId": "1198", "severity": 1, "message": "1325", "line": 33, "column": 12, "nodeType": "1200", "messageId": "1201", "endLine": 33, "endColumn": 21}, {"ruleId": "1238", "severity": 1, "message": "1326", "line": 103, "column": 6, "nodeType": "1240", "endLine": 103, "endColumn": 42, "suggestions": "1327"}, {"ruleId": "1238", "severity": 1, "message": "1328", "line": 50, "column": 6, "nodeType": "1240", "endLine": 50, "endColumn": 25, "suggestions": "1329"}, {"ruleId": "1198", "severity": 1, "message": "1330", "line": 54, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 54, "endColumn": 17}, {"ruleId": "1238", "severity": 1, "message": "1331", "line": 60, "column": 6, "nodeType": "1240", "endLine": 60, "endColumn": 39, "suggestions": "1332"}, {"ruleId": "1198", "severity": 1, "message": "1333", "line": 31, "column": 24, "nodeType": "1200", "messageId": "1201", "endLine": 31, "endColumn": 34}, {"ruleId": "1238", "severity": 1, "message": "1334", "line": 110, "column": 6, "nodeType": "1240", "endLine": 110, "endColumn": 22, "suggestions": "1335"}, {"ruleId": "1238", "severity": 1, "message": "1334", "line": 133, "column": 6, "nodeType": "1240", "endLine": 133, "endColumn": 19, "suggestions": "1336"}, {"ruleId": "1238", "severity": 1, "message": "1334", "line": 145, "column": 6, "nodeType": "1240", "endLine": 145, "endColumn": 19, "suggestions": "1337"}, {"ruleId": "1238", "severity": 1, "message": "1334", "line": 162, "column": 6, "nodeType": "1240", "endLine": 162, "endColumn": 19, "suggestions": "1338"}, {"ruleId": "1198", "severity": 1, "message": "1339", "line": 10, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 10, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1340", "line": 13, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 13, "endColumn": 8}, {"ruleId": "1198", "severity": 1, "message": "1341", "line": 14, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 14, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1342", "line": 15, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 15, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1343", "line": 16, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 16, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1344", "line": 17, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 17, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1345", "line": 18, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 18, "endColumn": 11}, {"ruleId": "1198", "severity": 1, "message": "1248", "line": 20, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 20, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 22, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 22, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1346", "line": 31, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 31, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1347", "line": 52, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 52, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1348", "line": 52, "column": 23, "nodeType": "1200", "messageId": "1201", "endLine": 52, "endColumn": 37}, {"ruleId": "1198", "severity": 1, "message": "1349", "line": 53, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 53, "endColumn": 22}, {"ruleId": "1198", "severity": 1, "message": "1350", "line": 53, "column": 24, "nodeType": "1200", "messageId": "1201", "endLine": 53, "endColumn": 39}, {"ruleId": "1198", "severity": 1, "message": "1351", "line": 227, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 227, "endColumn": 24}, {"ruleId": "1198", "severity": 1, "message": "1352", "line": 236, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 236, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1353", "line": 245, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 245, "endColumn": 29}, {"ruleId": "1198", "severity": 1, "message": "1354", "line": 258, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 258, "endColumn": 28}, {"ruleId": "1198", "severity": 1, "message": "1355", "line": 2, "column": 23, "nodeType": "1200", "messageId": "1201", "endLine": 2, "endColumn": 34}, {"ruleId": "1198", "severity": 1, "message": "1356", "line": 4, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 4, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1248", "line": 13, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 13, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 15, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 15, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1347", "line": 45, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 45, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1348", "line": 45, "column": 23, "nodeType": "1200", "messageId": "1201", "endLine": 45, "endColumn": 37}, {"ruleId": "1198", "severity": 1, "message": "1349", "line": 46, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 46, "endColumn": 22}, {"ruleId": "1198", "severity": 1, "message": "1350", "line": 46, "column": 24, "nodeType": "1200", "messageId": "1201", "endLine": 46, "endColumn": 39}, {"ruleId": "1198", "severity": 1, "message": "1357", "line": 5, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 5, "endColumn": 9}, {"ruleId": "1198", "severity": 1, "message": "1268", "line": 7, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 7, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1263", "line": 9, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 9, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1264", "line": 10, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 10, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1339", "line": 11, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 11, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1298", "line": 16, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 16, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1299", "line": 17, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 17, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1300", "line": 18, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 18, "endColumn": 9}, {"ruleId": "1198", "severity": 1, "message": "1301", "line": 19, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 19, "endColumn": 11}, {"ruleId": "1198", "severity": 1, "message": "1358", "line": 33, "column": 18, "nodeType": "1200", "messageId": "1201", "endLine": 33, "endColumn": 33}, {"ruleId": "1198", "severity": 1, "message": "1359", "line": 46, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 46, "endColumn": 33}, {"ruleId": "1198", "severity": 1, "message": "1360", "line": 46, "column": 35, "nodeType": "1200", "messageId": "1201", "endLine": 46, "endColumn": 59}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 51, "column": 11, "nodeType": "1200", "messageId": "1201", "endLine": 51, "endColumn": 22}, {"ruleId": "1198", "severity": 1, "message": "1361", "line": 58, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 58, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1362", "line": 110, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 110, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1363", "line": 154, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 154, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1364", "line": 215, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 215, "endColumn": 26}, {"ruleId": "1198", "severity": 1, "message": "1298", "line": 21, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 21, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1299", "line": 22, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 22, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1300", "line": 23, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 23, "endColumn": 9}, {"ruleId": "1198", "severity": 1, "message": "1301", "line": 24, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 24, "endColumn": 11}, {"ruleId": "1198", "severity": 1, "message": "1365", "line": 42, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 42, "endColumn": 15}, {"ruleId": "1198", "severity": 1, "message": "1359", "line": 47, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 47, "endColumn": 33}, {"ruleId": "1198", "severity": 1, "message": "1360", "line": 47, "column": 64, "nodeType": "1200", "messageId": "1201", "endLine": 47, "endColumn": 88}, {"ruleId": "1198", "severity": 1, "message": "1361", "line": 68, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 68, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1366", "line": 73, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 73, "endColumn": 30}, {"ruleId": "1198", "severity": 1, "message": "1347", "line": 74, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 74, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1367", "line": 75, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 75, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1368", "line": 76, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 76, "endColumn": 22}, {"ruleId": "1198", "severity": 1, "message": "1369", "line": 76, "column": 24, "nodeType": "1200", "messageId": "1201", "endLine": 76, "endColumn": 39}, {"ruleId": "1238", "severity": 1, "message": "1370", "line": 172, "column": 6, "nodeType": "1240", "endLine": 172, "endColumn": 16, "suggestions": "1371"}, {"ruleId": "1238", "severity": 1, "message": "1372", "line": 179, "column": 6, "nodeType": "1240", "endLine": 179, "endColumn": 31, "suggestions": "1373"}, {"ruleId": "1198", "severity": 1, "message": "1374", "line": 481, "column": 13, "nodeType": "1200", "messageId": "1201", "endLine": 481, "endColumn": 21}, {"ruleId": "1238", "severity": 1, "message": "1273", "line": 180, "column": 6, "nodeType": "1240", "endLine": 180, "endColumn": 28, "suggestions": "1375"}, {"ruleId": "1238", "severity": 1, "message": "1376", "line": 217, "column": 6, "nodeType": "1240", "endLine": 217, "endColumn": 28, "suggestions": "1377"}, {"ruleId": "1238", "severity": 1, "message": "1378", "line": 257, "column": 6, "nodeType": "1240", "endLine": 257, "endColumn": 14, "suggestions": "1379"}, {"ruleId": "1238", "severity": 1, "message": "1380", "line": 324, "column": 6, "nodeType": "1240", "endLine": 324, "endColumn": 53, "suggestions": "1381"}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 14, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 14, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1251", "line": 30, "column": 15, "nodeType": "1200", "messageId": "1201", "endLine": 30, "endColumn": 27}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 71, "column": 64, "nodeType": "1208", "messageId": "1279", "endLine": 71, "endColumn": 65, "suggestions": "1382"}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 71, "column": 73, "nodeType": "1208", "messageId": "1279", "endLine": 71, "endColumn": 74, "suggestions": "1383"}, {"ruleId": "1277", "severity": 1, "message": "1282", "line": 71, "column": 75, "nodeType": "1208", "messageId": "1279", "endLine": 71, "endColumn": 76, "suggestions": "1384"}, {"ruleId": "1198", "severity": 1, "message": "1333", "line": 92, "column": 17, "nodeType": "1200", "messageId": "1201", "endLine": 92, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1284", "line": 93, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 93, "endColumn": 15}, {"ruleId": "1198", "severity": 1, "message": "1385", "line": 93, "column": 17, "nodeType": "1200", "messageId": "1201", "endLine": 93, "endColumn": 25}, {"ruleId": "1198", "severity": 1, "message": "1386", "line": 25, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 25, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1292", "line": 22, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 22, "endColumn": 23}, {"ruleId": "1238", "severity": 1, "message": "1323", "line": 46, "column": 6, "nodeType": "1240", "endLine": 46, "endColumn": 45, "suggestions": "1387"}, {"ruleId": "1238", "severity": 1, "message": "1388", "line": 65, "column": 6, "nodeType": "1240", "endLine": 65, "endColumn": 45, "suggestions": "1389"}, {"ruleId": "1238", "severity": 1, "message": "1326", "line": 83, "column": 6, "nodeType": "1240", "endLine": 83, "endColumn": 32, "suggestions": "1390"}, {"ruleId": "1198", "severity": 1, "message": "1391", "line": 17, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 17, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1392", "line": 29, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 29, "endColumn": 11}, {"ruleId": "1198", "severity": 1, "message": "1272", "line": 32, "column": 11, "nodeType": "1200", "messageId": "1201", "endLine": 32, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1393", "line": 33, "column": 17, "nodeType": "1200", "messageId": "1201", "endLine": 33, "endColumn": 31}, {"ruleId": "1198", "severity": 1, "message": "1394", "line": 40, "column": 17, "nodeType": "1200", "messageId": "1201", "endLine": 40, "endColumn": 31}, {"ruleId": "1198", "severity": 1, "message": "1395", "line": 41, "column": 17, "nodeType": "1200", "messageId": "1201", "endLine": 41, "endColumn": 31}, {"ruleId": "1198", "severity": 1, "message": "1284", "line": 49, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 49, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1396", "line": 68, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 68, "endColumn": 25}, {"ruleId": "1198", "severity": 1, "message": "1397", "line": 289, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 289, "endColumn": 29}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 4, "column": 29, "nodeType": "1200", "messageId": "1201", "endLine": 4, "endColumn": 36}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 28, "column": 11, "nodeType": "1200", "messageId": "1201", "endLine": 28, "endColumn": 22}, {"ruleId": "1198", "severity": 1, "message": "1268", "line": 14, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 14, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1269", "line": 31, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 31, "endColumn": 9}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 36, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 36, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1271", "line": 39, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 39, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1398", "line": 42, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 42, "endColumn": 9}, {"ruleId": "1198", "severity": 1, "message": "1399", "line": 43, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 43, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1400", "line": 44, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 44, "endColumn": 16}, {"ruleId": "1198", "severity": 1, "message": "1401", "line": 45, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 45, "endColumn": 16}, {"ruleId": "1198", "severity": 1, "message": "1272", "line": 51, "column": 11, "nodeType": "1200", "messageId": "1201", "endLine": 51, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1330", "line": 174, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 174, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1402", "line": 203, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 203, "endColumn": 19}, {"ruleId": "1238", "severity": 1, "message": "1302", "line": 298, "column": 6, "nodeType": "1240", "endLine": 298, "endColumn": 48, "suggestions": "1403"}, {"ruleId": "1198", "severity": 1, "message": "1275", "line": 337, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 337, "endColumn": 26}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 422, "column": 68, "nodeType": "1208", "messageId": "1279", "endLine": 422, "endColumn": 69, "suggestions": "1404"}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 422, "column": 77, "nodeType": "1208", "messageId": "1279", "endLine": 422, "endColumn": 78, "suggestions": "1405"}, {"ruleId": "1277", "severity": 1, "message": "1282", "line": 422, "column": 79, "nodeType": "1208", "messageId": "1279", "endLine": 422, "endColumn": 80, "suggestions": "1406"}, {"ruleId": "1198", "severity": 1, "message": "1401", "line": 30, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 30, "endColumn": 16}, {"ruleId": "1198", "severity": 1, "message": "1258", "line": 63, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 63, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1407", "line": 71, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 71, "endColumn": 24}, {"ruleId": "1198", "severity": 1, "message": "1408", "line": 72, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 72, "endColumn": 22}, {"ruleId": "1238", "severity": 1, "message": "1409", "line": 321, "column": 6, "nodeType": "1240", "endLine": 321, "endColumn": 39, "suggestions": "1410"}, {"ruleId": "1198", "severity": 1, "message": "1411", "line": 403, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 403, "endColumn": 23}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 493, "column": 68, "nodeType": "1208", "messageId": "1279", "endLine": 493, "endColumn": 69, "suggestions": "1412"}, {"ruleId": "1277", "severity": 1, "message": "1278", "line": 493, "column": 77, "nodeType": "1208", "messageId": "1279", "endLine": 493, "endColumn": 78, "suggestions": "1413"}, {"ruleId": "1277", "severity": 1, "message": "1282", "line": 493, "column": 79, "nodeType": "1208", "messageId": "1279", "endLine": 493, "endColumn": 80, "suggestions": "1414"}, {"ruleId": "1198", "severity": 1, "message": "1415", "line": 7, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 7, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1416", "line": 22, "column": 19, "nodeType": "1200", "messageId": "1201", "endLine": 22, "endColumn": 30}, {"ruleId": "1238", "severity": 1, "message": "1273", "line": 196, "column": 6, "nodeType": "1240", "endLine": 196, "endColumn": 28, "suggestions": "1417"}, {"ruleId": "1238", "severity": 1, "message": "1376", "line": 233, "column": 6, "nodeType": "1240", "endLine": 233, "endColumn": 28, "suggestions": "1418"}, {"ruleId": "1238", "severity": 1, "message": "1378", "line": 273, "column": 6, "nodeType": "1240", "endLine": 273, "endColumn": 14, "suggestions": "1419"}, {"ruleId": "1198", "severity": 1, "message": "1420", "line": 24, "column": 24, "nodeType": "1200", "messageId": "1201", "endLine": 24, "endColumn": 29}, {"ruleId": "1238", "severity": 1, "message": "1326", "line": 84, "column": 6, "nodeType": "1240", "endLine": 84, "endColumn": 32, "suggestions": "1421"}, {"ruleId": "1198", "severity": 1, "message": "1339", "line": 5, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 5, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1263", "line": 6, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 6, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1289", "line": 20, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 20, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1199", "line": 28, "column": 18, "nodeType": "1200", "messageId": "1201", "endLine": 28, "endColumn": 29}, {"ruleId": "1198", "severity": 1, "message": "1422", "line": 27, "column": 18, "nodeType": "1200", "messageId": "1201", "endLine": 27, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1365", "line": 29, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 29, "endColumn": 15}, {"ruleId": "1198", "severity": 1, "message": "1361", "line": 44, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 44, "endColumn": 21}, {"ruleId": "1238", "severity": 1, "message": "1423", "line": 66, "column": 6, "nodeType": "1240", "endLine": 66, "endColumn": 19, "suggestions": "1424"}, {"ruleId": "1198", "severity": 1, "message": "1425", "line": 127, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 127, "endColumn": 40}, {"ruleId": "1198", "severity": 1, "message": "1426", "line": 135, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 135, "endColumn": 43}, {"ruleId": "1198", "severity": 1, "message": "1427", "line": 147, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 147, "endColumn": 22}, {"ruleId": "1198", "severity": 1, "message": "1428", "line": 156, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 156, "endColumn": 40}, {"ruleId": "1198", "severity": 1, "message": "1429", "line": 184, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 184, "endColumn": 23}, {"ruleId": "1238", "severity": 1, "message": "1323", "line": 74, "column": 6, "nodeType": "1240", "endLine": 74, "endColumn": 45, "suggestions": "1430"}, {"ruleId": "1198", "severity": 1, "message": "1391", "line": 14, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 14, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1431", "line": 17, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 17, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 18, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 18, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1432", "line": 19, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 19, "endColumn": 9}, {"ruleId": "1198", "severity": 1, "message": "1433", "line": 49, "column": 40, "nodeType": "1200", "messageId": "1201", "endLine": 49, "endColumn": 49}, {"ruleId": "1198", "severity": 1, "message": "1434", "line": 51, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 51, "endColumn": 32}, {"ruleId": "1198", "severity": 1, "message": "1360", "line": 51, "column": 34, "nodeType": "1200", "messageId": "1201", "endLine": 51, "endColumn": 58}, {"ruleId": "1198", "severity": 1, "message": "1359", "line": 51, "column": 89, "nodeType": "1200", "messageId": "1201", "endLine": 51, "endColumn": 112}, {"ruleId": "1198", "severity": 1, "message": "1330", "line": 59, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 59, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1258", "line": 62, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 62, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1428", "line": 285, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 285, "endColumn": 40}, {"ruleId": "1198", "severity": 1, "message": "1435", "line": 416, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 416, "endColumn": 28}, {"ruleId": "1198", "severity": 1, "message": "1436", "line": 435, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 435, "endColumn": 35}, {"ruleId": "1198", "severity": 1, "message": "1437", "line": 458, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 458, "endColumn": 36}, {"ruleId": "1198", "severity": 1, "message": "1438", "line": 719, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 719, "endColumn": 29}, {"ruleId": "1198", "severity": 1, "message": "1298", "line": 25, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 25, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1299", "line": 26, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 26, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1300", "line": 27, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 27, "endColumn": 9}, {"ruleId": "1198", "severity": 1, "message": "1301", "line": 28, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 28, "endColumn": 11}, {"ruleId": "1198", "severity": 1, "message": "1439", "line": 33, "column": 13, "nodeType": "1200", "messageId": "1201", "endLine": 33, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1366", "line": 136, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 136, "endColumn": 30}, {"ruleId": "1198", "severity": 1, "message": "1347", "line": 137, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 137, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1367", "line": 138, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 138, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1368", "line": 139, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 139, "endColumn": 22}, {"ruleId": "1198", "severity": 1, "message": "1369", "line": 139, "column": 24, "nodeType": "1200", "messageId": "1201", "endLine": 139, "endColumn": 39}, {"ruleId": "1198", "severity": 1, "message": "1374", "line": 346, "column": 13, "nodeType": "1200", "messageId": "1201", "endLine": 346, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1440", "line": 714, "column": 12, "nodeType": "1200", "messageId": "1201", "endLine": 714, "endColumn": 20}, {"ruleId": "1198", "severity": 1, "message": "1441", "line": 766, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 766, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1272", "line": 27, "column": 11, "nodeType": "1200", "messageId": "1201", "endLine": 27, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1330", "line": 36, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 36, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1284", "line": 37, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 37, "endColumn": 14}, {"ruleId": "1238", "severity": 1, "message": "1442", "line": 467, "column": 6, "nodeType": "1240", "endLine": 467, "endColumn": 32, "suggestions": "1443"}, {"ruleId": "1198", "severity": 1, "message": "1444", "line": 18, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 18, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1304", "line": 22, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 22, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1289", "line": 34, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 34, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1445", "line": 49, "column": 20, "nodeType": "1200", "messageId": "1201", "endLine": 49, "endColumn": 37}, {"ruleId": "1238", "severity": 1, "message": "1446", "line": 124, "column": 6, "nodeType": "1240", "endLine": 124, "endColumn": 38, "suggestions": "1447"}, {"ruleId": "1198", "severity": 1, "message": "1391", "line": 8, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 8, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1248", "line": 17, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 17, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1448", "line": 21, "column": 17, "nodeType": "1200", "messageId": "1201", "endLine": 21, "endColumn": 31}, {"ruleId": "1198", "severity": 1, "message": "1449", "line": 28, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 28, "endColumn": 14}, {"ruleId": "1198", "severity": 1, "message": "1450", "line": 18, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 18, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1451", "line": 148, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 148, "endColumn": 25}, {"ruleId": "1198", "severity": 1, "message": "1452", "line": 158, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 158, "endColumn": 24}, {"ruleId": "1198", "severity": 1, "message": "1453", "line": 177, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 177, "endColumn": 29}, {"ruleId": "1198", "severity": 1, "message": "1454", "line": 306, "column": 17, "nodeType": "1200", "messageId": "1201", "endLine": 306, "endColumn": 30}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 11, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 11, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1455", "line": 26, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 26, "endColumn": 29}, {"ruleId": "1198", "severity": 1, "message": "1456", "line": 27, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 27, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1455", "line": 21, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 21, "endColumn": 29}, {"ruleId": "1198", "severity": 1, "message": "1456", "line": 22, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 22, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1457", "line": 18, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 18, "endColumn": 12}, {"ruleId": "1198", "severity": 1, "message": "1458", "line": 19, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 19, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1459", "line": 20, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 20, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1271", "line": 21, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 21, "endColumn": 19}, {"ruleId": "1198", "severity": 1, "message": "1460", "line": 22, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 22, "endColumn": 11}, {"ruleId": "1198", "severity": 1, "message": "1461", "line": 24, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 24, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 34, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 34, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1394", "line": 43, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 43, "endColumn": 22}, {"ruleId": "1198", "severity": 1, "message": "1462", "line": 44, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 44, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1463", "line": 45, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 45, "endColumn": 16}, {"ruleId": "1198", "severity": 1, "message": "1464", "line": 127, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 127, "endColumn": 18}, {"ruleId": "1198", "severity": 1, "message": "1285", "line": 1, "column": 38, "nodeType": "1200", "messageId": "1201", "endLine": 1, "endColumn": 44}, {"ruleId": "1198", "severity": 1, "message": "1465", "line": 1, "column": 46, "nodeType": "1200", "messageId": "1201", "endLine": 1, "endColumn": 53}, {"ruleId": "1198", "severity": 1, "message": "1466", "line": 4, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 4, "endColumn": 18}, {"ruleId": "1198", "severity": 1, "message": "1467", "line": 5, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 5, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1468", "line": 6, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 6, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1469", "line": 9, "column": 46, "nodeType": "1200", "messageId": "1201", "endLine": 9, "endColumn": 71}, {"ruleId": "1198", "severity": 1, "message": "1470", "line": 9, "column": 73, "nodeType": "1200", "messageId": "1201", "endLine": 9, "endColumn": 88}, {"ruleId": "1198", "severity": 1, "message": "1471", "line": 14, "column": 8, "nodeType": "1200", "messageId": "1201", "endLine": 14, "endColumn": 27}, {"ruleId": "1238", "severity": 1, "message": "1472", "line": 49, "column": 6, "nodeType": "1240", "endLine": 49, "endColumn": 22, "suggestions": "1473"}, {"ruleId": "1238", "severity": 1, "message": "1474", "line": 40, "column": 8, "nodeType": "1240", "endLine": 40, "endColumn": 33, "suggestions": "1475"}, {"ruleId": "1238", "severity": 1, "message": "1476", "line": 128, "column": 6, "nodeType": "1240", "endLine": 128, "endColumn": 33, "suggestions": "1477"}, {"ruleId": "1238", "severity": 1, "message": "1478", "line": 132, "column": 6, "nodeType": "1240", "endLine": 132, "endColumn": 37, "suggestions": "1479"}, {"ruleId": "1238", "severity": 1, "message": "1480", "line": 137, "column": 6, "nodeType": "1240", "endLine": 137, "endColumn": 8, "suggestions": "1481"}, {"ruleId": "1238", "severity": 1, "message": "1482", "line": 300, "column": 6, "nodeType": "1240", "endLine": 300, "endColumn": 8, "suggestions": "1483"}, {"ruleId": "1238", "severity": 1, "message": "1478", "line": 403, "column": 6, "nodeType": "1240", "endLine": 403, "endColumn": 8, "suggestions": "1484"}, {"ruleId": "1238", "severity": 1, "message": "1485", "line": 28, "column": 6, "nodeType": "1240", "endLine": 28, "endColumn": 8, "suggestions": "1486"}, {"ruleId": "1238", "severity": 1, "message": "1487", "line": 230, "column": 6, "nodeType": "1240", "endLine": 230, "endColumn": 34, "suggestions": "1488"}, {"ruleId": "1238", "severity": 1, "message": "1489", "line": 220, "column": 6, "nodeType": "1240", "endLine": 220, "endColumn": 50, "suggestions": "1490"}, {"ruleId": "1238", "severity": 1, "message": "1491", "line": 59, "column": 6, "nodeType": "1240", "endLine": 59, "endColumn": 23, "suggestions": "1492"}, {"ruleId": "1493", "severity": 1, "message": "1494", "line": 148, "column": 37, "nodeType": "1495", "messageId": "1213", "endLine": 148, "endColumn": 39}, {"ruleId": "1493", "severity": 1, "message": "1496", "line": 173, "column": 82, "nodeType": "1495", "messageId": "1213", "endLine": 173, "endColumn": 84}, {"ruleId": "1493", "severity": 1, "message": "1496", "line": 228, "column": 104, "nodeType": "1495", "messageId": "1213", "endLine": 228, "endColumn": 106}, {"ruleId": "1493", "severity": 1, "message": "1496", "line": 257, "column": 44, "nodeType": "1495", "messageId": "1213", "endLine": 257, "endColumn": 46}, {"ruleId": "1493", "severity": 1, "message": "1496", "line": 261, "column": 44, "nodeType": "1495", "messageId": "1213", "endLine": 261, "endColumn": 46}, {"ruleId": "1493", "severity": 1, "message": "1496", "line": 265, "column": 44, "nodeType": "1495", "messageId": "1213", "endLine": 265, "endColumn": 46}, {"ruleId": "1493", "severity": 1, "message": "1496", "line": 271, "column": 44, "nodeType": "1495", "messageId": "1213", "endLine": 271, "endColumn": 46}, {"ruleId": "1497", "severity": 1, "message": "1498", "line": 9, "column": 23, "nodeType": "1499", "messageId": "1213", "endLine": 9, "endColumn": 26}, {"ruleId": "1238", "severity": 1, "message": "1500", "line": 32, "column": 6, "nodeType": "1240", "endLine": 32, "endColumn": 28, "suggestions": "1501"}, {"ruleId": "1198", "severity": 1, "message": "1391", "line": 17, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 17, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1502", "line": 23, "column": 15, "nodeType": "1200", "messageId": "1201", "endLine": 23, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1503", "line": 25, "column": 14, "nodeType": "1200", "messageId": "1201", "endLine": 25, "endColumn": 25}, {"ruleId": "1198", "severity": 1, "message": "1504", "line": 28, "column": 14, "nodeType": "1200", "messageId": "1201", "endLine": 28, "endColumn": 25}, {"ruleId": "1198", "severity": 1, "message": "1448", "line": 30, "column": 17, "nodeType": "1200", "messageId": "1201", "endLine": 30, "endColumn": 31}, {"ruleId": "1198", "severity": 1, "message": "1505", "line": 31, "column": 17, "nodeType": "1200", "messageId": "1201", "endLine": 31, "endColumn": 31}, {"ruleId": "1198", "severity": 1, "message": "1391", "line": 15, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 15, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1294", "line": 20, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 20, "endColumn": 8}, {"ruleId": "1198", "severity": 1, "message": "1502", "line": 23, "column": 15, "nodeType": "1200", "messageId": "1201", "endLine": 23, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1504", "line": 26, "column": 14, "nodeType": "1200", "messageId": "1201", "endLine": 26, "endColumn": 25}, {"ruleId": "1198", "severity": 1, "message": "1439", "line": 28, "column": 13, "nodeType": "1200", "messageId": "1201", "endLine": 28, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1506", "line": 29, "column": 13, "nodeType": "1200", "messageId": "1201", "endLine": 29, "endColumn": 23}, {"ruleId": "1198", "severity": 1, "message": "1359", "line": 19, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 19, "endColumn": 33}, {"ruleId": "1198", "severity": 1, "message": "1502", "line": 33, "column": 15, "nodeType": "1200", "messageId": "1201", "endLine": 33, "endColumn": 27}, {"ruleId": "1198", "severity": 1, "message": "1270", "line": 20, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 20, "endColumn": 10}, {"ruleId": "1198", "severity": 1, "message": "1507", "line": 104, "column": 11, "nodeType": "1200", "messageId": "1201", "endLine": 104, "endColumn": 25}, {"ruleId": "1238", "severity": 1, "message": "1508", "line": 225, "column": 7, "nodeType": "1509", "endLine": 225, "endColumn": 48}, {"ruleId": "1198", "severity": 1, "message": "1510", "line": 836, "column": 33, "nodeType": "1200", "messageId": "1201", "endLine": 836, "endColumn": 42}, {"ruleId": "1198", "severity": 1, "message": "1248", "line": 15, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 15, "endColumn": 13}, {"ruleId": "1198", "severity": 1, "message": "1511", "line": 16, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 16, "endColumn": 7}, {"ruleId": "1198", "severity": 1, "message": "1301", "line": 17, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 17, "endColumn": 11}, {"ruleId": "1198", "severity": 1, "message": "1256", "line": 28, "column": 16, "nodeType": "1200", "messageId": "1201", "endLine": 28, "endColumn": 29}, {"ruleId": "1198", "severity": 1, "message": "1512", "line": 31, "column": 18, "nodeType": "1200", "messageId": "1201", "endLine": 31, "endColumn": 34}, {"ruleId": "1198", "severity": 1, "message": "1513", "line": 31, "column": 36, "nodeType": "1200", "messageId": "1201", "endLine": 31, "endColumn": 53}, {"ruleId": "1198", "severity": 1, "message": "1514", "line": 31, "column": 55, "nodeType": "1200", "messageId": "1201", "endLine": 31, "endColumn": 74}, {"ruleId": "1198", "severity": 1, "message": "1515", "line": 31, "column": 76, "nodeType": "1200", "messageId": "1201", "endLine": 31, "endColumn": 95}, {"ruleId": "1198", "severity": 1, "message": "1330", "line": 41, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 41, "endColumn": 17}, {"ruleId": "1198", "severity": 1, "message": "1361", "line": 46, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 46, "endColumn": 21}, {"ruleId": "1198", "severity": 1, "message": "1259", "line": 249, "column": 9, "nodeType": "1200", "messageId": "1201", "endLine": 249, "endColumn": 29}, {"ruleId": "1238", "severity": 1, "message": "1516", "line": 328, "column": 6, "nodeType": "1240", "endLine": 328, "endColumn": 90, "suggestions": "1517"}, {"ruleId": "1238", "severity": 1, "message": "1518", "line": 356, "column": 6, "nodeType": "1240", "endLine": 356, "endColumn": 36, "suggestions": "1519"}, {"ruleId": "1238", "severity": 1, "message": "1520", "line": 365, "column": 6, "nodeType": "1240", "endLine": 365, "endColumn": 43, "suggestions": "1521"}, {"ruleId": "1198", "severity": 1, "message": "1522", "line": 26, "column": 3, "nodeType": "1200", "messageId": "1201", "endLine": 26, "endColumn": 8}, {"ruleId": "1198", "severity": 1, "message": "1523", "line": 48, "column": 10, "nodeType": "1200", "messageId": "1201", "endLine": 48, "endColumn": 17}, {"ruleId": "1238", "severity": 1, "message": "1524", "line": 91, "column": 6, "nodeType": "1240", "endLine": 91, "endColumn": 45, "suggestions": "1525"}, "no-unused-vars", "'currentUser' is assigned a value but never used.", "Identifier", "unusedVar", "'isAuthenticated' is assigned a value but never used.", "'location' is assigned a value but never used.", "'meetingIssuesEn' is defined but never used.", "'meetingIssuesAr' is defined but never used.", "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", "no-dupe-keys", "Duplicate key 'details'.", "ObjectExpression", "unexpected", "Duplicate key 'editInfo'.", "Duplicate key 'common'.", "Duplicate key 'noTeachersFound'.", "Duplicate key 'booking'.", "Duplicate key 'selectDuration'.", "Duplicate key 'minutes'.", "Duplicate key 'timeRemaining'.", "Duplicate key 'hours'.", "Duplicate key 'lessonOverdue'.", "Duplicate key 'lateCancelWarning'.", "Duplicate key 'currentBooking'.", "Duplicate key 'invalidCode'.", "Duplicate key 'verificationFailed'.", "Duplicate key 'updateProfile'.", "Duplicate key 'nativeLanguage'.", "Duplicate key 'teachingLanguages'.", "Duplicate key 'qualifications'.", "Duplicate key 'formHasErrors'.", "Duplicate key 'allowedFormats'.", "Duplicate key 'maxFileSize'.", "Duplicate key 'admin'.", "Duplicate key 'about'.", "Duplicate key 'errorCancelling'.", "Duplicate key 'earnings'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleLogout'. Either include it or remove the dependency array.", "ArrayExpression", ["1526"], "React Hook useEffect has a missing dependency: 'socket'. Either include it or remove the dependency array.", ["1527"], "React Hook useEffect has a missing dependency: 'fetchUnreadCount'. Either include it or remove the dependency array.", ["1528"], "Duplicate key 'gap'.", "'CardMedia' is defined but never used.", "'IconButton' is defined but never used.", "'Slide' is defined but never used.", "'Zoom' is defined but never used.", "'LanguageIcon' is defined but never used.", "'Star' is defined but never used.", "'Timeline' is defined but never used.", "'LocalLibrary' is defined but never used.", "'Language' is defined but never used.", "'TranslateIcon' is defined but never used.", "'useAuth' is defined but never used.", "'isMobile' is assigned a value but never used.", "'handleLanguageChange' is assigned a value but never used.", "no-const-assign", "'isRtl' is constant.", "const", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'PeopleIcon' is defined but never used.", "'PublicIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "'TextField' is defined but never used.", "'Drawer' is defined but never used.", "'Divider' is defined but never used.", "'FormControlLabel' is defined but never used.", "'StarIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", ["1529"], "'getPriceRangeText' is assigned a value but never used.", "'theme' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\&.", "unnecessaryEscape", ["1530", "1531"], ["1532", "1533"], "Unnecessary escape character: \\?.", ["1534", "1535"], "'isRtl' is assigned a value but never used.", "'useRef' is defined but never used.", "'GoogleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'location'. Either include it or remove the dependency array.", ["1536"], "'Link' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'ArrowUpwardIcon' is defined but never used.", "'ArrowDownwardIcon' is defined but never used.", "'Stack' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1537"], "'CircularProgress' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTeachers'. Either include it or remove the dependency array.", ["1538"], "'Tooltip' is defined but never used.", "'debounce' is defined but never used.", "'openVideoDialog' is assigned a value but never used.", "'handleOpenVideoDialog' is assigned a value but never used.", "'handleCloseVideoDialog' is assigned a value but never used.", ["1539", "1540"], ["1541", "1542"], ["1543", "1544"], "'safeParseJSON' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLanguages'. Either include it or remove the dependency array.", ["1545"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["1546"], "'t' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUpdates'. Either include it or remove the dependency array.", ["1547"], "'recentSessions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSessions'. Either include it or remove the dependency array.", ["1548"], "React Hook useEffect has missing dependencies: 'fetchBalance' and 'fetchTransactions'. Either include them or remove the dependency array.", ["1549"], "'CheckIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["1550"], "React Hook useEffect has a missing dependency: 'fetchEarnings'. Either include it or remove the dependency array.", ["1551"], "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchWithdrawals'. Either include it or remove the dependency array.", ["1552"], "'updateUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProfileData'. Either include it or remove the dependency array.", ["1553"], ["1554"], ["1555"], ["1556"], "'Grid' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'EventBusyIcon' is defined but never used.", "'selectedDay' is assigned a value but never used.", "'setSelectedDay' is assigned a value but never used.", "'selectedHour' is assigned a value but never used.", "'setSelectedHour' is assigned a value but never used.", "'selectAllForDay' is assigned a value but never used.", "'clearAllForDay' is assigned a value but never used.", "'selectTimeForAllDays' is assigned a value but never used.", "'clearTimeForAllDays' is assigned a value but never used.", "'useLocation' is defined but never used.", "'axios' is defined but never used.", "'Button' is defined but never used.", "'ContentCopyIcon' is defined but never used.", "'convertFromDatabaseTime' is defined but never used.", "'getCurrentTimeInTimezone' is defined but never used.", "'currentTime' is assigned a value but never used.", "'calculatePrice' is assigned a value but never used.", "'handleCopyLink' is assigned a value but never used.", "'getMeetingActions' is assigned a value but never used.", "'toast' is defined but never used.", "'availableTimesForDay' is assigned a value but never used.", "'loadingDays' is assigned a value but never used.", "'loadingTimes' is assigned a value but never used.", "'setLoadingTimes' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableHours' and 'fetchWeeklyBreaks'. Either include them or remove the dependency array.", ["1557"], "React Hook useEffect has a missing dependency: 'fetchWeeklyBreaks'. Either include it or remove the dependency array.", ["1558"], "'response' is assigned a value but never used.", ["1559"], "React Hook useEffect has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1560"], "React Hook useCallback has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1561"], "React Hook useCallback has an unnecessary dependency: 'messages'. Either exclude it or remove the dependency array.", ["1562"], ["1563", "1564"], ["1565", "1566"], ["1567", "1568"], "'setIsRtl' is assigned a value but never used.", "'i18n' is defined but never used.", ["1569"], "React Hook useEffect has missing dependencies: 'fetchBalance', 'fetchSettings', and 'fetchWithdrawals'. Either include them or remove the dependency array.", ["1570"], ["1571"], "'Chip' is defined but never used.", "'Collapse' is defined but never used.", "'StarBorderIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'ExpandLessIcon' is defined but never used.", "'expandedReplies' is assigned a value but never used.", "'toggleReplyExpansion' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'fullScreen' is assigned a value but never used.", ["1572"], ["1573", "1574"], ["1575", "1576"], ["1577", "1578"], "'availableSlots' is assigned a value but never used.", "'loadingSlots' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'id'. Either exclude it or remove the dependency array.", ["1579"], "'handleBookSlot' is assigned a value but never used.", ["1580", "1581"], ["1582", "1583"], ["1584", "1585"], "'Typography' is defined but never used.", "'isConnected' is assigned a value but never used.", ["1586"], ["1587"], ["1588"], "'token' is assigned a value but never used.", ["1589"], "'MoneyIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMeetings'. Either include it or remove the dependency array.", ["1590"], "'getMeetingDateInStudentTimezone' is assigned a value but never used.", "'formatMeetingDateInStudentTimezone' is assigned a value but never used.", "'dateFnsFormat' is assigned a value but never used.", "'getCurrentTimeInStudentTimezone' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", ["1591"], "'CardActions' is defined but never used.", "'Avatar' is defined but never used.", "'isSameDay' is defined but never used.", "'convertBookingDateTime' is defined but never used.", "'isFullHourAvailable' is assigned a value but never used.", "'checkCrossHourAvailability' is assigned a value but never used.", "'checkSecondHalfAvailability' is assigned a value but never used.", "'renderBookingSuccess' is assigned a value but never used.", "'PersonIcon' is defined but never used.", "'datePart' is assigned a value but never used.", "'renderBookings' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'teachers'. Either include it or remove the dependency array.", ["1592"], "'ListItemButton' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPending'. Either include it or remove the dependency array.", ["1593"], "'AccessTimeIcon' is defined but never used.", "'moment' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'isPartOfFullHour' is assigned a value but never used.", "'isFullHourStart' is assigned a value but never used.", "'isFullHourSecondSlot' is assigned a value but never used.", "'formattedDate' is defined but never used.", "'formatDistanceToNow' is defined but never used.", "'ar' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'Checkbox' is defined but never used.", "'FormHelperText' is defined but never used.", "'VideoFileIcon' is defined but never used.", "'LinkIcon' is defined but never used.", "'timeSlots' is assigned a value but never used.", "'useMemo' is defined but never used.", "'MeetingConsumer' is defined but never used.", "'useMeeting' is defined but never used.", "'useParticipant' is defined but never used.", "'createMeetingWithCustomId' is defined but never used.", "'validateMeeting' is defined but never used.", "'WaitingToJoinScreen' is defined but never used.", "React Hook useEffect has a missing dependency: 'isExemptPage'. Either include it or remove the dependency array.", ["1594"], "React Hook useEffect has an unnecessary dependency: 'raisedHandsParticipants'. Either exclude it or remove the dependency array. Outer scope values like 'raisedHandsParticipants' aren't valid dependencies because mutating them doesn't re-render the component.", ["1595"], "React Hook useEffect has a missing dependency: 'getCameraDevices'. Either include it or remove the dependency array.", ["1596"], "React Hook useEffect has a missing dependency: 'getAudioDevices'. Either include it or remove the dependency array.", ["1597"], "React Hook useEffect has a missing dependency: 'checkMediaPermission'. Either include it or remove the dependency array.", ["1598"], "React Hook useEffect has a missing dependency: 'onDeviceChanged'. Either include it or remove the dependency array.", ["1599"], ["1600"], "React Hook useEffect has a missing dependency: 'waitingMessages'. Either include it or remove the dependency array.", ["1601"], "React Hook useEffect has missing dependencies: 'leave', 'onClose', and 'setIsMeetingLeft'. Either include them or remove the dependency array. If 'setIsMeetingLeft' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1602"], "React Hook useEffect has a missing dependency: 'updateStats'. Either include it or remove the dependency array.", ["1603"], "React Hook useEffect has a missing dependency: 'setDidDeviceChange'. Either include it or remove the dependency array. If 'setDidDeviceChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1604"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "Expected '===' and instead saw '=='.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "React Hook useEffect has a missing dependency: 'meetingId'. Either include it or remove the dependency array.", ["1605"], "'ScheduleIcon' is defined but never used.", "'PaymentIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'CreditCardIcon' is defined but never used.", "'SchoolIcon' is defined but never used.", "'oneHourFromNow' is assigned a value but never used.", "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'isEvening' is assigned a value but never used.", "'Menu' is defined but never used.", "'differenceInDays' is defined but never used.", "'differenceInHours' is defined but never used.", "'differenceInMinutes' is defined but never used.", "'differenceInSeconds' is defined but never used.", "React Hook useEffect has a missing dependency: 'location.pathname'. Either include it or remove the dependency array.", ["1606"], "React Hook useEffect has a missing dependency: 'userTimezone'. Either include it or remove the dependency array.", ["1607"], "React Hook useEffect has a missing dependency: 'currentUser'. Either include it or remove the dependency array.", ["1608"], "'Alert' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1609"], {"desc": "1610", "fix": "1611"}, {"desc": "1612", "fix": "1613"}, {"desc": "1614", "fix": "1615"}, {"desc": "1616", "fix": "1617"}, {"messageId": "1618", "fix": "1619", "desc": "1620"}, {"messageId": "1621", "fix": "1622", "desc": "1623"}, {"messageId": "1618", "fix": "1624", "desc": "1620"}, {"messageId": "1621", "fix": "1625", "desc": "1623"}, {"messageId": "1618", "fix": "1626", "desc": "1620"}, {"messageId": "1621", "fix": "1627", "desc": "1623"}, {"desc": "1628", "fix": "1629"}, {"desc": "1630", "fix": "1631"}, {"desc": "1632", "fix": "1633"}, {"messageId": "1618", "fix": "1634", "desc": "1620"}, {"messageId": "1621", "fix": "1635", "desc": "1623"}, {"messageId": "1618", "fix": "1636", "desc": "1620"}, {"messageId": "1621", "fix": "1637", "desc": "1623"}, {"messageId": "1618", "fix": "1638", "desc": "1620"}, {"messageId": "1621", "fix": "1639", "desc": "1623"}, {"desc": "1640", "fix": "1641"}, {"desc": "1642", "fix": "1643"}, {"desc": "1644", "fix": "1645"}, {"desc": "1646", "fix": "1647"}, {"desc": "1648", "fix": "1649"}, {"desc": "1650", "fix": "1651"}, {"desc": "1652", "fix": "1653"}, {"desc": "1654", "fix": "1655"}, {"desc": "1656", "fix": "1657"}, {"desc": "1658", "fix": "1659"}, {"desc": "1658", "fix": "1660"}, {"desc": "1658", "fix": "1661"}, {"desc": "1662", "fix": "1663"}, {"desc": "1664", "fix": "1665"}, {"desc": "1666", "fix": "1667"}, {"desc": "1668", "fix": "1669"}, {"desc": "1670", "fix": "1671"}, {"desc": "1672", "fix": "1673"}, {"messageId": "1618", "fix": "1674", "desc": "1620"}, {"messageId": "1621", "fix": "1675", "desc": "1623"}, {"messageId": "1618", "fix": "1676", "desc": "1620"}, {"messageId": "1621", "fix": "1677", "desc": "1623"}, {"messageId": "1618", "fix": "1678", "desc": "1620"}, {"messageId": "1621", "fix": "1679", "desc": "1623"}, {"desc": "1648", "fix": "1680"}, {"desc": "1681", "fix": "1682"}, {"desc": "1683", "fix": "1684"}, {"desc": "1685", "fix": "1686"}, {"messageId": "1618", "fix": "1687", "desc": "1620"}, {"messageId": "1621", "fix": "1688", "desc": "1623"}, {"messageId": "1618", "fix": "1689", "desc": "1620"}, {"messageId": "1621", "fix": "1690", "desc": "1623"}, {"messageId": "1618", "fix": "1691", "desc": "1620"}, {"messageId": "1621", "fix": "1692", "desc": "1623"}, {"desc": "1693", "fix": "1694"}, {"messageId": "1618", "fix": "1695", "desc": "1620"}, {"messageId": "1621", "fix": "1696", "desc": "1623"}, {"messageId": "1618", "fix": "1697", "desc": "1620"}, {"messageId": "1621", "fix": "1698", "desc": "1623"}, {"messageId": "1618", "fix": "1699", "desc": "1620"}, {"messageId": "1621", "fix": "1700", "desc": "1623"}, {"desc": "1666", "fix": "1701"}, {"desc": "1668", "fix": "1702"}, {"desc": "1670", "fix": "1703"}, {"desc": "1683", "fix": "1704"}, {"desc": "1705", "fix": "1706"}, {"desc": "1648", "fix": "1707"}, {"desc": "1708", "fix": "1709"}, {"desc": "1710", "fix": "1711"}, {"desc": "1712", "fix": "1713"}, {"desc": "1714", "fix": "1715"}, {"desc": "1716", "fix": "1717"}, {"desc": "1718", "fix": "1719"}, {"desc": "1720", "fix": "1721"}, {"desc": "1722", "fix": "1723"}, {"desc": "1724", "fix": "1725"}, {"desc": "1726", "fix": "1727"}, {"desc": "1728", "fix": "1729"}, {"desc": "1730", "fix": "1731"}, {"desc": "1732", "fix": "1733"}, {"desc": "1734", "fix": "1735"}, {"desc": "1736", "fix": "1737"}, {"desc": "1738", "fix": "1739"}, {"desc": "1740", "fix": "1741"}, {"desc": "1742", "fix": "1743"}, "Update the dependencies array to be: [handleLogout]", {"range": "1744", "text": "1745"}, "Update the dependencies array to be: [isAuthenticated, token, currentUser, socket]", {"range": "1746", "text": "1747"}, "Update the dependencies array to be: [socket, isConnected, currentUser, fetchUnreadCount]", {"range": "1748", "text": "1749"}, "Update the dependencies array to be: [appliedFilters, page, searchFilters.priceRange, t]", {"range": "1750", "text": "1751"}, "removeEscape", {"range": "1752", "text": "1753"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1754", "text": "1755"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1756", "text": "1753"}, {"range": "1757", "text": "1755"}, {"range": "1758", "text": "1753"}, {"range": "1759", "text": "1755"}, "Update the dependencies array to be: [location.state, i18n.language, location]", {"range": "1760", "text": "1761"}, "Update the dependencies array to be: [fetchStudents, page, rowsPerPage, searchQuery, t]", {"range": "1762", "text": "1763"}, "Update the dependencies array to be: [fetchTeachers, page, rowsPerPage, searchQuery, t]", {"range": "1764", "text": "1765"}, {"range": "1766", "text": "1753"}, {"range": "1767", "text": "1755"}, {"range": "1768", "text": "1753"}, {"range": "1769", "text": "1755"}, {"range": "1770", "text": "1753"}, {"range": "1771", "text": "1755"}, "Update the dependencies array to be: [fetchLanguages]", {"range": "1772", "text": "1773"}, "Update the dependencies array to be: [fetchCategories]", {"range": "1774", "text": "1775"}, "Update the dependencies array to be: [fetchUpdates, page, rowsPerPage, statusFilter]", {"range": "1776", "text": "1777"}, "Update the dependencies array to be: [fetchSessions, filters]", {"range": "1778", "text": "1779"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", {"range": "1780", "text": "1781"}, "Update the dependencies array to be: [token, tabValue, page, rowsPerPage, fetchMessages]", {"range": "1782", "text": "1783"}, "Update the dependencies array to be: [fetchEarnings, page, rowsPerPage]", {"range": "1784", "text": "1785"}, "Update the dependencies array to be: [fetchWithdrawals, page, rowsPerPage, statusFilter]", {"range": "1786", "text": "1787"}, "Update the dependencies array to be: [t, currentUser, fetchProfileData]", {"range": "1788", "text": "1789"}, "Update the dependencies array to be: [currentUser, fetchProfileData]", {"range": "1790", "text": "1791"}, {"range": "1792", "text": "1791"}, {"range": "1793", "text": "1791"}, "Update the dependencies array to be: [token, t, fetchAvailableHours, fetchWeeklyBreaks]", {"range": "1794", "text": "1795"}, "Update the dependencies array to be: [currentWeekStart, fetchWeeklyBreaks, token]", {"range": "1796", "text": "1797"}, "Update the dependencies array to be: [socket, selectedChat, t]", {"range": "1798", "text": "1799"}, "Update the dependencies array to be: [socket, selectedChat, decreaseUnreadCount]", {"range": "1800", "text": "1801"}, "Update the dependencies array to be: [decreaseUnreadCount, socket]", {"range": "1802", "text": "1803"}, "Update the dependencies array to be: [socket, isConnected, currentUser, t]", {"range": "1804", "text": "1805"}, {"range": "1806", "text": "1753"}, {"range": "1807", "text": "1755"}, {"range": "1808", "text": "1753"}, {"range": "1809", "text": "1755"}, {"range": "1810", "text": "1753"}, {"range": "1811", "text": "1755"}, {"range": "1812", "text": "1781"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", {"range": "1813", "text": "1814"}, "Update the dependencies array to be: [token, page, rowsPerPage, fetchMessages]", {"range": "1815", "text": "1816"}, "Update the dependencies array to be: [page, currentUser, token, appliedFilters, fetchTeachers]", {"range": "1817", "text": "1818"}, {"range": "1819", "text": "1753"}, {"range": "1820", "text": "1755"}, {"range": "1821", "text": "1753"}, {"range": "1822", "text": "1755"}, {"range": "1823", "text": "1753"}, {"range": "1824", "text": "1755"}, "Update the dependencies array to be: [socket, chatId, currentUser]", {"range": "1825", "text": "1826"}, {"range": "1827", "text": "1753"}, {"range": "1828", "text": "1755"}, {"range": "1829", "text": "1753"}, {"range": "1830", "text": "1755"}, {"range": "1831", "text": "1753"}, {"range": "1832", "text": "1755"}, {"range": "1833", "text": "1799"}, {"range": "1834", "text": "1801"}, {"range": "1835", "text": "1803"}, {"range": "1836", "text": "1816"}, "Update the dependencies array to be: [currentUser, fetchMeetings]", {"range": "1837", "text": "1838"}, {"range": "1839", "text": "1781"}, "Update the dependencies array to be: [success, editingReviewId, teachers]", {"range": "1840", "text": "1841"}, "Update the dependencies array to be: [currentUser, fetchPending, location.pathname]", {"range": "1842", "text": "1843"}, "Update the dependencies array to be: [t, exemptPages, isExemptPage]", {"range": "1844", "text": "1845"}, "Update the dependencies array to be: []", {"range": "1846", "text": "1847"}, "Update the dependencies array to be: [getCameraDevices, isCameraPermissionAllowed]", {"range": "1848", "text": "1849"}, "Update the dependencies array to be: [getAudioDevices, isMicrophonePermissionAllowed]", {"range": "1850", "text": "1851"}, "Update the dependencies array to be: [checkMediaPermission]", {"range": "1852", "text": "1853"}, "Update the dependencies array to be: [onDeviceChanged]", {"range": "1854", "text": "1855"}, "Update the dependencies array to be: [getAudioDevices]", {"range": "1856", "text": "1857"}, "Update the dependencies array to be: [waitingMessages]", {"range": "1858", "text": "1859"}, "Update the dependencies array to be: [leave, meetingData, onClose, participantTz, setIsMeetingLeft]", {"range": "1860", "text": "1861"}, "Update the dependencies array to be: [webcamStream, micStream, screenShareStream, updateStats]", {"range": "1862", "text": "1863"}, "Update the dependencies array to be: [didDeviceChange, setDidDeviceChange]", {"range": "1864", "text": "1865"}, "Update the dependencies array to be: [teacherId, studentId, meetingId]", {"range": "1866", "text": "1867"}, "Update the dependencies array to be: [isAuthenticated, currentUser, allowPendingDeletion, handleLogout, t, i18n.language, location.pathname]", {"range": "1868", "text": "1869"}, "Update the dependencies array to be: [isAuthenticated, currentUser, userTimezone]", {"range": "1870", "text": "1871"}, "Update the dependencies array to be: [currentUser, currentUser.timezone, userTimezone]", {"range": "1872", "text": "1873"}, "Update the dependencies array to be: [page, rowsPerPage, search, roleFilter, fetchUsers]", {"range": "1874", "text": "1875"}, [3797, 3799], "[handleLogout]", [3199, 3236], "[isAuthenticated, token, currentUser, socket]", [3029, 3063], "[socket, isConnected, currentUser, fetchUnreadCount]", [9307, 9355], "[appliedFilters, page, searchFilters.priceRange, t]", [2348, 2349], "", [2348, 2348], "\\", [2357, 2358], [2357, 2357], [2359, 2360], [2359, 2359], [3432, 3463], "[location.state, i18n.language, location]", [2116, 2151], "[fetchStudents, page, rowsPerPage, searchQuery, t]", [2095, 2130], "[fetchTeachers, page, rowsPerPage, searchQuery, t]", [5698, 5699], [5698, 5698], [5707, 5708], [5707, 5707], [5709, 5710], [5709, 5709], [1057, 1059], "[fetchLanguages]", [1069, 1071], "[fetchCategories]", [2347, 2380], "[fetchUpdates, page, rowsPerPage, statusFilter]", [2831, 2840], "[fetchSessions, filters]", [1299, 1338], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", [2708, 2744], "[token, tabValue, page, rowsPerPage, fetchMessages]", [1259, 1278], "[fetchEarnings, page, rowsPerPage]", [1628, 1661], "[fetchWithdrawals, page, rowsPerPage, statusFilter]", [3445, 3461], "[t, currentUser, fetchProfileData]", [4176, 4189], "[currentUser, fetchProfileData]", [4567, 4580], [5070, 5083], [5791, 5801], "[token, t, fetchAvailableHours, fetchWeeklyBreaks]", [5923, 5948], "[currentWeekStart, fetchWeeklyBreaks, token]", [6755, 6777], "[socket, selectedChat, t]", [8233, 8255], "[socket, selectedChat, decreaseUnreadCount]", [9760, 9768], "[decreaseUnreadCount, socket]", [11815, 11862], "[socket, isConnected, currentUser, t]", [1819, 1820], [1819, 1819], [1828, 1829], [1828, 1828], [1830, 1831], [1830, 1830], [1366, 1405], [1858, 1897], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", [2027, 2053], "[token, page, rowsPerPage, fetchMessages]", [8622, 8664], "[page, currentUser, token, appliedFilters, fetchTeachers]", [12227, 12228], [12227, 12227], [12236, 12237], [12236, 12236], [12238, 12239], [12238, 12238], [9297, 9330], "[socket, chatId, currentUser]", [14335, 14336], [14335, 14335], [14344, 14345], [14344, 14344], [14346, 14347], [14346, 14346], [7027, 7049], [8505, 8527], [10032, 10040], [2113, 2139], [1892, 1905], "[current<PERSON><PERSON>, fetchMeetings]", [2364, 2403], [20708, 20734], "[success, editing<PERSON><PERSON><PERSON>wId, teachers]", [3632, 3664], "[currentUser, fetchPending, location.pathname]", [1680, 1696], "[t, exemptPages, isExemptPage]", [1678, 1703], "[]", [4052, 4079], "[getCameraDevices, isCameraPermissionAllowed]", [4135, 4166], "[getAudioDevices, isMicrophonePermissionAllowed]", [4250, 4252], "[checkMediaPermission]", [9249, 9251], "[onDevice<PERSON>hanged]", [12542, 12544], "[getAudioDevices]", [845, 847], "[waitingMessages]", [6972, 7000], "[leave, meeting<PERSON><PERSON>, onClose, participantTz, setIsMeetingLeft]", [6514, 6558], "[webcamStream, micStream, screenShareStream, updateStats]", [1859, 1876], "[didD<PERSON><PERSON><PERSON><PERSON><PERSON>, setDidDeviceChange]", [1198, 1220], "[teacherId, studentId, meetingId]", [11622, 11706], "[isAuthenticated, currentUser, allowPendingDeletion, handleLogout, t, i18n.language, location.pathname]", [12682, 12712], "[isAuthenticated, currentUser, userTimezone]", [13106, 13143], "[currentUser, currentUser.timezone, userTimezone]", [2226, 2265], "[page, rowsPerPage, search, roleFilter, fetchUsers]"]