import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Paper,
  Tooltip,
  Chip,
  useTheme,
  alpha,
  CircularProgress
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';

const AvailableHoursTable = ({ availableHours, loading = false, showStats = true }) => {
  const { t } = useTranslation();
  const theme = useTheme();

  // Define time slots - 30-minute intervals from 00:00 to 23:30
  const timeSlots = [
    { key: "00:00-00:30", label: "00:00", time: "00:00", hour: 0, minute: 0 },
    { key: "00:30-01:00", label: "00:30", time: "00:30", hour: 0, minute: 30 },
    { key: "01:00-01:30", label: "01:00", time: "01:00", hour: 1, minute: 0 },
    { key: "01:30-02:00", label: "01:30", time: "01:30", hour: 1, minute: 30 },
    { key: "02:00-02:30", label: "02:00", time: "02:00", hour: 2, minute: 0 },
    { key: "02:30-03:00", label: "02:30", time: "02:30", hour: 2, minute: 30 },
    { key: "03:00-03:30", label: "03:00", time: "03:00", hour: 3, minute: 0 },
    { key: "03:30-04:00", label: "03:30", time: "03:30", hour: 3, minute: 30 },
    { key: "04:00-04:30", label: "04:00", time: "04:00", hour: 4, minute: 0 },
    { key: "04:30-05:00", label: "04:30", time: "04:30", hour: 4, minute: 30 },
    { key: "05:00-05:30", label: "05:00", time: "05:00", hour: 5, minute: 0 },
    { key: "05:30-06:00", label: "05:30", time: "05:30", hour: 5, minute: 30 },
    { key: "06:00-06:30", label: "06:00", time: "06:00", hour: 6, minute: 0 },
    { key: "06:30-07:00", label: "06:30", time: "06:30", hour: 6, minute: 30 },
    { key: "07:00-07:30", label: "07:00", time: "07:00", hour: 7, minute: 0 },
    { key: "07:30-08:00", label: "07:30", time: "07:30", hour: 7, minute: 30 },
    { key: "08:00-08:30", label: "08:00", time: "08:00", hour: 8, minute: 0 },
    { key: "08:30-09:00", label: "08:30", time: "08:30", hour: 8, minute: 30 },
    { key: "09:00-09:30", label: "09:00", time: "09:00", hour: 9, minute: 0 },
    { key: "09:30-10:00", label: "09:30", time: "09:30", hour: 9, minute: 30 },
    { key: "10:00-10:30", label: "10:00", time: "10:00", hour: 10, minute: 0 },
    { key: "10:30-11:00", label: "10:30", time: "10:30", hour: 10, minute: 30 },
    { key: "11:00-11:30", label: "11:00", time: "11:00", hour: 11, minute: 0 },
    { key: "11:30-12:00", label: "11:30", time: "11:30", hour: 11, minute: 30 },
    { key: "12:00-12:30", label: "12:00", time: "12:00", hour: 12, minute: 0 },
    { key: "12:30-13:00", label: "12:30", time: "12:30", hour: 12, minute: 30 },
    { key: "13:00-13:30", label: "13:00", time: "13:00", hour: 13, minute: 0 },
    { key: "13:30-14:00", label: "13:30", time: "13:30", hour: 13, minute: 30 },
    { key: "14:00-14:30", label: "14:00", time: "14:00", hour: 14, minute: 0 },
    { key: "14:30-15:00", label: "14:30", time: "14:30", hour: 14, minute: 30 },
    { key: "15:00-15:30", label: "15:00", time: "15:00", hour: 15, minute: 0 },
    { key: "15:30-16:00", label: "15:30", time: "15:30", hour: 15, minute: 30 },
    { key: "16:00-16:30", label: "16:00", time: "16:00", hour: 16, minute: 0 },
    { key: "16:30-17:00", label: "16:30", time: "16:30", hour: 16, minute: 30 },
    { key: "17:00-17:30", label: "17:00", time: "17:00", hour: 17, minute: 0 },
    { key: "17:30-18:00", label: "17:30", time: "17:30", hour: 17, minute: 30 },
    { key: "18:00-18:30", label: "18:00", time: "18:00", hour: 18, minute: 0 },
    { key: "18:30-19:00", label: "18:30", time: "18:30", hour: 18, minute: 30 },
    { key: "19:00-19:30", label: "19:00", time: "19:00", hour: 19, minute: 0 },
    { key: "19:30-20:00", label: "19:30", time: "19:30", hour: 19, minute: 30 },
    { key: "20:00-20:30", label: "20:00", time: "20:00", hour: 20, minute: 0 },
    { key: "20:30-21:00", label: "20:30", time: "20:30", hour: 20, minute: 30 },
    { key: "21:00-21:30", label: "21:00", time: "21:00", hour: 21, minute: 0 },
    { key: "21:30-22:00", label: "21:30", time: "21:30", hour: 21, minute: 30 },
    { key: "22:00-22:30", label: "22:00", time: "22:00", hour: 22, minute: 0 },
    { key: "22:30-23:00", label: "22:30", time: "22:30", hour: 22, minute: 30 },
    { key: "23:00-23:30", label: "23:00", time: "23:00", hour: 23, minute: 0 },
    { key: "23:30-00:00", label: "23:30", time: "23:30", hour: 23, minute: 30 }
  ];

  // Define days of the week
  const daysOfWeek = [
    { key: 'monday', label: t('days.monday') },
    { key: 'tuesday', label: t('days.tuesday') },
    { key: 'wednesday', label: t('days.wednesday') },
    { key: 'thursday', label: t('days.thursday') },
    { key: 'friday', label: t('days.friday') },
    { key: 'saturday', label: t('days.saturday') },
    { key: 'sunday', label: t('days.sunday') }
  ];

  // Get total selected hours
  const getTotalSelectedHours = () => {
    if (!availableHours) return 0;
    return Object.values(availableHours).reduce((total, daySlots) => total + (daySlots ? daySlots.length : 0), 0);
  };

  // Get abbreviated day names for mobile
  const getAbbreviatedDayName = (dayKey) => {
    const abbreviations = {
      monday: t('days.mondayShort') || 'Mon',
      tuesday: t('days.tuesdayShort') || 'Tue',
      wednesday: t('days.wednesdayShort') || 'Wed',
      thursday: t('days.thursdayShort') || 'Thu',
      friday: t('days.fridayShort') || 'Fri',
      saturday: t('days.saturdayShort') || 'Sat',
      sunday: t('days.sundayShort') || 'Sun'
    };
    return abbreviations[dayKey] || dayKey.substring(0, 3);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!availableHours || getTotalSelectedHours() === 0) {
    return (
      <Paper elevation={3} sx={{ p: 3, mb: 4, borderRadius: 2, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary">
          {t('teacher.noAvailableHours')}
        </Typography>
      </Paper>
    );
  }

  return (
    <Paper elevation={3} sx={{ mb: 4, borderRadius: 2 }}>
      {/* Header with Stats */}
      {showStats && (
        <Box sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          p: 3,
          color: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 2
        }}>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
              📅 {t('teacher.availableHours')}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.9 }}>
              {t('teacher.scheduleDescription')}
            </Typography>
          </Box>
          <Chip
            icon={<EventAvailableIcon />}
            label={`${getTotalSelectedHours()} ${t('teacher.timeSlots')}`}
            sx={{
              bgcolor: 'rgba(255,255,255,0.2)',
              color: 'white',
              fontWeight: 'bold',
              '& .MuiChip-icon': { color: 'white' }
            }}
          />
        </Box>
      )}

      {/* Calendar Table */}
      <Box sx={{ overflow: 'auto' }}>
        {/* Days Header */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '60px repeat(7, 1fr)',
            sm: '80px repeat(7, 1fr)',
            md: '120px repeat(7, 1fr)',
          },
          bgcolor: alpha(theme.palette.primary.main, 0.05),
          borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          position: 'sticky',
          top: 0,
          zIndex: 10
        }}>
          <Box sx={{
            p: { xs: 1, sm: 1.5, md: 2 },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
          }}>
            <Typography variant="body2" sx={{
              fontWeight: 'bold',
              color: theme.palette.text.secondary,
              fontSize: { xs: '0.7rem', sm: '0.8rem', md: '0.9rem' }
            }}>
              ⏰ {t('teacher.time')}
            </Typography>
          </Box>
          {daysOfWeek.map((day) => (
            <Box
              key={day.key}
              sx={{
                p: { xs: 1, sm: 1.5, md: 2 },
                textAlign: 'center',
                borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                '&:last-child': { borderRight: 'none' }
              }}
            >
              <Typography variant="h6" sx={{
                fontWeight: 'bold',
                color: theme.palette.primary.main,
                fontSize: { xs: '0.7rem', sm: '0.9rem', md: '1.1rem' }
              }}>
                <Box component="span" sx={{ display: { xs: 'none', md: 'block' } }}>
                  {day.label}
                </Box>
                <Box component="span" sx={{ display: { xs: 'none', sm: 'block', md: 'none' } }}>
                  {day.label.length > 6 ? day.label.substring(0, 6) : day.label}
                </Box>
                <Box component="span" sx={{ display: { xs: 'block', sm: 'none' } }}>
                  {getAbbreviatedDayName(day.key)}
                </Box>
              </Typography>
            </Box>
          ))}
        </Box>

        {/* Time Slots Grid */}
        <Box>
          {timeSlots.map((timeSlot, index) => {
            const isHourStart = timeSlot.minute === 0;
            return (
              <Box
                key={timeSlot.key}
                sx={{
                  display: 'grid',
                  gridTemplateColumns: {
                    xs: '60px repeat(7, 1fr)',
                    sm: '80px repeat(7, 1fr)',
                    md: '120px repeat(7, 1fr)',
                  },
                  borderBottom: `1px solid ${alpha(theme.palette.divider, isHourStart ? 0.3 : 0.1)}`,
                  bgcolor: index % 4 < 2 ? alpha(theme.palette.primary.main, 0.02) : 'transparent',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                {/* Time Label */}
                <Box sx={{
                  p: { xs: 0.5, sm: 1, md: 1.5 },
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  bgcolor: isHourStart ? alpha(theme.palette.primary.main, 0.05) : 'transparent'
                }}>
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: isHourStart ? 'bold' : 'medium',
                      color: isHourStart ? theme.palette.primary.main : theme.palette.text.secondary,
                      fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.8rem' }
                    }}
                  >
                    {timeSlot.label}
                  </Typography>
                </Box>

                {/* Day Cells */}
                {daysOfWeek.map((day) => {
                  const isSelected = availableHours[day.key] && availableHours[day.key].includes(timeSlot.key);
                  return (
                    <Box
                      key={`${day.key}-${timeSlot.key}`}
                      sx={{
                        p: { xs: 0.2, sm: 0.5, md: 1 },
                        minHeight: { xs: '28px', sm: '36px', md: '45px' },
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRight: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                        '&:last-child': { borderRight: 'none' }
                      }}
                    >
                      {isSelected ? (
                        <Tooltip title={`${t('teacher.available')} - ${timeSlot.label}`} arrow>
                          <Box sx={{
                            width: '100%',
                            height: { xs: '20px', sm: '26px', md: '32px' },
                            borderRadius: 1,
                            background: `linear-gradient(135deg, ${alpha(theme.palette.success.main, 0.8)} 0%, ${alpha(theme.palette.success.dark, 0.9)} 100%)`,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            boxShadow: '0 2px 8px rgba(76, 175, 80, 0.3)'
                          }}>
                            <CheckCircleIcon
                              sx={{
                                color: 'white',
                                fontSize: { xs: '0.8rem', sm: '1rem', md: '1.2rem' }
                              }}
                            />
                          </Box>
                        </Tooltip>
                      ) : (
                        <Box sx={{
                          width: '100%',
                          height: { xs: '20px', sm: '26px', md: '32px' },
                          borderRadius: 1,
                          border: `1px dashed ${alpha(theme.palette.grey[400], 0.5)}`,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          bgcolor: alpha(theme.palette.grey[100], 0.3)
                        }}>
                          <RadioButtonUncheckedIcon
                            sx={{
                              color: alpha(theme.palette.grey[400], 0.7),
                              fontSize: { xs: '0.7rem', sm: '0.9rem', md: '1.1rem' }
                            }}
                          />
                        </Box>
                      )}
                    </Box>
                  );
                })}
              </Box>
            );
          })}
        </Box>
      </Box>
    </Paper>
  );
};

export default AvailableHoursTable;
