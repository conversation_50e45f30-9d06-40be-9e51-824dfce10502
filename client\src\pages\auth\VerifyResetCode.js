import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  useTheme,
  alpha,
  Fade,
  Alert,
  CircularProgress,
  Stack
} from '@mui/material';
import {
  ArrowForward as ArrowForwardIcon,
  ArrowBack as ArrowBackIcon,
  LockReset
} from '@mui/icons-material';

const VerifyResetCode = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';

  const [code, setCode] = useState('');
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Get email from session storage
    const storedEmail = sessionStorage.getItem('resetEmail');
    if (!storedEmail) {
      // Redirect to forgot password page if email is not found
      navigate('/forgot-password');
    } else {
      setEmail(storedEmail);
    }
  }, [navigate]);

  const handleChange = (e) => {
    // Only allow numbers and limit to 6 digits
    const value = e.target.value.replace(/[^0-9]/g, '').slice(0, 6);
    setCode(value);
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (!code || code.length !== 6) {
      setError(t('auth.invalidCode'));
      setLoading(false);
      return;
    }

    try {
      const response = await axios.post('/api/auth/verify-reset-code', { email, code });
      
      if (response.data.success) {
        // Store reset token in session storage for the next step
        sessionStorage.setItem('resetToken', response.data.resetToken);
        // Redirect to reset password page
        navigate('/reset-password');
      }
    } catch (error) {
      setError(error.response?.data?.message || t('auth.verificationFailed'));
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    setLoading(true);
    setError('');

    try {
      await axios.post('/api/auth/forgot-password', { email });
      setError(''); // Clear any existing errors
      // Show success message
      alert(t('auth.resetCodeResent'));
    } catch (error) {
      setError(error.response?.data?.message || t('auth.resetRequestFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        background: `linear-gradient(to bottom, ${alpha(theme.palette.primary.dark, 0.9)}, ${alpha('#000', 0.7)})`,
        py: 8,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: 'url("https://png.pngtree.com/background/20230216/original/pngtree-islamic-background-picture-image_2027687.jpg")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          zIndex: -1
        },
        '&::after': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: `radial-gradient(circle at center, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.dark, 0.4)})`,
          zIndex: -1
        }
      }}
    >
      <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 1 }}>
        <Fade in timeout={1000}>
          <Paper
            elevation={24}
            sx={{
              p: { xs: 3, sm: 6 },
              borderRadius: 4,
              backdropFilter: 'blur(20px)',
              backgroundColor: alpha(theme.palette.background.paper, 0.8),
              boxShadow: `0 8px 32px ${alpha(theme.palette.primary.dark, 0.2)}`,
              position: 'relative',
              overflow: 'hidden',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: 4,
                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              }
            }}
          >
            <Box component="form" onSubmit={handleSubmit}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
                <LockReset sx={{ fontSize: 60, color: theme.palette.primary.main }} />
              </Box>

              <Typography
                variant="h4"
                align="center"
                gutterBottom
                sx={{
                  fontWeight: 700,
                  mb: 2,
                  background: `linear-gradient(120deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  textShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}
              >
                {t('auth.verifyCode')}
              </Typography>

              <Typography variant="body1" align="center" sx={{ mb: 4 }}>
                {t('auth.verifyCodeInstructions')}
                <Box component="span" sx={{ fontWeight: 'bold', display: 'block', mt: 1 }}>
                  {email}
                </Box>
              </Typography>

              {error && (
                <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
                  {error}
                </Alert>
              )}

              <TextField
                fullWidth
                name="code"
                label={t('auth.verificationCode')}
                value={code}
                onChange={handleChange}
                error={Boolean(error)}
                sx={{ mb: 3 }}
                inputProps={{
                  maxLength: 6,
                  inputMode: 'numeric',
                  pattern: '[0-9]*'
                }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                endIcon={!isRtl ? <ArrowForwardIcon /> : <ArrowBackIcon />}
                sx={{
                  height: 48,
                  background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  '&:hover': {
                    background: `linear-gradient(90deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
                  },
                }}
              >
                {loading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  t('auth.verifyAndContinue')
                )}
              </Button>

              <Stack direction="row" spacing={2} justifyContent="center" sx={{ mt: 3 }}>
                <Button
                  variant="text"
                  onClick={handleResendCode}
                  disabled={loading}
                >
                  {t('auth.resendCode')}
                </Button>

                <Link
                  to="/forgot-password"
                  style={{
                    color: theme.palette.primary.main,
                    textDecoration: 'none',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  {t('auth.changeEmail')}
                </Link>
              </Stack>
            </Box>
          </Paper>
        </Fade>
      </Container>
    </Box>
  );
};

export default VerifyResetCode;
