import React, { useState } from 'react';
import {
  CardElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import {
  Box,
  Button,
  Typography,
  Alert,
  CircularProgress
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import axios from '../utils/axios';
import { useAuth } from '../contexts/AuthContext';

const StripePayment = ({ amount, onSuccess, onError }) => {
  const { t } = useTranslation();
  const { token } = useAuth();
  const stripe = useStripe();
  const elements = useElements();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setProcessing(true);
    setError('');

    try {
      // Create payment intent
      const { data: intentData } = await axios.post('/wallet/stripe/create-payment-intent', {
        amount: parseFloat(amount)
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!intentData.success) {
        throw new Error(intentData.error || 'Failed to create payment intent');
      }

      const cardElement = elements.getElement(CardElement);

      // Confirm payment
      const { error: stripeError, paymentIntent } = await stripe.confirmCardPayment(
        intentData.clientSecret,
        {
          payment_method: {
            card: cardElement,
          }
        }
      );

      if (stripeError) {
        setError(stripeError.message);
        if (onError) onError(stripeError.message);
      } else if (paymentIntent.status === 'succeeded') {
        // Confirm payment on backend
        const { data: confirmData } = await axios.post('/wallet/stripe/confirm-payment', {
          paymentIntentId: paymentIntent.id
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (confirmData.success) {
          if (onSuccess) onSuccess(confirmData);
        } else {
          throw new Error(confirmData.error || 'Failed to confirm payment');
        }
      }
    } catch (err) {
      console.error('Payment error:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Payment failed';
      setError(errorMessage);
      if (onError) onError(errorMessage);
    } finally {
      setProcessing(false);
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#9e2146',
      },
    },
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
      <Typography variant="h6" gutterBottom>
        {t('wallet.payWithStripe')}
      </Typography>
      
      <Box sx={{ 
        p: 2, 
        border: '1px solid #ddd', 
        borderRadius: 1, 
        mb: 2,
        backgroundColor: '#fafafa'
      }}>
        <CardElement options={cardElementOptions} />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Button
        type="submit"
        variant="contained"
        fullWidth
        disabled={!stripe || processing}
        sx={{ 
          mt: 2,
          backgroundColor: '#635bff',
          '&:hover': {
            backgroundColor: '#5a52e8'
          }
        }}
      >
        {processing ? (
          <CircularProgress size={24} color="inherit" />
        ) : (
          `${t('wallet.pay')} $${amount}`
        )}
      </Button>
    </Box>
  );
};

export default StripePayment;
