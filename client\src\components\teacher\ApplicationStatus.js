import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  CardContent,
  Typography,
  Alert,
  Box
} from '@mui/material';

const ApplicationStatus = ({ status, message, nextSteps }) => {
  const { t } = useTranslation();

  return (
    <Card>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          {t('teacher.application.statusCardTitle')}
        </Typography>
        <Alert 
          severity={
            status === 'approved' ? 'success' :
            status === 'rejected' ? 'error' :
            'info'
          }
          sx={{ mt: 2 }}
        >
          {t(`teacher.application.status.${status}`)}
        </Alert>
        <Typography variant="body1" sx={{ mt: 2 }}>
          {t(message)}
        </Typography>
        {status === 'approved' && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              {t('teacher.application.applicationNextSteps.approved.title')}
            </Typography>
            <Box component="ol" sx={{ pl: 2 }}>
              {t('teacher.application.applicationNextSteps.approved.steps', { returnObjects: true }).map((step, index) => (
                <Typography component="li" key={index} variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {step}
                </Typography>
              ))}
            </Box>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default ApplicationStatus;
