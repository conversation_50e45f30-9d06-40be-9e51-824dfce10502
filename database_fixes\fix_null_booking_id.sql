-- Fix NULL booking_id values and make the column NOT NULL
-- This script must be run in order to fix existing data before applying constraints

-- Step 1: Show current NULL booking_id records
SELECT 
    mi.id,
    mi.meeting_id,
    mi.user_id,
    mi.issue_type,
    mi.booking_id,
    mi.created_at,
    m.student_id,
    m.teacher_id,
    m.meeting_date,
    u.full_name as student_name
FROM meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
JOIN users u ON m.student_id = u.id
WHERE mi.booking_id IS NULL
ORDER BY mi.created_at DESC;

-- Step 2: Try to find and update booking_id for NULL records
UPDATE meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
JOIN (
    SELECT 
        b.id as booking_id,
        b.student_id,
        tp.user_id as teacher_id,
        b.datetime
    FROM bookings b
    JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
    WHERE b.status != 'cancelled'
) b ON b.student_id = m.student_id 
    AND b.teacher_id = m.teacher_id
    AND DATE(b.datetime) = DATE(m.meeting_date)
    AND ABS(TIMESTAMPDIFF(MINUTE, b.datetime, m.meeting_date)) <= 120
SET mi.booking_id = b.booking_id
WHERE mi.booking_id IS NULL;

-- Step 3: For remaining NULL records, try alternative matching
UPDATE meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
JOIN (
    SELECT 
        b.id as booking_id,
        b.student_id,
        tp.user_id as teacher_id,
        b.datetime,
        ROW_NUMBER() OVER (
            PARTITION BY b.student_id, tp.user_id, DATE(b.datetime) 
            ORDER BY b.datetime DESC
        ) as rn
    FROM bookings b
    JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id
    WHERE b.status IN ('completed', 'scheduled', 'ongoing')
) b ON b.student_id = m.student_id 
    AND b.teacher_id = m.teacher_id
    AND DATE(b.datetime) = DATE(m.meeting_date)
    AND b.rn = 1
SET mi.booking_id = b.booking_id
WHERE mi.booking_id IS NULL;

-- Step 4: Show remaining NULL records that couldn't be fixed
SELECT 
    mi.id,
    mi.meeting_id,
    mi.user_id,
    mi.issue_type,
    mi.booking_id,
    mi.created_at,
    m.student_id,
    m.teacher_id,
    m.meeting_date,
    u.full_name as student_name,
    'Could not find matching booking' as note
FROM meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
JOIN users u ON m.student_id = u.id
WHERE mi.booking_id IS NULL
ORDER BY mi.created_at DESC;

-- Step 5: Delete records that still have NULL booking_id (optional - uncomment if needed)
-- WARNING: This will delete feedback records that couldn't be linked to bookings
-- DELETE FROM meeting_issues WHERE booking_id IS NULL;

-- Step 6: Add NOT NULL constraint to booking_id column
-- This will fail if there are still NULL values, so run steps 1-4 first
ALTER TABLE meeting_issues 
MODIFY COLUMN booking_id INT NOT NULL;

-- Step 7: Add foreign key constraint to ensure booking_id references valid bookings
ALTER TABLE meeting_issues 
ADD CONSTRAINT fk_meeting_issues_booking_id 
FOREIGN KEY (booking_id) REFERENCES bookings(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Step 8: Add index for better performance
CREATE INDEX idx_meeting_issues_booking_id ON meeting_issues (booking_id);

-- Step 9: Verify the changes
DESCRIBE meeting_issues;

-- Step 10: Show final count
SELECT 
    COUNT(*) as total_records,
    COUNT(booking_id) as records_with_booking_id,
    COUNT(*) - COUNT(booking_id) as null_booking_ids
FROM meeting_issues;
