import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  Grid,
  Paper,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Divider,
  TextField
} from '@mui/material';
import {
  Close as CloseIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Schedule as ScheduleIcon,
  CalendarToday as CalendarIcon,
  AccessTime as TimeIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { format, addDays, startOfMonth, endOfMonth, startOfWeek, endOfWeek, isSameMonth, isSameDay, addMonths, subMonths } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import moment from 'moment-timezone';
import { formatDateInStudentTimezone, parseTimezoneOffset } from '../utils/timezone';

const RescheduleDialog = ({
  open,
  onClose,
  booking,
  availableDays,
  onReschedule,
  loading,
  userProfile,
  isTeacherView = false
}) => {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.language === 'ar';
  
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState('');
  const [availableTimesForDate, setAvailableTimesForDate] = useState([]);
  const [loadingTimes, setLoadingTimes] = useState(false);
  const [rescheduleReason, setRescheduleReason] = useState('');
  const [currentTime, setCurrentTime] = useState(new Date());

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (open) {
      setCurrentMonth(new Date());
      setSelectedDate(null);
      setSelectedTime('');
      setAvailableTimesForDate([]);
      setRescheduleReason('');
    }
  }, [open]);

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Generate calendar days - properly organized in weeks
  const generateCalendarDays = () => {
    const monthStart = startOfMonth(currentMonth);
    const monthEnd = endOfMonth(currentMonth);
    const startDate = startOfWeek(monthStart, { weekStartsOn: 0 }); // Start week on Sunday for Arabic calendar
    const endDate = endOfWeek(monthEnd, { weekStartsOn: 0 });

    const days = [];
    let day = startDate;

    while (day <= endDate) {
      days.push(day);
      day = addDays(day, 1);
    }

    return days;
  };

  // Process available days with proper timezone conversion
  const processedAvailableDays = useMemo(() => {
    if (!availableDays || availableDays.length === 0) return {};



    const processedDays = {};
    const now = new Date();
    const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);

    availableDays.forEach(day => {
      if (day.slots && day.slots.length > 0) {
        day.slots.forEach(slot => {
          // تحويل التاريخ والوقت مع بعض لمنطقة المستخدم (نفس المنطقة المستخدمة في أعلى النافذة)
          let convertedDateTime, displayTime, convertedDateStr;
          if (userProfile?.timezone) {
            // استخدام moment-timezone للتحويل الصحيح
            const originalMoment = moment.utc(slot.datetime);
            const offsetMinutes = parseTimezoneOffset(userProfile.timezone);
            const convertedMoment = originalMoment.clone().add(offsetMinutes, 'minutes');

            // إنشاء Date object يعكس الوقت في المنطقة الزمنية المطلوبة
            // نحتاج لتعديل الوقت ليظهر صحيح في المتصفح
            const browserOffsetMinutes = new Date().getTimezoneOffset();
            const adjustedMoment = convertedMoment.clone().subtract(browserOffsetMinutes, 'minutes');
            convertedDateTime = adjustedMoment.toDate();

            displayTime = formatDateInStudentTimezone(slot.datetime, userProfile.timezone, 'HH:mm');

            // استخراج التاريخ الجديد (بعد التحويل لمنطقة المستخدم، مش بعد تعديل المتصفح)
            convertedDateStr = convertedMoment.format('YYYY-MM-DD');
          } else {
            convertedDateTime = new Date(slot.datetime);
            displayTime = convertedDateTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: false });
            convertedDateStr = convertedDateTime.toISOString().split('T')[0];
          }


          // التحقق من أن التاريخ والوقت لم يمضيا بعد
          // استخدام الوقت الحالي في منطقة المستخدم للمقارنة الصحيحة
          let currentTimeInUserTimezone;
          if (userProfile?.timezone) {
            const currentMoment = moment.utc();
            const offsetMinutes = parseTimezoneOffset(userProfile.timezone);
            const userCurrentMoment = currentMoment.clone().add(offsetMinutes, 'minutes');
            const browserOffsetMinutes = new Date().getTimezoneOffset();
            const adjustedCurrentMoment = userCurrentMoment.clone().subtract(browserOffsetMinutes, 'minutes');
            currentTimeInUserTimezone = adjustedCurrentMoment.toDate();
          } else {
            currentTimeInUserTimezone = new Date();
          }



          if (convertedDateTime > currentTimeInUserTimezone) {
            if (!processedDays[convertedDateStr]) {
              processedDays[convertedDateStr] = [];
            }

            processedDays[convertedDateStr].push({
              ...slot,
              convertedDateTime: convertedDateTime,
              displayTime: displayTime
            });
          }
        });
      }
    });


    return processedDays;
  }, [availableDays, userProfile?.timezone]);

  // Memoize available slots check to improve performance
  const availableSlotsCache = useMemo(() => {
    const cache = {};
    Object.keys(processedAvailableDays).forEach(dateStr => {
      cache[dateStr] = processedAvailableDays[dateStr].length > 0;
    });
    return cache;
  }, [processedAvailableDays]);

  // Check if a date has available slots (using cached results)
  const hasAvailableSlots = useCallback((date) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    return availableSlotsCache[dateStr] || false;
  }, [availableSlotsCache]);

  // Auto-refresh available times when current time changes (but preserve selected time)
  useEffect(() => {
    if (selectedDate) {
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      const daySlots = processedAvailableDays[dateStr] || [];

      if (daySlots.length > 0) {
        const validSlots = daySlots
          .filter(slot => {
            return slot.convertedDateTime > new Date();
          })
          .map(slot => ({
            displayTime: slot.displayTime,
            datetime: slot.datetime,
            timeString: slot.displayTime,
            originalSlot: slot
          }))
          .sort((a, b) => new Date(a.originalSlot.convertedDateTime) - new Date(b.originalSlot.convertedDateTime));

        setAvailableTimesForDate(validSlots);

        // إذا كان الوقت المختار لم يعد متاحاً، امسحه
        if (selectedTime && !validSlots.find(slot => slot.datetime === selectedTime)) {
          setSelectedTime('');
        }
      } else {
        setAvailableTimesForDate([]);
        setSelectedTime('');
      }
    }
  }, [processedAvailableDays, selectedDate, selectedTime]);

  // Check if selected time is still valid every minute (not every second)
  useEffect(() => {
    if (selectedTime && availableTimesForDate.length > 0) {
      const selectedSlot = availableTimesForDate.find(slot => slot.datetime === selectedTime);
      if (selectedSlot && selectedSlot.originalSlot.convertedDateTime <= new Date()) {
        // الوقت المختار لم يعد متاحاً
        setSelectedTime('');
      }
    }
  }, [Math.floor(currentTime.getTime() / 60000), selectedTime, availableTimesForDate]); // كل دقيقة مش كل ثانية

  // Handle date selection
  const handleDateSelect = (date) => {
    setSelectedDate(date);
    setSelectedTime('');
    setLoadingTimes(true);

    const dateStr = format(date, 'yyyy-MM-dd');
    const daySlots = processedAvailableDays[dateStr] || [];

    if (daySlots.length > 0) {
      // استخدام البيانات المعالجة مسبقاً (محولة للمنطقة الزمنية الصحيحة)
      const validSlots = daySlots
        .filter(slot => {
          // التحقق من أن الوقت المحول لم يمضي بعد
          return slot.convertedDateTime > new Date();
        })
        .map(slot => ({
          displayTime: slot.displayTime,
          datetime: slot.datetime, // الوقت الأصلي للإرسال للخادم
          timeString: slot.displayTime,
          originalSlot: slot
        }))
        .sort((a, b) => new Date(a.originalSlot.convertedDateTime) - new Date(b.originalSlot.convertedDateTime));

      setAvailableTimesForDate(validSlots);
    } else {
      setAvailableTimesForDate([]);
    }

    setLoadingTimes(false);
  };

  // Handle month navigation
  const navigateMonth = (direction) => {
    if (direction === 'prev') {
      setCurrentMonth(subMonths(currentMonth, 1));
    } else {
      setCurrentMonth(addMonths(currentMonth, 1));
    }
    setSelectedDate(null);
    setSelectedTime('');
    setAvailableTimesForDate([]);
  };

  // Handle reschedule confirmation
  const handleConfirm = () => {
    if (!selectedDate || !selectedTime) return;

    const selectedSlot = availableTimesForDate.find(slot => slot.datetime === selectedTime);
    if (selectedSlot) {
      onReschedule(selectedSlot, rescheduleReason);
    }
  };

  const calendarDays = generateCalendarDays();
  const monthName = format(currentMonth, 'MMMM yyyy', { locale: isRtl ? ar : enUS });

  // Day names for calendar header - properly ordered starting from Sunday
  const dayNames = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: '500px',
          maxHeight: '80vh'
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <ScheduleIcon color="primary" />
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
            {t('bookings.rescheduleTitle', 'إعادة جدولة درس')}
          </Typography>
        </Box>
        <IconButton
          onClick={onClose}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 2, py: 2 }}>
        {/* Current Time Display */}
        <Paper sx={{
          p: 2,
          mb: 2,
          bgcolor: 'info.50',
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'info.200',
          textAlign: 'center'
        }}>
          <Typography variant="body2" sx={{ color: 'info.dark', fontWeight: 'bold', mb: 0.5 }}>
            {t('bookings.currentTime', 'الوقت الحالي')}
          </Typography>
          <Typography variant="h6" sx={{ color: 'info.main', fontWeight: 'bold' }}>
            {userProfile?.timezone
              ? formatDateInStudentTimezone(currentTime.toISOString(), userProfile.timezone, 'h:mm:ss A')
              : currentTime.toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  second: '2-digit',
                  hour12: true
                })
            }
          </Typography>
          <Typography variant="caption" sx={{ color: 'info.dark', opacity: 0.8 }}>
            {userProfile?.timezone ? (
              // عرض التاريخ في منطقة المستخدم الزمنية
              formatDateInStudentTimezone(currentTime.toISOString(), userProfile.timezone, 'dddd, MMMM D, YYYY')
            ) : (
              // عرض التاريخ المحلي
              currentTime.toLocaleDateString('ar-EG', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })
            )}
          </Typography>
          {userProfile?.timezone && (
            <Typography variant="caption" sx={{ color: 'info.dark', opacity: 0.6, fontSize: '0.7rem' }}>
              المنطقة الزمنية: {userProfile.timezone}
            </Typography>
          )}
        </Paper>

        {/* Current booking info */}
        {booking && (
          <Paper sx={{
            p: 2,
            mb: 2,
            bgcolor: 'warning.50',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'warning.200'
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
              <Box sx={{
                p: 0.5,
                borderRadius: '50%',
                bgcolor: 'warning.main',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <CalendarIcon sx={{ color: 'white', fontSize: '1rem' }} />
              </Box>
              <Typography variant="subtitle1" color="warning.dark" sx={{ fontWeight: 'bold' }}>
                {t('bookings.currentBooking', 'الحجز الحالي')}
              </Typography>
            </Box>

            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1.5,
              flexWrap: 'wrap',
              p: 1.5,
              bgcolor: 'white',
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'warning.200'
            }}>
              <Typography variant="body2" sx={{ fontWeight: 'medium', flex: 1 }}>
                {userProfile?.timezone ? (
                  moment(formatDateInStudentTimezone(booking.datetime, userProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('dddd, MMMM D, YYYY - h:mm A')
                ) : (
                  format(new Date(booking.datetime), 'EEEE, MMMM d, yyyy - p', { locale: isRtl ? ar : enUS })
                )}
              </Typography>
              <Chip
                label={`${booking.duration} ${t('bookings.minutes', 'دقيقة')}`}
                size="small"
                color="warning"
                sx={{
                  fontWeight: 'bold',
                  fontSize: '0.75rem'
                }}
              />
            </Box>
          </Paper>
        )}

        <Grid container spacing={2}>
          {/* Calendar Section */}
          <Grid item xs={12} md={7}>
            <Paper sx={{
              p: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'primary.100',
              minHeight: 350
            }}>
              {/* Loading indicator for calendar */}
              {loading && (
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: 300,
                  flexDirection: 'column',
                  gap: 1.5
                }}>
                  <CircularProgress size={30} />
                  <Typography variant="body2" color="text.secondary">
                    {t('bookings.loadingDays', 'جاري تحميل الأيام المتاحة...')}
                  </Typography>
                </Box>
              )}

              {/* Calendar content */}
              {!loading && (
                <>
                  {/* Calendar Header */}
                  <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                mb: 3,
                p: 1,
                bgcolor: 'grey.50',
                borderRadius: 2,
                border: '1px solid',
                borderColor: 'grey.200'
              }}>
                <IconButton
                  onClick={() => navigateMonth('prev')}
                  size="small"
                  sx={{
                    bgcolor: 'white',
                    border: '1px solid',
                    borderColor: 'grey.300',
                    '&:hover': {
                      bgcolor: 'primary.main',
                      color: 'white',
                      borderColor: 'primary.main'
                    },
                    transition: 'all 0.2s ease'
                  }}
                >
                  <ChevronLeftIcon />
                </IconButton>

                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 'bold',
                    color: 'primary.main',
                    textAlign: 'center',
                    minWidth: 200,
                    py: 1,
                    px: 2,
                    bgcolor: 'white',
                    borderRadius: 1,
                    border: '1px solid',
                    borderColor: 'primary.200',
                    fontSize: '1.1rem'
                  }}
                >
                  {monthName}
                </Typography>

                <IconButton
                  onClick={() => navigateMonth('next')}
                  size="small"
                  sx={{
                    bgcolor: 'white',
                    border: '1px solid',
                    borderColor: 'grey.300',
                    '&:hover': {
                      bgcolor: 'primary.main',
                      color: 'white',
                      borderColor: 'primary.main'
                    },
                    transition: 'all 0.2s ease'
                  }}
                >
                  <ChevronRightIcon />
                </IconButton>
              </Box>

              {/* Day names header - properly aligned with calendar grid */}
              <Grid container spacing={0.5} sx={{ mb: 2 }}>
                {dayNames.map((dayName, index) => (
                  <Grid item xs key={index}>
                    <Box sx={{
                      textAlign: 'center',
                      py: 2,
                      fontSize: '0.875rem',
                      fontWeight: 'bold',
                      color: 'white',
                      background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                      borderRadius: 2,
                      border: '1px solid',
                      borderColor: 'primary.300',
                      letterSpacing: '0.5px',
                      boxShadow: '0 2px 8px rgba(25, 118, 210, 0.2)',
                      transition: 'all 0.2s ease',
                      height: 45,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      '&:hover': {
                        transform: 'translateY(-1px)',
                        boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)'
                      }
                    }}>
                      {dayName}
                    </Box>
                  </Grid>
                ))}
              </Grid>

              {/* Calendar Grid - Properly organized in 7x6 grid */}
              <Box sx={{ width: '100%' }}>
                {Array.from({ length: 6 }, (_, weekIndex) => (
                  <Grid container spacing={0.5} key={weekIndex} sx={{ mb: 0.5 }}>
                    {Array.from({ length: 7 }, (_, dayIndex) => {
                      const dayIndexInMonth = weekIndex * 7 + dayIndex;
                      if (dayIndexInMonth >= calendarDays.length) return null;

                      const day = calendarDays[dayIndexInMonth];
                      const isCurrentMonth = isSameMonth(day, currentMonth);
                      const hasSlots = hasAvailableSlots(day);
                      const isSelected = selectedDate && isSameDay(day, selectedDate);

                      // حساب اليوم الحالي والأيام الماضية بناءً على منطقة المستخدم الزمنية
                      let todayInUserTimezone, startOfTodayInUserTimezone;
                      if (userProfile?.timezone) {
                        // الحصول على التاريخ الحالي في منطقة المستخدم
                        const currentMoment = moment.utc();
                        const offsetMinutes = parseTimezoneOffset(userProfile.timezone);
                        const userCurrentMoment = currentMoment.clone().add(offsetMinutes, 'minutes');
                        const browserOffsetMinutes = new Date().getTimezoneOffset();
                        const adjustedCurrentMoment = userCurrentMoment.clone().subtract(browserOffsetMinutes, 'minutes');
                        todayInUserTimezone = adjustedCurrentMoment.toDate();
                        startOfTodayInUserTimezone = new Date(todayInUserTimezone);
                        startOfTodayInUserTimezone.setHours(0, 0, 0, 0);
                      } else {
                        todayInUserTimezone = new Date();
                        startOfTodayInUserTimezone = new Date().setHours(0, 0, 0, 0);
                      }

                      const isPast = day < startOfTodayInUserTimezone;
                      const isToday = isSameDay(day, todayInUserTimezone);

                      return (
                        <Grid item xs key={dayIndex}>
                          <Box
                            onClick={() => {
                              if (isCurrentMonth && !isPast) {
                                handleDateSelect(day);
                              }
                            }}
                            sx={{
                              height: 35,
                              width: '100%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              cursor: isCurrentMonth && !isPast ? 'pointer' : 'default',
                              borderRadius: 1,
                              fontSize: '0.875rem',
                              fontWeight: isSelected ? 'bold' : isToday ? 'medium' : 'normal',
                              position: 'relative',

                              // Colors and backgrounds
                              color: !isCurrentMonth
                                ? 'text.disabled'
                                : isSelected
                                  ? 'white'
                                  : isPast
                                    ? 'text.disabled'
                                    : isToday
                                      ? 'primary.main'
                                      : hasSlots
                                        ? 'success.dark'
                                        : 'text.secondary',

                              bgcolor: isSelected
                                ? 'primary.main'
                                : hasSlots && isCurrentMonth && !isPast
                                  ? 'success.50'
                                  : 'transparent',

                              background: isSelected
                                ? 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)'
                                : undefined,

                              border: isToday && !isSelected
                                ? '2px solid'
                                : hasSlots && isCurrentMonth && !isPast && !isSelected
                                  ? '1px solid'
                                  : '1px solid transparent',

                              borderColor: isToday && !isSelected
                                ? 'primary.main'
                                : hasSlots && isCurrentMonth && !isPast && !isSelected
                                  ? 'success.main'
                                  : 'transparent',

                              boxShadow: isSelected
                                ? '0 4px 12px rgba(25, 118, 210, 0.4)'
                                : hasSlots && isCurrentMonth && !isPast
                                  ? '0 2px 4px rgba(76, 175, 80, 0.2)'
                                  : 'none',

                              '&:hover': isCurrentMonth && !isPast ? {
                                transform: 'scale(1.05)',
                                bgcolor: isSelected ? undefined : hasSlots ? 'success.main' : 'grey.300',
                                background: isSelected
                                  ? 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)'
                                  : undefined,
                                color: 'white',
                                boxShadow: isSelected
                                  ? '0 6px 16px rgba(25, 118, 210, 0.5)'
                                  : hasSlots
                                    ? '0 4px 8px rgba(76, 175, 80, 0.4)'
                                    : '0 2px 4px rgba(0,0,0,0.2)',
                                zIndex: 1
                              } : {},

                              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',

                              // Add a subtle indicator for available days
                              '&::after': hasSlots && isCurrentMonth && !isPast && !isSelected ? {
                                content: '""',
                                position: 'absolute',
                                bottom: 4,
                                left: '50%',
                                transform: 'translateX(-50%)',
                                width: 6,
                                height: 6,
                                borderRadius: '50%',
                                bgcolor: 'success.main',
                                opacity: 0.8
                              } : isCurrentMonth && !isPast && !isSelected ? {
                                content: '""',
                                position: 'absolute',
                                bottom: 4,
                                left: '50%',
                                transform: 'translateX(-50%)',
                                width: 4,
                                height: 4,
                                borderRadius: '50%',
                                bgcolor: 'grey.400',
                                opacity: 0.5
                              } : {}
                            }}
                          >
                            {format(day, 'd')}

                            {/* Today indicator */}
                            {isToday && !isSelected && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: 3,
                                  right: 3,
                                  width: 6,
                                  height: 6,
                                  borderRadius: '50%',
                                  bgcolor: 'primary.main'
                                }}
                              />
                            )}

                            {/* Selected indicator */}
                            {isSelected && (
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: -2,
                                  right: -2,
                                  width: 14,
                                  height: 14,
                                  borderRadius: '50%',
                                  bgcolor: 'white',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '0.7rem',
                                  color: 'primary.main',
                                  fontWeight: 'bold',
                                  boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                                }}
                              >
                                ✓
                              </Box>
                            )}
                          </Box>
                        </Grid>
                      );
                    })}
                  </Grid>
                ))}
              </Box>
                </>
              )}

              {/* No available days message */}
              {!loading && availableDays.length === 0 && (
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  minHeight: 400,
                  flexDirection: 'column',
                  gap: 2
                }}>
                  <CalendarIcon sx={{ fontSize: 48, color: 'text.disabled' }} />
                  <Typography variant="h6" color="text.secondary" textAlign="center">
                    {t('bookings.noAvailableDays', 'لا توجد أيام متاحة')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" textAlign="center">
                    يرجى المحاولة مرة أخرى لاحقاً
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>

          {/* Time Selection Section */}
          <Grid item xs={12} md={5}>
            <Paper sx={{
              p: 2,
              borderRadius: 2,
              border: '1px solid',
              borderColor: 'success.100',
              minHeight: 300
            }}>
              {!selectedDate ? (
                <Box sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  color: 'text.secondary',
                  p: 3
                }}>
                  <TimeIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
                  <Typography variant="h6" textAlign="center" sx={{ mb: 1, fontWeight: 'bold' }}>
                    {t('bookings.selectDateFirst', 'اختر تاريخاً من التقويم أولاً')}
                  </Typography>
                  <Typography variant="body2" textAlign="center" color="text.secondary">
                    انقر على أي يوم متاح (باللون الأخضر) لرؤية الأوقات المتاحة
                  </Typography>
                </Box>
              ) : (
                <Box>
                  <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TimeIcon fontSize="small" />
                    {t('bookings.availableTimes', 'الأوقات المتاحة')}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {format(selectedDate, 'EEEE, MMMM d', { locale: isRtl ? ar : enUS })}
                  </Typography>

                  {loadingTimes ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                      <CircularProgress size={24} />
                    </Box>
                  ) : availableTimesForDate.length === 0 ? (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      {t('bookings.noAvailableTimes', 'لا توجد أوقات متاحة')}
                    </Alert>
                  ) : (
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <InputLabel
                        sx={{
                          color: 'success.main',
                          '&.Mui-focused': { color: 'success.main' }
                        }}
                      >
                        {t('bookings.selectTime', 'اختر الوقت')}
                      </InputLabel>
                      <Select
                        value={selectedTime}
                        label={t('bookings.selectTime', 'اختر الوقت')}
                        onChange={(e) => setSelectedTime(e.target.value)}
                        sx={{
                          '& .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'success.main',
                            borderWidth: 2
                          },
                          '&:hover .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'success.dark'
                          },
                          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                            borderColor: 'success.main'
                          }
                        }}
                      >
                        {availableTimesForDate.map((timeSlot, index) => {
                          // تحديد فترة اليوم بناءً على الساعة
                          const hour = parseInt(timeSlot.timeString.split(':')[0]);
                          const isMorning = hour >= 6 && hour < 12;
                          const isAfternoon = hour >= 12 && hour < 18;
                          const isEvening = hour >= 18 || hour < 6;

                          let timeColor, timeIcon, timeBg;
                          if (isMorning) {
                            timeColor = 'warning.main';
                            timeBg = 'warning.100';
                            timeIcon = '🌅';
                          } else if (isAfternoon) {
                            timeColor = 'info.main';
                            timeBg = 'info.100';
                            timeIcon = '☀️';
                          } else {
                            timeColor = 'secondary.main';
                            timeBg = 'secondary.100';
                            timeIcon = '🌙';
                          }

                          return (
                            <MenuItem
                              key={index}
                              value={timeSlot.datetime}
                              sx={{
                                '&:hover': {
                                  bgcolor: 'success.50'
                                },
                                '&.Mui-selected': {
                                  bgcolor: 'success.100',
                                  '&:hover': {
                                    bgcolor: 'success.200'
                                  }
                                }
                              }}
                            >
                              <Box sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1.5,
                                py: 0.5,
                                width: '100%'
                              }}>
                                <Box sx={{
                                  p: 0.5,
                                  borderRadius: 1,
                                  bgcolor: timeBg,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  minWidth: 24,
                                  height: 24
                                }}>
                                  <Typography sx={{ fontSize: '0.75rem' }}>
                                    {timeIcon}
                                  </Typography>
                                </Box>
                                <Typography
                                  variant="body1"
                                  sx={{
                                    fontWeight: 'medium',
                                    color: timeColor,
                                    flex: 1
                                  }}
                                >
                                  {timeSlot.displayTime}
                                </Typography>
                              </Box>
                            </MenuItem>
                          );
                        })}
                      </Select>
                    </FormControl>
                  )}

                  {selectedTime && (
                    <Box sx={{
                      mt: 3,
                      p: 3,
                      bgcolor: 'success.50',
                      borderRadius: 2,
                      border: '2px solid',
                      borderColor: 'success.main',
                      textAlign: 'center'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1, mb: 1 }}>
                        <Box sx={{
                          p: 1,
                          borderRadius: '50%',
                          bgcolor: 'success.main',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <TimeIcon sx={{ color: 'white', fontSize: '1rem' }} />
                        </Box>
                        <Typography variant="body2" color="success.dark" sx={{ fontWeight: 'bold' }}>
                          {t('bookings.selectedTime', 'الوقت المختار')}
                        </Typography>
                      </Box>
                      <Typography
                        variant="h6"
                        sx={{
                          fontWeight: 'bold',
                          color: 'success.dark',
                          fontSize: '1.2rem'
                        }}
                      >
                        {availableTimesForDate.find(slot => slot.datetime === selectedTime)?.displayTime}
                      </Typography>
                    </Box>
                  )}

                  {/* Reschedule Reason */}
                  {selectedTime && (
                    <Box sx={{ mt: 3 }}>
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        label={t('bookings.rescheduleReason', 'سبب إعادة الجدولة')}
                        placeholder={t('bookings.rescheduleReasonPlaceholder', 'اكتب سبب إعادة الجدولة (اختياري)')}
                        value={rescheduleReason}
                        onChange={(e) => setRescheduleReason(e.target.value)}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            '& fieldset': {
                              borderColor: 'primary.main',
                            },
                            '&:hover fieldset': {
                              borderColor: 'primary.dark',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'primary.main',
                            },
                          },
                          '& .MuiInputLabel-root': {
                            color: 'primary.main',
                            '&.Mui-focused': {
                              color: 'primary.main',
                            },
                          },
                        }}
                      />
                    </Box>
                  )}
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ px: 2, py: 1.5 }}>
        <Button onClick={onClose} variant="outlined" size="small">
          {t('common.cancel', 'إلغاء')}
        </Button>
        <Button
          onClick={handleConfirm}
          variant="contained"
          disabled={!selectedDate || !selectedTime || loading}
          size="small"
          sx={{
            minWidth: 100,
            bgcolor: 'success.main',
            '&:hover': { bgcolor: 'success.dark' }
          }}
        >
          {loading ? (
            <CircularProgress size={16} color="inherit" />
          ) : (
            t('bookings.confirmReschedule', 'تأكيد إعادة الجدولة')
          )}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default RescheduleDialog;
