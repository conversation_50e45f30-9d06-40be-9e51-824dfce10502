const mysql = require('mysql2/promise');
const config = require('../config/db.config');

const searchTeachers = async (req, res) => {
  let connection;
  try {
    // Log the entire request for debugging
    console.log('Search request received:');
    console.log('- URL:', req.url);
    console.log('- Query params:', req.query);

    // Simple pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;

    console.log('Pagination parameters:', { page, limit, offset });

    // Create a simple database connection
    connection = await mysql.createConnection(config);

    // Use direct values in the query instead of parameters
    const query = `
      SELECT 
        SQL_CALC_FOUND_ROWS
        u.id,
        u.full_name,
        u.profile_picture_url,
        tp.price_per_lesson,
        tp.teaching_experience
      FROM users u
      JOIN teacher_profiles tp ON u.id = tp.user_id
      WHERE u.role = 'platform_teacher'
      AND tp.status = 'approved'
      AND u.deleted_at IS NULL
      AND u.status != 'pending_deletion'
      GROUP BY u.id
      ORDER BY u.id DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    console.log('Executing simplified query:', query);

    // Execute the query using query() instead of execute()
    let [teachers] = await connection.query(query);
    console.log('Query executed successfully, fetching row count...');
    
    let [countResult] = await connection.query('SELECT FOUND_ROWS() as total');
    console.log('Row count fetched successfully');

    const total = countResult[0].total;
    console.log(`Found ${total} teachers matching the criteria`);

    // Process teacher data to ensure proper format
    const processedTeachers = teachers.map(teacher => {
      return {
        id: teacher.id || 0,
        full_name: teacher.full_name || '',
        profile_picture_url: teacher.profile_picture_url || null,
        price_per_lesson: teacher.price_per_lesson || 0,
        teaching_experience: teacher.teaching_experience || 0,
        average_rating: 0,
        review_count: 0,
        teaching_languages: [],
        subjects: []
      };
    });

    // Send the response
    res.json({
      success: true,
      data: {
        teachers: processedTeachers,
        pagination: {
          total,
          pages: Math.ceil(total / limit),
          current: page,
          limit
        }
      }
    });
  } catch (error) {
    console.error('Error searching teachers:', error);
    console.error('Error stack:', error.stack);

    // Log more details about the error
    if (error.code) console.error('Error code:', error.code);
    if (error.errno) console.error('Error number:', error.errno);
    if (error.sqlMessage) console.error('SQL message:', error.sqlMessage);
    if (error.sqlState) console.error('SQL state:', error.sqlState);
    if (error.sql) console.error('SQL query:', error.sql);

    // Send a more detailed error message for debugging
    res.status(500).json({
      success: false,
      message: 'Error searching teachers',
      error: error.message
    });
  } finally {
    try {
      if (connection) await connection.end();
    } catch (err) {
      console.error('Error closing database connection:', err);
    }
  }
};

const getSearchFilters = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Get all categories - use query instead of execute
    const [categories] = await connection.query(
      'SELECT id, name, description FROM categories'
    );

    // Get languages from languages table - use query instead of execute
    const [languages] = await connection.query(
      'SELECT id, name FROM languages ORDER BY name'
    );

    // Get price range - use query instead of execute
    const [priceRange] = await connection.query(`
      SELECT
        MIN(price_per_lesson) as min_price,
        MAX(price_per_lesson) as max_price
      FROM teacher_profiles
      WHERE status = 'approved'
    `);

    res.json({
      success: true,
      data: {
        categories,
        languages: languages.map(l => l.name),
        priceRange: {
          min: Math.floor(priceRange[0].min_price || 0),
          max: Math.ceil(priceRange[0].max_price || 100)
        }
      }
    });
  } catch (error) {
    console.error('Error getting search filters:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting search filters'
    });
  } finally {
    if (connection) connection.end();
  }
};

module.exports = {
  searchTeachers,
  getSearchFilters
};
