import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from '../utils/axios';
import {
  Box,
  CssBaseline,
  Drawer,
  AppBar,
  Toolbar,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Container,
  useTheme,
  useMediaQuery,
  Tooltip,
  Menu,
  MenuItem,
  Avatar,
  Button,
  alpha,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Badge,
  Link
} from '@mui/material';
import {
  Menu as MenuIcon,
  Dashboard as DashboardIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Chat as ChatIcon,
  Search as SearchIcon,
  Category as CategoryIcon,
  Assignment as AssignmentIcon,
  Translate as TranslateIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  Language as LanguageIcon,
  Logout as LogoutIcon,
  AccountCircle as AccountCircleIcon,
  VideoCall as VideoCallIcon,
  ContactSupport as ContactSupportIcon,
  AccountBalanceWallet as AccountBalanceWalletIcon,
  Payment as PaymentIcon,
  TrendingUp as TrendingUpIcon,
  Email as EmailIcon,
  CalendarMonth as CalendarMonthIcon,
  Star as StarIcon,
  RateReview as RateReviewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  AccessTime as AccessTimeIcon,
  Policy as PolicyIcon,
  MenuBook as MenuBookIcon,
  ReportProblem as ReportProblemIcon
} from '@mui/icons-material';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

import { useAuth } from '../contexts/AuthContext';
import MeetingFeedbackDialog from './MeetingFeedbackDialog';
import { useUnreadMessages } from '../contexts/UnreadMessagesContext';

dayjs.extend(utc);
const drawerWidth = 240;

const Layout = ({ children }) => {
  const [pendingDialogOpen, setPendingDialogOpen] = useState(false);
  const [pendingIssue, setPendingIssue] = useState(null);
  const { t, i18n } = useTranslation();
  const { currentUser, handleLogout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [langMenuAnchor, setLangMenuAnchor] = useState(null);
  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);
  const [balance, setBalance] = useState(null);
  const { unreadCount } = useUnreadMessages();

  // Helper to fetch pending feedback
  const fetchPending = async () => {
    if (currentUser?.role !== 'student') return;
    try {
      const { data } = await axios.get('/meeting-issues/pending');
      if (data.success && data.data) {
        console.log('Found pending feedback issue:', data.data);
        setPendingIssue(data.data);
        setPendingDialogOpen(true);
      } else {
        console.log('No pending feedback issues found');
        setPendingIssue(null);
        setPendingDialogOpen(false);
      }
    } catch (err) {
      console.error('Error fetching pending feedback', err);
    }
  };

  // Expose fetchPending globally for other components to trigger
  useEffect(() => {
    window.triggerGlobalFeedbackCheck = fetchPending;
    return () => {
      delete window.triggerGlobalFeedbackCheck;
    };
  }, [currentUser]);

  // Fetch on mount / route change
  // Skip if we're on bookings page to avoid conflict with local feedback system
  useEffect(() => {
    const isBookingsPage = location.pathname.includes('/bookings');
    if (!isBookingsPage) {
      fetchPending();
    } else {
      console.log('Skipping global feedback check on bookings page');
      setPendingDialogOpen(false);
      setPendingIssue(null);
    }
  }, [currentUser, location.pathname]);

  // Try to attach booking_id to pending issue if missing
  useEffect(() => {
    if (!pendingIssue || pendingIssue.booking_id) return;

    // Fetch bookings to find matching one
    const findMatchingBooking = async () => {
      try {
        const { data } = await axios.get('/bookings');
        if (data.success && data.data) {
          const bookings = data.data;
          const targetTime = dayjs.utc(pendingIssue.datetime);

          // Find booking with same teacher and date within 2 hours window
          const candidates = bookings.filter(b => {
            if (b.teacher_name !== pendingIssue.teacher_name) return false;
            const bTime = dayjs.utc(b.datetime);
            const timeDiff = Math.abs(bTime.diff(targetTime, 'minutes'));
            return timeDiff <= 120;
          });

          // Sort by closest time match
          candidates.sort((a, b) => {
            const aDiff = Math.abs(dayjs.utc(a.datetime).diff(targetTime, 'minutes'));
            const bDiff = Math.abs(dayjs.utc(b.datetime).diff(targetTime, 'minutes'));
            return aDiff - bDiff;
          });

          if (candidates.length > 0) {
            const bestMatch = candidates[0];
            console.log(`Global feedback: Linking meeting ${pendingIssue.meeting_id} -> booking ${bestMatch.id}`);
            setPendingIssue(prev => ({ ...prev, booking_id: bestMatch.id }));
          } else {
            console.warn(`Global feedback: No matching booking found for meeting ${pendingIssue.meeting_id}`);
          }
        }
      } catch (err) {
        console.error('Error fetching bookings for global feedback:', err);
      }
    };

    findMatchingBooking();
  }, [pendingIssue]);

  useEffect(() => {
    document.dir = isRtl ? 'rtl' : 'ltr';
  }, [isRtl]);

  useEffect(() => {
    if (currentUser) {
      fetchBalance();
    }
  }, [currentUser]);

  const fetchBalance = async () => {
    try {
      const response = await axios.get('/api/wallet/balance');
      if (response.data.success) {
        setBalance(response.data.balance);
      }
    } catch (error) {
      console.error('Error fetching balance:', error);
    }
  };

  const handleDrawerToggle = () => {
    const newState = !mobileOpen;
    setMobileOpen(newState);
    if (isMobile) {
      localStorage.setItem('drawerOpen', newState ? 'true' : 'false');
    }
    // Check for pending feedback when drawer opens
    if (newState) {
      fetchPending();
    }
  };



  const handleLangMenuOpen = (event) => {
    setLangMenuAnchor(event.currentTarget);
  };

  const handleLangMenuClose = () => {
    setLangMenuAnchor(null);
  };

  const handleLanguageChange = (lang) => {
    i18n.changeLanguage(lang);
    localStorage.setItem('language', lang);
    handleLangMenuClose();
  };

  const getMenuItems = () => {
    if (currentUser?.role === 'platform_teacher') {
      return [
        { text: t('menu.dashboard'), icon: <DashboardIcon />, path: '/teacher/dashboard' },
        { text: t('bookings.title'), icon: <CalendarMonthIcon />, path: '/teacher/bookings' },
        { text: t('menu.meetings'), icon: <VideoCallIcon />, path: '/teacher/meetings' },
        { text: t('teacher.myLessons'), icon: <MenuBookIcon />, path: '/teacher/my-lessons' },
        { text: t('menu.profile'), icon: <PersonIcon />, path: '/teacher/profile' },
        { text: t('menu.chat'), icon: <ChatIcon />, path: '/teacher/chat', badge: unreadCount },
        { text: t('reviews.teacherReviews'), icon: <StarIcon />, path: '/teacher/reviews' },
        { text: t('wallet.title'), icon: <AccountBalanceWalletIcon />, path: '/teacher/wallet' },
        { text: t('withdrawal.title'), icon: <PaymentIcon />, path: '/teacher/withdrawal' },
        { text: t('contactUs.title'), icon: <ContactSupportIcon />, path: '/teacher/contact-us' },
        { text: t('myMessages.title'), icon: <EmailIcon />, path: '/teacher/my-messages' },
        { text: t('menu.platformPolicy'), icon: <PolicyIcon />, path: '/platform-policy' },
      ];
    }

    const adminItems = [
      { text: t('nav.dashboard'), path: '/admin/dashboard', icon: <DashboardIcon /> },
      { text: t('nav.applications'), path: '/admin/applications', icon: <AssignmentIcon /> },
      { text: t('nav.teachers'), path: '/admin/teachers', icon: <SchoolIcon /> },
      { text: t('nav.students'), path: '/admin/students', icon: <PersonIcon /> },
      { text: t('nav.deletedUsers', 'المستخدمون المحذوفون'), path: '/admin/deleted-users', icon: <DeleteIcon /> },
      { text: t('nav.profileUpdates'), path: '/admin/profile-updates', icon: <EditIcon /> },
      { text: t('admin.meetingSessions.title'), path: '/admin/meeting-sessions', icon: <AccessTimeIcon /> },
      { text: t('admin.meetingIssues.title', 'Meeting Issues'), path: '/admin/meeting-issues', icon: <ReportProblemIcon /> },
      { text: t('nav.categories'), path: '/admin/categories', icon: <CategoryIcon /> },
      { text: t('nav.languages'), path: '/admin/languages', icon: <LanguageIcon /> },
      { text: t('nav.withdrawalManagement'), icon: <PaymentIcon />, path: '/admin/withdrawals' },
      { text: t('admin.earnings.title'), icon: <TrendingUpIcon />, path: '/admin/earnings' },
      { text: t('common.profile'), path: '/admin/profile', icon: <PersonIcon /> },
      { text: t('wallet.title'), path: '/admin/wallet', icon: <AccountBalanceWalletIcon /> },
      { text: t('admin.messages.title'), path: '/admin/messages', icon: <ContactSupportIcon /> },
      { text: t('menu.platformPolicy'), path: '/platform-policy', icon: <PolicyIcon /> }
    ];

    const newTeacherItems = [
      { text: t('nav.applications'), path: '/teacher/application', icon: <AssignmentIcon /> },
      { text: t('common.profile'), path: '/teacher/profile', icon: <PersonIcon /> },
      { text: t('menu.platformPolicy'), path: '/platform-policy', icon: <PolicyIcon /> }
    ];

    const studentItems = [
      { text: t('nav.dashboard'), path: '/student/dashboard', icon: <DashboardIcon /> },
      { text: t('nav.findTeacher'), path: '/student/find-teacher', icon: <SearchIcon /> },
      { text: t('bookings.title'), path: '/student/bookings', icon: <CalendarMonthIcon /> },
      { text: t('nav.myTeachers'), path: '/student/my-teachers', icon: <SchoolIcon /> },
      { text: t('nav.meetings'), path: '/student/meetings', icon: <VideoCallIcon /> },
      { text: t('nav.chat'), path: '/student/chat', icon: <ChatIcon />, badge: unreadCount },
      { text: t('reviews.writeReview'), path: '/student/write-review', icon: <RateReviewIcon /> },
      { text: t('common.profile'), path: '/student/profile', icon: <PersonIcon /> },
      { text: t('wallet.title'), path: '/student/wallet', icon: <AccountBalanceWalletIcon /> },
      { text: t('contactUs.title'), path: '/student/contact-us', icon: <ContactSupportIcon /> },
      { text: t('myMessages.title'), path: '/student/my-messages', icon: <EmailIcon /> },
      { text: t('menu.platformPolicy'), path: '/platform-policy', icon: <PolicyIcon /> }
    ];

    switch (currentUser?.role) {
      case 'admin':
        return adminItems;
      case 'new_teacher':
        return newTeacherItems;
      case 'student':
        return studentItems;
      default:
        return [];
    }
  };

  const handleProfileClick = () => {
    switch (currentUser?.role) {
      case 'admin':
        navigate('/admin/profile');
        break;
      case 'platform_teacher':
      case 'new_teacher':
        navigate('/teacher/profile');
        break;
      case 'student':
        navigate('/student/profile');
        break;
      default:
        navigate('/login');
    }
  };

  const handleLogoutClick = () => {
    setAnchorEl(null); // Close menu immediately for better UX
    setLogoutDialogOpen(true);
  };

  const handleConfirmLogout = async () => {
    setLogoutDialogOpen(false);
    try {
      await handleLogout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleCancelLogout = () => {
    setLogoutDialogOpen(false);
  };

  const drawer = (
    <Box>
      {/* Balance Display */}
      {currentUser && balance !== null ? (
        <Box sx={{
          mx: 2,
          my: 2,
          p: 2,
          bgcolor: 'primary.light',
          borderRadius: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 1.5,
          minHeight: 64
        }}>
          <AccountBalanceWalletIcon sx={{
            color: 'primary.contrastText',
            fontSize: '1.5rem'
          }} />
          <Box sx={{ textAlign: 'center' }}>
            <Typography
              variant="caption"
              sx={{
                color: 'primary.contrastText',
                fontSize: '0.75rem',
                opacity: 0.9,
                display: 'block',
                lineHeight: 1.2
              }}
            >
              {t('wallet.balance')}
            </Typography>
            <Typography
              variant="h6"
              sx={{
                color: 'primary.contrastText',
                fontSize: '1.1rem',
                fontWeight: 700,
                lineHeight: 1.2,
                mt: 0.5
              }}
            >
              ${balance}
            </Typography>
          </Box>
        </Box>
      ) : (
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          p: 2,
          minHeight: 64
        }}>
          <Typography
            variant="h6"
            sx={{
              fontFamily: 'Tajawal, sans-serif',
              fontWeight: 600,
              color: 'primary.main'
            }}
          >
            {t('brand.name')}
          </Typography>
        </Box>
      )}
      <Divider />

      <List>
        {getMenuItems().map((item) => (
          <ListItem
            component="div"
            key={item.text}
            onClick={() => {
              if (location.pathname === item.path) {
                navigate(0); // Reload current page
              } else {
                navigate(item.path);
              }
            }}
            selected={location.pathname === item.path}
            sx={{
              minHeight: 48,
              px: 2.5,
              cursor: 'pointer',
              borderRadius: 1,
              mx: 1,
              mb: 0.5,
              '&:hover': {
                backgroundColor: 'action.hover'
              },
              '&.Mui-selected': {
                backgroundColor: 'primary.main',
                color: 'primary.contrastText',
                '&:hover': {
                  backgroundColor: 'primary.dark'
                },
                '& .MuiListItemIcon-root': {
                  color: 'primary.contrastText'
                }
              }
            }}
          >
            <ListItemIcon
              sx={{
                minWidth: 0,
                mr: isRtl ? 0 : 2,
                ml: isRtl ? 2 : 0,
                justifyContent: 'center',
              }}
            >
              {item.badge && item.badge > 0 ? (
                <Badge badgeContent={item.badge} color="error" max={99}>
                  {item.icon}
                </Badge>
              ) : (
                item.icon
              )}
            </ListItemIcon>
            <ListItemText
              primary={item.text}
              sx={{
                '& .MuiListItemText-primary': {
                  fontSize: '0.9rem',
                  fontWeight: 500
                }
              }}
            />
          </ListItem>
        ))}
      </List>
    </Box>
  );

  const handleGlobalFeedbackSubmit = async (meetingId, values) => {
    try {
      console.log('Submitting global feedback:', { meetingId, values });

      // Ensure booking_id is provided
      if (!values.booking_id) {
        console.error('booking_id is missing in global feedback, cannot submit');
        alert('Error: Booking information is missing. Please refresh the page and try again.');
        return;
      }

      await axios.post('/meeting-issues', {
        meeting_id: meetingId,
        booking_id: values.booking_id, // Include booking_id
        issue_type: values.issue_type,
        description: values.description
      });

      console.log('Global feedback submitted successfully');
    } catch (err) {
      console.error('Failed to submit global feedback', err);
      // Show error message to user
      alert('Failed to submit feedback. Please try again.');
      return; // Don't close dialog on error
    } finally {
      setPendingDialogOpen(false);
      setPendingIssue(null);
      // Check if there is another pending meeting
      await fetchPending();
    }
  };

  return (
    <Box sx={{ display: 'flex', direction: isRtl ? 'rtl' : 'ltr' }}>
      <CssBaseline />
      <AppBar
        position="fixed"
        sx={{
          width: '100%',
          zIndex: theme.zIndex.drawer + 1
        }}
      >
        <Toolbar sx={{ minHeight: { xs: 56, sm: 64, md: 70 }, px: { xs: 1, sm: 2, md: 3 } }}>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge={isRtl ? 'end' : 'start'}
            onClick={handleDrawerToggle}
            sx={{
              mr: isRtl ? 0 : { xs: 1, sm: 2 },
              ml: isRtl ? { xs: 1, sm: 2 } : 0,
              display: { sm: 'none' },
              p: { xs: 1, sm: 1.5 }
            }}
          >
            <MenuIcon />
          </IconButton>
          <Typography
            variant="h6"
            noWrap
            component="div"
            sx={{
              flexGrow: 1,
              fontSize: { xs: '1rem', sm: '1.1rem', md: '1.25rem' },
              fontFamily: 'Tajawal, sans-serif',
              fontWeight: { xs: 500, md: 600 }
            }}
          >
            {t('brand.name')}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1, md: 1.5 } }}>
            {/* Language Menu */}
            <Box>
              <Button
                color="inherit"
                startIcon={<TranslateIcon sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' } }} />}
                endIcon={<KeyboardArrowDownIcon sx={{ fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' } }} />}
                onClick={handleLangMenuOpen}
                sx={{
                  minWidth: { xs: 80, sm: 100, md: 120 },
                  borderRadius: 2,
                  px: { xs: 1, sm: 1.5, md: 2 },
                  py: { xs: 0.5, sm: 0.75, md: 1 },
                  gap: { xs: 0.5, sm: 0.75, md: 1 },
                  fontSize: { xs: '0.75rem', sm: '0.85rem', md: '0.9rem' },
                  '& .MuiButton-startIcon': {
                    margin: 0
                  },
                  '& .MuiButton-endIcon': {
                    margin: 0,
                    ml: 0.5
                  },
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.common.white, 0.1),
                    transform: 'translateY(-1px)'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                {i18n.language === 'ar' ? 'العربية' : 'English'}
              </Button>
              <Menu
                anchorEl={langMenuAnchor}
                open={Boolean(langMenuAnchor)}
                onClose={handleLangMenuClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                slotProps={{
                  paper: {
                    elevation: 0,
                    sx: {
                      mt: 1.5,
                      overflow: 'visible',
                      filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
                      '&:before': {
                        content: '""',
                        display: 'block',
                        position: 'absolute',
                        top: 0,
                        right: 14,
                        width: 10,
                        height: 10,
                        bgcolor: 'background.paper',
                        transform: 'translateY(-50%) rotate(45deg)',
                        zIndex: 0,
                      },
                    },
                  }
                }}
              >
                <MenuItem onClick={() => handleLanguageChange('ar')}>
                  <ListItemIcon>
                    <LanguageIcon fontSize="small" />
                  </ListItemIcon>
                  العربية
                </MenuItem>
                <MenuItem onClick={() => handleLanguageChange('en')}>
                  <ListItemIcon>
                    <LanguageIcon fontSize="small" />
                  </ListItemIcon>
                  English
                </MenuItem>
              </Menu>
            </Box>
            {/* User Avatar */}
            {currentUser && (
              <>
                <IconButton
                  onClick={(event) => setAnchorEl(event.currentTarget)}
                  sx={{
                    ml: { xs: 0.5, sm: 1, md: 1.5 },
                    p: { xs: 0.5, sm: 0.75, md: 1 },
                    '&:hover': {
                      transform: 'scale(1.05)',
                      bgcolor: 'rgba(255,255,255,0.1)'
                    },
                    transition: 'all 0.2s ease-in-out'
                  }}
                >
                  <Avatar
                    src={currentUser.profile_picture_url ? (
                      currentUser.profile_picture_url.startsWith('http')
                        ? currentUser.profile_picture_url
                        : `https://allemnionline.com${currentUser.profile_picture_url}`
                    ) : ''}
                    alt={currentUser.full_name}
                    sx={{
                      width: { xs: 32, sm: 36, md: 40 },
                      height: { xs: 32, sm: 36, md: 40 },
                      bgcolor: 'primary.main',
                      border: '2px solid',
                      borderColor: 'background.paper',
                      fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                      fontWeight: 600
                    }}
                  >
                    {!currentUser.profile_picture_url && currentUser.full_name?.charAt(0)}
                  </Avatar>
                </IconButton>

                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={() => setAnchorEl(null)}
                  onClick={() => setAnchorEl(null)}
                  transformOrigin={{ horizontal: isRtl ? 'left' : 'right', vertical: 'top' }}
                  anchorOrigin={{ horizontal: isRtl ? 'left' : 'right', vertical: 'bottom' }}
                >
                  <MenuItem onClick={handleProfileClick}>
                    <ListItemIcon>
                      <PersonIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={t('common.profile')} />
                  </MenuItem>
                  <Divider />
                  <MenuItem onClick={handleLogoutClick}>
                    <ListItemIcon>
                      <LogoutIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={t('auth.logout')} />
                  </MenuItem>
                </Menu>
              </>
            )}

            {/* Login Button for non-authenticated users */}
            {!currentUser && (
              <Button
                color="inherit"
                onClick={() => navigate('/login')}
                sx={{
                  ml: { xs: 0.5, sm: 1, md: 1.5 },
                  px: { xs: 1.5, sm: 2, md: 2.5 },
                  py: { xs: 0.5, sm: 0.75, md: 1 },
                  fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
                  borderRadius: 2,
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.common.white, 0.1),
                    transform: 'translateY(-1px)'
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                {t('auth.login')}
              </Button>
            )}
          </Box>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{
          width: { sm: drawerWidth },
          flexShrink: { sm: 0 }
        }}
      >
        <Drawer
          variant={isMobile ? 'temporary' : 'permanent'}
          open={isMobile ? mobileOpen : true}
          onClose={handleDrawerToggle}
          anchor={isRtl ? 'right' : 'left'}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              top: { xs: '56px', sm: '64px', md: '70px' },
              height: { xs: 'calc(100vh - 56px)', sm: 'calc(100vh - 64px)', md: 'calc(100vh - 70px)' },
              borderRight: isRtl ? 'none' : '1px solid rgba(0, 0, 0, 0.12)',
              borderLeft: isRtl ? '1px solid rgba(0, 0, 0, 0.12)' : 'none',
              overflowX: 'hidden',
              position: 'fixed',
              zIndex: 1000,
              '&::-webkit-scrollbar': {
                width: '6px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#888',
                borderRadius: '3px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: '#555',
              },
            },
          }}
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: { xs: 0.5, sm: 1, md: 1.5 },
          width: '100%',
          marginTop: { xs: '56px', sm: '64px', md: '70px' },
          minHeight: `calc(100vh - ${isMobile ? '56px' : isTablet ? '64px' : '70px'})`,
          overflow: 'auto'
        }}
      >
        <Container
          maxWidth="xl"
          sx={{
            px: { xs: 1, sm: 2, md: 3 },
            py: { xs: 1, sm: 1.5, md: 2 }
          }}
        >
          {children}

      {/* Global Meeting Feedback Dialog */}
      {currentUser?.role === 'student' && !location.pathname.includes('/bookings') && (
        <MeetingFeedbackDialog
           open={pendingDialogOpen && pendingIssue && dayjs.utc().isAfter(dayjs.utc(pendingIssue.datetime).add(pendingIssue.duration || 50, 'minute'))}
           meeting={pendingIssue}
           timezone={currentUser?.timezone || null}
           onSubmit={handleGlobalFeedbackSubmit}
           onClose={null /* Prevent manual close */}
         />
      )}
        </Container>
      </Box>
      <Dialog
        open={logoutDialogOpen}
        onClose={handleCancelLogout}
        aria-labelledby="logout-dialog-title"
        aria-describedby="logout-dialog-description"
      >
        <DialogTitle id="logout-dialog-title">
          {t('auth.logoutConfirmTitle')}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="logout-dialog-description">
            {t('auth.logoutConfirmMessage')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelLogout} color="primary">
            {t('common.cancel')}
          </Button>
          <Button onClick={handleConfirmLogout} color="primary" autoFocus>
            {t('common.confirm')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Layout;