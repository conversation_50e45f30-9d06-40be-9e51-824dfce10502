const mysql = require('mysql2/promise');
const config = require('../config/db.config');

const getHomeData = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    // Get featured categories with teacher count
    const [subjects] = await connection.execute(`
      SELECT
        c.id,
        c.name,
        c.description,
        COUNT(DISTINCT tc.teacher_profile_id) as teacherCount
      FROM categories c
      LEFT JOIN teacher_categories tc ON c.id = tc.category_id
      LEFT JOIN teacher_profiles tp ON tc.teacher_profile_id = tp.id
      WHERE tp.status = 'approved' OR tp.status IS NULL
      GROUP BY c.id
      LIMIT 4
    `);

    // Get testimonials from reviews - show all reviews, not just high-rated ones
    const [testimonials] = await connection.execute(`
      SELECT
        u.full_name as student_name,
        u.role,
        u.profile_picture_url as student_avatar,
        r.rating,
        r.comment as text,
        r.created_at,
        teacher_u.full_name as teacher_name,
        teacher_u.profile_picture_url as teacher_avatar,
        tp.qualifications,
        tp.teaching_experience
      FROM reviews r
      JOIN users u ON r.student_id = u.id
      LEFT JOIN teacher_profiles tp ON r.teacher_profile_id = tp.id
      LEFT JOIN users teacher_u ON tp.user_id = teacher_u.id
      WHERE r.comment IS NOT NULL AND r.comment != ''
      ORDER BY r.created_at DESC
      LIMIT 5
    `);

    // Get platform statistics
    const [stats] = await connection.execute(`
      SELECT
        (SELECT COUNT(DISTINCT tp.id)
         FROM teacher_profiles tp
         WHERE tp.status = 'approved') as teacherCount,
        (SELECT COUNT(*)
         FROM users
         WHERE role = 'student') as studentCount,
        (SELECT COUNT(*)
         FROM categories) as courseCount
    `);

    // Get featured teachers
    const [teachers] = await connection.execute(`
      SELECT
        u.full_name,
        tp.id as teacher_profile_id,
        tp.profile_picture_url,
        tp.teaching_experience,
        tp.qualifications,
        tp.native_language,
        tp.teaching_languages,
        tp.cv,
        GROUP_CONCAT(DISTINCT c.name) as subjects
      FROM users u
      JOIN teacher_profiles tp ON u.id = tp.user_id
      LEFT JOIN teacher_categories tc ON tp.id = tc.teacher_profile_id
      LEFT JOIN categories c ON tc.category_id = c.id
      WHERE u.role = 'platform_teacher'
      AND tp.status = 'approved'
      GROUP BY u.id, tp.id
      ORDER BY tp.teaching_experience DESC
      LIMIT 4
    `);

    // Get teaching languages for each teacher
    for (let teacher of teachers) {
      const [teachingLanguages] = await connection.execute(`
        SELECT l.name
        FROM teacher_languages tl
        JOIN languages l ON tl.language_id = l.id
        WHERE tl.teacher_profile_id = ?
      `, [teacher.teacher_profile_id]);

      teacher.teaching_languages = teachingLanguages.map(lang => lang.name);
    }

    console.log('Teachers data from database:', teachers.map(t => ({
      name: t.full_name,
      profile_pic: t.profile_picture_url
    })));

    res.json({
      success: true,
      data: {
        subjects: subjects.map(s => ({
          id: s.id,
          name: s.name,
          description: s.description,
          teacherCount: s.teacherCount || 0,
          image: `/images/${s.name.toLowerCase().replace(/\s+/g, '-')}.jpg`
        })),
        testimonials: testimonials.map(t => ({
          full_name: t.student_name,
          teacher_name: t.teacher_name,
          avatar: t.student_avatar,
          teacher_avatar: t.teacher_avatar,
          rating: t.rating,
          text: t.text,
          created_at: t.created_at,
          role: t.role === 'student' ? 'testimonials.student' : 'testimonials.teacher'
        })),
        stats: {
          teacherCount: stats[0].teacherCount || 0,
          studentCount: stats[0].studentCount || 0,
          courseCount: stats[0].courseCount || 0
        },
        teachers: teachers.map(t => ({
          ...t,
          // teaching_languages is already processed above as an array of language names
          teaching_languages: t.teaching_languages || []
        })),
        features: [
          {
            icon: 'School',
            title: 'features.expert',
            description: 'features.expertDesc'
          },
          {
            icon: 'MenuBook',
            title: 'features.curriculum',
            description: 'features.curriculumDesc'
          },
          {
            icon: 'AccessTime',
            title: 'features.flexible',
            description: 'features.flexibleDesc'
          },
          {
            icon: 'Group',
            title: 'features.community',
            description: 'features.communityDesc'
          }
        ]
      }
    });
  } catch (error) {
    console.error('Error fetching home data:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching home data'
    });
  } finally {
    if (connection) connection.end();
  }
};

module.exports = {
  getHomeData
};
