import React, { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Box,
  CircularProgress,
  Typography,
  Alert
} from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import ChatWindow from '../../components/chat/ChatWindow';

// Helper function to get query parameters
const useQuery = () => {
  return new URLSearchParams(useLocation().search);
};

const ChatEmbed = () => {
  const { t } = useTranslation();
  const { currentUser, token } = useAuth();
  const { socket, isConnected } = useSocket();
  const query = useQuery();
  
  const teacherId = query.get('teacherId');
  const chatId = query.get('chatId');
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [teacher, setTeacher] = useState(null);
  const [messages, setMessages] = useState([]);

  // Fetch teacher details
  useEffect(() => {
    const fetchTeacherDetails = async () => {
      if (!teacherId || !token) return;
      
      try {
        const response = await fetch(`/api/teachers/${teacherId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        const data = await response.json();
        if (data.success) {
          setTeacher(data.data);
        } else {
          setError(t('errors.teacherFetchFailed'));
        }
      } catch (error) {
        console.error('Error fetching teacher details:', error);
        setError(t('errors.teacherFetchFailed'));
      }
    };
    
    fetchTeacherDetails();
  }, [teacherId, token, t]);

  // Fetch chat messages
  useEffect(() => {
    if (!socket || !chatId) return;
    
    setLoading(true);
    socket.emit('get_chat_messages', { chatId }, (response) => {
      if (response.success) {
        // Convert all MySQL timestamps to Unix timestamps
        const convertedMessages = response.messages.map(msg => {
          if (msg.created_at && typeof msg.created_at === 'string') {
            return {
              ...msg,
              created_at: Math.floor(new Date(msg.created_at.replace(' ', 'T')).getTime() / 1000)
            };
          }
          return msg;
        });
        setMessages(convertedMessages);
        
        // Mark messages as read
        socket.emit('mark_messages_read', { chatId });
      } else {
        setError(t('errors.messagesFetchFailed'));
      }
      setLoading(false);
    });
  }, [socket, chatId, t]);

  // Listen for new messages
  useEffect(() => {
    if (!socket || !chatId) return;
    
    // Listen for new messages
    socket.on('new_message', (message) => {
      // Convert MySQL timestamp to Unix timestamp
      if (message.created_at && typeof message.created_at === 'string') {
        message.created_at = Math.floor(new Date(message.created_at.replace(' ', 'T')).getTime() / 1000);
      }
      
      // Add message to messages list if it's for the current chat
      if (message.conversation_id === chatId) {
        setMessages(prev => [...prev, message]);
        // Mark message as read
        socket.emit('mark_messages_read', { chatId: message.conversation_id });
      }
    });
    
    // Listen for message sent confirmation
    socket.on('message_sent', ({ success, message, tempId }) => {
      if (success) {
        // Convert MySQL timestamp to Unix timestamp
        if (message.created_at && typeof message.created_at === 'string') {
          message.created_at = Math.floor(new Date(message.created_at.replace(' ', 'T')).getTime() / 1000);
        }
        
        setMessages(prev => prev.map(msg => 
          msg.id === tempId ? { ...message } : msg
        ));
      } else {
        // Remove temporary message if sending failed
        setMessages(prev => prev.filter(msg => msg.id !== tempId));
      }
    });
    
    return () => {
      socket.off('new_message');
      socket.off('message_sent');
    };
  }, [socket, chatId]);

  // Send message handler
  const handleSendMessage = useCallback((content) => {
    if (!socket || !chatId || !content.trim() || !currentUser) return;
    
    // Create a temporary message with a unique ID
    const tempId = `temp-${Date.now()}`;
    
    // Get current timestamp in seconds
    const timestamp = Math.floor(Date.now() / 1000);
    
    const tempMessage = {
      id: tempId,
      conversation_id: chatId,
      sender_id: currentUser.id,
      content,
      created_at: timestamp,
      is_read: false,
      is_temp: true
    };
    
    // Add to local messages immediately
    setMessages(prev => [...prev, tempMessage]);
    
    // Send to server
    socket.emit('send_message', {
      conversation_id: chatId,
      recipient_id: teacherId,
      content,
      temp_id: tempId
    });
  }, [socket, chatId, teacherId, currentUser]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  if (!teacher || !chatId) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="warning">{t('errors.invalidChatParameters')}</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <ChatWindow
        messages={messages}
        onSendMessage={handleSendMessage}
        currentUserId={currentUser?.id}
        recipientId={teacherId}
        recipientName={teacher.full_name}
        recipientAvatar={teacher.profile_picture_url}
      />
    </Box>
  );
};

export default ChatEmbed;
