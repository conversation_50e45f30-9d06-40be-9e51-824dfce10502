/**
 * Utility functions for handling URLs in the application
 */

/**
 * Constructs a proper URL for profile pictures and other assets
 * @param {string} url - The URL to process
 * @param {string} baseUrl - The base URL to use (defaults to production URL)
 * @returns {string} - The properly formatted URL
 */
export const getAssetUrl = (url, baseUrl = 'https://allemnionline.com') => {
  if (!url) return '';
  
  // If URL already starts with http/https, return as is
  if (url.startsWith('http')) {
    return url;
  }
  
  // If URL starts with /, prepend base URL
  if (url.startsWith('/')) {
    return `${baseUrl}${url}`;
  }
  
  // Otherwise, prepend base URL with /
  return `${baseUrl}/${url}`;
};

/**
 * Constructs a proper URL for profile pictures
 * @param {string} profilePictureUrl - The profile picture URL
 * @returns {string} - The properly formatted profile picture URL
 */
export const getProfilePictureUrl = (profilePictureUrl) => {
  return getAssetUrl(profilePictureUrl);
};

/**
 * Constructs a proper URL for video files
 * @param {string} videoUrl - The video URL
 * @returns {string} - The properly formatted video URL
 */
export const getVideoUrl = (videoUrl) => {
  return getAssetUrl(videoUrl);
};

/**
 * Checks if a URL is a YouTube video
 * @param {string} url - The URL to check
 * @returns {boolean} - True if it's a YouTube URL
 */
export const isYoutubeVideo = (url) => {
  if (!url) return false;
  return url.includes('youtube.com') || url.includes('youtu.be');
};

/**
 * Extracts YouTube video ID from URL
 * @param {string} url - The YouTube URL
 * @returns {string|null} - The video ID or null if not found
 */
export const getYoutubeVideoId = (url) => {
  if (!url) return null;
  
  try {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);
    
    if (match && match[2] && match[2].length === 11) {
      return match[2];
    }
    
    // If the URL is already a video ID (11 characters)
    if (url.length === 11 && /^[a-zA-Z0-9_-]{11}$/.test(url)) {
      return url;
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting YouTube video ID:', error);
    return null;
  }
};

/**
 * Checks if a URL is a local video file
 * @param {string} url - The URL to check
 * @returns {boolean} - True if it's a local video file
 */
export const isLocalVideoFile = (url) => {
  if (!url) return false;
  return url.startsWith('/uploads/') && (
    url.endsWith('.mp4') ||
    url.endsWith('.webm') ||
    url.endsWith('.ogg') ||
    url.endsWith('.mov') ||
    url.endsWith('.avi')
  );
};
