const pool = require('../config/database');
const stripe = require('stripe');
const stripeConfig = require('../config/stripe.config');

// Initialize Stripe
const stripeClient = stripe(stripeConfig.secretKey);

const walletController = {
  getBalance: async (req, res) => {
    try {
      const userId = req.user.id;

      const [rows] = await pool.query(
        'SELECT balance FROM users WHERE id = ?',
        [userId]
      );

      if (rows.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'User not found'
        });
      }

      const balance = parseFloat(rows[0].balance);
      res.json({
        success: true,
        balance: balance, // For backward compatibility
        data: {
          balance: balance
        }
      });
    } catch (error) {
      console.error('Error fetching balance:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  },

  getTransactions: async (req, res) => {
    try {
      const userId = req.user.id;
      const role = req.user.role;
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const offset = (page - 1) * limit;

      let query = '';
      let countQuery = '';
      let queryParams = [];

      if (role === 'student') {
        // For students, show payments they've made (including wallet deposits)
        query = `
          SELECT
            p.id,
            p.amount,
            p.status,
            p.created_at,
            CASE
              WHEN p.teacher_profile_id IS NULL THEN
                CASE
                  WHEN p.payment_method = 'paypal' THEN 'PayPal Deposit'
                  WHEN p.payment_method = 'stripe' THEN 'Stripe Deposit'
                  ELSE 'Wallet Deposit'
                END
              ELSE u.full_name
            END as teacher_name,
            COALESCE(p.type, CASE WHEN p.teacher_profile_id IS NULL THEN 'deposit' ELSE 'payment' END) as type,
            COALESCE(p.payment_method, 'wallet') as payment_method,
            CASE
              WHEN p.teacher_profile_id IS NULL AND p.status = 'completed' THEN 'credit'
              WHEN p.status = 'completed' THEN 'debit'
              WHEN p.status = 'failed' AND EXISTS (SELECT 1 FROM bookings b WHERE b.teacher_profile_id = p.teacher_profile_id AND b.student_id = p.student_id AND b.status = 'cancelled' AND DATE(b.created_at) = DATE(p.created_at)) THEN 'credit'
              ELSE 'pending'
            END as transaction_type
          FROM payments p
          LEFT JOIN teacher_profiles tp ON p.teacher_profile_id = tp.id
          LEFT JOIN users u ON tp.user_id = u.id
          WHERE p.student_id = ?
          ORDER BY p.created_at DESC
          LIMIT ? OFFSET ?
        `;

        countQuery = `
          SELECT COUNT(*) as total
          FROM payments
          WHERE student_id = ?
        `;

        queryParams = [userId, limit, offset];
      } else if (role === 'platform_teacher') {
        // For teachers, show payments they've received
        query = `
          SELECT
            p.id,
            p.amount,
            p.status,
            p.created_at,
            u.full_name as student_name,
            'payment' as type,
            CASE
              WHEN p.status = 'completed' THEN 'credit'
              WHEN p.status = 'failed' THEN 'cancelled'
              ELSE 'pending'
            END as transaction_type
          FROM payments p
          JOIN teacher_profiles tp ON p.teacher_profile_id = tp.id
          JOIN users u ON p.student_id = u.id
          WHERE tp.user_id = ?
          ORDER BY p.created_at DESC
          LIMIT ? OFFSET ?
        `;

        countQuery = `
          SELECT COUNT(*) as total
          FROM payments p
          JOIN teacher_profiles tp ON p.teacher_profile_id = tp.id
          WHERE tp.user_id = ?
        `;

        queryParams = [userId, limit, offset];
      } else if (role === 'admin') {
        // For admins, show all payments (including wallet deposits)
        query = `
          SELECT
            p.id,
            p.amount,
            p.status,
            p.created_at,
            s.full_name as student_name,
            CASE
              WHEN p.teacher_profile_id IS NULL THEN
                CASE
                  WHEN p.payment_method = 'paypal' THEN 'PayPal Deposit'
                  WHEN p.payment_method = 'stripe' THEN 'Stripe Deposit'
                  ELSE 'Wallet Deposit'
                END
              ELSE t.full_name
            END as teacher_name,
            COALESCE(p.type, CASE WHEN p.teacher_profile_id IS NULL THEN 'deposit' ELSE 'payment' END) as type,
            COALESCE(p.payment_method, 'wallet') as payment_method,
            p.status as transaction_type
          FROM payments p
          JOIN users s ON p.student_id = s.id
          LEFT JOIN teacher_profiles tp ON p.teacher_profile_id = tp.id
          LEFT JOIN users t ON tp.user_id = t.id
          ORDER BY p.created_at DESC
          LIMIT ? OFFSET ?
        `;

        countQuery = `
          SELECT COUNT(*) as total
          FROM payments
        `;

        queryParams = [limit, offset];
      } else {
        return res.status(403).json({
          success: false,
          error: 'Unauthorized'
        });
      }

      // Execute the queries
      const [transactions] = await pool.query(query, queryParams);
      const [countResult] = await pool.query(countQuery, role === 'admin' ? [] : [userId]);

      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        data: {
          transactions,
          pagination: {
            total,
            page,
            limit,
            totalPages
          }
        }
      });
    } catch (error) {
      console.error('Error fetching transactions:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  },

  deposit: async (req, res) => {
    const { paymentId, amount, status } = req.body;
    const userId = req.user.id;

    let connection;
    try {
      connection = await pool.getConnection();
      await connection.beginTransaction();

      // For now, we'll trust the frontend verification
      // In production, you should verify with PayPal API
      console.log('PayPal payment received:', { paymentId, amount, status, userId });

      if (status !== 'COMPLETED') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'Payment not completed'
        });
      }

      // Update user's wallet balance
      await connection.query(
        'UPDATE users SET balance = balance + ? WHERE id = ?',
        [amount, userId]
      );

      // Record the transaction - using teacher_profile_id = NULL for wallet deposits
      await connection.query(
        `INSERT INTO payments
        (teacher_profile_id, student_id, amount, status, payment_id, type, payment_method)
        VALUES (NULL, ?, ?, ?, ?, 'deposit', 'paypal')`,
        [userId, amount, status, paymentId]
      );

      await connection.commit();

      res.json({
        success: true,
        message: 'Deposit successful'
      });
    } catch (error) {
      if (connection) {
        await connection.rollback();
      }
      console.error('Error processing deposit:', error);
      res.status(500).json({
        success: false,
        error: 'Error processing deposit'
      });
    } finally {
      if (connection) {
        connection.release();
      }
    }
  },

  // Create Stripe Payment Intent
  createStripePaymentIntent: async (req, res) => {
    try {
      const { amount } = req.body;
      const userId = req.user.id;

      if (!amount || amount <= 0) {
        return res.status(400).json({
          success: false,
          error: 'Invalid amount'
        });
      }

      // Create payment intent
      const paymentIntent = await stripeClient.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: stripeConfig.currency,
        metadata: {
          userId: userId.toString(),
          type: 'wallet_deposit'
        }
      });

      res.json({
        success: true,
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id
      });
    } catch (error) {
      console.error('Error creating payment intent:', error);
      res.status(500).json({
        success: false,
        error: 'Error creating payment intent'
      });
    }
  },

  // Confirm Stripe Payment and Add to Wallet
  confirmStripePayment: async (req, res) => {
    const { paymentIntentId } = req.body;
    const userId = req.user.id;

    let connection;
    try {
      connection = await pool.getConnection();
      await connection.beginTransaction();

      // Retrieve payment intent from Stripe
      const paymentIntent = await stripeClient.paymentIntents.retrieve(paymentIntentId);

      if (paymentIntent.status !== 'succeeded') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'Payment not completed'
        });
      }

      // Verify the payment belongs to this user
      if (paymentIntent.metadata.userId !== userId.toString()) {
        await connection.rollback();
        return res.status(403).json({
          success: false,
          error: 'Unauthorized payment'
        });
      }

      const amount = paymentIntent.amount / 100; // Convert from cents

      // Update user's wallet balance
      await connection.query(
        'UPDATE users SET balance = balance + ? WHERE id = ?',
        [amount, userId]
      );

      // Record the transaction - using teacher_profile_id = NULL for wallet deposits
      await connection.query(
        `INSERT INTO payments
        (teacher_profile_id, student_id, amount, status, payment_id, type, payment_method)
        VALUES (NULL, ?, ?, 'completed', ?, 'deposit', 'stripe')`,
        [userId, amount, paymentIntentId]
      );

      await connection.commit();

      res.json({
        success: true,
        message: 'Deposit successful',
        amount: amount
      });
    } catch (error) {
      if (connection) {
        await connection.rollback();
      }
      console.error('Error confirming stripe payment:', error);
      res.status(500).json({
        success: false,
        error: 'Error processing payment'
      });
    } finally {
      if (connection) {
        connection.release();
      }
    }
  }
};

module.exports = walletController;
