import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  IconButton,
  Tooltip
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import Layout from '../../components/Layout';
import { useAuth } from '../../contexts/AuthContext';

const Categories = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const [categories, setCategories] = useState([]);
  const [open, setOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await axios.get('/admin/categories');
      setCategories(response.data.categories);
    } catch (err) {
      console.error('Error fetching categories:', err);
      setError(t('admin.categories.fetchError'));
    }
  };

  const handleOpen = (category = null) => {
    if (category) {
      setEditingCategory(category);
      setFormData({
        name: category.name,
        description: category.description || ''
      });
    } else {
      setEditingCategory(null);
      setFormData({
        name: '',
        description: ''
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingCategory(null);
    setFormData({
      name: '',
      description: ''
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      if (!currentUser) {
        setError(t('admin.categories.unauthorized'));
        return;
      }

      const data = {
        ...formData,
        created_by: currentUser.id
      };

      if (editingCategory) {
        await axios.put(`/admin/categories/${editingCategory.id}`, data);
        setSuccess(t('admin.categories.updateSuccess'));
        handleClose();
        fetchCategories();
      } else {
        await axios.post('/admin/categories', data);
        setSuccess(t('admin.categories.createSuccess'));
        handleClose();
        fetchCategories();
      }
    } catch (err) {
      console.error('Error saving category:', err);
      setError(err.response?.data?.message || t('admin.categories.saveError'));
    }
  };

  const handleDelete = async (id) => {
    if (!id) {
      setError(t('admin.categories.invalidId'));
      return;
    }

    if (window.confirm(t('admin.categories.deleteConfirm'))) {
      try {
        await axios.delete(`/admin/categories/${id}`);
        setSuccess(t('admin.categories.deleteSuccess'));
        fetchCategories();
      } catch (err) {
        console.error('Error deleting category:', err);
        setError(err.response?.data?.message || t('admin.categories.deleteError'));
      }
    }
  };

  const content = (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4" component="h1">
          {t('admin.categories.title')}
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpen()}
        >
          {t('admin.categories.addNew')}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('admin.categories.name')}</TableCell>
              <TableCell>{t('admin.categories.description')}</TableCell>
              <TableCell>{t('admin.categories.createdBy')}</TableCell>
              <TableCell>{t('common.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {categories.map((category) => (
              <TableRow key={category.id}>
                <TableCell>{category.name}</TableCell>
                <TableCell>{category.description}</TableCell>
                <TableCell>{category.creator_name}</TableCell>
                <TableCell>
                  <Tooltip title={t('common.edit')}>
                    <IconButton onClick={() => handleOpen(category)}>
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title={t('common.delete')}>
                    <IconButton onClick={() => handleDelete(category.id)}>
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingCategory ? t('admin.categories.editTitle') : t('admin.categories.addTitle')}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'grid', gap: 2, pt: 2 }}>
            <TextField
              name="name"
              label={t('admin.categories.name')}
              value={formData.name}
              onChange={handleInputChange}
              fullWidth
              required
            />
            <TextField
              name="description"
              label={t('admin.categories.description')}
              value={formData.description}
              onChange={handleInputChange}
              fullWidth
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>{t('common.cancel')}</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingCategory ? t('common.save') : t('common.create')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );

  return <Layout>{content}</Layout>;
};

export default Categories;
