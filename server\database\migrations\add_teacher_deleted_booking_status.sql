-- إضافة حالة جديدة للحجوزات الملغية بسبب حذف المدرس
-- تاريخ التنفيذ: 2024-07-31

-- تحديث enum لإضافة حالة جديدة للحجوزات
ALTER TABLE bookings
MODIFY COLUMN status ENUM(
  'scheduled',
  'completed',
  'cancelled',
  'cancelled_teacher_deleted',
  'issue_reported',
  'ongoing'
) NOT NULL DEFAULT 'scheduled'
COMMENT 'حالة الحجز - تم إضافة cancelled_teacher_deleted للحجوزات الملغية بسبب حذف المدرس';

-- تحديث enum لإضافة حالة جديدة للاجتماعات
ALTER TABLE meetings
MODIFY COLUMN status ENUM(
  'scheduled',
  'completed',
  'cancelled',
  'cancelled_teacher_deleted',
  'issue_reported',
  'ongoing'
) NOT NULL DEFAULT 'scheduled'
COMMENT 'حالة الاجتماع - تم إضافة cancelled_teacher_deleted للاجتماعات الملغية بسبب حذف المدرس';

-- إضافة فهارس لتحسين الأداء
CREATE INDEX idx_bookings_status ON bookings(status);

CREATE INDEX idx_meetings_status ON meetings(status);

-- عرض النتيجة
SELECT
    'Teacher deleted status added successfully' as status,
    (SELECT COUNT(*) FROM bookings) as total_bookings,
    (SELECT COUNT(*) FROM bookings WHERE status = 'cancelled_teacher_deleted') as teacher_deleted_bookings,
    (SELECT COUNT(*) FROM meetings) as total_meetings,
    (SELECT COUNT(*) FROM meetings WHERE status = 'cancelled_teacher_deleted') as teacher_deleted_meetings;
