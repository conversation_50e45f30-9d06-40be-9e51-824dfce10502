import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TablePagination,
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';

const MyMessages = () => {
  const { t, i18n } = useTranslation();
  const { token } = useAuth();
  const isRtl = i18n.language === 'ar';

  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Dialog states
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);

  // Fetch messages
  const fetchMessages = async () => {
    setLoading(true);
    setError('');

    try {
      const { data } = await axios.get('/contact-us/my-messages', {
        params: {
          page: page + 1,
          limit: rowsPerPage
        }
      });

      if (data.success) {
        setMessages(data.data.messages);
        setTotalCount(data.data.total);
      } else {
        setError(data.message || t('myMessages.fetchError'));
      }
    } catch (err) {
      console.error('Error fetching messages:', err);
      setError(err.response?.data?.message || t('myMessages.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchMessages();
    }
  }, [token, page, rowsPerPage]);

  // Handle pagination
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle view message
  const handleViewMessage = (message) => {
    setSelectedMessage(message);
    setViewDialogOpen(true);
  };

  // Get message type label
  const getMessageTypeLabel = (type) => {
    switch (type) {
      case 'question':
        return t('contactUs.typeQuestion');
      case 'problem':
        return t('contactUs.typeProblem');
      case 'suggestion':
        return t('contactUs.typeSuggestion');
      case 'payment':
        return t('contactUs.typePayment');
      case 'other':
        return t('contactUs.typeOther');
      default:
        return type;
    }
  };

  // Get message status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'answered':
        return 'success';
      default:
        return 'default';
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            {t('myMessages.title')}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Messages table */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>{t('myMessages.subject')}</TableCell>
                  <TableCell>{t('myMessages.type')}</TableCell>
                  <TableCell>{t('myMessages.date')}</TableCell>
                  <TableCell>{t('myMessages.status')}</TableCell>
                  <TableCell align="center">{t('common.actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                      <CircularProgress />
                    </TableCell>
                  </TableRow>
                ) : messages.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                      <Typography variant="body1" color="text.secondary">
                        {t('myMessages.noMessages')}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  messages.map((message) => (
                    <TableRow
                      key={message.id}
                      sx={{
                        backgroundColor: message.status === 'answered' && !message.read_at ? 'rgba(25, 118, 210, 0.08)' : 'inherit',
                        '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
                      }}
                    >
                      <TableCell>
                        <Typography
                          variant="body2"
                          sx={{
                            maxWidth: 300,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {message.subject}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getMessageTypeLabel(message.type)}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {format(new Date(message.created_at), 'PPP', { locale: isRtl ? ar : enUS })}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={message.status === 'pending' ? t('myMessages.pending') : t('myMessages.answered')}
                          size="small"
                          color={getStatusColor(message.status)}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleViewMessage(message)}
                        >
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          <TablePagination
            component="div"
            count={totalCount}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25]}
            labelRowsPerPage={t('common.rowsPerPage')}
          />
        </Paper>

        {/* View Message Dialog */}
        <Dialog
          open={viewDialogOpen}
          onClose={() => setViewDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          {selectedMessage && (
            <>
              <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <IconButton
                    edge="start"
                    color="inherit"
                    onClick={() => setViewDialogOpen(false)}
                    aria-label="close"
                  >
                    <ArrowBackIcon />
                  </IconButton>
                  <Typography variant="h6">{selectedMessage.subject}</Typography>
                </Box>
              </DialogTitle>
              <DialogContent dividers>
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    {t('myMessages.yourMessage')}:
                  </Typography>
                  <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                    {selectedMessage.message}
                  </Typography>
                  <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', color: 'text.secondary' }}>
                    <Typography variant="caption">
                      {t('myMessages.type')}: {getMessageTypeLabel(selectedMessage.type)}
                    </Typography>
                    <Typography variant="caption">
                      {format(new Date(selectedMessage.created_at), 'PPP p', { locale: isRtl ? ar : enUS })}
                    </Typography>
                  </Box>
                </Box>

                {selectedMessage.reply ? (
                  <>
                    <Divider sx={{ my: 2 }} />
                    <Box sx={{ mt: 3 }}>
                      <Typography variant="subtitle2" color="primary" gutterBottom>
                        {t('myMessages.adminReply')}:
                      </Typography>
                      <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                        {selectedMessage.reply}
                      </Typography>
                      {selectedMessage.answered_at && (
                        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', color: 'text.secondary' }}>
                          <Typography variant="caption">
                            {format(new Date(selectedMessage.answered_at), 'PPP p', { locale: isRtl ? ar : enUS })}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </>
                ) : (
                  <Box sx={{ mt: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                    <Typography variant="body2" color="text.secondary" align="center">
                      {t('myMessages.awaitingReply')}
                    </Typography>
                  </Box>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setViewDialogOpen(false)} color="primary">
                  {t('common.close')}
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </Container>
    </Layout>
  );
};

export default MyMessages;
