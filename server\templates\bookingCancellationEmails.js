/**
 * Email templates for booking cancellation notifications
 */

/**
 * Template for teacher when they cancel a booking
 * @param {Object} data - Template data
 * @param {string} data.teacherName - Teacher's name
 * @param {string} data.studentName - Student's name
 * @param {string} data.bookingDate - Formatted booking date
 * @param {string} data.bookingTime - Formatted booking time
 * @param {string} data.duration - Lesson duration
 * @param {string} data.cancellationReason - Reason for cancellation (optional)
 * @param {string} data.refundAmount - Refund amount
 * @returns {string} HTML email template
 */
const getTeacherCancellationTemplate = (data) => {
  return `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
      <div style="background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #dc3545; margin: 0; font-size: 24px;">درس تم إلغاؤه - Lesson Cancelled</h1>
          <div style="width: 50px; height: 3px; background-color: #dc3545; margin: 10px auto;"></div>
        </div>

        <!-- Content -->
        <div style="margin-bottom: 25px;">
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 15px;">
            <strong>عزيزي الأستاذ ${data.teacherName} / Dear Teacher ${data.teacherName},</strong>
          </p>
          
          <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
            تم إلغاء الدرس التالي بنجاح:<br>
            <em>The following lesson has been successfully cancelled:</em>
          </p>
        </div>

        <!-- Booking Details -->
        <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="color: #495057; margin: 0 0 15px 0; font-size: 18px;">تفاصيل الدرس - Lesson Details</h3>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">الطالب - Student:</span>
            <span style="color: #333; margin-left: 10px;">${data.studentName}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.bookingDate}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.bookingTime}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">المدة - Duration:</span>
            <span style="color: #333; margin-left: 10px;">${data.duration} دقيقة - minutes</span>
          </div>

          ${data.cancellationReason ? `
          <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
            <span style="color: #6c757d; font-weight: bold;">سبب الإلغاء - Cancellation Reason:</span>
            <div style="color: #333; margin-top: 8px; padding: 10px; background-color: white; border-radius: 5px; border-left: 4px solid #dc3545;">
              ${data.cancellationReason}
            </div>
          </div>
          ` : ''}
        </div>

        <!-- Refund Information -->
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
          <p style="color: #155724; margin: 0; font-size: 14px;">
            <strong>💰 تم إعادة المبلغ - Refund Processed:</strong> $${data.refundAmount}
            <br><small>تم إضافة المبلغ إلى محفظة الطالب - Amount has been added to student's wallet</small>
          </p>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            إذا كان لديك أي استفسارات، يرجى التواصل معنا<br>
            <em>If you have any questions, please contact us</em>
          </p>
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            <a href="https://allemnionline.com" style="color: #007bff; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="color: #999; font-size: 11px; margin: 10px 0 0 0;">
            &copy; ${new Date().getFullYear()} Allemnionline. جميع الحقوق محفوظة - All rights reserved.
          </p>
        </div>
      </div>
    </div>
  `;
};

/**
 * Template for student when teacher cancels their booking
 * @param {Object} data - Template data
 * @param {string} data.studentName - Student's name
 * @param {string} data.teacherName - Teacher's name
 * @param {string} data.bookingDate - Formatted booking date
 * @param {string} data.bookingTime - Formatted booking time
 * @param {string} data.duration - Lesson duration
 * @param {string} data.cancellationReason - Reason for cancellation (optional)
 * @param {string} data.refundAmount - Refund amount
 * @returns {string} HTML email template
 */
const getStudentNotificationTemplate = (data) => {
  return `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
      <div style="background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #dc3545; margin: 0; font-size: 24px;">تم إلغاء درسك - Your Lesson Has Been Cancelled</h1>
          <div style="width: 50px; height: 3px; background-color: #dc3545; margin: 10px auto;"></div>
        </div>

        <!-- Content -->
        <div style="margin-bottom: 25px;">
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 15px;">
            <strong>عزيزي الطالب ${data.studentName} / Dear Student ${data.studentName},</strong>
          </p>
          
          <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
            نأسف لإبلاغك بأن المعلم قد اضطر لإلغاء الدرس التالي:<br>
            <em>We regret to inform you that your teacher had to cancel the following lesson:</em>
          </p>
        </div>

        <!-- Booking Details -->
        <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="color: #495057; margin: 0 0 15px 0; font-size: 18px;">تفاصيل الدرس - Lesson Details</h3>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">المعلم - Teacher:</span>
            <span style="color: #333; margin-left: 10px;">${data.teacherName}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.bookingDate}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.bookingTime}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">المدة - Duration:</span>
            <span style="color: #333; margin-left: 10px;">${data.duration} دقيقة - minutes</span>
          </div>

          ${data.cancellationReason ? `
          <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
            <span style="color: #6c757d; font-weight: bold;">سبب الإلغاء - Cancellation Reason:</span>
            <div style="color: #333; margin-top: 8px; padding: 10px; background-color: white; border-radius: 5px; border-left: 4px solid #dc3545;">
              ${data.cancellationReason}
            </div>
          </div>
          ` : ''}
        </div>

        <!-- Refund Information -->
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
          <p style="color: #155724; margin: 0; font-size: 14px;">
            <strong>💰 تم إعادة المبلغ كاملاً - Full Refund Processed:</strong> $${data.refundAmount}
            <br><small>تم إضافة المبلغ إلى محفظتك - Amount has been added to your wallet</small>
          </p>
        </div>

        <!-- Action Button -->
        <div style="text-align: center; margin: 25px 0;">
          <a href="https://allemnionline.com/student/find-teacher" 
             style="background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
            ابحث عن معلم آخر - Find Another Teacher
          </a>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            نعتذر عن أي إزعاج، ونتطلع لخدمتك قريباً<br>
            <em>We apologize for any inconvenience and look forward to serving you soon</em>
          </p>
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            <a href="https://allemnionline.com" style="color: #007bff; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="color: #999; font-size: 11px; margin: 10px 0 0 0;">
            &copy; ${new Date().getFullYear()} Allemnionline. جميع الحقوق محفوظة - All rights reserved.
          </p>
        </div>
      </div>
    </div>
  `;
};

/**
 * Template for student when they cancel their own booking
 * @param {Object} data - Template data
 * @param {string} data.studentName - Student's name
 * @param {string} data.teacherName - Teacher's name
 * @param {string} data.bookingDate - Formatted booking date
 * @param {string} data.bookingTime - Formatted booking time
 * @param {string} data.duration - Lesson duration
 * @param {string} data.cancellationReason - Reason for cancellation (optional)
 * @param {string} data.refundAmount - Refund amount
 * @returns {string} HTML email template
 */
const getStudentCancellationTemplate = (data) => {
  return `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
      <div style="background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #dc3545; margin: 0; font-size: 24px;">تأكيد إلغاء الدرس - Lesson Cancellation Confirmed</h1>
          <div style="width: 50px; height: 3px; background-color: #dc3545; margin: 10px auto;"></div>
        </div>

        <!-- Content -->
        <div style="margin-bottom: 25px;">
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 15px;">
            <strong>عزيزي الطالب ${data.studentName} / Dear Student ${data.studentName},</strong>
          </p>
          
          <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
            تم إلغاء درسك بنجاح كما طلبت:<br>
            <em>Your lesson has been successfully cancelled as requested:</em>
          </p>
        </div>

        <!-- Booking Details -->
        <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="color: #495057; margin: 0 0 15px 0; font-size: 18px;">تفاصيل الدرس المُلغى - Cancelled Lesson Details</h3>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">المعلم - Teacher:</span>
            <span style="color: #333; margin-left: 10px;">${data.teacherName}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.bookingDate}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.bookingTime}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #6c757d; font-weight: bold;">المدة - Duration:</span>
            <span style="color: #333; margin-left: 10px;">${data.duration} دقيقة - minutes</span>
          </div>

          ${data.cancellationReason ? `
          <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
            <span style="color: #6c757d; font-weight: bold;">سبب الإلغاء - Your Cancellation Reason:</span>
            <div style="color: #333; margin-top: 8px; padding: 10px; background-color: white; border-radius: 5px; border-left: 4px solid #dc3545;">
              ${data.cancellationReason}
            </div>
          </div>
          ` : ''}
        </div>

        <!-- Refund Information -->
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
          <p style="color: #155724; margin: 0; font-size: 14px;">
            <strong>💰 تم إعادة المبلغ - Refund Processed:</strong> $${data.refundAmount}
            <br><small>تم إضافة المبلغ إلى محفظتك - Amount has been added to your wallet</small>
          </p>
        </div>

        <!-- Action Button -->
        <div style="text-align: center; margin: 25px 0;">
          <a href="https://allemnionline.com/student/find-teacher" 
             style="background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
            احجز درساً جديداً - Book a New Lesson
          </a>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            شكراً لاستخدامك منصتنا<br>
            <em>Thank you for using our platform</em>
          </p>
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            <a href="https://allemnionline.com" style="color: #007bff; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="color: #999; font-size: 11px; margin: 10px 0 0 0;">
            &copy; ${new Date().getFullYear()} Allemnionline. جميع الحقوق محفوظة - All rights reserved.
          </p>
        </div>
      </div>
    </div>
  `;
};

module.exports = {
  getTeacherCancellationTemplate,
  getStudentNotificationTemplate,
  getStudentCancellationTemplate
};
