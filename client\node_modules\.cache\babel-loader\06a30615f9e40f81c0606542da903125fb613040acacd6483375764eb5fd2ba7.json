{"ast": null, "code": "import React,{useState}from'react';import dayjs from'dayjs';import utc from'dayjs/plugin/utc';import{Dialog,DialogTitle,DialogContent,DialogActions,Button,RadioGroup,FormControlLabel,Radio,TextField,Typography}from'@mui/material';import{useTranslation}from'react-i18next';import{convertFromDatabaseTime,formatDateInStudentTimezone}from'../utils/timezone';// Extend dayjs plugins\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";dayjs.extend(utc);const MeetingFeedbackDialog=_ref=>{let{open,meeting,timezone=null,onSubmit,onClose}=_ref;const{t}=useTranslation();const[issueType,setIssueType]=useState('');const[description,setDescription]=useState('');const handleSubmit=()=>{if(onSubmit&&meeting){onSubmit(meeting.meeting_id||meeting.id,{booking_id:typeof meeting.booking_id==='number'||typeof meeting.booking_id==='string'&&/^\\d+$/.test(meeting.booking_id)?Number(meeting.booking_id):null,issue_type:issueType,description});}};const disabled=!issueType;return/*#__PURE__*/_jsxs(Dialog,{open:open,onClose:(_,reason)=>{// Prevent closing by backdrop click or escape to force answer\nif(reason==='backdropClick'||reason==='escapeKeyDown')return;if(onClose)onClose();},disableEscapeKeyDown:true,fullWidth:true,maxWidth:\"sm\",children:[/*#__PURE__*/_jsx(DialogTitle,{children:t('meetings.feedback.title')}),/*#__PURE__*/_jsxs(DialogContent,{dividers:true,children:[meeting&&/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",gutterBottom:true,children:t('meetings.feedback.detailsHeader')}),meeting&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[t('meetings.teacher'),\": \",meeting.teacher_name||'—']}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",gutterBottom:true,children:(()=>{const dateStr=timezone?formatDateInStudentTimezone(meeting.datetime,timezone,'YYYY-MM-DD'):dayjs.utc(meeting.datetime).local().format('YYYY-MM-DD');return`${t('meetings.date')}: ${dateStr}`;})()})]}),meeting&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",gutterBottom:true,children:(()=>{const startStr=timezone?formatDateInStudentTimezone(meeting.datetime,timezone,'hh:mm A'):dayjs.utc(meeting.datetime).local().format('hh:mm A');const endStr=timezone?formatDateInStudentTimezone(dayjs(meeting.datetime).add(meeting.duration||50,'minute').toDate(),timezone,'hh:mm A'):dayjs.utc(meeting.datetime).local().add(meeting.duration||50,'minute').format('hh:mm A');return`${t('meetings.time')}: ${startStr} - ${endStr} (${meeting.duration||50} ${t('common.minutes')})`;})()}),/*#__PURE__*/_jsx(Typography,{gutterBottom:true,children:t('meetings.feedback.question')}),/*#__PURE__*/_jsxs(RadioGroup,{value:issueType,onChange:e=>setIssueType(e.target.value),children:[/*#__PURE__*/_jsx(FormControlLabel,{value:\"no_issue\",control:/*#__PURE__*/_jsx(Radio,{}),label:t('meetings.feedback.success')}),/*#__PURE__*/_jsx(FormControlLabel,{value:\"teacher_absent\",control:/*#__PURE__*/_jsx(Radio,{}),label:t('meetings.feedback.teacher_absent')}),/*#__PURE__*/_jsx(FormControlLabel,{value:\"technical_issue\",control:/*#__PURE__*/_jsx(Radio,{}),label:t('meetings.feedback.technical_issue')})]}),(issueType==='teacher_absent'||issueType==='technical_issue')&&/*#__PURE__*/_jsx(TextField,{label:t('meetings.feedback.details'),multiline:true,minRows:3,fullWidth:true,value:description,onChange:e=>setDescription(e.target.value),sx:{mt:2}})]}),/*#__PURE__*/_jsx(DialogActions,{children:/*#__PURE__*/_jsx(Button,{disabled:disabled,variant:\"contained\",onClick:handleSubmit,children:t('common.submit')})})]});};export default MeetingFeedbackDialog;", "map": {"version": 3, "names": ["React", "useState", "dayjs", "utc", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "RadioGroup", "FormControlLabel", "Radio", "TextField", "Typography", "useTranslation", "convertFromDatabaseTime", "formatDateInStudentTimezone", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "extend", "MeetingFeedbackDialog", "_ref", "open", "meeting", "timezone", "onSubmit", "onClose", "t", "issueType", "setIssueType", "description", "setDescription", "handleSubmit", "meeting_id", "id", "booking_id", "test", "Number", "issue_type", "disabled", "_", "reason", "disableEscapeKeyDown", "fullWidth", "max<PERSON><PERSON><PERSON>", "children", "dividers", "variant", "gutterBottom", "teacher_name", "dateStr", "datetime", "local", "format", "startStr", "endStr", "add", "duration", "toDate", "value", "onChange", "e", "target", "control", "label", "multiline", "minRows", "sx", "mt", "onClick"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/MeetingFeedbackDialog.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport dayjs from 'dayjs';\nimport utc from 'dayjs/plugin/utc';\n\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  RadioGroup,\n  FormControlLabel,\n  Radio,\n  TextField,\n  Typography\n} from '@mui/material';\nimport { useTranslation } from 'react-i18next';\n\nimport { convertFromDatabaseTime, formatDateInStudentTimezone } from '../utils/timezone';\n\n// Extend dayjs plugins\ndayjs.extend(utc);\n\nconst MeetingFeedbackDialog = ({ open, meeting, timezone = null, onSubmit, onClose }) => {\n  const { t } = useTranslation();\n  const [issueType, setIssueType] = useState('');\n  const [description, setDescription] = useState('');\n\n  const handleSubmit = () => {\n    if (onSubmit && meeting) {\n      onSubmit(meeting.meeting_id || meeting.id, {\n        booking_id: (typeof meeting.booking_id === 'number' || (typeof meeting.booking_id === 'string' && /^\\d+$/.test(meeting.booking_id)) ) ? Number(meeting.booking_id) : null,\n        issue_type: issueType,\n        description\n      });\n    }\n  };\n\n  const disabled = !issueType;\n\n  return (\n    <Dialog\n      open={open}\n      onClose={(_, reason) => {\n        // Prevent closing by backdrop click or escape to force answer\n        if (reason === 'backdropClick' || reason === 'escapeKeyDown') return;\n        if (onClose) onClose();\n      }}\n      disableEscapeKeyDown\n      fullWidth\n      maxWidth=\"sm\"\n    >\n      <DialogTitle>{t('meetings.feedback.title')}</DialogTitle>\n      <DialogContent dividers>\n        {meeting && (\n          <Typography variant=\"subtitle1\" gutterBottom>\n            {t('meetings.feedback.detailsHeader')}\n          </Typography>\n        )}\n        {meeting && (\n          <>\n            <Typography variant=\"body2\" gutterBottom>\n              {t('meetings.teacher')}: {meeting.teacher_name || '—'}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom>\n              {(() => {\n                const dateStr = timezone ? formatDateInStudentTimezone(meeting.datetime, timezone, 'YYYY-MM-DD') : dayjs.utc(meeting.datetime).local().format('YYYY-MM-DD');\n                return `${t('meetings.date')}: ${dateStr}`;\n              })()}\n            </Typography>\n          </>\n        )}\n        {meeting && (\n          <Typography variant=\"body2\" gutterBottom>\n            {(() => {\n              const startStr = timezone ? formatDateInStudentTimezone(meeting.datetime, timezone, 'hh:mm A') : dayjs.utc(meeting.datetime).local().format('hh:mm A');\n              const endStr = timezone ? formatDateInStudentTimezone(dayjs(meeting.datetime).add(meeting.duration || 50, 'minute').toDate(), timezone, 'hh:mm A') : dayjs.utc(meeting.datetime).local().add(meeting.duration || 50, 'minute').format('hh:mm A');\n              return `${t('meetings.time')}: ${startStr} - ${endStr} (${meeting.duration || 50} ${t('common.minutes')})`;\n            })()}\n          </Typography>\n        )}\n        <Typography gutterBottom>\n          {t('meetings.feedback.question')}\n        </Typography>\n        <RadioGroup\n          value={issueType}\n          onChange={(e) => setIssueType(e.target.value)}\n        >\n          <FormControlLabel value=\"no_issue\" control={<Radio />} label={t('meetings.feedback.success')} />\n          <FormControlLabel value=\"teacher_absent\" control={<Radio />} label={t('meetings.feedback.teacher_absent')} />\n          <FormControlLabel value=\"technical_issue\" control={<Radio />} label={t('meetings.feedback.technical_issue')} />\n        </RadioGroup>\n        {(issueType === 'teacher_absent' || issueType === 'technical_issue') && (\n          <TextField\n            label={t('meetings.feedback.details')}\n            multiline\n            minRows={3}\n            fullWidth\n            value={description}\n            onChange={(e) => setDescription(e.target.value)}\n            sx={{ mt: 2 }}\n          />\n        )}\n      </DialogContent>\n      <DialogActions>\n        {/* no cancel button to force selection */}\n        <Button disabled={disabled} variant=\"contained\" onClick={handleSubmit}>\n          {t('common.submit')}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default MeetingFeedbackDialog;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,GAAG,KAAM,kBAAkB,CAElC,OACEC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,MAAM,CACNC,UAAU,CACVC,gBAAgB,CAChBC,KAAK,CACLC,SAAS,CACTC,UAAU,KACL,eAAe,CACtB,OAASC,cAAc,KAAQ,eAAe,CAE9C,OAASC,uBAAuB,CAAEC,2BAA2B,KAAQ,mBAAmB,CAExF;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACApB,KAAK,CAACqB,MAAM,CAACpB,GAAG,CAAC,CAEjB,KAAM,CAAAqB,qBAAqB,CAAGC,IAAA,EAA2D,IAA1D,CAAEC,IAAI,CAAEC,OAAO,CAAEC,QAAQ,CAAG,IAAI,CAAEC,QAAQ,CAAEC,OAAQ,CAAC,CAAAL,IAAA,CAClF,KAAM,CAAEM,CAAE,CAAC,CAAGjB,cAAc,CAAC,CAAC,CAC9B,KAAM,CAACkB,SAAS,CAAEC,YAAY,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACiC,WAAW,CAAEC,cAAc,CAAC,CAAGlC,QAAQ,CAAC,EAAE,CAAC,CAElD,KAAM,CAAAmC,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIP,QAAQ,EAAIF,OAAO,CAAE,CACvBE,QAAQ,CAACF,OAAO,CAACU,UAAU,EAAIV,OAAO,CAACW,EAAE,CAAE,CACzCC,UAAU,CAAG,MAAO,CAAAZ,OAAO,CAACY,UAAU,GAAK,QAAQ,EAAK,MAAO,CAAAZ,OAAO,CAACY,UAAU,GAAK,QAAQ,EAAI,OAAO,CAACC,IAAI,CAACb,OAAO,CAACY,UAAU,CAAE,CAAKE,MAAM,CAACd,OAAO,CAACY,UAAU,CAAC,CAAG,IAAI,CACzKG,UAAU,CAAEV,SAAS,CACrBE,WACF,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAS,QAAQ,CAAG,CAACX,SAAS,CAE3B,mBACEZ,KAAA,CAAChB,MAAM,EACLsB,IAAI,CAAEA,IAAK,CACXI,OAAO,CAAEA,CAACc,CAAC,CAAEC,MAAM,GAAK,CACtB;AACA,GAAIA,MAAM,GAAK,eAAe,EAAIA,MAAM,GAAK,eAAe,CAAE,OAC9D,GAAIf,OAAO,CAAEA,OAAO,CAAC,CAAC,CACxB,CAAE,CACFgB,oBAAoB,MACpBC,SAAS,MACTC,QAAQ,CAAC,IAAI,CAAAC,QAAA,eAEb/B,IAAA,CAACb,WAAW,EAAA4C,QAAA,CAAElB,CAAC,CAAC,yBAAyB,CAAC,CAAc,CAAC,cACzDX,KAAA,CAACd,aAAa,EAAC4C,QAAQ,MAAAD,QAAA,EACpBtB,OAAO,eACNT,IAAA,CAACL,UAAU,EAACsC,OAAO,CAAC,WAAW,CAACC,YAAY,MAAAH,QAAA,CACzClB,CAAC,CAAC,iCAAiC,CAAC,CAC3B,CACb,CACAJ,OAAO,eACNP,KAAA,CAAAE,SAAA,EAAA2B,QAAA,eACE7B,KAAA,CAACP,UAAU,EAACsC,OAAO,CAAC,OAAO,CAACC,YAAY,MAAAH,QAAA,EACrClB,CAAC,CAAC,kBAAkB,CAAC,CAAC,IAAE,CAACJ,OAAO,CAAC0B,YAAY,EAAI,GAAG,EAC3C,CAAC,cACbnC,IAAA,CAACL,UAAU,EAACsC,OAAO,CAAC,OAAO,CAACC,YAAY,MAAAH,QAAA,CACrC,CAAC,IAAM,CACN,KAAM,CAAAK,OAAO,CAAG1B,QAAQ,CAAGZ,2BAA2B,CAACW,OAAO,CAAC4B,QAAQ,CAAE3B,QAAQ,CAAE,YAAY,CAAC,CAAG1B,KAAK,CAACC,GAAG,CAACwB,OAAO,CAAC4B,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC,CAC3J,MAAO,GAAG1B,CAAC,CAAC,eAAe,CAAC,KAAKuB,OAAO,EAAE,CAC5C,CAAC,EAAE,CAAC,CACM,CAAC,EACb,CACH,CACA3B,OAAO,eACNT,IAAA,CAACL,UAAU,EAACsC,OAAO,CAAC,OAAO,CAACC,YAAY,MAAAH,QAAA,CACrC,CAAC,IAAM,CACN,KAAM,CAAAS,QAAQ,CAAG9B,QAAQ,CAAGZ,2BAA2B,CAACW,OAAO,CAAC4B,QAAQ,CAAE3B,QAAQ,CAAE,SAAS,CAAC,CAAG1B,KAAK,CAACC,GAAG,CAACwB,OAAO,CAAC4B,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,CAAC,CACtJ,KAAM,CAAAE,MAAM,CAAG/B,QAAQ,CAAGZ,2BAA2B,CAACd,KAAK,CAACyB,OAAO,CAAC4B,QAAQ,CAAC,CAACK,GAAG,CAACjC,OAAO,CAACkC,QAAQ,EAAI,EAAE,CAAE,QAAQ,CAAC,CAACC,MAAM,CAAC,CAAC,CAAElC,QAAQ,CAAE,SAAS,CAAC,CAAG1B,KAAK,CAACC,GAAG,CAACwB,OAAO,CAAC4B,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAC,CAACI,GAAG,CAACjC,OAAO,CAACkC,QAAQ,EAAI,EAAE,CAAE,QAAQ,CAAC,CAACJ,MAAM,CAAC,SAAS,CAAC,CAChP,MAAO,GAAG1B,CAAC,CAAC,eAAe,CAAC,KAAK2B,QAAQ,MAAMC,MAAM,KAAKhC,OAAO,CAACkC,QAAQ,EAAI,EAAE,IAAI9B,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAC5G,CAAC,EAAE,CAAC,CACM,CACb,cACDb,IAAA,CAACL,UAAU,EAACuC,YAAY,MAAAH,QAAA,CACrBlB,CAAC,CAAC,4BAA4B,CAAC,CACtB,CAAC,cACbX,KAAA,CAACX,UAAU,EACTsD,KAAK,CAAE/B,SAAU,CACjBgC,QAAQ,CAAGC,CAAC,EAAKhC,YAAY,CAACgC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAd,QAAA,eAE9C/B,IAAA,CAACR,gBAAgB,EAACqD,KAAK,CAAC,UAAU,CAACI,OAAO,cAAEjD,IAAA,CAACP,KAAK,GAAE,CAAE,CAACyD,KAAK,CAAErC,CAAC,CAAC,2BAA2B,CAAE,CAAE,CAAC,cAChGb,IAAA,CAACR,gBAAgB,EAACqD,KAAK,CAAC,gBAAgB,CAACI,OAAO,cAAEjD,IAAA,CAACP,KAAK,GAAE,CAAE,CAACyD,KAAK,CAAErC,CAAC,CAAC,kCAAkC,CAAE,CAAE,CAAC,cAC7Gb,IAAA,CAACR,gBAAgB,EAACqD,KAAK,CAAC,iBAAiB,CAACI,OAAO,cAAEjD,IAAA,CAACP,KAAK,GAAE,CAAE,CAACyD,KAAK,CAAErC,CAAC,CAAC,mCAAmC,CAAE,CAAE,CAAC,EACrG,CAAC,CACZ,CAACC,SAAS,GAAK,gBAAgB,EAAIA,SAAS,GAAK,iBAAiB,gBACjEd,IAAA,CAACN,SAAS,EACRwD,KAAK,CAAErC,CAAC,CAAC,2BAA2B,CAAE,CACtCsC,SAAS,MACTC,OAAO,CAAE,CAAE,CACXvB,SAAS,MACTgB,KAAK,CAAE7B,WAAY,CACnB8B,QAAQ,CAAGC,CAAC,EAAK9B,cAAc,CAAC8B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChDQ,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CACf,CACF,EACY,CAAC,cAChBtD,IAAA,CAACX,aAAa,EAAA0C,QAAA,cAEZ/B,IAAA,CAACV,MAAM,EAACmC,QAAQ,CAAEA,QAAS,CAACQ,OAAO,CAAC,WAAW,CAACsB,OAAO,CAAErC,YAAa,CAAAa,QAAA,CACnElB,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,CACI,CAAC,EACV,CAAC,CAEb,CAAC,CAED,cAAe,CAAAP,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}