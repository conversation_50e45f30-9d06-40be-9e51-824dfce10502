const mysql = require('mysql2/promise');
const config = require('../config/db.config');

// إرسال طلب تعديل بيانات المعلم
const submitProfileUpdate = async (req, res) => {
  try {
    console.log('=== Submitting teacher profile update request ===');
    console.log('Auth user:', req.user);
    console.log('Request body:', req.body);
    console.log('Request file:', req.file);

    if (!req.user || !req.user.id) {
      console.log('Authentication failed - no user');
      return res.status(401).json({
        success: false,
        message: 'غير مصرح بالوصول'
      });
    }

    console.log('User authenticated successfully, proceeding...');

  let connection;
  try {
    console.log('Connecting to database...');
    connection = await mysql.createConnection(config);
    await connection.beginTransaction();
    console.log('Database connection established');

    // التحقق من وجود ملف المعلم
    console.log('Checking for teacher profile with user_id:', req.user.id);
    const [existingProfiles] = await connection.execute(
      'SELECT id FROM teacher_profiles WHERE user_id = ?',
      [req.user.id]
    );
    console.log('Teacher profiles found:', existingProfiles);

    if (existingProfiles.length === 0) {
      await connection.rollback();
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على ملف المعلم'
      });
    }

    const teacherProfileId = existingProfiles[0].id;
    console.log('Teacher profile ID:', teacherProfileId);

    // التحقق من وجود طلب تعديل معلق
    const [pendingUpdates] = await connection.execute(
      'SELECT id FROM teacher_profile_updates WHERE teacher_profile_id = ? AND status = "pending"',
      [teacherProfileId]
    );

    if (pendingUpdates.length > 0) {
      await connection.rollback();
      return res.status(400).json({
        success: false,
        message: 'يوجد طلب تعديل معلق بالفعل. يرجى انتظار مراجعة الطلب السابق.'
      });
    }

    // استخراج البيانات من الطلب
    const {
      fullName,
      email,
      country,
      residence,
      qualifications,
      teachingExperience,
      cv,
      introVideoUrl
    } = req.body;

    // معالجة الصورة الشخصية
    let profilePictureUrl = null;
    if (req.file) {
      profilePictureUrl = `/uploads/profile-pictures/${req.file.filename}`;
    }

    // إدراج طلب التعديل في الجدول
    console.log('Inserting profile update with data:', {
      teacherProfileId,
      userId: req.user.id,
      fullName,
      email,
      country,
      residence,
      profilePictureUrl,
      introVideoUrl,
      cv,
      qualifications,
      teachingExperience: parseInt(teachingExperience) || 0
    });

    const [insertResult] = await connection.execute(
      `INSERT INTO teacher_profile_updates
       (teacher_profile_id, user_id, full_name, email, country, residence,
        profile_picture_url, intro_video_url, cv, qualifications, teaching_experience, status)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')`,
      [
        teacherProfileId,
        req.user.id,
        fullName,
        email,
        country,
        residence,
        profilePictureUrl,
        introVideoUrl,
        cv,
        qualifications,
        parseInt(teachingExperience) || 0
      ]
    );

    console.log('Insert result:', insertResult);

    await connection.commit();

    console.log('Profile update request submitted successfully:', insertResult.insertId);

    res.json({
      success: true,
      message: 'تم إرسال طلب تعديل البيانات بنجاح وسيتم مراجعته من قبل الإدارة',
      updateId: insertResult.insertId
    });

  } catch (error) {
    if (connection) {
      await connection.rollback();
    }
    console.error('Error submitting profile update request:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء إرسال طلب التعديل'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
  } catch (outerError) {
    console.error('Outer catch - Error in submitProfileUpdate:', outerError);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ غير متوقع',
      error: outerError.message
    });
  }
};

// الحصول على حالة طلب التعديل للمعلم
const getUpdateStatus = async (req, res) => {
  let connection;
  try {
    connection = await mysql.createConnection(config);

    // الحصول على ID ملف المعلم
    const [profiles] = await connection.execute(
      'SELECT id FROM teacher_profiles WHERE user_id = ?',
      [req.user.id]
    );

    if (profiles.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'لم يتم العثور على ملف المعلم'
      });
    }

    const teacherProfileId = profiles[0].id;

    // البحث عن طلب التعديل المعلق
    const [updates] = await connection.execute(
      `SELECT id, status, admin_notes, created_at, reviewed_at 
       FROM teacher_profile_updates 
       WHERE teacher_profile_id = ? 
       ORDER BY created_at DESC 
       LIMIT 1`,
      [teacherProfileId]
    );

    if (updates.length === 0) {
      return res.json({
        success: true,
        hasUpdate: false
      });
    }

    const update = updates[0];
    res.json({
      success: true,
      hasUpdate: true,
      update: {
        id: update.id,
        status: update.status,
        adminNotes: update.admin_notes,
        createdAt: update.created_at,
        reviewedAt: update.reviewed_at
      }
    });

  } catch (error) {
    console.error('Error getting update status:', error);
    res.status(500).json({
      success: false,
      message: 'حدث خطأ أثناء جلب حالة التعديل'
    });
  } finally {
    if (connection) {
      connection.end();
    }
  }
};

module.exports = {
  submitProfileUpdate,
  getUpdateStatus
};
