const mysql = require('mysql2/promise');
const config = require('../config/db.config');
const db = require('../config/database');

const getOrCreateConversation = async (req, res) => {
  let connection;
  try {
    const { teacherId } = req.params;
    const studentId = req.user.id;

    if (req.user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: 'Only students can initiate conversations'
      });
    }

    connection = await mysql.createConnection(config);

    // Check if conversation exists
    let [conversations] = await connection.execute(
      'SELECT id FROM conversations WHERE student_id = ? AND teacher_id = ?',
      [studentId, teacherId]
    );

    let conversationId;

    if (conversations.length === 0) {
      // Create new conversation
      const [result] = await connection.execute(
        'INSERT INTO conversations (student_id, teacher_id) VALUES (?, ?)',
        [studentId, teacherId]
      );
      conversationId = result.insertId;
    } else {
      conversationId = conversations[0].id;
    }

    // Get conversation details
    const [conversation] = await connection.execute(`
      SELECT 
        c.id,
        c.created_at,
        student.id as student_id,
        student.full_name as student_name,
        student.profile_picture_url as student_picture,
        teacher.id as teacher_id,
        teacher.full_name as teacher_name,
        teacher.profile_picture_url as teacher_picture
      FROM conversations c
      JOIN users student ON c.student_id = student.id
      JOIN users teacher ON c.teacher_id = teacher.id
      WHERE c.id = ?
    `, [conversationId]);

    res.json({
      success: true,
      data: conversation[0]
    });
  } catch (error) {
    console.error('Error in chat:', error);
    res.status(500).json({
      success: false,
      message: 'Error processing chat request'
    });
  } finally {
    if (connection) connection.end();
  }
};

const getConversations = async (req, res) => {
  let connection;
  try {
    const userId = req.user.id;
    const isStudent = req.user.role === 'student';

    connection = await mysql.createConnection(config);

    const [conversations] = await connection.execute(`
      SELECT 
        c.id,
        c.created_at,
        student.id as student_id,
        student.full_name as student_name,
        student.profile_picture_url as student_picture,
        teacher.id as teacher_id,
        teacher.full_name as teacher_name,
        teacher.profile_picture_url as teacher_picture,
        (
          SELECT COUNT(*) 
          FROM messages m 
          WHERE m.conversation_id = c.id 
          AND m.read_at IS NULL 
          AND m.sender_id != ?
        ) as unread_count,
        (
          SELECT content 
          FROM messages 
          WHERE conversation_id = c.id 
          ORDER BY created_at DESC 
          LIMIT 1
        ) as last_message,
        (
          SELECT created_at 
          FROM messages 
          WHERE conversation_id = c.id 
          ORDER BY created_at DESC 
          LIMIT 1
        ) as last_message_time
      FROM conversations c
      JOIN users student ON c.student_id = student.id
      JOIN users teacher ON c.teacher_id = teacher.id
      WHERE ${isStudent ? 'c.student_id = ?' : 'c.teacher_id = ?'}
      ORDER BY COALESCE(last_message_time, c.created_at) DESC
    `, [userId, userId]);

    res.json({
      success: true,
      data: conversations
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching conversations'
    });
  } finally {
    if (connection) connection.end();
  }
};

const getMessages = async (req, res) => {
  let connection;
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    connection = await mysql.createConnection(config);

    // Verify user has access to this conversation
    const [conversation] = await connection.execute(
      'SELECT * FROM conversations WHERE id = ? AND (student_id = ? OR teacher_id = ?)',
      [conversationId, userId, userId]
    );

    if (conversation.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized access to conversation'
      });
    }

    // Get messages
    const [messages] = await connection.execute(`
      SELECT 
        id,
        sender_id,
        content,
        read_at IS NOT NULL as is_read,
        created_at
      FROM messages 
      WHERE conversation_id = ?
      ORDER BY created_at ASC
    `, [conversationId]);

    // Mark messages as read if recipient is requesting
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage.sender_id !== userId) {
        await connection.execute(
          'UPDATE messages SET read_at = CURRENT_TIMESTAMP WHERE conversation_id = ? AND sender_id != ? AND read_at IS NULL',
          [conversationId, userId]
        );
      }
    }

    res.json({
      success: true,
      data: messages
    });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching messages'
    });
  } finally {
    if (connection) connection.end();
  }
};

const sendMessage = async (req, res) => {
  let connection;
  try {
    const { conversationId } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    if (!content || !content.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Message content is required'
      });
    }

    connection = await mysql.createConnection(config);

    // Verify user has access to this conversation
    const [conversation] = await connection.execute(
      'SELECT * FROM conversations WHERE id = ? AND (student_id = ? OR teacher_id = ?)',
      [conversationId, userId, userId]
    );

    if (conversation.length === 0) {
      return res.status(403).json({
        success: false,
        message: 'Unauthorized access to conversation'
      });
    }

    // Get recipient ID
    const recipientId = conversation[0].student_id === userId ? conversation[0].teacher_id : conversation[0].student_id;

    // Insert message
    const [result] = await connection.execute(
      'INSERT INTO messages (conversation_id, sender_id, recipient_id, content) VALUES (?, ?, ?, ?)',
      [conversationId, userId, recipientId, content]
    );

    const messageId = result.insertId;

    // Get message details
    const [messages] = await connection.execute(
      'SELECT id, sender_id, content, read_at IS NOT NULL as is_read, created_at FROM messages WHERE id = ?',
      [messageId]
    );

    res.json({
      success: true,
      data: messages[0]
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      message: 'Error sending message'
    });
  } finally {
    if (connection) connection.end();
  }
};

const editMessage = async (req, res) => {
  try {
    const { messageId } = req.params;
    const { content } = req.body;
    const userId = req.user.id;

    // Verify message ownership
    const [message] = await db.query(
      'SELECT * FROM messages WHERE id = ? AND sender_id = ?',
      [messageId, userId]
    );

    if (!message.length) {
      return res.status(403).json({
        success: false,
        error: 'You are not authorized to edit this message'
      });
    }

    // Update the message
    await db.query(
      'UPDATE messages SET content = ? WHERE id = ?',
      [content, messageId]
    );

    res.json({
      success: true,
      message: 'Message updated successfully'
    });
  } catch (error) {
    console.error('Error editing message:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

const deleteMessage = async (req, res) => {
  try {
    const { messageId } = req.params;
    const userId = req.user.id;

    // Verify message ownership
    const [message] = await db.query(
      'SELECT * FROM messages WHERE id = ? AND sender_id = ?',
      [messageId, userId]
    );

    if (!message.length) {
      return res.status(403).json({
        success: false,
        error: 'You are not authorized to delete this message'
      });
    }

    // Delete the message
    await db.query('DELETE FROM messages WHERE id = ?', [messageId]);

    res.json({
      success: true,
      message: 'Message deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
};

module.exports = {
  getOrCreateConversation,
  getConversations,
  getMessages,
  sendMessage,
  editMessage,
  deleteMessage
};
