-- Fix for preventing duplicate feedback issues
-- This script adds constraints and indexes to prevent duplicate pending feedback

-- 1. First, clean up any existing duplicate pending issues
-- Keep only the oldest pending issue for each meeting+user combination
DELETE mi1 FROM meeting_issues mi1
INNER JOIN meeting_issues mi2 
WHERE mi1.id > mi2.id 
  AND mi1.meeting_id = mi2.meeting_id 
  AND mi1.user_id = mi2.user_id 
  AND mi1.issue_type = 'pending' 
  AND mi2.issue_type = 'pending';

-- 2. Add a unique index to prevent future duplicates for pending issues
-- This will prevent multiple pending issues for the same meeting+user combination
ALTER TABLE meeting_issues 
ADD CONSTRAINT unique_pending_feedback 
UNIQUE KEY (meeting_id, user_id, issue_type);

-- Note: This constraint allows multiple non-pending issues for the same meeting+user
-- but prevents duplicate pending issues which cause the double feedback problem

-- 3. Update any meeting_issues with NULL booking_id to try to find the correct booking
UPDATE meeting_issues mi
JOIN meetings m ON mi.meeting_id = m.id
JOIN bookings b ON b.student_id = m.student_id
JOIN teacher_profiles tp ON b.teacher_profile_id = tp.id AND tp.user_id = m.teacher_id
SET mi.booking_id = b.id
WHERE mi.booking_id IS NULL
  AND DATE(b.datetime) = DATE(m.meeting_date)
  AND b.status != 'cancelled'
  AND ABS(TIMESTAMPDIFF(MINUTE, b.datetime, m.meeting_date)) <= 120;

-- 4. Add an index for better performance on the pending issues query
CREATE INDEX idx_meeting_issues_pending ON meeting_issues (user_id, issue_type, created_at);

-- 5. Add an index for better performance on meeting_id lookups
CREATE INDEX idx_meeting_issues_meeting_user ON meeting_issues (meeting_id, user_id);
