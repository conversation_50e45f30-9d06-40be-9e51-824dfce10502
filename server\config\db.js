const mysql = require('mysql2/promise');
require('dotenv').config();

const pool = mysql.createPool({
  host: process.env.DB_HOST || '127.0.0.1',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'Teachislam99@',
  database: process.env.DB_NAME || 'teach_me_islam_arabic',
  timezone: process.env.DB_TIMEZONE || '+00:00',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  multipleStatements: true
});

const getConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('Database connection established');
    connection.release();
    return true;
  } catch (error) {
    console.error('Error connecting to database:', error);
    throw error;
  }
};

module.exports = {
  pool,
  getConnection
};
