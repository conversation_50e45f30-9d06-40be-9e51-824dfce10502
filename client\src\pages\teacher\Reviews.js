import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import {
  Container,
  Paper,
  Typography,
  Box,
  Rating,
  Alert,
  CircularProgress,
  Grid,
  Avatar,
  Divider,
  Card,
  CardContent,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Collapse
} from '@mui/material';
import {
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Sort as SortIcon,
  FilterList as FilterIcon,
  Reply as ReplyIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'react-hot-toast';

const TeacherReviews = () => {
  const { t, i18n } = useTranslation();
  const isRtl = i18n.dir() === 'rtl';
  const { currentUser } = useAuth();

  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    average_rating: 0,
    review_count: 0
  });

  const [sortBy, setSortBy] = useState('newest');
  const [filterRating, setFilterRating] = useState('all');

  // Reply functionality state
  const [replyDialogOpen, setReplyDialogOpen] = useState(false);
  const [selectedReview, setSelectedReview] = useState(null);
  const [replyText, setReplyText] = useState('');
  const [submittingReply, setSubmittingReply] = useState(false);
  const [expandedReplies, setExpandedReplies] = useState({});

  // Fetch teacher's reviews
  useEffect(() => {
    const fetchReviews = async () => {
      try {
        setLoading(true);

        if (!currentUser || !currentUser.id) {
          throw new Error('User information not available');
        }

        console.log('Current user:', currentUser);

        // Get teacher profile directly
        const profileResponse = await axios.get('/api/teacher/profile');

        console.log('Teacher profile response:', profileResponse.data);

        if (!profileResponse.data || !profileResponse.data.success) {
          console.error('Failed to fetch teacher profile:', profileResponse.data);
          throw new Error('Failed to fetch teacher profile');
        }

        // Get the teacher profile ID - check different possible structures
        let teacherProfileId;
        if (profileResponse.data.profile && profileResponse.data.profile.id) {
          teacherProfileId = profileResponse.data.profile.id;
        } else if (profileResponse.data.data && profileResponse.data.data.profile_id) {
          teacherProfileId = profileResponse.data.data.profile_id;
        } else if (profileResponse.data.data && profileResponse.data.data.id) {
          teacherProfileId = profileResponse.data.data.id;
        } else {
          // If we can't find the profile ID, use the user ID to query for reviews
          teacherProfileId = currentUser.id;
        }

        console.log('Found teacher profile ID:', teacherProfileId);

        // Then fetch reviews for this teacher
        const token = localStorage.getItem('token');
        const reviewsResponse = await axios.get(`/api/reviews/teacher/${teacherProfileId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('Reviews response:', reviewsResponse.data);

        if (reviewsResponse.data && reviewsResponse.data.success) {
          // Check different possible structures for reviews data
          let reviewsData = [];
          let averageRating = 0;
          let reviewCount = 0;

          if (reviewsResponse.data.reviews) {
            reviewsData = reviewsResponse.data.reviews;
            // Ensure averageRating is a number
            averageRating = typeof reviewsResponse.data.average_rating === 'number'
              ? reviewsResponse.data.average_rating
              : parseFloat(reviewsResponse.data.average_rating || 0);
            reviewCount = parseInt(reviewsResponse.data.review_count || 0, 10);
          } else if (reviewsResponse.data.data) {
            if (Array.isArray(reviewsResponse.data.data)) {
              reviewsData = reviewsResponse.data.data;
              // Calculate average rating if not provided
              if (reviewsData.length > 0) {
                const sum = reviewsData.reduce((acc, review) => acc + parseFloat(review.rating || 0), 0);
                averageRating = sum / reviewsData.length;
                reviewCount = reviewsData.length;
              }
            } else if (reviewsResponse.data.data.reviews) {
              reviewsData = reviewsResponse.data.data.reviews;
              // Ensure averageRating is a number
              averageRating = typeof reviewsResponse.data.data.average_rating === 'number'
                ? reviewsResponse.data.data.average_rating
                : parseFloat(reviewsResponse.data.data.average_rating || 0);
              reviewCount = parseInt(reviewsResponse.data.data.review_count || 0, 10);
            }
          }

          // Final check to ensure averageRating is a valid number
          if (isNaN(averageRating)) {
            console.warn('Invalid average rating value, defaulting to 0');
            averageRating = 0;
          }

          setReviews(reviewsData);
          setStats({
            average_rating: averageRating,
            review_count: reviewCount
          });
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching reviews:', err);
        setError(err.message || 'Error fetching reviews');
        setLoading(false);
      }
    };

    if (currentUser) {
      fetchReviews();
    }
  }, [currentUser]);

  // Filter and sort reviews
  const filteredAndSortedReviews = () => {
    let filtered = [...reviews];

    // Apply rating filter
    if (filterRating !== 'all') {
      const ratingValue = parseFloat(filterRating);
      filtered = filtered.filter(review => review.rating >= ratingValue && review.rating < ratingValue + 1);
    }

    // Apply sorting
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
        break;
      case 'highest':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'lowest':
        filtered.sort((a, b) => a.rating - b.rating);
        break;
      default:
        filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    }

    return filtered;
  };

  const displayedReviews = filteredAndSortedReviews();

  // Reply functionality
  const handleReplyClick = (review) => {
    setSelectedReview(review);
    setReplyText(review.reply_text || '');
    setReplyDialogOpen(true);
  };

  const handleReplySubmit = async () => {
    if (!selectedReview || !replyText.trim()) {
      toast.error(t('reviews.replyRequired'));
      return;
    }

    try {
      setSubmittingReply(true);
      const token = localStorage.getItem('token');

      const response = await axios.post(`/api/reviews/${selectedReview.id}/reply`, {
        reply_text: replyText.trim()
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        toast.success(selectedReview.reply_text ? t('reviews.replyUpdateSuccess') : t('reviews.replySuccess'));

        // Update the review in the local state
        setReviews(prevReviews =>
          prevReviews.map(review =>
            review.id === selectedReview.id
              ? { ...review, reply_text: replyText.trim(), reply_created_at: new Date().toISOString() }
              : review
          )
        );

        setReplyDialogOpen(false);
        setSelectedReview(null);
        setReplyText('');
      }
    } catch (error) {
      console.error('Error submitting reply:', error);
      toast.error(error.response?.data?.message || t('reviews.replyError'));
    } finally {
      setSubmittingReply(false);
    }
  };

  const handleDeleteReply = async (reviewId) => {
    if (!window.confirm(t('reviews.confirmDeleteReply'))) {
      return;
    }

    try {
      const token = localStorage.getItem('token');

      const response = await axios.delete(`/api/reviews/${reviewId}/reply`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.data.success) {
        toast.success(t('reviews.replyDeleteSuccess'));

        // Update the review in the local state
        setReviews(prevReviews =>
          prevReviews.map(review =>
            review.id === reviewId
              ? { ...review, reply_text: null, reply_created_at: null }
              : review
          )
        );
      }
    } catch (error) {
      console.error('Error deleting reply:', error);
      toast.error(error.response?.data?.message || t('reviews.replyError'));
    }
  };

  const toggleReplyExpansion = (reviewId) => {
    setExpandedReplies(prev => ({
      ...prev,
      [reviewId]: !prev[reviewId]
    }));
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('reviews.teacherReviews')}
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {/* Reviews Summary */}
            <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
              <Grid container spacing={3} alignItems="center">
                <Grid item xs={12} md={4} sx={{ textAlign: 'center' }}>
                  <Typography variant="h5" gutterBottom>
                    {t('reviews.averageRating')}
                  </Typography>
                  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h3" component="span" sx={{ mr: 1 }}>
                      {typeof stats.average_rating === 'number'
                        ? stats.average_rating.toFixed(1)
                        : parseFloat(stats.average_rating || 0).toFixed(1)}
                    </Typography>
                    <Typography variant="body1" color="text.secondary">
                      {t('reviews.outOf5')}
                    </Typography>
                  </Box>
                  <Rating
                    value={typeof stats.average_rating === 'number'
                      ? stats.average_rating
                      : parseFloat(stats.average_rating || 0)}
                    precision={0.5}
                    readOnly
                    size="large"
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {stats.review_count === 0
                      ? t('reviews.noReviews')
                      : stats.review_count === 1
                        ? t('reviews.oneReview')
                        : t('reviews.totalReviews', { count: stats.review_count })}
                  </Typography>
                </Grid>

                <Grid item xs={12} md={8}>
                  <Typography variant="h6" gutterBottom>
                    {t('reviews.reviewsBy')}
                  </Typography>

                  {/* Filter and Sort Controls */}
                  <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
                    <FormControl size="small" sx={{ minWidth: 150 }}>
                      <InputLabel id="filter-rating-label">
                        {t('reviews.filterBy')}
                      </InputLabel>
                      <Select
                        labelId="filter-rating-label"
                        value={filterRating}
                        onChange={(e) => setFilterRating(e.target.value)}
                        label={t('reviews.filterBy')}
                        startAdornment={<FilterIcon fontSize="small" sx={{ mr: 1 }} />}
                      >
                        <MenuItem value="all">{t('search.anyRating')}</MenuItem>
                        <MenuItem value="5">5 {t('reviews.stars')}</MenuItem>
                        <MenuItem value="4">4 {t('reviews.stars')}</MenuItem>
                        <MenuItem value="3">3 {t('reviews.stars')}</MenuItem>
                        <MenuItem value="2">2 {t('reviews.stars')}</MenuItem>
                        <MenuItem value="1">1 {t('reviews.star')}</MenuItem>
                      </Select>
                    </FormControl>

                    <FormControl size="small" sx={{ minWidth: 150 }}>
                      <InputLabel id="sort-by-label">
                        {t('reviews.sortBy')}
                      </InputLabel>
                      <Select
                        labelId="sort-by-label"
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value)}
                        label={t('reviews.sortBy')}
                        startAdornment={<SortIcon fontSize="small" sx={{ mr: 1 }} />}
                      >
                        <MenuItem value="newest">{t('reviews.newest')}</MenuItem>
                        <MenuItem value="oldest">{t('reviews.oldest')}</MenuItem>
                        <MenuItem value="highest">{t('reviews.highestRated')}</MenuItem>
                        <MenuItem value="lowest">{t('reviews.lowestRated')}</MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            {/* Reviews List */}
            {displayedReviews.length === 0 ? (
              <Alert severity="info">
                {filterRating !== 'all'
                  ? t('reviews.noReviewsWithFilter')
                  : t('reviews.noReviews')}
              </Alert>
            ) : (
              <Grid container spacing={3}>
                {displayedReviews.map((review) => (
                  <Grid item xs={12} key={review.id}>
                    <Card>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar
                            src={review.student_profile_picture}
                            alt={review.student_name}
                            sx={{ mr: 2 }}
                          />
                          <Box>
                            <Typography variant="h6">{review.student_name}</Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Rating value={review.rating} precision={0.5} readOnly size="small" />
                              <Typography variant="body2" sx={{ ml: 1 }}>
                                ({review.rating})
                              </Typography>
                            </Box>
                          </Box>
                        </Box>

                        <Divider sx={{ my: 2 }} />

                        <Typography variant="body1" sx={{ mb: 2 }}>
                          {review.comment || t('reviews.noComment')}
                        </Typography>

                        {/* Teacher Reply Section */}
                        {review.reply_text && (
                          <Box sx={{
                            bgcolor: 'primary.light',
                            p: 2,
                            borderRadius: 1,
                            mb: 2,
                            border: '1px solid',
                            borderColor: 'primary.main'
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <ReplyIcon color="primary" sx={{ mr: 1, fontSize: '1rem' }} />
                              <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 'bold' }}>
                                {t('reviews.teacherReply')}
                              </Typography>
                              <Box sx={{ ml: 'auto', display: 'flex', gap: 1 }}>
                                <IconButton
                                  size="small"
                                  onClick={() => handleReplyClick(review)}
                                  sx={{ color: 'primary.main' }}
                                >
                                  <EditIcon fontSize="small" />
                                </IconButton>
                                <IconButton
                                  size="small"
                                  onClick={() => handleDeleteReply(review.id)}
                                  sx={{ color: 'error.main' }}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Box>
                            </Box>
                            <Typography variant="body2" sx={{ color: 'primary.dark' }}>
                              {review.reply_text}
                            </Typography>
                            {review.reply_created_at && (
                              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                                {new Date(review.reply_created_at).toLocaleDateString()}
                              </Typography>
                            )}
                          </Box>
                        )}

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Box>
                            {!review.reply_text && (
                              <Button
                                variant="outlined"
                                size="small"
                                startIcon={<ReplyIcon />}
                                onClick={() => handleReplyClick(review)}
                                sx={{ mr: 1 }}
                              >
                                {t('reviews.replyToReview')}
                              </Button>
                            )}
                          </Box>
                          <Typography variant="caption" color="text.secondary">
                            {t('reviews.reviewBy')} {review.student_name} {t('reviews.on')} {new Date(review.created_at).toLocaleDateString()}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </>
        )}

        {/* Reply Dialog */}
        <Dialog
          open={replyDialogOpen}
          onClose={() => setReplyDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6">
                {selectedReview?.reply_text ? t('reviews.editReply') : t('reviews.replyToReview')}
              </Typography>
              <IconButton onClick={() => setReplyDialogOpen(false)}>
                <CloseIcon />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent>
            {selectedReview && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  {t('reviews.reviewBy')} {selectedReview.student_name}:
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Rating value={selectedReview.rating} readOnly size="small" />
                  <Typography variant="body2" sx={{ ml: 1 }}>
                    ({selectedReview.rating})
                  </Typography>
                </Box>
                <Typography variant="body2" sx={{
                  bgcolor: 'grey.100',
                  p: 2,
                  borderRadius: 1,
                  fontStyle: 'italic'
                }}>
                  "{selectedReview.comment || t('reviews.noComment')}"
                </Typography>
              </Box>
            )}
            <TextField
              fullWidth
              multiline
              rows={4}
              label={t('reviews.replyToReview')}
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
              placeholder={t('reviews.replyPlaceholder')}
              variant="outlined"
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setReplyDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button
              onClick={handleReplySubmit}
              variant="contained"
              disabled={submittingReply || !replyText.trim()}
            >
              {submittingReply ? t('reviews.sending') : (selectedReview?.reply_text ? t('reviews.updateReply') : t('reviews.sendReply'))}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Layout>
  );
};

export default TeacherReviews;
