import React from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  Card, 
  CardContent,
  Paper, 
  useTheme,
  alpha,
  Fade,
  Divider
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  School as SchoolIcon,
  Language as LanguageIcon,
  People as PeopleIcon,
  MenuBook as MenuBookIcon,
  Public as PublicIcon,
  Security as SecurityIcon,
  Email as EmailIcon,
} from '@mui/icons-material';

const AboutUs = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const isRTL = i18n.language === 'ar';

  // Shared card-like styles for each section (mimics the Privacy Policy cards)
  const cardStyles = {
    direction: isRTL ? 'rtl' : 'ltr',
    p: { xs: 3, md: 4 },
    borderRadius: 3,
    backgroundColor: alpha(theme.palette.primary.main, 0.02),
    ...(isRTL
      ? { borderRight: `6px solid ${theme.palette.primary.main}` }
      : { borderLeft: `6px solid ${theme.palette.primary.main}` }),
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,
        pt: 4,
        pb: 8
      }}
    >
      <Container maxWidth="lg">
        <Fade in timeout={1000}>
          <Paper 
            elevation={0}
            sx={{ 
              p: { xs: 3, md: 6 }, 
              borderRadius: 3,
              background: 'transparent'
            }}
          >
            {/* Title */}
            <Typography 
              variant="h3" 
              component="h1" 
              gutterBottom 
              align="center" 
              sx={{ 
                mb: 6, 
                fontWeight: 800,
                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                color: theme.palette.primary.main,
                position: 'relative',
                display: 'inline-block',
                width: '100%',
                '&:after': {
                  content: '""',
                  position: 'absolute',
                  bottom: -8,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: '80px',
                  height: 4,
                  backgroundColor: theme.palette.secondary.main,
                  borderRadius: 2
                }
              }}
            >
              {t('about.title')}
            </Typography>

            {/* Main Content */}
            <Box sx={{ ...cardStyles, mb: 6 }}>
              <Typography 
                paragraph 
                align={isRTL ? 'right' : 'left'} 
                sx={{ 
                  mb: 4, 
                  lineHeight: 2.2,
                  fontSize: '1.1rem',
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.text.primary,
                  textAlign: isRTL ? 'justify' : 'left'
                }}
              >
                {t('about.intro')}
              </Typography>

              <Typography 
                paragraph 
                align={isRTL ? 'right' : 'left'} 
                sx={{ 
                  mb: 6, 
                  lineHeight: 2.2,
                  fontSize: '1.1rem',
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.text.primary,
                  textAlign: isRTL ? 'justify' : 'left'
                }}
              >
                {t('about.mission')}
              </Typography>
            </Box>

            {/* What We Offer Section */}
            <Box sx={{ ...cardStyles, mb: 6 }}>
              <Typography 
                variant="h4" 
                component="h2" 
                gutterBottom 
                align={isRTL ? 'right' : 'left'} 
                sx={{ 
                  mb: 4, 
                  fontWeight: 700,
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.primary.main,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  justifyContent: isRTL ? 'flex-start' : 'flex-start'
                }}
              >
                <MenuBookIcon sx={{ fontSize: '2rem' }} />
                {t('about.whatWeOffer')}
              </Typography>

              <Box sx={{ pl: isRTL ? 4 : 0, pr: isRTL ? 0 : 4 }}>
                <Typography 
                  component="div" 
                  align={isRTL ? 'right' : 'left'} 
                  sx={{ 
                    mb: 2, 
                    lineHeight: 2,
                    fontSize: '1.1rem',
                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    color: theme.palette.text.primary
                  }}
                >
                  • {t('about.services.privateLessons')}
                </Typography>
                <Typography 
                  component="div" 
                  align={isRTL ? 'right' : 'left'} 
                  sx={{ 
                    mb: 2, 
                    lineHeight: 2,
                    fontSize: '1.1rem',
                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    color: theme.palette.text.primary
                  }}
                >
                  • {t('about.services.conversationTraining')}
                </Typography>
                <Typography 
                  component="div" 
                  align={isRTL ? 'right' : 'left'} 
                  sx={{ 
                    mb: 2, 
                    lineHeight: 2,
                    fontSize: '1.1rem',
                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    color: theme.palette.text.primary
                  }}
                >
                  • {t('about.services.culturalElements')}
                </Typography>
                <Typography 
                  component="div" 
                  align={isRTL ? 'right' : 'left'} 
                  sx={{ 
                    mb: 2, 
                    lineHeight: 2,
                    fontSize: '1.1rem',
                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    color: theme.palette.text.primary
                  }}
                >
                  • {t('about.services.digitalPlatform')}
                </Typography>
                <Typography 
                  component="div" 
                  align={isRTL ? 'right' : 'left'} 
                  sx={{ 
                    mb: 2, 
                    lineHeight: 2,
                    fontSize: '1.1rem',
                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                    color: theme.palette.text.primary
                  }}
                >
                  • {t('about.services.targetAudience')}
                </Typography>
              </Box>
            </Box>

            <Divider sx={{ my: 4, borderColor: alpha(theme.palette.primary.main, 0.2) }} />

            {/* Our Mission Section */}
            <Box sx={{ ...cardStyles, mb: 6 }}>
              <Typography 
                variant="h4" 
                component="h2" 
                gutterBottom 
                align={isRTL ? 'right' : 'left'} 
                sx={{ 
                  mb: 4, 
                  fontWeight: 700,
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.primary.main,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  justifyContent: isRTL ? 'flex-start' : 'flex-start'
                }}
              >
                <SchoolIcon sx={{ fontSize: '2rem' }} />
                {t('about.ourMission')}
              </Typography>

              <Typography 
                paragraph 
                align={isRTL ? 'right' : 'left'} 
                sx={{ 
                  lineHeight: 2.2,
                  fontSize: '1.1rem',
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.text.primary,
                  textAlign: isRTL ? 'justify' : 'left'
                }}
              >
                {t('about.missionText')}
              </Typography>
            </Box>

            <Divider sx={{ my: 4, borderColor: alpha(theme.palette.primary.main, 0.2) }} />

            {/* Contact Section */}
            <Box sx={cardStyles}>
              <Typography 
                variant="h4" 
                component="h2" 
                gutterBottom 
                align={isRTL ? 'right' : 'left'} 
                sx={{ 
                  mb: 4, 
                  fontWeight: 700,
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.primary.main,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  justifyContent: isRTL ? 'flex-start' : 'flex-start'
                }}
              >
                <EmailIcon sx={{ fontSize: '2rem' }} />
                {t('about.contactUs')}
              </Typography>

              <Typography 
                paragraph 
                align={isRTL ? 'right' : 'left'} 
                sx={{ 
                  mb: 2, 
                  lineHeight: 2,
                  fontSize: '1.1rem',
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.text.primary
                }}
              >
                {t('about.contactText')}
              </Typography>

              <Typography 
                component="div" 
                align={isRTL ? 'right' : 'left'} 
                sx={{ 
                  lineHeight: 2,
                  fontSize: '1.2rem',
                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.primary.main,
                  fontWeight: 600,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  justifyContent: isRTL ? 'flex-start' : 'flex-start'
                }}
              >
                📧 {t('about.email')}
              </Typography>
            </Box>
          </Paper>
        </Fade>
      </Container>
    </Box>
  );
};

export default AboutUs;
