import axios from 'axios';

// Set base URL for all API requests
axios.defaults.baseURL = process.env.REACT_APP_API_URL || '';

// Add default headers
axios.defaults.headers.common['Content-Type'] = 'application/json';

// Add request interceptor to add API prefix if needed
axios.interceptors.request.use(
  (config) => {
    // If URL doesn't start with http or / (for absolute paths), add /api prefix
    if (!config.url.startsWith('http') && !config.url.startsWith('/') && !config.url.startsWith('api/')) {
      config.url = `/api/${config.url}`;
    }

    // Get the token from localStorage
    const token = localStorage.getItem('token');

    // If token exists, add it to the headers
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor to handle errors globally
axios.interceptors.response.use(
  response => response,
  error => {
    if (error.response) {
      // Server responded with a status code outside the 2xx range
      console.error('API Error:', error.response.data);
    } else if (error.request) {
      // Request was made but no response received
      console.error('Network Error:', error.request);
    } else {
      // Something else happened while setting up the request
      console.error('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default axios;
