import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Divider,
  Avatar,
  useTheme,
  Tooltip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  CalendarMonth as CalendarIcon,
  AccessTime as TimeIcon,
  Person as PersonIcon,
  Cancel as CancelIcon,
  Close as CloseIcon,
  Event as EventIcon,
  VideoCall as VideoCallIcon,
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Schedule as RescheduleIcon
} from '@mui/icons-material';
import { format, addDays, startOfWeek, addWeeks, subWeeks } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import axios from '../../utils/axios';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';
import WeeklyBookingsTable from '../../components/WeeklyBookingsTable';
import RescheduleDialog from '../../components/RescheduleDialog';
import { convertFromDatabaseTime, formatDateInStudentTimezone, getCurrentTimeInTimezone } from '../../utils/timezone';
import moment from 'moment-timezone';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';
import VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';
import MeetingFeedbackDialog from '../../components/MeetingFeedbackDialog';
import { toast } from 'react-hot-toast';

dayjs.extend(utc);

const Bookings = () => {
  const { t, i18n } = useTranslation();
  const { token } = useAuth();
  const theme = useTheme();
  const isRtl = i18n.language === 'ar';

  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [cancellingBooking, setCancellingBooking] = useState(false);
  const [cancellationReason, setCancellationReason] = useState('');

  // Function to calculate time remaining until lesson
  const calculateTimeRemaining = (lessonDateTime) => {
    const now = new Date();
    const lessonTime = new Date(lessonDateTime);
    const timeDifference = lessonTime.getTime() - now.getTime();

    if (timeDifference <= 0) {
      return { hours: 0, minutes: 0, isOverdue: true };
    }

    const hours = Math.floor(timeDifference / (1000 * 60 * 60));
    const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));

    return { hours, minutes, isOverdue: false };
  };

  // Function to check if booking can be rescheduled by student
  const canStudentReschedule = (booking) => {
    // Check if booking exists
    if (!booking) return false;

    // Check if booking is scheduled
    if (booking.status !== 'scheduled') return false;

    // Check if teacher is not deleted
    if (booking.teacher_deleted_at) return false;

    // Check if student has already rescheduled this booking
    const rescheduleCount = parseInt(booking.reschedule_count) || 0;
    if (rescheduleCount >= 1) return false;

    // Check if reschedule is at least 12 hours before booking time
    const timeRemaining = calculateTimeRemaining(booking.datetime);

    // Debug log
    console.log('🔍 Reschedule check:', {
      bookingId: booking.id,
      bookingDateTime: booking.datetime,
      currentTime: new Date().toISOString(),
      timeRemaining: timeRemaining,
      canReschedule: !timeRemaining.isOverdue && timeRemaining.hours >= 12
    });

    if (timeRemaining.isOverdue || timeRemaining.hours < 12) return false;

    return true;
  };
  const [studentProfile, setStudentProfile] = useState(null);
  const [currentWeekStart, setCurrentWeekStart] = useState(() => {
    const today = new Date();
    return startOfWeek(today, { weekStartsOn: 1 }); // Start from current week
  });
  const [openMeeting, setOpenMeeting] = useState(false);
  const [currentMeeting, setCurrentMeeting] = useState(null);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);
  const [feedbackMeeting, setFeedbackMeeting] = useState(null);

  // Reschedule states
  const [rescheduleDialogOpen, setRescheduleDialogOpen] = useState(false);
  const [availableDays, setAvailableDays] = useState([]);
  const [availableTimesForDay, setAvailableTimesForDay] = useState([]);
  const [selectedDay, setSelectedDay] = useState(null);
  const [loadingDays, setLoadingDays] = useState(false);
  const [loadingTimes, setLoadingTimes] = useState(false);
  const [rescheduling, setRescheduling] = useState(false);
  const [rescheduleReason, setRescheduleReason] = useState('');

  // Days of the week
  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  // Week navigation functions
  const goToPreviousWeek = () => {
    const previousWeek = subWeeks(currentWeekStart, 1);
    setCurrentWeekStart(previousWeek);
  };

  const goToNextWeek = () => {
    const nextWeek = addWeeks(currentWeekStart, 1);
    const today = new Date();
    const oneYearAhead = addWeeks(today, 52); // One year ahead from today
    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });

    // Don't allow going beyond one year ahead
    if (nextWeek <= maxWeek) {
      setCurrentWeekStart(nextWeek);
    }
  };

  // Check if navigation buttons should be disabled
  const isPreviousWeekDisabled = () => false;

  const isNextWeekDisabled = () => {
    const nextWeek = addWeeks(currentWeekStart, 1);
    const today = new Date();
    const oneYearAhead = addWeeks(today, 52); // One year ahead from today
    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });
    return nextWeek > maxWeek;
  };

  // Fetch student profile
  useEffect(() => {
    const fetchStudentProfile = async () => {
      if (!token) return;

      try {
        const { data } = await axios.get('/api/students/profile', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (data.success && data.profile) {
          setStudentProfile(data.profile);
        }
      } catch (error) {
        console.error('Error fetching student profile:', error);
      }
    };

    fetchStudentProfile();
  }, [token]);

  // Helper function to check for pending feedback
  const checkPendingFeedback = async () => {
    try {
      console.log('Checking for pending feedback in Bookings page...');
      const { data } = await axios.get('/meeting-issues/pending');
      if (data.success && data.data) {
        const issue = data.data;
        const shouldOpen = dayjs.utc().isAfter(dayjs.utc(issue.datetime).add(issue.duration || 50, 'minute'));
        console.log('Found pending feedback:', issue, 'Should open:', shouldOpen);
        if (shouldOpen) {
          setFeedbackMeeting(issue);
          setFeedbackDialogOpen(true);
          return true; // Found and opened feedback
        }
      } else {
        console.log('No pending feedback found in Bookings page');
      }
      return false; // No feedback found or not ready to open
    } catch (err) {
      console.error('Error checking pending feedback', err);
      return false;
    }
  };

  // Check for pending feedback on mount and periodically
  useEffect(() => {
    // Check immediately on mount
    checkPendingFeedback();

    // Set up periodic checking every 30 seconds while on bookings page
    const interval = setInterval(checkPendingFeedback, 30000);

    // Also check when window gains focus (user returns to tab)
    const handleFocus = () => {
      console.log('Window focused, checking for pending feedback...');
      checkPendingFeedback();
    };

    window.addEventListener('focus', handleFocus);

    // Cleanup interval and event listener on unmount
    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  // Attach booking_id to feedback meeting if missing
  useEffect(() => {
    if (!feedbackMeeting || feedbackMeeting.booking_id) return;
    if (!bookings.length) return;

    const targetTime = moment(feedbackMeeting.datetime);
    // find booking with same teacher and date (same day) within 2 hours window
    const candidates = bookings.filter(b => {
      if (b.teacher_name !== feedbackMeeting.teacher_name) return false;
      const bTime = moment(b.datetime);
      const timeDiff = Math.abs(bTime.diff(targetTime, 'minutes'));
      return timeDiff <= 120;
    });

    // Sort by closest time match
    candidates.sort((a, b) => {
      const aDiff = Math.abs(moment(a.datetime).diff(targetTime, 'minutes'));
      const bDiff = Math.abs(moment(b.datetime).diff(targetTime, 'minutes'));
      return aDiff - bDiff;
    });

    if (candidates.length > 0) {
      const bestMatch = candidates[0];
      console.log(`Linking feedback to booking: Meeting ${feedbackMeeting.meeting_id} -> Booking ${bestMatch.id}`);
      setFeedbackMeeting(prev => ({ ...prev, booking_id: bestMatch.id }));
    } else {
      console.warn(`No matching booking found for meeting ${feedbackMeeting.meeting_id} with teacher ${feedbackMeeting.teacher_name}`);
    }
  }, [bookings, feedbackMeeting]);

  // Cleanup feedback state when leaving the page
  useEffect(() => {
    return () => {
      // Clear any pending feedback when leaving bookings page
      // This allows global feedback system to take over on other pages
      console.log('Cleaning up bookings page feedback state');
      setFeedbackDialogOpen(false);
      setFeedbackMeeting(null);
    };
  }, []);

  // Fetch bookings with retry logic
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setLoading(true);
        const { data } = await axios.get('/bookings/student', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (data.success) {
          console.log('Bookings data:', data.data);

          // Make sure all bookings have the correct data types
          const processedBookings = data.data.map(booking => ({
            ...booking,
            price_per_lesson: parseFloat(booking.price_per_lesson || 0),
            price_paid: booking.price_paid !== undefined ? parseFloat(booking.price_paid) : null,
            duration: booking.duration ? String(booking.duration) : '50'
          }));

          console.log('Processed bookings:', processedBookings);
          setBookings(processedBookings);
        } else {
          setError(data.message || t('bookings.fetchError'));
        }
      } catch (error) {
        console.error('Error fetching bookings:', error);
        setError(t('bookings.fetchError'));
      } finally {
        setLoading(false);
      }
    };

    const fetchWithRetry = async (maxRetries = 3) => {
      let retries = 0;
      while (retries < maxRetries) {
        try {
          await fetchBookings();
          break;
        } catch (error) {
          retries++;
          if (retries === maxRetries) {
            throw error;
          }
          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait before retry
        }
      }
    };

    if (token) {
      fetchWithRetry();
    }

    // Update current time every second
    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timeInterval);
  }, [token, t]);

  // Handle view details
  const handleViewDetails = (booking) => {
    setSelectedBooking(booking);
    setDetailsDialogOpen(true);
  };

  // Handle cancel booking
  const handleCancelBookingClick = (booking) => {
    setSelectedBooking(booking);
    setCancelDialogOpen(true);
  };

  // Handle join meeting
  const handleJoinMeeting = async (booking) => {
    try {
      // Check if room_name exists from the booking data
      if (!booking.room_name) {
        console.error('No room_name found for booking:', booking);
        toast.error(t('meetings.noRoomError') || 'Meeting room not found');
        return;
      }

      // Check if meeting_id exists
      if (!booking.meeting_id) {
        console.error('No meeting_id found for booking:', booking);
        toast.error(t('meetings.noMeetingError') || 'Meeting ID not found');
        return;
      }

      console.log('Joining meeting with data:', {
        room_name: booking.room_name,
        meeting_id: booking.meeting_id,
        datetime: booking.datetime,
        duration: booking.duration
      });

      // التحقق من صلاحية الغرفة
      const response = await axios.get(`/meetings/${booking.room_name}/validate`);
      setCurrentMeeting({ ...booking, room_name: booking.room_name });
      setOpenMeeting(true);
    } catch (error) {
      console.error('Error joining meeting:', error);
      toast.error(t('meetings.joinError'));
    }
  };

  const handleCloseMeeting = () => {
    // After meeting dialog closes, prompt for feedback
    if (currentMeeting) {
      const meetingEnd = new Date(currentMeeting.datetime);
      meetingEnd.setMinutes(meetingEnd.getMinutes() + (currentMeeting.duration || 50));
      const now = new Date();
      // Only prompt if meeting time has actually ended
      if (now >= meetingEnd) {
        setFeedbackMeeting(currentMeeting);

        // Find the corresponding booking for this meeting
        const targetTime = moment(currentMeeting.datetime);
        const correspondingBooking = bookings.find(b => {
          if (b.teacher_name !== currentMeeting.teacher_name) return false;
          const bTime = moment(b.datetime);
          return Math.abs(bTime.diff(targetTime, 'minutes')) <= 120;
        });

        // Send pending status immediately with correct booking_id
        axios.post('/meeting-issues', {
          booking_id: correspondingBooking ? correspondingBooking.id : null,
          meeting_id: currentMeeting.meeting_id || currentMeeting.id,
          issue_type: 'pending',
          description: ''
        }).catch(err => console.error('Failed to create pending issue', err));
        setFeedbackDialogOpen(true);
      }
    }
    setOpenMeeting(false);
    setCurrentMeeting(null);
  };

  // Get current time in student's timezone (same method as meetings page)
  const getCurrentTimeInStudentTimezone = () => {
    if (!studentProfile || !studentProfile.timezone) {
      return new Date();
    }
    return getCurrentTimeInTimezone(studentProfile.timezone);
  };

  // Get meeting date in student timezone (same method as meetings page)
  const getMeetingDateInStudentTimezone = (datetime) => {
    if (!studentProfile || !studentProfile.timezone) {
      return new Date(datetime);
    }
    return convertFromDatabaseTime(datetime, studentProfile.timezone);
  };

  // Get meeting status from database directly
  const getMeetingStatus = (booking) => {
    return booking.status || 'scheduled';
  };

  // Check if user can join meeting (same method as meetings page)
  const canJoinMeeting = (booking) => {
    if (!booking || !studentProfile) return false;

    // Cannot join if teacher is deleted
    if (booking.teacher_deleted_at) return false;

    const currentStatus = getMeetingStatus(booking);
    if (currentStatus === 'cancelled' || currentStatus === 'cancelled_teacher_deleted' || currentStatus === 'completed') {
      return false;
    }

    const meetingStartTime = getMeetingDateInStudentTimezone(booking.datetime);
    const meetingEndTime = new Date(meetingStartTime);
    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + (booking.duration || 50));
    const now = getCurrentTimeInStudentTimezone();

    return now >= meetingStartTime && now < meetingEndTime;
  };

  // Get meeting status text (same method as meetings page)
  const getMeetingStatusText = (booking) => {
    if (!booking || !studentProfile) return t('meetings.notStarted');

    // Special message for deleted teachers
    if (booking.teacher_deleted_at) {
      return t('meetings.teacherDeleted', 'Teacher Unavailable');
    }

    const currentStatus = getMeetingStatus(booking);
    const canJoin = canJoinMeeting(booking);

    if (canJoin) {
      return t('meetings.join');
    }

    switch (currentStatus) {
      case 'cancelled':
        return t('meetings.status.cancelled');
      case 'completed':
        return t('meetings.status.completed');
      case 'ongoing':
        return t('meetings.join');
      case 'scheduled':
      default:
        return t('meetings.notStarted');
    }
  };

  // Handle booking cancellation
  const handleCancelBooking = async () => {
    if (!selectedBooking) return;

    try {
      setCancellingBooking(true);
      const { data } = await axios.put(`/bookings/${selectedBooking.id}/cancel`, {
        cancellation_reason: cancellationReason
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        // Update the booking status in the local state
        setBookings(prevBookings =>
          prevBookings.map(booking =>
            booking.id === selectedBooking.id
              ? { ...booking, status: 'cancelled' }
              : booking
          )
        );

        // Show appropriate success message based on the response
        if (data.fullRefundToStudent) {
          toast.success(t('bookings.cancelSuccess'));
        } else if (data.commissionPaidToTeacher) {
          toast(t('bookings.commissionPaidToTeacher'), {
            icon: '⚠️',
            style: {
              background: '#ff9800',
              color: 'white',
            },
          });
        } else {
          toast.success(data.message || t('bookings.cancelSuccess'));
        }
      } else {
        toast.error(data.message || t('bookings.cancelError'));
      }
    } catch (error) {
      console.error('Error cancelling booking:', error);
      toast.error(error.response?.data?.message || t('bookings.cancelError'));
    } finally {
      setCancellingBooking(false);
      setCancelDialogOpen(false);
      setDetailsDialogOpen(false);
      setSelectedBooking(null);
      setCancellationReason('');
    }
  };

  // Handle reschedule booking
  const handleRescheduleBookingClick = async (booking) => {
    setSelectedBooking(booking);
    setSelectedDay(null);
    setAvailableTimesForDay([]);
    setRescheduleReason('');
    setLoadingDays(true);
    setRescheduleDialogOpen(true);

    try {
      // Get available slots for this booking using same API as booking page
      const queryParams = new URLSearchParams();
      if (studentProfile?.timezone) {
        queryParams.append('student_timezone', studentProfile.timezone);
      }

      const { data } = await axios.get(`/bookings/${booking.id}/available-slots?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        console.log('🔍 Student reschedule - API Response:', data);

        // لا نقوم بتصفية الأوقات هنا - سنتركها لنافذة RescheduleDialog
        // لتجنب التصفية المضاعفة
        const validSlots = data.data;

        console.log('🔍 All slots from API (no filtering here):', {
          total: data.data.length,
          note: 'Filtering will be done in RescheduleDialog'
        });

        // Group slots by date
        const slotsByDate = {};
        validSlots.forEach(slot => {
          const date = slot.date;
          if (!slotsByDate[date]) {
            slotsByDate[date] = [];
          }
          slotsByDate[date].push({
            ...slot,
            // Ensure we have the display time
            displayTime: slot.time || new Date(slot.datetime).toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true
            })
          });
        });

        // Convert to the format expected by the UI
        const availableDays = Object.keys(slotsByDate)
          .filter(date => slotsByDate[date].length > 0) // Only include dates with valid slots
          .map(date => {
            const slots = slotsByDate[date];
            const dateObj = new Date(date);

            return {
              date: date,
              dayName: dateObj.toLocaleDateString('en-US', { weekday: 'long' }),
              formattedDate: dateObj.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric'
              }),
              slots: slots,
              availableCount: slots.length,
              availableSlots: slots.length
            };
          });

        console.log('🔍 Available days for student:', availableDays);
        setAvailableDays(availableDays);
      } else {
        console.error('❌ API returned error:', data);
        toast.error(data.message || t('bookings.fetchDaysError'));
      }
    } catch (error) {
      console.error('Error fetching available slots:', error);
      toast.error(error.response?.data?.message || t('bookings.fetchDaysError'));
    } finally {
      setLoadingDays(false);
    }
  };



  // Handle reschedule confirmation
  const handleRescheduleConfirm = async (timeSlot, reason = '') => {
    if (!timeSlot) {
      toast.error('Please select a time slot');
      return;
    }

    try {
      setRescheduling(true);

      // Use the slot's datetime directly
      const newDateTime = timeSlot.datetime;

      const { data } = await axios.put(`/bookings/${selectedBooking.id}/reschedule`, {
        newDateTime: newDateTime,
        newDuration: selectedBooking.duration,
        reschedule_reason: reason || rescheduleReason
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (data.success) {
        // Update the booking in the list
        setBookings(prevBookings =>
          prevBookings.map(booking =>
            booking.id === selectedBooking.id
              ? { ...booking, datetime: newDateTime }
              : booking
          )
        );
        toast.success(t('bookings.rescheduleSuccess'));
      } else {
        toast.error(data.message || t('bookings.rescheduleError'));
      }
    } catch (error) {
      console.error('Error rescheduling booking:', error);
      toast.error(error.response?.data?.message || t('bookings.rescheduleError'));
    } finally {
      setRescheduling(false);
      setRescheduleDialogOpen(false);
      setDetailsDialogOpen(false);
      setSelectedDay(null);
      setAvailableTimesForDay([]);
      setRescheduleReason('');
    }
  };

  // Get status chip color
  const getStatusColor = (status) => {
    switch (status) {
      case 'scheduled':
        return 'primary';
      case 'completed':
        return 'success';
      case 'cancelled':
        return 'error';
      case 'issue_reported':
        return 'warning';
      case 'ongoing':
        return 'info';
      default:
        return 'default';
    }
  };

  // Get translated status text
  const getStatusText = (status) => {
    return t(`bookings.statusValues.${status}`, { 
      defaultValue: status.charAt(0).toUpperCase() + status.slice(1) 
    });
  };

  // Format booking date in student's timezone
  const formatBookingDate = (datetime) => {
    if (!studentProfile || !studentProfile.timezone) {
      return format(new Date(datetime), 'PPP', { locale: isRtl ? ar : enUS });
    }

    const formattedDate = formatDateInStudentTimezone(datetime, studentProfile.timezone, 'YYYY-MM-DD');
    return moment(formattedDate, 'YYYY-MM-DD').format('MMMM D, YYYY');
  };

  // Format booking time in student's timezone
  const formatBookingTime = (datetime) => {
    if (!studentProfile || !studentProfile.timezone) {
      return format(new Date(datetime), 'p', { locale: isRtl ? ar : enUS });
    }

    const formattedDateTime = formatDateInStudentTimezone(datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
    return moment(formattedDateTime, 'YYYY-MM-DD HH:mm:ss').format('h:mm A');
  };

  // Format booking time range (start - end) in student's timezone
  const formatBookingTimeRange = (datetime, duration) => {
    if (!studentProfile || !studentProfile.timezone) {
      const startDate = new Date(datetime);
      const endDate = new Date(startDate.getTime() + duration * 60000);

      const startTimeStr = startDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
      const endTimeStr = endDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });

      return `${startTimeStr} - ${endTimeStr}`;
    }

    // Use student timezone for accurate time calculation
    const formattedDateTime = formatDateInStudentTimezone(datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');
    const [datePart, timePart] = formattedDateTime.split(' ');
    const [hours, minutes] = timePart.split(':');

    // Calculate start and end times
    const startMinutes = parseInt(hours) * 60 + parseInt(minutes);
    const endMinutes = startMinutes + duration;

    const startHour = Math.floor(startMinutes / 60);
    const startMin = startMinutes % 60;
    const endHour = Math.floor(endMinutes / 60);
    const endMin = endMinutes % 60;

    const startTimeStr = `${String(startHour).padStart(2, '0')}:${String(startMin).padStart(2, '0')}`;
    const endTimeStr = `${String(endHour).padStart(2, '0')}:${String(endMin).padStart(2, '0')}`;

    return `${startTimeStr} - ${endTimeStr}`;
  };

  // Submit feedback after meeting
  const handleFeedbackSubmit = async (meetingId, values) => {
    try {
      console.log('Submitting feedback:', { meetingId, values });

      const response = await axios.post('/meeting-issues', {
        meeting_id: meetingId,
        booking_id: values.booking_id,
        issue_type: values.issue_type,
        description: values.description,
      });

      console.log('Feedback submitted successfully:', response.data);

      // Update booking status locally for immediate UI feedback
      if (values.booking_id) {
        const newStatus = values.issue_type === 'no_issue' ? 'completed' : 'issue_reported';
        setBookings(prev => prev.map(b => (
          b.id === values.booking_id ? { ...b, status: newStatus } : b
        )));
        console.log(`Updated booking ${values.booking_id} status to ${newStatus}`);
      }

      // Close current feedback dialog
      setFeedbackDialogOpen(false);
      setFeedbackMeeting(null);

      // Check for next pending feedback after a short delay
      setTimeout(() => {
        console.log('Checking for next pending feedback...');
        checkPendingFeedback();
      }, 1000); // Wait 1 second before checking for next feedback

    } catch (err) {
      console.error('Failed to submit feedback', err);
      // Show error message to user
      alert('Failed to submit feedback. Please try again.');
      return; // Don't close dialog on error
    }
  };

  // Render booking cards
  const renderBookings = () => {
    if (bookings.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="body1" color="text.secondary">
            {t('bookings.noBookings')}
          </Typography>
        </Box>
      );
    }

    return (
      <Grid container spacing={3}>
        {bookings.map((booking) => (
          <Grid item xs={12} sm={6} md={4} key={booking.id}>
            <Card
              elevation={3}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                opacity: booking.teacher_deleted_at ? 0.8 : 1,
                border: booking.teacher_deleted_at ? `2px solid ${theme.palette.error.light}` : 'none',
                transition: 'all 0.2s ease'
              }}
            >
              <Box sx={{
                bgcolor: 'primary.main',
                color: 'white',
                p: 2,
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}>
                <CalendarIcon />
                <Typography variant="h6">
                  {formatBookingDate(booking.datetime)}
                </Typography>
              </Box>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    src={booking.teacher_picture}
                    alt={booking.teacher_name}
                    sx={{
                      mr: 2,
                      opacity: booking.teacher_deleted_at ? 0.6 : 1,
                      filter: booking.teacher_deleted_at ? 'grayscale(100%)' : 'none'
                    }}
                  />
                  <Box sx={{ flex: 1 }}>
                    <Typography
                      variant="subtitle1"
                      sx={{
                        color: booking.teacher_deleted_at ? 'text.secondary' : 'text.primary',
                        textDecoration: booking.teacher_deleted_at ? 'line-through' : 'none'
                      }}
                    >
                      {booking.teacher_name}
                    </Typography>
                    {booking.teacher_deleted_at && (
                      <Chip
                        label={t('bookings.teacherDeleted', 'Teacher Account Deleted')}
                        size="small"
                        color="error"
                        variant="outlined"
                        sx={{ mt: 0.5, fontSize: '0.7rem' }}
                      />
                    )}
                  </Box>
                </Box>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    {formatBookingTime(booking.datetime)}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    {t('bookings.duration')}: {booking.duration || 50} {t('bookings.minutes')}
                    {booking.duration === '25' || booking.duration === 25 ?
                      ` (${t('booking.halfLesson') || 'نصف درس'})` :
                      ` (${t('booking.fullLesson') || 'درس كامل'})`}
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <EventIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  <Chip
                    label={getStatusText(booking.status)}
                    color={getStatusColor(booking.status)}
                    size="small"
                  />
                </Box>

                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" fontWeight="bold">
                    {t('bookings.price')}: {(() => {
                      if (booking.price_paid !== null && !isNaN(booking.price_paid)) {
                        return booking.price_paid.toFixed(2);
                      }
                      const basePrice = parseFloat(booking.price_per_lesson || 0);
                      const isDurationHalf = booking.duration === '25' || booking.duration === 25;
                      const finalPrice = isDurationHalf ? basePrice / 2 : basePrice;
                      return finalPrice.toFixed(2);
                    })()} $
                  </Typography>

                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      setSelectedBooking(booking);
                      setDetailsDialogOpen(true);
                    }}
                  >
                    {t('bookings.details')}
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Details dialog
  const renderDetailsDialog = () => (
    <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth="sm" fullWidth>
      <DialogTitle>
        {t('bookings.bookingDetails')}
        <IconButton
          aria-label="close"
          onClick={() => setDetailsDialogOpen(false)}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        {selectedBooking && (
          <Box sx={{ py: 2 }}>
            {/* Teacher Info */}
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
              <Avatar
                src={selectedBooking.teacher_picture ? (
                  selectedBooking.teacher_picture.startsWith('http')
                    ? selectedBooking.teacher_picture
                    : `https://allemnionline.com${selectedBooking.teacher_picture}`
                ) : ''}
                alt={selectedBooking.teacher_name}
                sx={{
                  width: 120,
                  height: 120,
                  border: `3px solid ${selectedBooking.teacher_deleted_at ? theme.palette.error.main : theme.palette.primary.main}`,
                  mb: 2,
                  opacity: selectedBooking.teacher_deleted_at ? 0.6 : 1,
                  filter: selectedBooking.teacher_deleted_at ? 'grayscale(100%)' : 'none'
                }}
              />
              <Box sx={{ textAlign: 'center' }}>
                <Typography
                  variant="h6"
                  gutterBottom
                  sx={{
                    color: selectedBooking.teacher_deleted_at ? 'text.secondary' : 'text.primary',
                    textDecoration: selectedBooking.teacher_deleted_at ? 'line-through' : 'none'
                  }}
                >
                  {selectedBooking.teacher_name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {t('bookings.teacher')}
                </Typography>
                {selectedBooking.teacher_deleted_at && (
                  <Chip
                    label={t('bookings.teacherDeleted', 'Teacher Account Deleted')}
                    size="small"
                    color="error"
                    variant="filled"
                    sx={{ mt: 1 }}
                  />
                )}
              </Box>
            </Box>

            <Divider sx={{ my: 2 }} />

            {/* Booking Details */}
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {t('bookings.date')}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {formatBookingDate(selectedBooking.datetime)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {t('bookings.time')}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {formatBookingTime(selectedBooking.datetime)}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  {formatBookingTimeRange(selectedBooking.datetime, parseInt(selectedBooking.duration) || 50)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {t('bookings.duration')}
                </Typography>
                <Typography variant="body1" gutterBottom>
                  {selectedBooking.duration || 50} {t('bookings.minutes')}
                  {selectedBooking.duration === '25' || selectedBooking.duration === 25 ?
                    ` (${t('booking.halfLesson')})` :
                    ` (${t('booking.fullLesson')})`}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {t('bookings.status.title')}
                </Typography>
                <Chip
                  label={getStatusText(selectedBooking.status)}
                  color={getStatusColor(selectedBooking.status)}
                  size="small"
                />
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {t('bookings.price')}
                </Typography>
                <Typography variant="h6" color="primary">
                  {(() => {
                    if (selectedBooking.price_paid !== null && !isNaN(selectedBooking.price_paid)) {
                      return selectedBooking.price_paid.toFixed(2);
                    }
                    const basePrice = parseFloat(selectedBooking.price_per_lesson || 0);
                    const isDurationHalf = selectedBooking.duration === '25' || selectedBooking.duration === 25;
                    const finalPrice = isDurationHalf ? basePrice / 2 : basePrice;
                    return finalPrice.toFixed(2);
                  })()} {t('common.currency')}
                </Typography>
              </Grid>
            </Grid>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setDetailsDialogOpen(false)}>
          {t('common.close')}
        </Button>

        {/* Join Meeting Button */}
        {selectedBooking && (
          <Button
            onClick={() => canJoinMeeting(selectedBooking) && handleJoinMeeting(selectedBooking)}
            color={canJoinMeeting(selectedBooking) ? "success" : "inherit"}
            variant={canJoinMeeting(selectedBooking) ? "contained" : "outlined"}
            startIcon={<VideoCallIcon />}
            disabled={!canJoinMeeting(selectedBooking)}
            sx={{
              mr: 1,
              ...(canJoinMeeting(selectedBooking) ? {} : {
                color: theme.palette.grey[500],
                borderColor: theme.palette.grey[300],
                backgroundColor: theme.palette.grey[100],
                '&:hover': {
                  backgroundColor: theme.palette.grey[200],
                  borderColor: theme.palette.grey[400]
                }
              })
            }}
          >
            {getMeetingStatusText(selectedBooking)}
          </Button>
        )}

        {/* Reschedule button - only if can reschedule */}
        {selectedBooking && canStudentReschedule(selectedBooking) && (
          <Button
            onClick={() => {
              setDetailsDialogOpen(false);
              handleRescheduleBookingClick(selectedBooking);
            }}
            color="primary"
            variant="outlined"
            startIcon={<RescheduleIcon />}
            sx={{ mr: 1 }}
          >
            {t('bookings.reschedule')}
          </Button>
        )}

        {/* Cancel button - always show for scheduled bookings with non-deleted teachers */}
        {selectedBooking?.status === 'scheduled' && !selectedBooking?.teacher_deleted_at && (
          <Button
            onClick={() => {
              setDetailsDialogOpen(false);
              setCancelDialogOpen(true);
            }}
            color="error"
            variant="contained"
            startIcon={<CancelIcon />}
          >
            {t('bookings.cancel')}
          </Button>
        )}

        {/* Show reschedule restriction message if applicable */}
        {selectedBooking && selectedBooking.status === 'scheduled' && !selectedBooking.teacher_deleted_at && !canStudentReschedule(selectedBooking) && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
            <Typography variant="body2" color="warning.dark">
              ⚠️ {(() => {
                const rescheduleCount = parseInt(selectedBooking.reschedule_count) || 0;
                const timeRemaining = calculateTimeRemaining(selectedBooking.datetime);

                if (rescheduleCount >= 1) {
                  return t('bookings.rescheduleAlreadyUsed', 'You have already rescheduled this booking once. Students can only reschedule each booking once.');
                } else if (timeRemaining.hours < 12 && !timeRemaining.isOverdue) {
                  return t('bookings.rescheduleTimeLimit', 'You can only reschedule bookings at least 12 hours before the lesson time.');
                } else if (timeRemaining.isOverdue) {
                  return t('bookings.rescheduleTimeExpired', 'This lesson time has already passed.');
                }
                return '';
              })()}
            </Typography>
          </Box>
        )}

        {/* Message for deleted teacher bookings */}
        {selectedBooking?.teacher_deleted_at && selectedBooking?.status === 'scheduled' && (
          <Alert severity="info" sx={{ mt: 2, mx: 2 }}>
            {t('bookings.teacherDeletedMessage', 'This booking cannot be modified because the teacher account has been deleted. The amount will be automatically refunded to your wallet.')}
          </Alert>
        )}
      </DialogActions>
    </Dialog>
  );

  // Cancel confirmation dialog
  const renderCancelDialog = () => (
    <Dialog open={cancelDialogOpen} onClose={() => setCancelDialogOpen(false)}>
      <DialogTitle>
        {t('bookings.confirmCancel')}
        <IconButton
          aria-label="close"
          onClick={() => setCancelDialogOpen(false)}
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Typography variant="body1" sx={{ mb: 2 }}>
          {t('bookings.cancelWarning')}
        </Typography>

        {/* Late cancellation policy warning */}
        <Alert severity="warning" sx={{ mb: 2 }}>
          <Typography variant="body2">
            {t('bookings.lateCancellationPolicy')}
          </Typography>
        </Alert>
        {selectedBooking && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body2" gutterBottom>
              <strong>{t('bookings.teacher')}:</strong> {selectedBooking.teacher_name}
            </Typography>
            <Typography variant="body2" gutterBottom>
              <strong>{t('bookings.date')}:</strong> {formatBookingDate(selectedBooking.datetime)}
            </Typography>
            <Typography variant="body2" gutterBottom>
              <strong>{t('bookings.time')}:</strong> {formatBookingTime(selectedBooking.datetime)}
            </Typography>
            <Typography variant="body2" gutterBottom color="text.secondary">
              <strong>{t('bookings.timeRange')}:</strong> {formatBookingTimeRange(selectedBooking.datetime, parseInt(selectedBooking.duration) || 50)}
            </Typography>

            {/* Time remaining until lesson */}
            {(() => {
              const timeRemaining = calculateTimeRemaining(selectedBooking.datetime);
              const isLateCancel = timeRemaining.hours < 12 && !timeRemaining.isOverdue;

              return (
                <Box sx={{ mt: 2, p: 2, borderRadius: 1, bgcolor: isLateCancel ? 'warning.light' : 'info.light' }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold', color: isLateCancel ? 'warning.dark' : 'info.dark' }}>
                    <strong>{t('bookings.timeRemaining')}:</strong> {' '}
                    {timeRemaining.isOverdue
                      ? t('bookings.lessonOverdue')
                      : `${timeRemaining.hours} ${t('bookings.hours')} ${timeRemaining.minutes} ${t('bookings.minutes')}`
                    }
                  </Typography>
                  {isLateCancel && (
                    <Typography variant="caption" sx={{ display: 'block', mt: 1, color: 'warning.dark' }}>
                      ⚠️ {t('bookings.lateCancelWarning')}
                    </Typography>
                  )}
                </Box>
              );
            })()}
          </Box>
        )}
        <TextField
          fullWidth
          multiline
          rows={3}
          label={t('bookings.cancellationReason')}
          placeholder={t('bookings.cancellationReasonPlaceholder')}
          value={cancellationReason}
          onChange={(e) => setCancellationReason(e.target.value)}
          sx={{ mt: 2 }}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setCancelDialogOpen(false)}>
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleCancelBooking}
          color="error"
          variant="contained"
          disabled={cancellingBooking}
        >
          {cancellingBooking ? t('bookings.cancelling') : t('bookings.confirmCancelButton')}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
          <Paper elevation={3} sx={{ p: 3, mb: 4, bgcolor: 'primary.main', color: 'white' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
              <Box>
                <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold' }}>
                  {t('bookings.weeklyTitle')}
                </Typography>
                <Typography variant="body1" sx={{ opacity: 0.9 }}>
                  {t('bookings.weeklyDescription')}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box sx={{ textAlign: 'right' }}>
                  <Typography variant="body2" sx={{ opacity: 0.8, mb: 0.5 }}>
                    {t('booking.weekNavigation')}
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                    📅 {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    {studentProfile?.timezone ? (
                      moment(formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('h:mm A')
                    ) : (
                      format(currentTime, 'p', {
                        locale: isRtl ? ar : enUS
                      })
                    )}
                    {studentProfile?.timezone && ` (${studentProfile.timezone})`}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Tooltip title={t('booking.previousWeek')}>
                    <span>
                      <IconButton
                        onClick={goToPreviousWeek}
                        disabled={isPreviousWeekDisabled()}
                        sx={{
                          color: 'white',
                          bgcolor: 'rgba(255, 255, 255, 0.1)',
                          '&:hover': {
                            bgcolor: 'rgba(255, 255, 255, 0.2)',
                          },
                          '&:disabled': {
                            color: 'rgba(255, 255, 255, 0.3)',
                            bgcolor: 'rgba(255, 255, 255, 0.05)',
                          }
                        }}
                      >
                        <ChevronLeftIcon />
                      </IconButton>
                    </span>
                  </Tooltip>
                  <Tooltip title={t('booking.nextWeek')}>
                    <span>
                      <IconButton
                        onClick={goToNextWeek}
                        disabled={isNextWeekDisabled()}
                        sx={{
                          color: 'white',
                          bgcolor: 'rgba(255, 255, 255, 0.1)',
                          '&:hover': {
                            bgcolor: 'rgba(255, 255, 255, 0.2)',
                          },
                          '&:disabled': {
                            color: 'rgba(255, 255, 255, 0.3)',
                            bgcolor: 'rgba(255, 255, 255, 0.05)',
                          }
                        }}
                      >
                        <ChevronRightIcon />
                      </IconButton>
                    </span>
                  </Tooltip>
                </Box>
              </Box>
            </Box>
          </Paper>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        ) : (
          <WeeklyBookingsTable
            bookings={bookings}
            loading={loading}
            currentWeekStart={currentWeekStart}
            daysOfWeek={daysOfWeek}
            onViewDetails={handleViewDetails}
            onCancelBooking={handleCancelBookingClick}
            studentProfile={studentProfile}
            formatBookingTime={formatBookingTime}
            getStatusColor={getStatusColor}
          />
        )}

        {renderDetailsDialog()}
        {renderCancelDialog()}
        </ProfileCompletionAlert>
      </Container>

      {/* Meeting Dialog */}
      <Dialog
        fullScreen
        open={openMeeting}
        onClose={handleCloseMeeting}
      >
        {currentMeeting && (
          <VideoSDKMeeting
            roomId={currentMeeting.room_name}
            meetingId={currentMeeting.meeting_id}
            meetingData={{
              ...currentMeeting,
              meeting_date: currentMeeting.datetime,
              duration: parseInt(currentMeeting.duration) || 50
            }}
            onClose={handleCloseMeeting}
          />
        )}
      </Dialog>

      {/* New Reschedule Dialog */}
      <RescheduleDialog
        open={rescheduleDialogOpen}
        onClose={() => setRescheduleDialogOpen(false)}
        booking={selectedBooking}
        availableDays={availableDays}
        onReschedule={handleRescheduleConfirm}
        loading={rescheduling}
        userProfile={studentProfile}
        isTeacherView={false}
      />

      {/* Feedback Dialog */}
      <MeetingFeedbackDialog
        open={feedbackDialogOpen}
        meeting={feedbackMeeting}
        timezone={studentProfile?.timezone || null}
        onSubmit={handleFeedbackSubmit}
        onClose={() => {
          setFeedbackDialogOpen(false);
          setFeedbackMeeting(null);
        }}
      />
    </Layout>
  );
};

export default Bookings;
