import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import axios from '../../utils/axios';
import { useSocket } from '../../contexts/SocketContext';
import ApplicationForm from '../../components/teacher/ApplicationForm';
import ApplicationStatus from '../../components/teacher/ApplicationStatus';
import { useAuth } from '../../contexts/AuthContext';
import Spinner from '../../components/common/Spinner';
import Layout from '../../components/Layout';

const Application = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const { socket, isConnected } = useSocket();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [applicationStatus, setApplicationStatus] = useState(null);

  // Handle status update callback
  const handleStatusUpdate = useCallback((data) => {
    console.log('Handling status update in Application component:', data);

    if (data?.status) {
      setApplicationStatus(prevStatus => {
        // Only update if the status has actually changed
        if (!prevStatus || prevStatus.status !== data.status) {
          const newStatus = {
            status: data.status,
            message: `teacher.application.statusMessage.${data.status}`
          };

          console.log('Updating application status:', {
            previous: prevStatus,
            new: newStatus
          });

          return newStatus;
        }
        return prevStatus;
      });
      setLoading(false);
    }
  }, []);

  // Initialize socket and check application status
  useEffect(() => {
    let mounted = true;

    const checkApplicationStatus = async () => {
      try {
        const response = await axios.get('/api/teacher/application/status');
        if (mounted && response.data) {
          handleStatusUpdate(response.data);
        }
      } catch (error) {
        console.error('Error checking application status:', error);
        if (mounted) {
          if (error.response?.status === 404) {
            // No application exists yet
            setApplicationStatus({ status: 'new', message: t('teacher.application.noApplication') });
          } else {
            setError(error.response?.data?.message || t('common.errorOccurred'));
          }
          setLoading(false);
        }
      }
    };

    // Check initial status
    if (currentUser) {
      checkApplicationStatus();
    }

    // Set up socket listener only if socket exists and is connected
    if (socket && isConnected) {
      console.log('Setting up application_status socket listener');
      socket.on('application_status', handleStatusUpdate);

      return () => {
        console.log('Cleaning up application_status socket listener');
        socket.off('application_status', handleStatusUpdate);
      };
    }

    return () => {
      mounted = false;
    };
  }, [socket, isConnected, currentUser, handleStatusUpdate, t]);

  const handleSubmit = async (formData) => {
    setError('');
    setLoading(true);

    try {
      const response = await axios.post('/api/teacher/application', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response.data) {
        console.log('Application submitted successfully:', response.data);

        // Actualizar el estado de la aplicación a "pendiente"
        const statusUpdate = {
          status: 'pending',
          message: t('teacher.application.statusMessage.pending')
        };

        // Actualizar el estado local
        setApplicationStatus(statusUpdate);

        // Limpiar la bandera de actualización de aplicación
        localStorage.removeItem('isApplicationUpdate');

        // Actualizar el contexto de autenticación si es necesario
        if (currentUser && currentUser.role === 'new_teacher') {
          // Aquí podríamos actualizar el rol del usuario si fuera necesario
        }

        setLoading(false);

        // Force re-render to show the application status
        setTimeout(() => {
          setApplicationStatus({
            status: 'pending',
            message: t('teacher.application.statusMessage.pending')
          });
        }, 100);
      }
    } catch (err) {
      console.error('Application submission error:', err);
      setError(err.response?.data?.message || t('common.errorOccurred'));
      setLoading(false);
    }
  };

  // Verificar si es una actualización de aplicación desde localStorage
  const isApplicationUpdate = localStorage.getItem('isApplicationUpdate') === 'true';

  // Determinar si debemos mostrar el formulario
  const shouldShowForm =
    // Mostrar el formulario si el estado es explícitamente 'new'
    applicationStatus?.status === 'new' ||
    // O si es una actualización explícita desde el perfil
    isApplicationUpdate ||
    // O si es un profesor nuevo que aún no ha enviado una solicitud
    (currentUser?.role === 'new_teacher' && !applicationStatus);

  // Solo mostrar el formulario si el usuario es un profesor nuevo Y no ha enviado una solicitud
  // O si el estado de la aplicación es explícitamente 'new'
  if (currentUser?.role === 'new_teacher' && (!applicationStatus || applicationStatus.status === 'new')) {
    // Mantener el estado como 'new' solo si no hay un estado existente
    if (!applicationStatus) {
      setApplicationStatus({ status: 'new', message: t('teacher.application.noApplication') });
    }
  }

  console.log('Application status:', applicationStatus?.status);
  console.log('Is application update:', isApplicationUpdate);
  console.log('Current user role:', currentUser?.role);
  console.log('Should show form:', shouldShowForm);

  const content = loading ? (
    <div className="text-center p-4"><Spinner /></div>
  ) : error ? (
    <div className="text-center p-4 text-red-600">
      <p>{error}</p>
      <button
        onClick={() => window.location.reload()}
        className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        {t('common.retry')}
      </button>
    </div>
  ) : shouldShowForm ? (
    <div className="max-w-4xl mx-auto p-4">
      <h2 className="text-2xl font-bold mb-6">{t('teacher.application.title')}</h2>
      <ApplicationForm onSubmit={handleSubmit} />
    </div>
  ) : applicationStatus ? (
    <div className="max-w-4xl mx-auto p-4">
      <ApplicationStatus
        status={applicationStatus.status}
        message={applicationStatus.message}
      />
    </div>
  ) : null;

  return (
    <Layout title={t('teacher.application.title')}>
      {content}
    </Layout>
  );
};

export default Application;
