/**
 * Email templates for booking reschedule notifications
 */

/**
 * Template for teacher when they reschedule a booking
 * @param {Object} data - Template data
 * @param {string} data.teacherName - Teacher's name
 * @param {string} data.studentName - Student's name
 * @param {string} data.oldBookingDate - Formatted old booking date
 * @param {string} data.oldBookingTime - Formatted old booking time
 * @param {string} data.newBookingDate - Formatted new booking date
 * @param {string} data.newBookingTime - Formatted new booking time
 * @param {string} data.duration - Lesson duration
 * @param {string} data.rescheduleReason - Reason for rescheduling (optional)
 * @returns {string} HTML email template
 */
const getTeacherRescheduleTemplate = (data) => {
  return `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
      <div style="background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #007bff; margin: 0; font-size: 24px;">تم تغيير موعد الدرس - Lesson Rescheduled</h1>
          <div style="width: 50px; height: 3px; background-color: #007bff; margin: 10px auto;"></div>
        </div>

        <!-- Content -->
        <div style="margin-bottom: 25px;">
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 15px;">
            <strong>عزيزي الأستاذ ${data.teacherName} / Dear Teacher ${data.teacherName},</strong>
          </p>
          
          <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
            تم تغيير موعد الدرس التالي بنجاح:<br>
            <em>The following lesson has been successfully rescheduled:</em>
          </p>
        </div>

        <!-- Old Booking Details -->
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #856404; margin: 0 0 15px 0; font-size: 16px;">الموعد السابق - Previous Schedule</h3>
          
          <div style="margin-bottom: 8px;">
            <span style="color: #856404; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.oldBookingDate}</span>
          </div>
          
          <div style="margin-bottom: 8px;">
            <span style="color: #856404; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.oldBookingTime}</span>
          </div>
        </div>

        <!-- New Booking Details -->
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="color: #155724; margin: 0 0 15px 0; font-size: 18px;">الموعد الجديد - New Schedule</h3>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">الطالب - Student:</span>
            <span style="color: #333; margin-left: 10px;">${data.studentName}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.newBookingDate}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.newBookingTime}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">المدة - Duration:</span>
            <span style="color: #333; margin-left: 10px;">${data.duration} دقيقة - minutes</span>
          </div>

          ${data.rescheduleReason ? `
          <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #c3e6cb;">
            <span style="color: #155724; font-weight: bold;">سبب تغيير الموعد - Reschedule Reason:</span>
            <div style="color: #333; margin-top: 8px; padding: 10px; background-color: white; border-radius: 5px; border-left: 4px solid #007bff;">
              ${data.rescheduleReason}
            </div>
          </div>
          ` : ''}
        </div>

        <!-- Action Button -->
        <div style="text-align: center; margin: 25px 0;">
          <a href="https://allemnionline.com/teacher/bookings" 
             style="background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
            عرض جدولي - View My Schedule
          </a>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            إذا كان لديك أي استفسارات، يرجى التواصل معنا<br>
            <em>If you have any questions, please contact us</em>
          </p>
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            <a href="https://allemnionline.com" style="color: #007bff; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="color: #999; font-size: 11px; margin: 10px 0 0 0;">
            &copy; ${new Date().getFullYear()} Allemnionline. جميع الحقوق محفوظة - All rights reserved.
          </p>
        </div>
      </div>
    </div>
  `;
};

/**
 * Template for student when teacher reschedules their booking
 * @param {Object} data - Template data
 * @param {string} data.studentName - Student's name
 * @param {string} data.teacherName - Teacher's name
 * @param {string} data.oldBookingDate - Formatted old booking date
 * @param {string} data.oldBookingTime - Formatted old booking time
 * @param {string} data.newBookingDate - Formatted new booking date
 * @param {string} data.newBookingTime - Formatted new booking time
 * @param {string} data.duration - Lesson duration
 * @param {string} data.rescheduleReason - Reason for rescheduling (optional)
 * @returns {string} HTML email template
 */
const getStudentRescheduleNotificationTemplate = (data) => {
  return `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
      <div style="background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #007bff; margin: 0; font-size: 24px;">تم تغيير موعد درسك - Your Lesson Has Been Rescheduled</h1>
          <div style="width: 50px; height: 3px; background-color: #007bff; margin: 10px auto;"></div>
        </div>

        <!-- Content -->
        <div style="margin-bottom: 25px;">
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 15px;">
            <strong>عزيزي الطالب ${data.studentName} / Dear Student ${data.studentName},</strong>
          </p>
          
          <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
            قام المعلم بتغيير موعد درسك:<br>
            <em>Your teacher has rescheduled your lesson:</em>
          </p>
        </div>

        <!-- Old Booking Details -->
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #856404; margin: 0 0 15px 0; font-size: 16px;">الموعد السابق - Previous Schedule</h3>
          
          <div style="margin-bottom: 8px;">
            <span style="color: #856404; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.oldBookingDate}</span>
          </div>
          
          <div style="margin-bottom: 8px;">
            <span style="color: #856404; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.oldBookingTime}</span>
          </div>
        </div>

        <!-- New Booking Details -->
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="color: #155724; margin: 0 0 15px 0; font-size: 18px;">الموعد الجديد - New Schedule</h3>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">المعلم - Teacher:</span>
            <span style="color: #333; margin-left: 10px;">${data.teacherName}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.newBookingDate}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.newBookingTime}</span>
          </div>
          
          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">المدة - Duration:</span>
            <span style="color: #333; margin-left: 10px;">${data.duration} دقيقة - minutes</span>
          </div>

          ${data.rescheduleReason ? `
          <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #c3e6cb;">
            <span style="color: #155724; font-weight: bold;">سبب تغيير الموعد - Reschedule Reason:</span>
            <div style="color: #333; margin-top: 8px; padding: 10px; background-color: white; border-radius: 5px; border-left: 4px solid #007bff;">
              ${data.rescheduleReason}
            </div>
          </div>
          ` : ''}
        </div>

        <!-- Important Note -->
        <div style="background-color: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
          <p style="color: #0056b3; margin: 0; font-size: 14px;">
            <strong>📅 ملاحظة مهمة - Important Note:</strong><br>
            يرجى تحديث تقويمك بالموعد الجديد<br>
            <em>Please update your calendar with the new schedule</em>
          </p>
        </div>

        <!-- Action Button -->
        <div style="text-align: center; margin: 25px 0;">
          <a href="https://allemnionline.com/student/bookings" 
             style="background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
            عرض حجوزاتي - View My Bookings
          </a>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            نتطلع لرؤيتك في الموعد الجديد<br>
            <em>We look forward to seeing you at the new scheduled time</em>
          </p>
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            <a href="https://allemnionline.com" style="color: #007bff; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="color: #999; font-size: 11px; margin: 10px 0 0 0;">
            &copy; ${new Date().getFullYear()} Allemnionline. جميع الحقوق محفوظة - All rights reserved.
          </p>
        </div>
      </div>
    </div>
  `;
};

/**
 * Template for student when they reschedule a booking
 * @param {Object} data - Template data
 * @param {string} data.studentName - Student's name
 * @param {string} data.teacherName - Teacher's name
 * @param {string} data.oldBookingDate - Formatted old booking date
 * @param {string} data.oldBookingTime - Formatted old booking time
 * @param {string} data.newBookingDate - Formatted new booking date
 * @param {string} data.newBookingTime - Formatted new booking time
 * @param {string} data.duration - Lesson duration
 * @param {string} data.rescheduleReason - Reason for rescheduling (optional)
 * @returns {string} HTML email template
 */
const getStudentRescheduleTemplate = (data) => {
  return `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
      <div style="background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #007bff; margin: 0; font-size: 24px;">تأكيد تغيير موعد الدرس - Lesson Reschedule Confirmed</h1>
          <div style="width: 50px; height: 3px; background-color: #007bff; margin: 10px auto;"></div>
        </div>

        <!-- Content -->
        <div style="margin-bottom: 25px;">
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 15px;">
            <strong>عزيزي الطالب ${data.studentName} / Dear Student ${data.studentName},</strong>
          </p>

          <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
            تم تغيير موعد درسك بنجاح كما طلبت:<br>
            <em>Your lesson has been successfully rescheduled as requested:</em>
          </p>
        </div>

        <!-- Old Booking Details -->
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #856404; margin: 0 0 15px 0; font-size: 16px;">الموعد السابق - Previous Schedule</h3>

          <div style="margin-bottom: 8px;">
            <span style="color: #856404; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.oldBookingDate}</span>
          </div>

          <div style="margin-bottom: 8px;">
            <span style="color: #856404; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.oldBookingTime}</span>
          </div>
        </div>

        <!-- New Booking Details -->
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="color: #155724; margin: 0 0 15px 0; font-size: 18px;">الموعد الجديد - New Schedule</h3>

          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">المعلم - Teacher:</span>
            <span style="color: #333; margin-left: 10px;">${data.teacherName}</span>
          </div>

          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.newBookingDate}</span>
          </div>

          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.newBookingTime}</span>
          </div>

          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">المدة - Duration:</span>
            <span style="color: #333; margin-left: 10px;">${data.duration} دقيقة - minutes</span>
          </div>

          ${data.rescheduleReason ? `
          <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #c3e6cb;">
            <span style="color: #155724; font-weight: bold;">سبب تغيير الموعد - Your Reschedule Reason:</span>
            <div style="color: #333; margin-top: 8px; padding: 10px; background-color: white; border-radius: 5px; border-left: 4px solid #007bff;">
              ${data.rescheduleReason}
            </div>
          </div>
          ` : ''}
        </div>

        <!-- Action Button -->
        <div style="text-align: center; margin: 25px 0;">
          <a href="https://allemnionline.com/student/bookings"
             style="background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
            عرض حجوزاتي - View My Bookings
          </a>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            شكراً لاستخدامك منصتنا<br>
            <em>Thank you for using our platform</em>
          </p>
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            <a href="https://allemnionline.com" style="color: #007bff; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="color: #999; font-size: 11px; margin: 10px 0 0 0;">
            &copy; ${new Date().getFullYear()} Allemnionline. جميع الحقوق محفوظة - All rights reserved.
          </p>
        </div>
      </div>
    </div>
  `;
};

/**
 * Template for teacher when student reschedules their booking
 * @param {Object} data - Template data
 * @param {string} data.teacherName - Teacher's name
 * @param {string} data.studentName - Student's name
 * @param {string} data.oldBookingDate - Formatted old booking date
 * @param {string} data.oldBookingTime - Formatted old booking time
 * @param {string} data.newBookingDate - Formatted new booking date
 * @param {string} data.newBookingTime - Formatted new booking time
 * @param {string} data.duration - Lesson duration
 * @param {string} data.rescheduleReason - Reason for rescheduling (optional)
 * @returns {string} HTML email template
 */
const getTeacherRescheduleNotificationTemplate = (data) => {
  return `
    <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8f9fa;">
      <div style="background-color: white; border-radius: 10px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #007bff; margin: 0; font-size: 24px;">الطالب غير موعد الدرس - Student Rescheduled Lesson</h1>
          <div style="width: 50px; height: 3px; background-color: #007bff; margin: 10px auto;"></div>
        </div>

        <!-- Content -->
        <div style="margin-bottom: 25px;">
          <p style="color: #333; font-size: 16px; line-height: 1.6; margin-bottom: 15px;">
            <strong>عزيزي الأستاذ ${data.teacherName} / Dear Teacher ${data.teacherName},</strong>
          </p>

          <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 20px;">
            قام الطالب بتغيير موعد الدرس التالي:<br>
            <em>The student has rescheduled the following lesson:</em>
          </p>
        </div>

        <!-- Old Booking Details -->
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
          <h3 style="color: #856404; margin: 0 0 15px 0; font-size: 16px;">الموعد السابق - Previous Schedule</h3>

          <div style="margin-bottom: 8px;">
            <span style="color: #856404; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.oldBookingDate}</span>
          </div>

          <div style="margin-bottom: 8px;">
            <span style="color: #856404; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.oldBookingTime}</span>
          </div>
        </div>

        <!-- New Booking Details -->
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
          <h3 style="color: #155724; margin: 0 0 15px 0; font-size: 18px;">الموعد الجديد - New Schedule</h3>

          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">الطالب - Student:</span>
            <span style="color: #333; margin-left: 10px;">${data.studentName}</span>
          </div>

          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">التاريخ - Date:</span>
            <span style="color: #333; margin-left: 10px;">${data.newBookingDate}</span>
          </div>

          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">الوقت - Time:</span>
            <span style="color: #333; margin-left: 10px;">${data.newBookingTime}</span>
          </div>

          <div style="margin-bottom: 10px;">
            <span style="color: #155724; font-weight: bold;">المدة - Duration:</span>
            <span style="color: #333; margin-left: 10px;">${data.duration} دقيقة - minutes</span>
          </div>

          ${data.rescheduleReason ? `
          <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #c3e6cb;">
            <span style="color: #155724; font-weight: bold;">سبب تغيير الموعد - Student's Reschedule Reason:</span>
            <div style="color: #333; margin-top: 8px; padding: 10px; background-color: white; border-radius: 5px; border-left: 4px solid #007bff;">
              ${data.rescheduleReason}
            </div>
          </div>
          ` : ''}
        </div>

        <!-- Important Note -->
        <div style="background-color: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 8px; padding: 15px; margin-bottom: 25px;">
          <p style="color: #0056b3; margin: 0; font-size: 14px;">
            <strong>📅 ملاحظة مهمة - Important Note:</strong><br>
            يرجى تحديث جدولك بالموعد الجديد<br>
            <em>Please update your schedule with the new time</em>
          </p>
        </div>

        <!-- Action Button -->
        <div style="text-align: center; margin: 25px 0;">
          <a href="https://allemnionline.com/teacher/bookings"
             style="background-color: #007bff; color: white; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
            عرض جدولي - View My Schedule
          </a>
        </div>

        <!-- Footer -->
        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            نتطلع لرؤيتك في الموعد الجديد<br>
            <em>We look forward to seeing you at the new scheduled time</em>
          </p>
          <p style="color: #666; font-size: 12px; margin: 5px 0;">
            <a href="https://allemnionline.com" style="color: #007bff; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="color: #999; font-size: 11px; margin: 10px 0 0 0;">
            &copy; ${new Date().getFullYear()} Allemnionline. جميع الحقوق محفوظة - All rights reserved.
          </p>
        </div>
      </div>
    </div>
  `;
};

module.exports = {
  getTeacherRescheduleTemplate,
  getStudentRescheduleNotificationTemplate,
  getStudentRescheduleTemplate,
  getTeacherRescheduleNotificationTemplate
};
