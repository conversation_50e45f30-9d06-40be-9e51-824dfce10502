import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Tooltip,
  TablePagination,
  Divider
} from '@mui/material';
import {
  Visibility as VisibilityIcon,
  Reply as ReplyIcon,
  Check as CheckIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Send as SendIcon
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import axios from '../../utils/axios';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';

const Messages = () => {
  const { t, i18n } = useTranslation();
  const { token } = useAuth();
  const isRtl = i18n.language === 'ar';

  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');

  // Dialog states
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [replyDialogOpen, setReplyDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState(null);
  const [replyText, setReplyText] = useState('');
  const [replySending, setReplySending] = useState(false);

  // Fetch messages
  const fetchMessages = async () => {
    setLoading(true);
    setError('');

    try {
      const status = tabValue === 0 ? 'pending' : tabValue === 1 ? 'answered' : 'all';

      const { data } = await axios.get('/contact-us/admin/messages', {
        params: {
          status,
          page: page + 1,
          limit: rowsPerPage,
          search: searchQuery
        }
      });

      if (data.success) {
        setMessages(data.data.messages);
        setTotalCount(data.data.total);
      } else {
        setError(data.message || t('admin.messages.fetchError'));
      }
    } catch (err) {
      console.error('Error fetching messages:', err);
      setError(err.response?.data?.message || t('admin.messages.fetchError'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchMessages();
    }
  }, [token, tabValue, page, rowsPerPage]);

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setPage(0);
  };

  // Handle pagination
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search
  const handleSearch = () => {
    setPage(0);
    fetchMessages();
  };

  const handleSearchKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // Handle view message
  const handleViewMessage = (message) => {
    setSelectedMessage(message);
    setViewDialogOpen(true);

    // If message is unread, mark it as read
    if (!message.read_at) {
      markAsRead(message.id);
    }
  };

  // Mark message as read
  const markAsRead = async (messageId) => {
    try {
      await axios.put(`/contact-us/admin/messages/${messageId}/read`, {});

      // Update the message in the list
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === messageId ? { ...msg, read_at: new Date().toISOString() } : msg
        )
      );
    } catch (err) {
      console.error('Error marking message as read:', err);
    }
  };

  // Handle reply dialog
  const handleReplyDialog = (message) => {
    setSelectedMessage(message);
    setReplyDialogOpen(true);
    setReplyText('');
  };

  // Handle send reply
  const handleSendReply = async () => {
    if (!replyText.trim()) {
      return;
    }

    setReplySending(true);

    try {
      const { data } = await axios.post(`/contact-us/admin/messages/${selectedMessage.id}/reply`, {
        reply: replyText
      });

      if (data.success) {
        setSuccess(t('admin.messages.replySent'));
        setReplyDialogOpen(false);

        // Update the message in the list
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            msg.id === selectedMessage.id ? { ...msg, status: 'answered', answered_at: new Date().toISOString() } : msg
          )
        );
      } else {
        setError(data.message || t('admin.messages.replyError'));
      }
    } catch (err) {
      console.error('Error sending reply:', err);
      setError(err.response?.data?.message || t('admin.messages.replyError'));
    } finally {
      setReplySending(false);
    }
  };

  // Handle delete dialog
  const handleDeleteDialog = (message) => {
    setSelectedMessage(message);
    setDeleteDialogOpen(true);
  };

  // Handle delete message
  const handleDeleteMessage = async () => {
    try {
      const { data } = await axios.delete(`/contact-us/admin/messages/${selectedMessage.id}`);

      if (data.success) {
        setSuccess(t('admin.messages.deleteSuccess'));
        setDeleteDialogOpen(false);

        // Remove the message from the list
        setMessages(prevMessages =>
          prevMessages.filter(msg => msg.id !== selectedMessage.id)
        );

        // Update total count
        setTotalCount(prev => prev - 1);
      } else {
        setError(data.message || t('admin.messages.deleteError'));
      }
    } catch (err) {
      console.error('Error deleting message:', err);
      setError(err.response?.data?.message || t('admin.messages.deleteError'));
    }
  };

  // Get message type label
  const getMessageTypeLabel = (type) => {
    switch (type) {
      case 'question':
        return t('contactUs.typeQuestion');
      case 'problem':
        return t('contactUs.typeProblem');
      case 'suggestion':
        return t('contactUs.typeSuggestion');
      case 'payment':
        return t('contactUs.typePayment');
      case 'other':
        return t('contactUs.typeOther');
      default:
        return type;
    }
  };

  // Get message status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'answered':
        return 'success';
      default:
        return 'default';
    }
  };

  // Get user role label
  const getUserRoleLabel = (role) => {
    switch (role) {
      case 'student':
        return t('common.student');
      case 'teacher':
        return t('common.teacher');
      default:
        return role;
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {t('admin.messages.title')}
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        {/* Search and filter */}
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
          <TextField
            label={t('admin.messages.search')}
            variant="outlined"
            size="small"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={handleSearchKeyPress}
            sx={{ flexGrow: 1 }}
          />
          <Button
            variant="contained"
            color="primary"
            startIcon={<SearchIcon />}
            onClick={handleSearch}
          >
            {t('common.search')}
          </Button>
          <IconButton color="primary" onClick={fetchMessages} title={t('common.refresh')}>
            <RefreshIcon />
          </IconButton>
        </Box>

        {/* Tabs */}
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          sx={{ mb: 2, borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label={t('admin.messages.pending')} />
          <Tab label={t('admin.messages.answered')} />
          <Tab label={t('admin.messages.all')} />
        </Tabs>

        {/* Messages table */}
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t('admin.messages.from')}</TableCell>
                <TableCell>{t('admin.messages.type')}</TableCell>
                <TableCell>{t('admin.messages.subject')}</TableCell>
                <TableCell>{t('admin.messages.date')}</TableCell>
                <TableCell>{t('admin.messages.status')}</TableCell>
                <TableCell align="center">{t('common.actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : messages.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <Typography variant="body1" color="text.secondary">
                      {t('admin.messages.noMessages')}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                messages.map((message) => (
                  <TableRow
                    key={message.id}
                    sx={{
                      backgroundColor: !message.read_at ? 'rgba(25, 118, 210, 0.08)' : 'inherit',
                      '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
                    }}
                  >
                    <TableCell>
                      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Typography variant="body2" fontWeight={!message.read_at ? 'bold' : 'normal'}>
                          {message.user_name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {getUserRoleLabel(message.user_role)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getMessageTypeLabel(message.type)}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography
                        variant="body2"
                        fontWeight={!message.read_at ? 'bold' : 'normal'}
                        sx={{
                          maxWidth: 200,
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {message.subject}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {format(new Date(message.created_at), 'PPP', { locale: isRtl ? ar : enUS })}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {format(new Date(message.created_at), 'p', { locale: isRtl ? ar : enUS })}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={message.status === 'pending' ? t('admin.messages.pending') : t('admin.messages.answered')}
                        size="small"
                        color={getStatusColor(message.status)}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                        <Tooltip title={t('admin.messages.view')}>
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => handleViewMessage(message)}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title={t('admin.messages.reply')}>
                          <IconButton
                            size="small"
                            color="secondary"
                            onClick={() => handleReplyDialog(message)}
                          >
                            <ReplyIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>

                        <Tooltip title={t('admin.messages.delete')}>
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteDialog(message)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[5, 10, 25, 50]}
          labelRowsPerPage={t('common.rowsPerPage')}
        />
      </Paper>

      {/* View Message Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedMessage && (
          <>
            <DialogTitle>
              <Typography variant="h6">{selectedMessage.subject}</Typography>
              <Typography variant="caption" color="text.secondary">
                {t('admin.messages.from')}: {selectedMessage.user_name} ({getUserRoleLabel(selectedMessage.user_role)})
              </Typography>
            </DialogTitle>
            <DialogContent dividers>
              <Box sx={{ mb: 3 }}>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {selectedMessage.message}
                </Typography>
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', color: 'text.secondary' }}>
                  <Typography variant="caption">
                    {t('admin.messages.type')}: {getMessageTypeLabel(selectedMessage.type)}
                  </Typography>
                  <Typography variant="caption">
                    {format(new Date(selectedMessage.created_at), 'PPP p', { locale: isRtl ? ar : enUS })}
                  </Typography>
                </Box>
              </Box>

              {selectedMessage.reply && (
                <>
                  <Divider sx={{ my: 2 }} />
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="subtitle2" color="primary" gutterBottom>
                      {t('admin.messages.yourReply')}:
                    </Typography>
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                      {selectedMessage.reply}
                    </Typography>
                    <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', color: 'text.secondary' }}>
                      <Typography variant="caption">
                        {format(new Date(selectedMessage.answered_at), 'PPP p', { locale: isRtl ? ar : enUS })}
                      </Typography>
                    </Box>
                  </Box>
                </>
              )}
            </DialogContent>
            <DialogActions>
              {selectedMessage.status === 'pending' && (
                <Button
                  onClick={() => {
                    setViewDialogOpen(false);
                    handleReplyDialog(selectedMessage);
                  }}
                  color="primary"
                  startIcon={<ReplyIcon />}
                >
                  {t('admin.messages.reply')}
                </Button>
              )}
              <Button onClick={() => setViewDialogOpen(false)} color="primary">
                {t('common.close')}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Reply Dialog */}
      <Dialog
        open={replyDialogOpen}
        onClose={() => setReplyDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedMessage && (
          <>
            <DialogTitle>
              <Typography variant="h6">
                {t('admin.messages.replyTo')}: {selectedMessage.subject}
              </Typography>
            </DialogTitle>
            <DialogContent dividers>
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  {t('admin.messages.originalMessage')}:
                </Typography>
                <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', color: 'text.secondary' }}>
                  {selectedMessage.message}
                </Typography>
              </Box>

              <TextField
                autoFocus
                label={t('admin.messages.yourReply')}
                multiline
                rows={6}
                fullWidth
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
                variant="outlined"
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setReplyDialogOpen(false)} color="inherit">
                {t('common.cancel')}
              </Button>
              <Button
                onClick={handleSendReply}
                color="primary"
                variant="contained"
                disabled={!replyText.trim() || replySending}
                startIcon={replySending ? <CircularProgress size={20} /> : <SendIcon />}
              >
                {replySending ? t('common.sending') : t('admin.messages.sendReply')}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>{t('admin.messages.confirmDelete')}</DialogTitle>
        <DialogContent>
          <Typography>
            {t('admin.messages.deleteWarning')}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} color="inherit">
            {t('common.cancel')}
          </Button>
          <Button onClick={handleDeleteMessage} color="error" variant="contained">
            {t('common.delete')}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
    </Layout>
  );
};

export default Messages;
