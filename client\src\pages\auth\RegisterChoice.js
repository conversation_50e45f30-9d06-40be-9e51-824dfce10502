import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import {
  Container,
  Grid,
  Typography,
  Button,
  Box,
  Paper,
  useTheme,
  alpha,
  Fade
} from '@mui/material';
import {
  School as SchoolIcon,
  Person as PersonIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';

const RegisterChoice = () => {
  const { t, i18n } = useTranslation();
  const [isRtl] = React.useState(i18n.language === 'ar');
  const theme = useTheme();

  const choices = [
    {
      icon: <SchoolIcon sx={{ fontSize: 56, color: theme.palette.primary.main }} />,
      title: t('auth.registerAsTeacher'),
      description: t('auth.teacherDescription'),
      link: '/register/teacher',
      color: theme.palette.primary.main
    },
    {
      icon: <PersonIcon sx={{ fontSize: 56, color: theme.palette.secondary.main }} />,
      title: t('auth.registerAsStudent'),
      description: t('auth.studentDescription'),
      link: '/register/student',
      color: theme.palette.secondary.main
    }
  ];

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.dark, 0.95)}, ${alpha('#000', 0.8)})`,
        py: 8,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: 'url("/images/islamic-pattern.png")',
          backgroundSize: '800px',
          backgroundPosition: 'center',
          opacity: 0.08,
          zIndex: 0,
          animation: 'patternFloat 60s linear infinite',
        },
        '@keyframes patternFloat': {
          '0%': {
            backgroundPosition: '0% 0%',
          },
          '100%': {
            backgroundPosition: '800px 800px',
          },
        }
      }}
    >
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
        <Box sx={{ mb: 8, textAlign: 'center' }}>
          <Typography
            variant="h2"
            component="h1"
            sx={{
              color: 'white',
              fontWeight: 800,
              mb: 3,
              textShadow: '0 2px 4px rgba(0,0,0,0.3)',
              position: 'relative',
              display: 'inline-block',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: -12,
                left: '50%',
                transform: 'translateX(-50%)',
                width: 120,
                height: 4,
                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                borderRadius: 2
              }
            }}
          >
            {t('auth.chooseAccountType')}
          </Typography>
          <Typography
            variant="h5"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              maxWidth: 700,
              mx: 'auto',
              mt: 4,
              mb: 2,
              lineHeight: 1.8,
              fontWeight: 400,
              textShadow: '0 1px 2px rgba(0,0,0,0.2)'
            }}
          >
            {t('auth.registerDescription')}
          </Typography>
        </Box>

        <Grid container spacing={6} justifyContent="center">
          {choices.map((choice, index) => (
            <Grid item xs={12} md={6} key={choice.link}>
              <Fade in timeout={500 + index * 300}>
                <Paper
                  elevation={24}
                  sx={{
                    height: '100%',
                    background: alpha('#fff', 0.95),
                    backdropFilter: 'blur(20px)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    position: 'relative',
                    overflow: 'hidden',
                    borderRadius: 6,
                    border: `1px solid ${alpha(choice.color, 0.1)}`,
                    '&:hover': {
                      transform: 'translateY(-12px)',
                      '& .hover-gradient': {
                        opacity: 1
                      },
                      '& .choice-icon': {
                        transform: 'scale(1.15) rotate(10deg)',
                        boxShadow: `0 12px 40px ${alpha(choice.color, 0.3)}`
                      }
                    }
                  }}
                >
                  <Box
                    className="hover-gradient"
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: `linear-gradient(135deg, ${alpha(choice.color, 0.08)}, ${alpha(choice.color, 0.16)})`,
                      opacity: 0,
                      transition: 'opacity 0.4s ease-in-out'
                    }}
                  />
                  <Box
                    sx={{
                      p: 5,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      textAlign: 'center'
                    }}
                  >
                    <Box
                      className="choice-icon"
                      sx={{
                        mb: 4,
                        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                        transform: 'scale(1) rotate(0deg)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 120,
                        height: 120,
                        borderRadius: '50%',
                        background: `linear-gradient(135deg, ${alpha(choice.color, 0.12)}, ${alpha(choice.color, 0.05)})`,
                        boxShadow: `0 8px 32px ${alpha(choice.color, 0.2)}`,
                        border: `2px solid ${alpha(choice.color, 0.1)}`
                      }}
                    >
                      {choice.icon}
                    </Box>
                    <Typography
                      variant="h3"
                      component="h2"
                      gutterBottom
                      sx={{
                        fontWeight: 700,
                        color: 'text.primary',
                        mb: 2,
                        fontSize: '2.2rem'
                      }}
                    >
                      {choice.title}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: 'text.secondary',
                        mb: 5,
                        lineHeight: 1.8,
                        fontSize: '1.1rem'
                      }}
                    >
                      {choice.description}
                    </Typography>
                    <Box sx={{ mt: 'auto' }}>
                      <Button
                        component={Link}
                        to={choice.link}
                        variant="contained"
                        size="large"
                        endIcon={!isRtl ? <ArrowForwardIcon /> : <ArrowBackIcon />}
                        sx={{
                          px: 5,
                          py: 2,
                          borderRadius: 4,
                          fontSize: '1.1rem',
                          background: `linear-gradient(135deg, ${choice.color}, ${alpha(choice.color, 0.8)})`,
                          boxShadow: `0 4px 20px ${alpha(choice.color, 0.4)}`,
                          '&:hover': {
                            background: `linear-gradient(135deg, ${choice.color}, ${alpha(choice.color, 0.9)})`,
                            boxShadow: `0 8px 30px ${alpha(choice.color, 0.5)}`,
                            transform: 'translateY(-3px)'
                          }
                        }}
                      >
                        {t('auth.getStarted')}
                      </Button>
                    </Box>
                  </Box>
                </Paper>
              </Fade>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 6, textAlign: 'center' }}>
          <Typography
            variant="body1"
            sx={{
              color: 'rgba(255,255,255,0.9)',
              fontWeight: 500
            }}
          >
            {t('auth.alreadyHaveAccount')}{' '}
            <Link
              to="/login"
              style={{
                color: theme.palette.primary.light,
                textDecoration: 'none',
                fontWeight: 600,
                transition: 'color 0.2s',
                '&:hover': {
                  color: theme.palette.primary.main
                }
              }}
            >
              {t('auth.signIn')}
            </Link>
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default RegisterChoice;
