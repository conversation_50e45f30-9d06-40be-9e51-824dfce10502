import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Grid, Paper } from '@mui/material';
import ChatList from '../../components/chat/ChatList';
import ChatWindow from '../../components/chat/ChatWindow';
import { useAuth } from '../../contexts/AuthContext';
import { useSocket } from '../../contexts/SocketContext';
import { useUnreadMessages } from '../../contexts/UnreadMessagesContext';
import Layout from '../../components/Layout';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';
import { useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';

const StudentChat = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const { socket, isConnected } = useSocket();
  const { decreaseUnreadCount } = useUnreadMessages();
  const location = useLocation();
  const [chats, setChats] = useState([]);
  const [selectedChat, setSelectedChat] = useState(null);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);

  // Log dependencies changes
  useEffect(() => {
    console.log('Chat Dependencies Changed:', {
      hasCurrentUser: !!currentUser,
      isConnected,
      socketConnected: !!socket,
      userId: currentUser?.id
    });
  }, [currentUser, isConnected, socket]);

  // Fetch chats
  useEffect(() => {
    if (socket && isConnected && currentUser) {
      console.log('Emitting get_student_chats with studentId:', currentUser.id);
      socket.emit('get_student_chats', { studentId: currentUser.id }, (response) => {
        if (response.success) {
          setChats(response.chats);
        } else {
          console.error('Error fetching chats:', response.error);
        }
        setLoading(false);
      });
    }
  }, [socket, isConnected, currentUser]);

  // تحقق من وجود معلومات المعلم من صفحة FindTeacher
  useEffect(() => {
    const teacherInfo = location.state;
    if (teacherInfo?.selectedTeacherId && socket && isConnected) {
      // ابحث عن المحادثة الموجودة أو أنشئ واحدة جديدة
      socket.emit('get_or_create_chat', {
        teacherId: teacherInfo.selectedTeacherId,
        studentId: currentUser.id
      }, (response) => {
        if (response.success) {
          const newChat = {
            ...response.chat,
            teacher_name: teacherInfo.teacherName,
            teacher_picture: teacherInfo.teacherPicture
          };

          // أضف المحادثة إلى القائمة إذا لم تكن موجودة
          setChats(prevChats => {
            const chatExists = prevChats.some(chat => chat.id === newChat.id);
            if (!chatExists) {
              return [newChat, ...prevChats];
            }
            return prevChats;
          });

          // حدد المحادثة
          setSelectedChat(newChat);
        }
      });
    }
  }, [location.state, socket, isConnected, currentUser]);

  // Listen for new messages
  useEffect(() => {
    if (socket) {
      socket.on('new_message', (message) => {
        // Convert MySQL timestamp to Unix timestamp
        if (message.created_at && typeof message.created_at === 'string') {
          message.created_at = Math.floor(new Date(message.created_at.replace(' ', 'T')).getTime() / 1000);
        }

        // Add message to messages list if it's for the current chat
        if (selectedChat && message.conversation_id === selectedChat.id) {
          setMessages(prev => [...prev, message]);
        }

        // Update chat list
        setChats(prevChats => {
          const chatIndex = prevChats.findIndex(chat => chat.id === message.conversation_id);
          if (chatIndex === -1) return prevChats;

          const updatedChats = [...prevChats];
          const chat = { ...updatedChats[chatIndex] };
          chat.last_message = message.content;
          chat.last_message_time = message.created_at;

          // Only increment unread count if it's not the current chat
          if (!selectedChat || message.conversation_id !== selectedChat.id) {
            chat.unread_count = chat.unread_count ? chat.unread_count + 1 : 1;
          }

          // Move this chat to the top of the list
          updatedChats.splice(chatIndex, 1);
          updatedChats.unshift(chat);

          return updatedChats;
        });

        // If message is for current chat, mark it as read
        if (selectedChat && message.conversation_id === selectedChat.id) {
          socket.emit('mark_messages_read', { chatId: message.conversation_id });
        }
      });

      // Listen for message sent confirmation
      socket.on('message_sent', ({ success, message, tempId }) => {
        if (success) {
          // Convert MySQL timestamp to Unix timestamp
          if (message.created_at && typeof message.created_at === 'string') {
            message.created_at = Math.floor(new Date(message.created_at.replace(' ', 'T')).getTime() / 1000);
          }

          setMessages(prev => prev.map(msg =>
            msg.id === tempId ? { ...message } : msg
          ));

          // Update chat list with confirmed message
          setChats(prevChats => {
            const chatIndex = prevChats.findIndex(chat => chat.id === message.conversation_id);
            if (chatIndex === -1) return prevChats;

            const updatedChats = [...prevChats];
            const chat = { ...updatedChats[chatIndex] };
            chat.last_message = message.content;
            chat.last_message_time = message.created_at;

            // Move this chat to the top of the list
            updatedChats.splice(chatIndex, 1);
            updatedChats.unshift(chat);

            return updatedChats;
          });
        } else {
          // Remove temporary message if sending failed
          setMessages(prev => prev.filter(msg => msg.id !== tempId));
        }
      });

      // Listen for message updates
      socket.on('message_updated', ({ messageId, content }) => {
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            msg.id === messageId ? { ...msg, content } : msg
          )
        );
      });

      socket.on('message_deleted', ({ messageId }) => {
        setMessages(prevMessages =>
          prevMessages.filter(msg => msg.id !== messageId)
        );
      });

      socket.on('chat_deleted', ({ chatId }) => {
        console.log('Chat deleted notification received:', chatId);

        // Remove chat from list
        setChats(prevChats => prevChats.filter(chat => chat.id !== chatId));

        // If the deleted chat was selected, clear selection
        if (selectedChat && selectedChat.id === chatId) {
          setSelectedChat(null);
          setMessages([]);
        }

        toast.info(t('chat.conversationDeletedByOther'));
      });

      return () => {
        socket.off('new_message');
        socket.off('message_sent');
        socket.off('message_updated');
        socket.off('message_deleted');
        socket.off('chat_deleted');
      };
    }
  }, [socket, selectedChat]);

  useEffect(() => {
    if (socket && selectedChat) {
      socket.emit('get_chat_messages', { chatId: selectedChat.id }, (response) => {
        if (response.success) {
          // Convert all MySQL timestamps to Unix timestamps
          const convertedMessages = response.messages.map(msg => {
            if (msg.created_at && typeof msg.created_at === 'string') {
              return {
                ...msg,
                created_at: Math.floor(new Date(msg.created_at.replace(' ', 'T')).getTime() / 1000)
              };
            }
            return msg;
          });
          setMessages(convertedMessages);
          // Mark messages as read when opening chat
          socket.emit('mark_messages_read', { chatId: selectedChat.id });
          // Update unread count in chat list
          setChats((prevChats) => {
            const updatedChats = prevChats.map((c) =>
              c.id === selectedChat.id ? { ...c, unread_count: 0 } : c
            );
            // Calculate how many messages were marked as read and decrease the global counter
            const chat = prevChats.find(c => c.id === selectedChat.id);
            if (chat && chat.unread_count > 0) {
              decreaseUnreadCount(chat.unread_count);
            }
            return updatedChats;
          });
        } else {
          console.error('Error fetching messages:', response.error);
        }
        setLoading(false);
      });
    }
  }, [socket, selectedChat]);

  const handleChatSelect = useCallback((chat) => {
    setSelectedChat(chat);
    setLoading(true);

    if (socket) {
      socket.emit('get_chat_messages', { chatId: chat.id }, (response) => {
        if (response.success) {
          // Convert all MySQL timestamps to Unix timestamps
          const convertedMessages = response.messages.map(msg => {
            if (msg.created_at && typeof msg.created_at === 'string') {
              return {
                ...msg,
                created_at: Math.floor(new Date(msg.created_at.replace(' ', 'T')).getTime() / 1000)
              };
            }
            return msg;
          });
          setMessages(convertedMessages);
          // Mark messages as read when opening chat
          socket.emit('mark_messages_read', { chatId: chat.id });
          // Update unread count in chat list
          setChats((prevChats) => {
            const updatedChats = prevChats.map((c) =>
              c.id === chat.id ? { ...c, unread_count: 0 } : c
            );
            // Calculate how many messages were marked as read and decrease the global counter
            const chatData = prevChats.find(c => c.id === chat.id);
            if (chatData && chatData.unread_count > 0) {
              decreaseUnreadCount(chatData.unread_count);
            }
            return updatedChats;
          });
        } else {
          console.error('Error fetching messages:', response.error);
        }
        setLoading(false);
      });
    }
  }, [socket]);

  const handleSendMessage = useCallback((content) => {
    if (socket && selectedChat) {
      // Create a temporary message with a unique ID
      const tempId = `temp-${Date.now()}`;

      // Get current timestamp in seconds
      const timestamp = Math.floor(Date.now() / 1000);

      const tempMessage = {
        id: tempId,
        conversation_id: selectedChat.id,
        sender_id: currentUser.id,
        content,
        created_at: timestamp,
        is_read: false
      };

      // Add temporary message to the UI
      setMessages(prev => [...prev, tempMessage]);

      // Send the message
      socket.emit('send_message', {
        chatId: selectedChat.id,
        content,
        tempId,
        timestamp
      });

      // Update chat list with temporary message
      setChats(prevChats => {
        const chatIndex = prevChats.findIndex(chat => chat.id === selectedChat.id);
        if (chatIndex === -1) return prevChats;

        const updatedChats = [...prevChats];
        const chat = { ...updatedChats[chatIndex] };
        chat.last_message = content;
        chat.last_message_time = timestamp;
        updatedChats[chatIndex] = chat;
        return updatedChats;
      });
    }
  }, [socket, selectedChat, currentUser]);

  const handleEditMessage = useCallback(async (messageId, newContent) => {
    if (socket && isConnected) {
      console.log('Attempting to edit message:', messageId);
      socket.emit('edit_message', {
        messageId,
        content: newContent,
        userId: currentUser.id
      }, (response) => {
        console.log('Edit message response:', response);
        if (response.success) {
          setMessages(prevMessages =>
            prevMessages.map(msg =>
              msg.id === messageId ? { ...msg, content: newContent } : msg
            )
          );
          toast.success(t('chat.messageEdited'));
        } else {
          console.error('Error editing message:', response.error);
          toast.error(t('chat.editError'));
        }
      });
    }
  }, [socket, isConnected, currentUser, t]);

  const handleDeleteMessage = useCallback(async (messageId) => {
    if (socket && isConnected) {
      console.log('Attempting to delete message:', messageId);
      socket.emit('delete_message', {
        messageId,
        userId: currentUser.id
      }, (response) => {
        console.log('Delete message response:', response);
        if (response.success) {
          setMessages(prevMessages =>
            prevMessages.filter(msg => msg.id !== messageId)
          );
          toast.success(t('chat.messageDeleted'));
        } else {
          console.error('Error deleting message:', response.error);
          toast.error(t('chat.deleteError'));
        }
      });
    }
  }, [socket, isConnected, currentUser, t]);

  const handleDeleteChat = useCallback((chatId) => {
    if (socket && isConnected) {
      console.log('Attempting to delete chat:', chatId);
      socket.emit('delete_chat', {
        chatId,
        userId: currentUser.id
      }, (response) => {
        console.log('Delete chat response:', response);
        if (response.success) {
          // Remove chat from list
          setChats(prevChats => prevChats.filter(chat => chat.id !== chatId));

          // If the deleted chat was selected, clear selection
          if (selectedChat && selectedChat.id === chatId) {
            setSelectedChat(null);
            setMessages([]);
          }

          toast.success(t('chat.conversationDeleted'));
        } else {
          console.error('Error deleting chat:', response.error);
          toast.error(t('chat.deleteConversationError'));
        }
      });
    }
  }, [socket, isConnected, currentUser, selectedChat, t]);

  return (
    <Layout>
      <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
        <Box sx={{
          height: 'calc(100vh - 80px)', // Adjust for navbar height
          p: 2,
          display: 'flex',
          flexDirection: 'column'
        }}>
        <Paper sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}>
          <Grid container sx={{ height: '100%' }}>
            {/* Chat List */}
            <Grid
              item
              xs={12}
              md={4}
              sx={{
                height: '100%',
                borderRight: 1,
                borderColor: 'divider',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              <ChatList
                chats={chats}
                selectedChat={selectedChat}
                onChatSelect={handleChatSelect}
                onDeleteChat={handleDeleteChat}
                loading={loading}
                currentUser={currentUser}
              />
            </Grid>
            {/* Chat Window */}
            <Grid
              item
              xs={12}
              md={8}
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              <ChatWindow
                chat={selectedChat}
                messages={messages}
                onSendMessage={handleSendMessage}
                onEditMessage={handleEditMessage}
                onDeleteMessage={handleDeleteMessage}
                loading={loading}
                currentUser={currentUser}
              />
            </Grid>
          </Grid>
        </Paper>
      </Box>
      </ProfileCompletionAlert>
    </Layout>
  );
};

export default StudentChat;
