/*!
 * paypal-js v8.2.0 (2025-01-23T17:26:53.747Z)
 * Copyright 2020-present, PayPal, Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
function t(t){var r=t.sdkBaseUrl,n=t.environment,o=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(r[n[o]]=t[n[o]])}return r}(t,["sdkBaseUrl","environment"]),a=r||function(t){return"sandbox"===t?"https://www.sandbox.paypal.com/sdk/js":"https://www.paypal.com/sdk/js"}(n),i=o,u=Object.keys(i).filter((function(t){return void 0!==i[t]&&null!==i[t]&&""!==i[t]})).reduce((function(t,e){var r,n=i[e].toString();return r=function(t,e){return(e?"-":"")+t.toLowerCase()},"data"===(e=e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,r)).substring(0,4)||"crossorigin"===e?t.attributes[e]=n:t.queryParams[e]=n,t}),{queryParams:{},attributes:{}}),c=u.queryParams,s=u.attributes;return c["merchant-id"]&&-1!==c["merchant-id"].indexOf(",")&&(s["data-merchant-id"]=c["merchant-id"],c["merchant-id"]="*"),{url:"".concat(a,"?").concat(e(c)),attributes:s}}function e(t){var e="";return Object.keys(t).forEach((function(r){0!==e.length&&(e+="&"),e+=r+"="+t[r]})),e}function r(t,e){void 0===e&&(e={});var r=document.createElement("script");return r.src=t,Object.keys(e).forEach((function(t){r.setAttribute(t,e[t]),"data-csp-nonce"===t&&r.setAttribute("nonce",e["data-csp-nonce"])})),r}function n(e,n){if(void 0===n&&(n=Promise),i(e,n),"undefined"==typeof document)return n.resolve(null);var u=t(e),c=u.url,s=u.attributes,d=s["data-namespace"]||"paypal",l=a(d);return s["data-js-sdk-library"]||(s["data-js-sdk-library"]="paypal-js"),function(t,e){var n=document.querySelector('script[src="'.concat(t,'"]'));if(null===n)return null;var o=r(t,e),a=n.cloneNode();if(delete a.dataset.uidAuto,Object.keys(a.dataset).length!==Object.keys(o.dataset).length)return null;var i=!0;return Object.keys(a.dataset).forEach((function(t){a.dataset[t]!==o.dataset[t]&&(i=!1)})),i?n:null}(c,s)&&l?n.resolve(l):o({url:c,attributes:s},n).then((function(){var t=a(d);if(t)return t;throw new Error("The window.".concat(d," global variable is not available."))}))}function o(t,e){void 0===e&&(e=Promise),i(t,e);var n=t.url,o=t.attributes;if("string"!=typeof n||0===n.length)throw new Error("Invalid url.");if(void 0!==o&&"object"!=typeof o)throw new Error("Expected attributes to be an object.");return new e((function(t,e){if("undefined"==typeof document)return t();!function(t){var e=t.url,n=t.attributes,o=t.onSuccess,a=t.onError,i=r(e,n);i.onerror=a,i.onload=o,document.head.insertBefore(i,document.head.firstElementChild)}({url:n,attributes:o,onSuccess:function(){return t()},onError:function(){var t=new Error('The script "'.concat(n,'" failed to load. Check the HTTP status code and response body in DevTools to learn more.'));return e(t)}})}))}function a(t){return window[t]}function i(t,e){if("object"!=typeof t||null===t)throw new Error("Expected an options object.");var r=t.environment;if(r&&"production"!==r&&"sandbox"!==r)throw new Error('The `environment` option must be either "production" or "sandbox".');if(void 0!==e&&"function"!=typeof e)throw new Error("Expected PromisePonyfill to be a function.")}"function"==typeof SuppressedError&&SuppressedError;var u="8.2.0";export{o as loadCustomScript,n as loadScript,u as version};
