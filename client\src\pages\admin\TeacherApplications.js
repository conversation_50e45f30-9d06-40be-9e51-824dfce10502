import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  Card,
  CardContent,
  Chip,
  TablePagination,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Avatar,
  IconButton,
  Divider,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tooltip,
} from '@mui/material';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';
import { useSocket } from '../../contexts/SocketContext';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import LanguageIcon from '@mui/icons-material/Language';
import SchoolIcon from '@mui/icons-material/School';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import DescriptionIcon from '@mui/icons-material/Description';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import debounce from 'lodash/debounce';

const TeacherApplications = () => {
  const { t } = useTranslation();
  const { socket, isConnected } = useSocket();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [applications, setApplications] = useState([]);
  const [selectedApplication, setSelectedApplication] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [openVideoDialog, setOpenVideoDialog] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [filters, setFilters] = useState({
    status: '',
    search: ''
  });
  const [processing, setProcessing] = useState(false);
  const [searchText, setSearchText] = useState('');

  // Fetch applications with pagination and filters
  useEffect(() => {
    const fetchApplications = async () => {
      try {
        setLoading(true);
        const response = await axios.get('/admin/applications', {
          params: {
            page: page + 1,
            limit: rowsPerPage,
            status: filters.status,
            search: filters.search
          }
        });

        setApplications(response.data.applications);
        setTotalCount(response.data.total);
        setError('');
      } catch (err) {
        console.error('Error fetching applications:', err);
        setError(t('admin.applications.fetchError'));
      } finally {
        setLoading(false);
      }
    };

    fetchApplications();
  }, [page, rowsPerPage, filters.status, filters.search, t]);

  // Handle real-time updates
  useEffect(() => {
    if (!socket || !isConnected) {
      console.log('Socket not connected yet, waiting...');
      return;
    }

    console.log('Setting up socket listener');
    const handleStatusChange = (data) => {
      console.log('Received status update:', data);
      setApplications(prevApplications => {
        return prevApplications.map(app => {
          if (app.user_id === parseInt(data.applicationId)) {
            console.log('Updating application:', app.user_id, 'with new status:', data.status);
            return { ...app, status: data.status };
          }
          return app;
        });
      });
      setSuccess(t('admin.applications.statusUpdateSuccess'));
    };

    socket.on('application_status_changed', handleStatusChange);

    return () => {
      console.log('Cleaning up socket listener');
      socket.off('application_status_changed', handleStatusChange);
    };
  }, [socket, isConnected, t]);

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterChange = useCallback((field) => (event) => {
    const value = event.target.value;
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPage(0);
  }, []);

  const handleSearch = () => {
    setFilters(prev => ({
      ...prev,
      search: searchText
    }));
    setPage(0);
  };

  const handleSearchKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
  };

  const handleViewDetails = (application) => {
    setSelectedApplication(application);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedApplication(null);
    setTabValue(0);
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleOpenVideoDialog = () => {
    setOpenVideoDialog(true);
  };

  const handleCloseVideoDialog = () => {
    setOpenVideoDialog(false);
  };

  // Function to extract YouTube video ID from URL
  const getYoutubeVideoId = (url) => {
    if (!url) return null;

    // Regular expressions to match different YouTube URL formats
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
    const match = url.match(regExp);

    return (match && match[2].length === 11) ? match[2] : null;
  };

  // Function to check if a URL is a local video file
  const isLocalVideoFile = (url) => {
    if (!url) return false;
    return url.startsWith('/uploads/videos/');
  };

  // Function to check if a URL is a YouTube video
  const isYoutubeVideo = (url) => {
    if (!url) return false;
    return getYoutubeVideoId(url) !== null;
  };

  const handleStatusChange = async (applicationId, newStatus) => {
    try {
      setProcessing(true);
      console.log('Updating status for application:', applicationId, newStatus);
      const response = await axios.put(`/admin/applications/${applicationId}/status`, {
        status: newStatus
      });
      console.log('Status update response:', response.data);

      // Show success message
      setSuccess(t('admin.applications.statusUpdateSuccess'));

      // Close the dialog
      setOpenDialog(false);
    } catch (err) {
      console.error('Error updating application status:', err);
      setError(t('admin.applications.updateError'));
    } finally {
      setProcessing(false);
    }
  };

  const handleApprove = (application) => {
    console.log('Approving application:', application);
    handleStatusChange(application.user_id, 'approved');
  };

  const handleReject = (application) => {
    console.log('Rejecting application:', application);
    handleStatusChange(application.user_id, 'rejected');
  };

  const safeParseJSON = (jsonString, defaultValue = []) => {
    try {
      return typeof jsonString === 'string'
        ? JSON.parse(jsonString)
        : Array.isArray(jsonString)
          ? jsonString
          : defaultValue;
    } catch (e) {
      console.error('Error parsing JSON:', e);
      return defaultValue;
    }
  };

  if (loading && page === 0) {
    return (
      <Layout title={t('admin.applications.title')}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Layout>
    );
  }

  return (
    <Layout title={t('admin.applications.title')}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
      )}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>{success}</Alert>
      )}

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={4}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                fullWidth
                label={t('admin.applications.searchPlaceholder')}
                value={searchText}
                onChange={handleSearchChange}
                onKeyPress={handleSearchKeyPress}
                size="small"
              />
              <Button
                variant="contained"
                color="primary"
                onClick={handleSearch}
                sx={{ minWidth: '120px' }}
                startIcon={<SearchIcon />}
              >
                {t('common.search')}
              </Button>
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth>
              <InputLabel>{t('admin.applications.filterByStatus')}</InputLabel>
              <Select
                value={filters.status}
                onChange={handleFilterChange('status')}
                label={t('admin.applications.filterByStatus')}
              >
                <MenuItem value="">{t('admin.applications.allStatuses')}</MenuItem>
                <MenuItem value="pending">{t('admin.applicationStatus.pending')}</MenuItem>
                <MenuItem value="approved">{t('admin.applicationStatus.approved')}</MenuItem>
                <MenuItem value="rejected">{t('admin.applicationStatus.rejected')}</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Applications Table */}
      <Paper sx={{ width: '90%', overflow: 'hidden', mx: 2, my: 2 }}>
        <TableContainer sx={{ maxHeight: 'calc(100vh - 300px)' }}>
          <Table stickyHeader sx={{ minWidth: 600 }} size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{ fontWeight: 'bold', minWidth: 200 }}>{t('admin.applications.name')}</TableCell>
                <TableCell sx={{ fontWeight: 'bold', minWidth: 200 }}>{t('admin.applications.email')}</TableCell>
                <TableCell sx={{ fontWeight: 'bold', minWidth: 140 }}>{t('admin.applications.phone')}</TableCell>
                <TableCell sx={{ fontWeight: 'bold', minWidth: 120 }}>{t('admin.applications.country')}</TableCell>
                <TableCell sx={{ fontWeight: 'bold', minWidth: 200 }}>{t('admin.applications.languages')}</TableCell>
                <TableCell sx={{ fontWeight: 'bold', minWidth: 120 }}>{t('admin.applications.status')}</TableCell>
                <TableCell sx={{ fontWeight: 'bold', minWidth: 120 }}>{t('admin.applications.actions')}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {applications.map((app) => (
                <TableRow key={app.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {app.profile_picture_url ? (
                        <Avatar sx={{ width: 32, height: 32 }} src={app.profile_picture_url} alt={app.full_name} />
                      ) : (
                        <Avatar sx={{ width: 32, height: 32 }}>{app.full_name[0]}</Avatar>
                      )}
                      <Typography noWrap>{app.full_name}</Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography noWrap>{app.email}</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography noWrap>{app.phone}</Typography>
                  </TableCell>
                  <TableCell>
                    <Typography noWrap>{app.country}</Typography>
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, maxWidth: 200 }}>
                      {app.teaching_languages.map((lang, index) => (
                        <Chip
                          key={index}
                          label={lang}
                          size="small"
                          variant="outlined"
                          sx={{ m: '2px' }}
                        />
                      ))}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={t(`admin.applications.statuses.${app.status}`)}
                      color={
                        app.status === 'approved'
                          ? 'success'
                          : app.status === 'rejected'
                          ? 'error'
                          : 'warning'
                      }
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton
                        size="small"
                        onClick={() => handleViewDetails(app)}
                        color="primary"
                        title={t('admin.applications.viewDetails')}
                      >
                        <VisibilityIcon fontSize="small" />
                      </IconButton>
                      {app.status === 'pending' && (
                        <>
                          <IconButton
                            size="small"
                            onClick={() => handleApprove(app)}
                            color="success"
                            title={t('admin.applications.approve')}
                            disabled={processing}
                          >
                            <CheckIcon fontSize="small" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleReject(app)}
                            color="error"
                            title={t('admin.applications.reject')}
                            disabled={processing}
                          >
                            <CloseIcon fontSize="small" />
                          </IconButton>
                        </>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          component="div"
          count={totalCount}
          page={page}
          onPageChange={handlePageChange}
          rowsPerPage={rowsPerPage}
          onRowsPerPageChange={handleRowsPerPageChange}
          rowsPerPageOptions={[10, 25, 50]}
        />
      </Paper>

      {/* Application Details Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        {selectedApplication && (
          <>
            <DialogTitle sx={{ borderBottom: 1, borderColor: 'divider', pb: 1 }}>
              {t('admin.applications.applicationDetails')}: {selectedApplication.full_name}
            </DialogTitle>
            <DialogContent sx={{ p: 0 }}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs value={tabValue} onChange={handleTabChange} aria-label="teacher application tabs">
                  <Tab label={t('admin.applications.basicInfo')} />
                  <Tab label={t('admin.applications.teachingDetails')} />
                  <Tab label={t('admin.applications.documents')} />
                  <Tab label={t('admin.applications.schedule')} />
                </Tabs>
              </Box>

              {/* Basic Info Tab */}
              <Box role="tabpanel" hidden={tabValue !== 0} sx={{ p: 3 }}>
                {tabValue === 0 && (
                  <Grid container spacing={3}>
                    {/* Profile Picture and Status */}
                    <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>
                      <Avatar
                        src={selectedApplication.profile_picture_url}
                        alt={selectedApplication.full_name}
                        sx={{ width: 180, height: 180, border: '4px solid', borderColor: 'primary.main' }}
                      />
                      <Typography variant="h5" fontWeight="bold">{selectedApplication.full_name}</Typography>
                      <Chip
                        label={t(`admin.applications.statuses.${selectedApplication.status}`)}
                        color={
                          selectedApplication.status === 'approved'
                            ? 'success'
                            : selectedApplication.status === 'rejected'
                            ? 'error'
                            : 'warning'
                        }
                        sx={{ fontSize: '1rem', py: 2, px: 1 }}
                      />
                    </Grid>

                    {/* Personal Information */}
                    <Grid item xs={12} md={8}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        {t('admin.applications.personalInfo')}
                      </Typography>
                      <List>
                        <ListItem>
                          <ListItemIcon><EmailIcon color="primary" /></ListItemIcon>
                          <ListItemText primary={t('admin.applications.email')} secondary={selectedApplication.email} />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><PhoneIcon color="primary" /></ListItemIcon>
                          <ListItemText primary={t('admin.applications.phone')} secondary={selectedApplication.phone || t('common.notProvided')} />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><LocationOnIcon color="primary" /></ListItemIcon>
                          <ListItemText
                            primary={t('admin.applications.location')}
                            secondary={`${selectedApplication.country}${selectedApplication.residence ? `, ${selectedApplication.residence}` : ''}`}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><LanguageIcon color="primary" /></ListItemIcon>
                          <ListItemText primary={t('admin.applications.nativeLanguage')} secondary={selectedApplication.native_language} />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><CalendarMonthIcon color="primary" /></ListItemIcon>
                          <ListItemText
                            primary={t('admin.applications.applicationDate')}
                            secondary={new Date(selectedApplication.created_at).toLocaleDateString()}
                          />
                        </ListItem>
                      </List>
                    </Grid>
                  </Grid>
                )}
              </Box>

              {/* Teaching Details Tab */}
              <Box role="tabpanel" hidden={tabValue !== 1} sx={{ p: 3 }}>
                {tabValue === 1 && (
                  <Grid container spacing={3}>
                    {/* Teaching Languages */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <LanguageIcon color="primary" />
                        {t('admin.applications.teachingLanguages')}
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 2, mt: 2 }}>
                        {selectedApplication.teaching_languages.map((lang, index) => (
                          <Chip
                            key={index}
                            label={lang}
                            color="primary"
                            variant="outlined"
                            sx={{ m: 0.5 }}
                          />
                        ))}
                      </Box>
                    </Grid>

                    {/* Course Types */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <SchoolIcon color="primary" />
                        {t('admin.applications.courseTypes')}
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 2, mt: 2 }}>
                        {selectedApplication.course_types.map((course, index) => (
                          <Chip
                            key={index}
                            label={course}
                            color="secondary"
                            variant="outlined"
                            sx={{ m: 0.5 }}
                          />
                        ))}
                      </Box>
                    </Grid>

                    {/* Experience and Price */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <AccessTimeIcon color="primary" />
                        {t('admin.applications.experience')}
                      </Typography>
                      <Typography variant="body1" sx={{ ml: 2, mt: 2 }}>
                        {t('admin.applications.yearsOfExperience', { years: selectedApplication.teaching_experience })}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <AttachMoneyIcon color="primary" />
                        {t('admin.applications.pricing')}
                      </Typography>
                      <Typography variant="body1" sx={{ ml: 2, mt: 2 }}>
                        <strong>{t('admin.applications.pricePerLesson')}:</strong> ${selectedApplication.price_per_lesson}/hr
                        <br />
                        <strong>{t('admin.applications.trialLessonPrice')}:</strong> ${selectedApplication.trial_lesson_price}/hr
                      </Typography>

                    </Grid>

                    {/* Qualifications */}
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <SchoolIcon color="primary" />
                        {t('admin.applications.qualifications')}
                      </Typography>
                      <Typography variant="body1" sx={{ ml: 2, mt: 2 }}>
                        {selectedApplication.qualifications}
                      </Typography>
                    </Grid>
                  </Grid>
                )}
              </Box>

              {/* Documents Tab */}
              <Box role="tabpanel" hidden={tabValue !== 2} sx={{ p: 3 }}>
                {tabValue === 2 && (
                  <Grid container spacing={3}>
                    {/* Introduction Video */}
                    {selectedApplication.intro_video_url && (
                      <Grid item xs={12}>
                        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                          <PlayCircleOutlineIcon color="primary" />
                          {t('admin.applications.introVideo')}
                        </Typography>
                        <Box sx={{ mt: 2 }}>
                          {isYoutubeVideo(selectedApplication.intro_video_url) ? (
                            // YouTube video embed
                            <Box sx={{ position: 'relative', width: '100%', paddingTop: '56.25%' /* 16:9 Aspect Ratio */ }}>
                              <iframe
                                style={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  width: '100%',
                                  height: '100%',
                                  border: 'none'
                                }}
                                src={`https://www.youtube.com/embed/${getYoutubeVideoId(selectedApplication.intro_video_url)}`}
                                title="Teacher Introduction Video"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                allowFullScreen
                              />
                            </Box>
                          ) : isLocalVideoFile(selectedApplication.intro_video_url) ? (
                            // Local video file
                            <Box sx={{ position: 'relative', width: '100%', paddingTop: '56.25%' /* 16:9 Aspect Ratio */ }}>
                              <video
                                style={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  width: '100%',
                                  height: '100%',
                                  border: 'none'
                                }}
                                src={`https://************:3000${selectedApplication.intro_video_url}`}
                                controls
                                preload="metadata"
                              />
                            </Box>
                          ) : (
                            // External video URL (not YouTube)
                            <Typography variant="body1">
                              <a href={selectedApplication.intro_video_url} target="_blank" rel="noopener noreferrer">
                                {t('admin.applications.openVideo')}
                              </a>
                            </Typography>
                          )}
                        </Box>
                      </Grid>
                    )}

                    {/* CV/Resume */}
                    {selectedApplication.cv && (
                      <Grid item xs={12}>
                        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                          <DescriptionIcon color="primary" />
                          {t('admin.applications.cv')}
                        </Typography>
                        <Typography variant="body1" sx={{ ml: 2, mt: 2 }}>
                          {selectedApplication.cv}
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                )}
              </Box>

              {/* Schedule Tab */}
              <Box role="tabpanel" hidden={tabValue !== 3} sx={{ p: 3 }}>
                {tabValue === 3 && (
                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1, borderBottom: 1, borderColor: 'divider', pb: 1 }}>
                        <AccessTimeIcon color="primary" />
                        {t('admin.applications.availableHours')}
                      </Typography>
                      <Box sx={{ mt: 2 }}>
                        {Object.entries(selectedApplication.available_hours || {}).map(([day, hours]) => (
                          hours.length > 0 && (
                            <Box key={day} sx={{ mb: 2 }}>
                              <Typography variant="body1" fontWeight="bold">
                                {t(`days.${day.toLowerCase()}`)}
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, ml: 2 }}>
                                {hours.map((hour, index) => (
                                  <Chip
                                    key={index}
                                    label={hour}
                                    size="small"
                                    variant="outlined"
                                  />
                                ))}
                              </Box>
                            </Box>
                          )
                        ))}
                      </Box>
                    </Grid>
                  </Grid>
                )}
              </Box>
            </DialogContent>
            <DialogActions>
              {selectedApplication.status === 'pending' && (
                <>
                  <Button
                    onClick={() => handleReject(selectedApplication)}
                    color="error"
                    disabled={processing}
                  >
                    {t('admin.applications.reject')}
                  </Button>
                  <Button
                    onClick={() => handleApprove(selectedApplication)}
                    color="success"
                    variant="contained"
                    disabled={processing}
                  >
                    {t('admin.applications.approve')}
                  </Button>
                </>
              )}
              <Button onClick={handleCloseDialog}>
                {t('common.close')}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </Layout>
  );
};

export default TeacherApplications;
