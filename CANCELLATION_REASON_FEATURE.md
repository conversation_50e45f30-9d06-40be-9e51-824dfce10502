# ميزة سبب إلغاء الحجز - Booking Cancellation Reason Feature

## نظرة عامة - Overview

تم إضافة ميزة جديدة تسمح للمعلمين والطلاب بكتابة سبب إلغاء الحجز عند إلغاء أي درس.

This feature allows both teachers and students to provide a reason when cancelling a booking.

## التغييرات المطلوبة - Required Changes

### 1. قاعدة البيانات - Database Changes

قم بتشغيل الملف SQL التالي على قاعدة البيانات:
Run the following SQL file on your database:

```bash
mysql -u your_username -p teach_me_islam_arabic < apply_cancellation_reason_migration.sql
```

أو قم بتشغيل الأوامر التالية مباشرة:
Or run these commands directly:

```sql
USE teach_me_islam_arabic;

ALTER TABLE `bookings` 
ADD COLUMN `cancellation_reason` TEXT NULL AFTER `status`;

ALTER TABLE `bookings` 
ADD COLUMN `cancelled_by` ENUM('student', 'teacher') NULL AFTER `cancellation_reason`;

ALTER TABLE `bookings` 
ADD COLUMN `cancelled_at` TIMESTAMP NULL AFTER `cancelled_by`;
```

### 2. الملفات المحدثة - Updated Files

#### Backend:
- `server/routes/bookings.routes.js` - تحديث API endpoint للإلغاء
- `server/migrations/add_cancellation_reason.sql` - ملف migration

#### Frontend:
- `client/src/pages/teacher/Bookings.js` - إضافة حقل سبب الإلغاء للمعلم
- `client/src/pages/student/Bookings.js` - إضافة حقل سبب الإلغاء للطالب
- `client/src/i18n/i18n.js` - إضافة النصوص للترجمة

## الميزات الجديدة - New Features

### 1. حقل سبب الإلغاء
- حقل نصي متعدد الأسطر (3 أسطر)
- اختياري - ليس مطلوباً
- يظهر في نافذة تأكيد الإلغاء

### 2. تتبع من قام بالإلغاء
- يتم حفظ معلومات من قام بالإلغاء (معلم أو طالب)
- يتم حفظ وقت الإلغاء

### 3. واجهة محسنة
- نافذة إلغاء محدثة مع حقل سبب الإلغاء
- نصوص مترجمة للعربية والإنجليزية

## كيفية الاستخدام - How to Use

### للمعلمين - For Teachers:
1. اذهب إلى صفحة الحجوزات: `/teacher/bookings`
2. اضغط على "إلغاء" للحجز المطلوب
3. اكتب سبب الإلغاء (اختياري)
4. اضغط "نعم، إلغاء الحجز"

### للطلاب - For Students:
1. اذهب إلى صفحة الحجوزات: `/student/bookings`
2. اضغط على "إلغاء" للحجز المطلوب
3. اكتب سبب الإلغاء (اختياري)
4. اضغط "نعم، إلغاء الحجز"

## هيكل قاعدة البيانات الجديد - New Database Structure

```sql
CREATE TABLE `bookings` (
  `id` int NOT NULL,
  `teacher_profile_id` int NOT NULL,
  `student_id` int NOT NULL,
  `datetime` datetime NOT NULL,
  `status` enum('scheduled','completed','cancelled','issue_reported','ongoing') NOT NULL DEFAULT 'scheduled',
  `cancellation_reason` TEXT NULL,                    -- جديد
  `cancelled_by` ENUM('student', 'teacher') NULL,     -- جديد
  `cancelled_at` TIMESTAMP NULL,                      -- جديد
  `duration` varchar(10) DEFAULT '50',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## الميزات الجديدة المضافة - New Added Features

### 📧 إرسال الإيميلات عند الإلغاء - Email Notifications

تم إضافة نظام إرسال إيميلات تلقائي عند إلغاء الدروس:

#### عند إلغاء المعلم للدرس:
1. **إيميل للمعلم**: تأكيد إلغاء الدرس مع التفاصيل وسبب الإلغاء
2. **إيميل للطالب**: إشعار بإلغاء الدرس مع التفاصيل وسبب الإلغاء

#### عند إلغاء الطالب للدرس:
1. **إيميل للطالب**: تأكيد إلغاء الدرس مع التفاصيل وسبب الإلغاء
2. **إيميل للمعلم**: إشعار بإلغاء الطالب للدرس مع التفاصيل

### 📁 الملفات الجديدة المضافة - New Files Added

#### Backend:
- `server/templates/bookingCancellationEmails.js` - قوالب الإيميلات
- `server/services/bookingCancellationEmailService.js` - خدمة إرسال الإيميلات
- `server/test/testCancellationEmails.js` - ملف اختبار الإيميلات

### 🎨 تصميم الإيميلات - Email Design

- **تصميم ثنائي اللغة**: عربي وإنجليزي في نفس الإيميل
- **تصميم احترافي**: ألوان متناسقة وتخطيط واضح
- **معلومات شاملة**: تفاصيل الدرس، سبب الإلغاء، معلومات الاسترداد
- **أزرار عمل**: روابط للبحث عن معلم آخر أو حجز درس جديد
- **⏰ مناطق زمنية ذكية**: كل مستخدم يرى الوقت بمنطقته الزمنية الصحيحة

### 🧪 اختبار الإيميلات - Testing Emails

```bash
# اختبار تنسيق التاريخ والوقت فقط
node server/test/testCancellationEmails.js --format-only

# اختبار إرسال الإيميلات فقط
node server/test/testCancellationEmails.js --emails-only

# اختبار شامل
node server/test/testCancellationEmails.js
```

## ⏰ المناطق الزمنية - Timezone Support

### كيف تعمل المناطق الزمنية:
- **المعلم**: يرى الوقت بمنطقته الزمنية المحفوظة في `teacher_profiles.timezone`
- **الطالب**: يرى الوقت بمنطقته الزمنية المحفوظة في `student_completion_data.timezone`
- **التحويل التلقائي**: النظام يحول الوقت من UTC إلى المنطقة الزمنية لكل مستخدم
- **عرض واضح**: الوقت يظهر مع اسم المنطقة الزمنية (مثل: UTC+03:00)

### أمثلة على التحويل:
```
الوقت في قاعدة البيانات (UTC): 2024-01-15 14:30:00
المعلم (UTC+03:00): ٠٥:٣٠ م - 05:30 PM (UTC+03:00)
الطالب (UTC-05:00): ٠٩:٣٠ ص - 09:30 AM (UTC-05:00)
```

## ملاحظات - Notes

- سبب الإلغاء اختياري ولا يمنع عملية الإلغاء إذا لم يتم كتابته
- يتم حفظ جميع البيانات في قاعدة البيانات لأغراض التتبع والتحليل
- الميزة متوافقة مع النظام الحالي ولا تؤثر على الوظائف الموجودة
- **الإيميلات ترسل بشكل غير متزامن** لعدم تأخير استجابة API
- **نظام إيميل مقاوم للأخطاء**: إذا فشل إرسال الإيميل، لا يؤثر على عملية الإلغاء
- **⏰ دعم المناطق الزمنية**: كل مستخدم يرى الوقت بمنطقته الزمنية الصحيحة

The cancellation reason is optional and doesn't prevent cancellation if not provided.
All data is saved in the database for tracking and analysis purposes.
The feature is compatible with the existing system and doesn't affect current functionality.
**Emails are sent asynchronously** to avoid delaying API response.
**Error-resistant email system**: If email sending fails, it doesn't affect the cancellation process.
