const pool = require('../config/database');
const emailService = require('../utils/emailService');

// Generate OTP code
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send OTP email
const sendOTPEmail = async (email, otpCode) => {
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #eee; border-radius: 5px;">
      <h2 style="color: #4a6da7; text-align: center;">Withdrawal Verification Code</h2>
      <p>Your withdrawal verification code is:</p>
      <h1 style="color: #4a6da7; text-align: center; font-size: 32px; letter-spacing: 5px;">${otpCode}</h1>
      <p>This code will expire in 15 minutes.</p>
      <p>If you did not request this withdrawal, please ignore this email.</p>
    </div>
  `;

  return emailService.sendEmail({
    to: email,
    subject: 'Withdrawal Verification Code - Allemni online',
    html
  });
};

const withdrawalController = {
  // Get teacher's withdrawal history
  getWithdrawals: async (req, res) => {
    try {
      const teacherId = req.user.id;
      const page = parseInt(req.query.page) || 0;
      const limit = parseInt(req.query.limit) || 10;
      const offset = page * limit;

      const connection = await pool.getConnection();

      // Get withdrawals with pagination
      const [withdrawals] = await connection.query(
        `SELECT 
          wr.*,
          u.full_name as processed_by_name
        FROM withdrawal_requests wr
        LEFT JOIN users u ON wr.processed_by = u.id
        WHERE wr.teacher_id = ?
        ORDER BY wr.created_at DESC
        LIMIT ? OFFSET ?`,
        [teacherId, limit, offset]
      );

      // Get total count
      const [countResult] = await connection.query(
        'SELECT COUNT(*) as total FROM withdrawal_requests WHERE teacher_id = ?',
        [teacherId]
      );

      connection.release();

      const total = countResult[0].total;
      const totalPages = Math.ceil(total / limit);

      res.json({
        success: true,
        data: {
          withdrawals,
          pagination: {
            page,
            limit,
            total,
            totalPages
          }
        }
      });
    } catch (error) {
      console.error('Error fetching withdrawals:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  },

  // Request withdrawal
  requestWithdrawal: async (req, res) => {
    const { amount, paypalEmail } = req.body;
    const teacherId = req.user.id;

    let connection;
    try {
      connection = await pool.getConnection();
      await connection.beginTransaction();

      // Validate input
      if (!amount || !paypalEmail) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'Amount and PayPal email are required'
        });
      }

      if (amount <= 0) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'Amount must be greater than 0'
        });
      }

      // Get minimum withdrawal amount from settings
      const [minAmountResult] = await connection.query(
        "SELECT value FROM settings WHERE `key` = 'min_withdrawal_amount'"
      );
      const minAmount = parseFloat(minAmountResult[0]?.value || 10);

      if (amount < minAmount) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: `Minimum withdrawal amount is $${minAmount}`
        });
      }

      // Check teacher's balance
      const [balanceResult] = await connection.query(
        'SELECT balance, email FROM users WHERE id = ? AND role IN ("platform_teacher", "new_teacher")',
        [teacherId]
      );

      if (!balanceResult.length) {
        await connection.rollback();
        return res.status(403).json({
          success: false,
          error: 'Teacher not found'
        });
      }

      const currentBalance = parseFloat(balanceResult[0].balance);
      const teacherEmail = balanceResult[0].email;

      if (currentBalance < amount) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'Insufficient balance'
        });
      }

      // Check for pending withdrawal requests
      const [pendingResult] = await connection.query(
        'SELECT COUNT(*) as count FROM withdrawal_requests WHERE teacher_id = ? AND status IN ("pending", "processing", "otp_pending")',
        [teacherId]
      );

      if (pendingResult[0].count > 0) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'You have a pending withdrawal request. Please wait for it to be processed.'
        });
      }

      // Generate OTP
      const otpCode = generateOTP();
      const otpExpiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

      // Create withdrawal request with OTP
      const [result] = await connection.query(
        `INSERT INTO withdrawal_requests 
        (teacher_id, amount, paypal_email, status, otp_code, otp_expiry, is_otp_verified) 
        VALUES (?, ?, ?, 'otp_pending', ?, ?, 0)`,
        [teacherId, amount, paypalEmail, otpCode, otpExpiry]
      );

      // Send OTP email
      try {
        await sendOTPEmail(teacherEmail, otpCode);
      } catch (emailError) {
        console.error('Error sending OTP email:', emailError);
        // Continue execution even if email fails
      }

      await connection.commit();

      res.json({
        success: true,
        message: 'OTP code has been sent to your email',
        withdrawalId: result.insertId
      });
    } catch (error) {
      if (connection) {
        await connection.rollback();
      }
      console.error('Error requesting withdrawal:', error);
      res.status(500).json({
        success: false,
        error: 'Error processing withdrawal request'
      });
    } finally {
      if (connection) {
        connection.release();
      }
    }
  },

  // Verify OTP and complete withdrawal
  verifyOTP: async (req, res) => {
    const { withdrawalId, otpCode } = req.body;
    const teacherId = req.user.id;

    let connection;
    try {
      connection = await pool.getConnection();
      await connection.beginTransaction();

      // Get withdrawal request
      const [withdrawalResult] = await connection.query(
        'SELECT * FROM withdrawal_requests WHERE id = ? AND teacher_id = ?',
        [withdrawalId, teacherId]
      );

      if (!withdrawalResult.length) {
        await connection.rollback();
        return res.status(404).json({
          success: false,
          error: 'Withdrawal request not found'
        });
      }

      const withdrawal = withdrawalResult[0];

      // Check if withdrawal is already cancelled or completed
      if (withdrawal.status !== 'otp_pending') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'This withdrawal request is no longer valid'
        });
      }

      // Check if OTP is already verified
      if (withdrawal.is_otp_verified) {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'OTP already verified'
        });
      }

      // Check if OTP is expired
      const now = new Date();
      if (now > new Date(withdrawal.otp_expiry)) {
        // Cancel expired withdrawal
        await connection.query(
          `UPDATE withdrawal_requests 
          SET status = 'cancelled',
              admin_notes = 'Cancelled due to OTP expiration'
          WHERE id = ?`,
          [withdrawalId]
        );

        await connection.commit();
        return res.status(400).json({
          success: false,
          error: 'OTP code has expired. The withdrawal request has been cancelled.'
        });
      }

      // Check OTP attempts
      if (withdrawal.otp_attempts >= 3) {
        // Cancel the withdrawal request
        await connection.query(
          `UPDATE withdrawal_requests 
          SET status = 'cancelled',
              admin_notes = 'Cancelled due to multiple failed OTP attempts'
          WHERE id = ?`,
          [withdrawalId]
        );

        await connection.commit();
        return res.status(400).json({
          success: false,
          error: 'Too many failed attempts. The withdrawal request has been cancelled.'
        });
      }

      // Verify OTP
      if (withdrawal.otp_code !== otpCode) {
        // Increment attempts
        await connection.query(
          'UPDATE withdrawal_requests SET otp_attempts = otp_attempts + 1 WHERE id = ?',
          [withdrawalId]
        );

        // If this was the third attempt, cancel the withdrawal
        if (withdrawal.otp_attempts + 1 >= 3) {
          // Cancel the withdrawal request
          await connection.query(
            `UPDATE withdrawal_requests 
            SET status = 'cancelled',
                admin_notes = 'Cancelled due to multiple failed OTP attempts'
            WHERE id = ?`,
            [withdrawalId]
          );
        }

        await connection.commit();
        return res.status(400).json({
          success: false,
          error: 'Invalid OTP code'
        });
      }

      // Update withdrawal status to completed and mark OTP as verified
      await connection.query(
        `UPDATE withdrawal_requests 
        SET status = 'completed', 
            is_otp_verified = 1,
            processed_at = CURRENT_TIMESTAMP
        WHERE id = ?`,
        [withdrawalId]
      );

      // Deduct amount from teacher's balance
      await connection.query(
        'UPDATE users SET balance = balance - ? WHERE id = ?',
        [withdrawal.amount, teacherId]
      );

      await connection.commit();

      res.json({
        success: true,
        message: 'Withdrawal request completed successfully'
      });
    } catch (error) {
      if (connection) {
        await connection.rollback();
      }
      console.error('Error verifying OTP:', error);
      res.status(500).json({
        success: false,
        error: 'Error verifying OTP'
      });
    } finally {
      if (connection) {
        connection.release();
      }
    }
  },

  // Cancel withdrawal request (only if pending)
  cancelWithdrawal: async (req, res) => {
    const { withdrawalId } = req.params;
    const teacherId = req.user.id;

    let connection;
    try {
      connection = await pool.getConnection();
      await connection.beginTransaction();

      // Get withdrawal request
      const [withdrawalResult] = await connection.query(
        'SELECT * FROM withdrawal_requests WHERE id = ? AND teacher_id = ?',
        [withdrawalId, teacherId]
      );

      if (!withdrawalResult.length) {
        await connection.rollback();
        return res.status(404).json({
          success: false,
          error: 'Withdrawal request not found'
        });
      }

      const withdrawal = withdrawalResult[0];

      // Only allow cancellation if status is otp_pending
      if (withdrawal.status !== 'otp_pending') {
        await connection.rollback();
        return res.status(400).json({
          success: false,
          error: 'Can only cancel pending withdrawal requests'
        });
      }

      // Update withdrawal status
      await connection.query(
        `UPDATE withdrawal_requests 
        SET status = 'cancelled',
            admin_notes = 'Cancelled by user'
        WHERE id = ?`,
        [withdrawalId]
      );

      await connection.commit();

      res.json({
        success: true,
        message: 'Withdrawal request cancelled successfully'
      });
    } catch (error) {
      if (connection) {
        await connection.rollback();
      }
      console.error('Error cancelling withdrawal:', error);
      res.status(500).json({
        success: false,
        error: 'Error cancelling withdrawal request'
      });
    } finally {
      if (connection) {
        connection.release();
      }
    }
  },

  // Get withdrawal settings
  getWithdrawalSettings: async (req, res) => {
    try {
      const connection = await pool.getConnection();

      const [settings] = await connection.query(
        `SELECT \`key\`, value FROM settings 
        WHERE \`key\` IN ('min_withdrawal_amount', 'withdrawal_fee_percentage', 'withdrawal_processing_days')`
      );

      connection.release();

      const settingsObj = {};
      settings.forEach(setting => {
        settingsObj[setting.key] = setting.value;
      });

      res.json({
        success: true,
        data: settingsObj
      });
    } catch (error) {
      console.error('Error fetching withdrawal settings:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
};

module.exports = withdrawalController;
