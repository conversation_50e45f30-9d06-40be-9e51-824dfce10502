{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{Container,Typography,Box,Paper,Grid,Card,CardContent,Button,Chip,CircularProgress,Alert,Dialog,DialogTitle,DialogContent,DialogActions,IconButton,Divider,Avatar,useTheme,Tooltip,TextField,FormControl,InputLabel,Select,MenuItem}from'@mui/material';import{CalendarMonth as CalendarIcon,AccessTime as TimeIcon,Person as PersonIcon,Cancel as CancelIcon,Close as CloseIcon,Event as EventIcon,VideoCall as VideoCallIcon,ChevronLeft as ChevronLeftIcon,ChevronRight as ChevronRightIcon,Schedule as RescheduleIcon}from'@mui/icons-material';import{format,addDays,startOfWeek,addWeeks,subWeeks}from'date-fns';import{ar,enUS}from'date-fns/locale';import axios from'../../utils/axios';import dayjs from'dayjs';import utc from'dayjs/plugin/utc';import{useAuth}from'../../contexts/AuthContext';import Layout from'../../components/Layout';import WeeklyBookingsTable from'../../components/WeeklyBookingsTable';import RescheduleDialog from'../../components/RescheduleDialog';import{convertFromDatabaseTime,formatDateInStudentTimezone,getCurrentTimeInTimezone}from'../../utils/timezone';import moment from'moment-timezone';import ProfileCompletionAlert from'../../components/student/ProfileCompletionAlert';import VideoSDKMeeting from'../../components/meeting/VideoSDKMeeting';import MeetingFeedbackDialog from'../../components/MeetingFeedbackDialog';import{toast}from'react-hot-toast';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";dayjs.extend(utc);const Bookings=()=>{const{t,i18n}=useTranslation();const{token}=useAuth();const theme=useTheme();const isRtl=i18n.language==='ar';const[bookings,setBookings]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const[selectedBooking,setSelectedBooking]=useState(null);const[cancelDialogOpen,setCancelDialogOpen]=useState(false);const[detailsDialogOpen,setDetailsDialogOpen]=useState(false);const[cancellingBooking,setCancellingBooking]=useState(false);const[cancellationReason,setCancellationReason]=useState('');// Function to calculate time remaining until lesson\nconst calculateTimeRemaining=lessonDateTime=>{const now=new Date();const lessonTime=new Date(lessonDateTime);const timeDifference=lessonTime.getTime()-now.getTime();if(timeDifference<=0){return{hours:0,minutes:0,isOverdue:true};}const hours=Math.floor(timeDifference/(1000*60*60));const minutes=Math.floor(timeDifference%(1000*60*60)/(1000*60));return{hours,minutes,isOverdue:false};};// Function to check if booking can be rescheduled by student\nconst canStudentReschedule=booking=>{// Check if booking exists\nif(!booking)return false;// Check if booking is scheduled\nif(booking.status!=='scheduled')return false;// Check if teacher is not deleted\nif(booking.teacher_deleted_at)return false;// Check if student has already rescheduled this booking\nconst rescheduleCount=parseInt(booking.reschedule_count)||0;if(rescheduleCount>=1)return false;// Check if reschedule is at least 12 hours before booking time\nconst timeRemaining=calculateTimeRemaining(booking.datetime);// Debug log\nconsole.log('🔍 Reschedule check:',{bookingId:booking.id,bookingDateTime:booking.datetime,currentTime:new Date().toISOString(),timeRemaining:timeRemaining,canReschedule:!timeRemaining.isOverdue&&timeRemaining.hours>=12});if(timeRemaining.isOverdue||timeRemaining.hours<12)return false;return true;};const[studentProfile,setStudentProfile]=useState(null);const[currentWeekStart,setCurrentWeekStart]=useState(()=>{const today=new Date();return startOfWeek(today,{weekStartsOn:1});// Start from current week\n});const[openMeeting,setOpenMeeting]=useState(false);const[currentMeeting,setCurrentMeeting]=useState(null);const[currentTime,setCurrentTime]=useState(new Date());const[feedbackDialogOpen,setFeedbackDialogOpen]=useState(false);const[feedbackMeeting,setFeedbackMeeting]=useState(null);// Reschedule states\nconst[rescheduleDialogOpen,setRescheduleDialogOpen]=useState(false);const[availableDays,setAvailableDays]=useState([]);const[availableTimesForDay,setAvailableTimesForDay]=useState([]);const[selectedDay,setSelectedDay]=useState(null);const[loadingDays,setLoadingDays]=useState(false);const[loadingTimes,setLoadingTimes]=useState(false);const[rescheduling,setRescheduling]=useState(false);const[rescheduleReason,setRescheduleReason]=useState('');// Days of the week\nconst daysOfWeek=['monday','tuesday','wednesday','thursday','friday','saturday','sunday'];// Week navigation functions\nconst goToPreviousWeek=()=>{const previousWeek=subWeeks(currentWeekStart,1);setCurrentWeekStart(previousWeek);};const goToNextWeek=()=>{const nextWeek=addWeeks(currentWeekStart,1);const today=new Date();const oneYearAhead=addWeeks(today,52);// One year ahead from today\nconst maxWeek=startOfWeek(oneYearAhead,{weekStartsOn:1});// Don't allow going beyond one year ahead\nif(nextWeek<=maxWeek){setCurrentWeekStart(nextWeek);}};// Check if navigation buttons should be disabled\nconst isPreviousWeekDisabled=()=>false;const isNextWeekDisabled=()=>{const nextWeek=addWeeks(currentWeekStart,1);const today=new Date();const oneYearAhead=addWeeks(today,52);// One year ahead from today\nconst maxWeek=startOfWeek(oneYearAhead,{weekStartsOn:1});return nextWeek>maxWeek;};// Fetch student profile\nuseEffect(()=>{const fetchStudentProfile=async()=>{if(!token)return;try{const{data}=await axios.get('/api/students/profile',{headers:{'Authorization':`Bearer ${token}`}});if(data.success&&data.profile){setStudentProfile(data.profile);}}catch(error){console.error('Error fetching student profile:',error);}};fetchStudentProfile();},[token]);// Helper function to check for pending feedback\nconst checkPendingFeedback=async()=>{try{console.log('Checking for pending feedback in Bookings page...');const{data}=await axios.get('/meeting-issues/pending');if(data.success&&data.data){const issue=data.data;const shouldOpen=dayjs.utc().isAfter(dayjs.utc(issue.datetime).add(issue.duration||50,'minute'));console.log('Found pending feedback:',issue,'Should open:',shouldOpen);if(shouldOpen){setFeedbackMeeting(issue);setFeedbackDialogOpen(true);return true;// Found and opened feedback\n}}else{console.log('No pending feedback found in Bookings page');}return false;// No feedback found or not ready to open\n}catch(err){console.error('Error checking pending feedback',err);return false;}};// Check for pending feedback on mount and periodically\nuseEffect(()=>{// Check immediately on mount\ncheckPendingFeedback();// Set up periodic checking every 30 seconds while on bookings page\nconst interval=setInterval(checkPendingFeedback,30000);// Also check when window gains focus (user returns to tab)\nconst handleFocus=()=>{console.log('Window focused, checking for pending feedback...');checkPendingFeedback();};window.addEventListener('focus',handleFocus);// Cleanup interval and event listener on unmount\nreturn()=>{clearInterval(interval);window.removeEventListener('focus',handleFocus);};},[]);// Attach booking_id to feedback meeting if missing\nuseEffect(()=>{if(!feedbackMeeting||feedbackMeeting.booking_id)return;if(!bookings.length)return;const targetTime=moment(feedbackMeeting.datetime);// find booking with same teacher and date (same day) within 2 hours window\nconst candidates=bookings.filter(b=>{if(b.teacher_name!==feedbackMeeting.teacher_name)return false;const bTime=moment(b.datetime);const timeDiff=Math.abs(bTime.diff(targetTime,'minutes'));return timeDiff<=120;});// Sort by closest time match\ncandidates.sort((a,b)=>{const aDiff=Math.abs(moment(a.datetime).diff(targetTime,'minutes'));const bDiff=Math.abs(moment(b.datetime).diff(targetTime,'minutes'));return aDiff-bDiff;});if(candidates.length>0){const bestMatch=candidates[0];console.log(`Linking feedback to booking: Meeting ${feedbackMeeting.meeting_id} -> Booking ${bestMatch.id}`);setFeedbackMeeting(prev=>({...prev,booking_id:bestMatch.id}));}else{console.warn(`No matching booking found for meeting ${feedbackMeeting.meeting_id} with teacher ${feedbackMeeting.teacher_name}`);}},[bookings,feedbackMeeting]);// Cleanup feedback state when leaving the page\nuseEffect(()=>{return()=>{// Clear any pending feedback when leaving bookings page\n// This allows global feedback system to take over on other pages\nconsole.log('Cleaning up bookings page feedback state');setFeedbackDialogOpen(false);setFeedbackMeeting(null);};},[]);// Fetch bookings with retry logic\nuseEffect(()=>{const fetchBookings=async()=>{try{setLoading(true);const{data}=await axios.get('/bookings/student',{headers:{'Authorization':`Bearer ${token}`}});if(data.success){console.log('Bookings data:',data.data);// Make sure all bookings have the correct data types\nconst processedBookings=data.data.map(booking=>({...booking,price_per_lesson:parseFloat(booking.price_per_lesson||0),price_paid:booking.price_paid!==undefined?parseFloat(booking.price_paid):null,duration:booking.duration?String(booking.duration):'50'}));console.log('Processed bookings:',processedBookings);setBookings(processedBookings);}else{setError(data.message||t('bookings.fetchError'));}}catch(error){console.error('Error fetching bookings:',error);setError(t('bookings.fetchError'));}finally{setLoading(false);}};const fetchWithRetry=async function(){let maxRetries=arguments.length>0&&arguments[0]!==undefined?arguments[0]:3;let retries=0;while(retries<maxRetries){try{await fetchBookings();break;}catch(error){retries++;if(retries===maxRetries){throw error;}await new Promise(resolve=>setTimeout(resolve,1000));// Wait before retry\n}}};if(token){fetchWithRetry();}// Update current time every second\nconst timeInterval=setInterval(()=>{setCurrentTime(new Date());},1000);return()=>clearInterval(timeInterval);},[token,t]);// Handle view details\nconst handleViewDetails=booking=>{setSelectedBooking(booking);setDetailsDialogOpen(true);};// Handle cancel booking\nconst handleCancelBookingClick=booking=>{setSelectedBooking(booking);setCancelDialogOpen(true);};// Handle join meeting\nconst handleJoinMeeting=async booking=>{try{// Check if room_name exists from the booking data\nif(!booking.room_name){console.error('No room_name found for booking:',booking);toast.error(t('meetings.noRoomError')||'Meeting room not found');return;}// Check if meeting_id exists\nif(!booking.meeting_id){console.error('No meeting_id found for booking:',booking);toast.error(t('meetings.noMeetingError')||'Meeting ID not found');return;}console.log('Joining meeting with data:',{room_name:booking.room_name,meeting_id:booking.meeting_id,datetime:booking.datetime,duration:booking.duration});// التحقق من صلاحية الغرفة\nconst response=await axios.get(`/meetings/${booking.room_name}/validate`);setCurrentMeeting({...booking,room_name:booking.room_name});setOpenMeeting(true);}catch(error){console.error('Error joining meeting:',error);toast.error(t('meetings.joinError'));}};const handleCloseMeeting=()=>{// After meeting dialog closes, prompt for feedback\nif(currentMeeting){const meetingEnd=new Date(currentMeeting.datetime);meetingEnd.setMinutes(meetingEnd.getMinutes()+(currentMeeting.duration||50));const now=new Date();// Only prompt if meeting time has actually ended\nif(now>=meetingEnd){setFeedbackMeeting(currentMeeting);// Find the corresponding booking for this meeting\nconst targetTime=moment(currentMeeting.datetime);const correspondingBooking=bookings.find(b=>{if(b.teacher_name!==currentMeeting.teacher_name)return false;const bTime=moment(b.datetime);return Math.abs(bTime.diff(targetTime,'minutes'))<=120;});// Send pending status immediately with correct booking_id\naxios.post('/meeting-issues',{booking_id:correspondingBooking?correspondingBooking.id:null,meeting_id:currentMeeting.meeting_id||currentMeeting.id,issue_type:'pending',description:''}).catch(err=>console.error('Failed to create pending issue',err));setFeedbackDialogOpen(true);}}setOpenMeeting(false);setCurrentMeeting(null);};// Get current time in student's timezone (same method as meetings page)\nconst getCurrentTimeInStudentTimezone=()=>{if(!studentProfile||!studentProfile.timezone){return new Date();}return getCurrentTimeInTimezone(studentProfile.timezone);};// Get meeting date in student timezone (same method as meetings page)\nconst getMeetingDateInStudentTimezone=datetime=>{if(!studentProfile||!studentProfile.timezone){return new Date(datetime);}return convertFromDatabaseTime(datetime,studentProfile.timezone);};// Get meeting status from database directly\nconst getMeetingStatus=booking=>{return booking.status||'scheduled';};// Check if user can join meeting (same method as meetings page)\nconst canJoinMeeting=booking=>{if(!booking||!studentProfile)return false;// Cannot join if teacher is deleted\nif(booking.teacher_deleted_at)return false;const currentStatus=getMeetingStatus(booking);if(currentStatus==='cancelled'||currentStatus==='cancelled_teacher_deleted'||currentStatus==='completed'){return false;}const meetingStartTime=getMeetingDateInStudentTimezone(booking.datetime);const meetingEndTime=new Date(meetingStartTime);meetingEndTime.setMinutes(meetingEndTime.getMinutes()+(booking.duration||50));const now=getCurrentTimeInStudentTimezone();return now>=meetingStartTime&&now<meetingEndTime;};// Get meeting status text (same method as meetings page)\nconst getMeetingStatusText=booking=>{if(!booking||!studentProfile)return t('meetings.notStarted');// Special message for deleted teachers\nif(booking.teacher_deleted_at){return t('meetings.teacherDeleted','Teacher Unavailable');}const currentStatus=getMeetingStatus(booking);const canJoin=canJoinMeeting(booking);if(canJoin){return t('meetings.join');}switch(currentStatus){case'cancelled':return t('meetings.status.cancelled');case'completed':return t('meetings.status.completed');case'ongoing':return t('meetings.join');case'scheduled':default:return t('meetings.notStarted');}};// Handle booking cancellation\nconst handleCancelBooking=async()=>{if(!selectedBooking)return;try{setCancellingBooking(true);const{data}=await axios.put(`/bookings/${selectedBooking.id}/cancel`,{cancellation_reason:cancellationReason},{headers:{'Authorization':`Bearer ${token}`}});if(data.success){// Update the booking status in the local state\nsetBookings(prevBookings=>prevBookings.map(booking=>booking.id===selectedBooking.id?{...booking,status:'cancelled'}:booking));// Show appropriate success message based on the response\nif(data.fullRefundToStudent){toast.success(t('bookings.cancelSuccess'));}else if(data.commissionPaidToTeacher){toast(t('bookings.commissionPaidToTeacher'),{icon:'⚠️',style:{background:'#ff9800',color:'white'}});}else{toast.success(data.message||t('bookings.cancelSuccess'));}}else{toast.error(data.message||t('bookings.cancelError'));}}catch(error){var _error$response,_error$response$data;console.error('Error cancelling booking:',error);toast.error(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||t('bookings.cancelError'));}finally{setCancellingBooking(false);setCancelDialogOpen(false);setDetailsDialogOpen(false);setSelectedBooking(null);setCancellationReason('');}};// Handle reschedule booking\nconst handleRescheduleBookingClick=async booking=>{setSelectedBooking(booking);setSelectedDay(null);setAvailableTimesForDay([]);setRescheduleReason('');setLoadingDays(true);setRescheduleDialogOpen(true);try{// Get available slots for this booking using same API as booking page\nconst queryParams=new URLSearchParams();if(studentProfile!==null&&studentProfile!==void 0&&studentProfile.timezone){queryParams.append('student_timezone',studentProfile.timezone);}const{data}=await axios.get(`/bookings/${booking.id}/available-slots?${queryParams.toString()}`,{headers:{'Authorization':`Bearer ${token}`}});if(data.success){console.log('🔍 Student reschedule - API Response:',data);// لا نقوم بتصفية الأوقات هنا - سنتركها لنافذة RescheduleDialog\n// لتجنب التصفية المضاعفة\nconst validSlots=data.data;console.log('🔍 All slots from API (no filtering here):',{total:data.data.length,note:'Filtering will be done in RescheduleDialog'});// Group slots by date\nconst slotsByDate={};validSlots.forEach(slot=>{const date=slot.date;if(!slotsByDate[date]){slotsByDate[date]=[];}slotsByDate[date].push({...slot,// Ensure we have the display time\ndisplayTime:slot.time||new Date(slot.datetime).toLocaleTimeString('en-US',{hour:'2-digit',minute:'2-digit',hour12:true})});});// Convert to the format expected by the UI\nconst availableDays=Object.keys(slotsByDate).filter(date=>slotsByDate[date].length>0)// Only include dates with valid slots\n.map(date=>{const slots=slotsByDate[date];const dateObj=new Date(date);return{date:date,dayName:dateObj.toLocaleDateString('en-US',{weekday:'long'}),formattedDate:dateObj.toLocaleDateString('en-US',{month:'short',day:'numeric'}),slots:slots,availableCount:slots.length,availableSlots:slots.length};});console.log('🔍 Available days for student:',availableDays);setAvailableDays(availableDays);}else{console.error('❌ API returned error:',data);toast.error(data.message||t('bookings.fetchDaysError'));}}catch(error){var _error$response2,_error$response2$data;console.error('Error fetching available slots:',error);toast.error(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||t('bookings.fetchDaysError'));}finally{setLoadingDays(false);}};// Handle reschedule confirmation\nconst handleRescheduleConfirm=async function(timeSlot){let reason=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'';if(!timeSlot){toast.error('Please select a time slot');return;}try{setRescheduling(true);// Use the slot's datetime directly\nconst newDateTime=timeSlot.datetime;const{data}=await axios.put(`/bookings/${selectedBooking.id}/reschedule`,{newDateTime:newDateTime,newDuration:selectedBooking.duration,reschedule_reason:reason||rescheduleReason},{headers:{'Authorization':`Bearer ${token}`}});if(data.success){// Update the booking in the list\nsetBookings(prevBookings=>prevBookings.map(booking=>booking.id===selectedBooking.id?{...booking,datetime:newDateTime}:booking));toast.success(t('bookings.rescheduleSuccess'));}else{toast.error(data.message||t('bookings.rescheduleError'));}}catch(error){var _error$response3,_error$response3$data;console.error('Error rescheduling booking:',error);toast.error(((_error$response3=error.response)===null||_error$response3===void 0?void 0:(_error$response3$data=_error$response3.data)===null||_error$response3$data===void 0?void 0:_error$response3$data.message)||t('bookings.rescheduleError'));}finally{setRescheduling(false);setRescheduleDialogOpen(false);setDetailsDialogOpen(false);setSelectedDay(null);setAvailableTimesForDay([]);setRescheduleReason('');}};// Get status chip color\nconst getStatusColor=status=>{switch(status){case'scheduled':return'primary';case'completed':return'success';case'cancelled':return'error';case'issue_reported':return'warning';case'ongoing':return'info';default:return'default';}};// Get translated status text\nconst getStatusText=status=>{return t(`bookings.statusValues.${status}`,{defaultValue:status.charAt(0).toUpperCase()+status.slice(1)});};// Format booking date in student's timezone\nconst formatBookingDate=datetime=>{if(!studentProfile||!studentProfile.timezone){return format(new Date(datetime),'PPP',{locale:isRtl?ar:enUS});}const formattedDate=formatDateInStudentTimezone(datetime,studentProfile.timezone,'YYYY-MM-DD');return moment(formattedDate,'YYYY-MM-DD').format('MMMM D, YYYY');};// Format booking time in student's timezone\nconst formatBookingTime=datetime=>{if(!studentProfile||!studentProfile.timezone){return format(new Date(datetime),'p',{locale:isRtl?ar:enUS});}const formattedDateTime=formatDateInStudentTimezone(datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');return moment(formattedDateTime,'YYYY-MM-DD HH:mm:ss').format('h:mm A');};// Format booking time range (start - end) in student's timezone\nconst formatBookingTimeRange=(datetime,duration)=>{if(!studentProfile||!studentProfile.timezone){const startDate=new Date(datetime);const endDate=new Date(startDate.getTime()+duration*60000);const startTimeStr=startDate.toLocaleTimeString([],{hour:'2-digit',minute:'2-digit',hour12:false});const endTimeStr=endDate.toLocaleTimeString([],{hour:'2-digit',minute:'2-digit',hour12:false});return`${startTimeStr} - ${endTimeStr}`;}// Use student timezone for accurate time calculation\nconst formattedDateTime=formatDateInStudentTimezone(datetime,studentProfile.timezone,'YYYY-MM-DD HH:mm:ss');const[datePart,timePart]=formattedDateTime.split(' ');const[hours,minutes]=timePart.split(':');// Calculate start and end times\nconst startMinutes=parseInt(hours)*60+parseInt(minutes);const endMinutes=startMinutes+duration;const startHour=Math.floor(startMinutes/60);const startMin=startMinutes%60;const endHour=Math.floor(endMinutes/60);const endMin=endMinutes%60;const startTimeStr=`${String(startHour).padStart(2,'0')}:${String(startMin).padStart(2,'0')}`;const endTimeStr=`${String(endHour).padStart(2,'0')}:${String(endMin).padStart(2,'0')}`;return`${startTimeStr} - ${endTimeStr}`;};// Submit feedback after meeting\nconst handleFeedbackSubmit=async(meetingId,values)=>{try{console.log('Submitting feedback:',{meetingId,values});const response=await axios.post('/meeting-issues',{meeting_id:meetingId,booking_id:values.booking_id,issue_type:values.issue_type,description:values.description});console.log('Feedback submitted successfully:',response.data);// Update booking status locally for immediate UI feedback\nif(values.booking_id){const newStatus=values.issue_type==='no_issue'?'completed':'issue_reported';setBookings(prev=>prev.map(b=>b.id===values.booking_id?{...b,status:newStatus}:b));console.log(`Updated booking ${values.booking_id} status to ${newStatus}`);}// Close current feedback dialog\nsetFeedbackDialogOpen(false);setFeedbackMeeting(null);// Check for next pending feedback after a short delay\nsetTimeout(()=>{console.log('Checking for next pending feedback...');checkPendingFeedback();},1000);// Wait 1 second before checking for next feedback\n}catch(err){console.error('Failed to submit feedback',err);// Show error message to user\nalert('Failed to submit feedback. Please try again.');return;// Don't close dialog on error\n}};// Render booking cards\nconst renderBookings=()=>{if(bookings.length===0){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",children:t('bookings.noBookings')})});}return/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,children:bookings.map(booking=>/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Card,{elevation:3,sx:{height:'100%',display:'flex',flexDirection:'column',opacity:booking.teacher_deleted_at?0.8:1,border:booking.teacher_deleted_at?`2px solid ${theme.palette.error.light}`:'none',transition:'all 0.2s ease'},children:[/*#__PURE__*/_jsxs(Box,{sx:{bgcolor:'primary.main',color:'white',p:2,display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(CalendarIcon,{}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:formatBookingDate(booking.datetime)})]}),/*#__PURE__*/_jsxs(CardContent,{sx:{flexGrow:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Avatar,{src:booking.teacher_picture,alt:booking.teacher_name,sx:{mr:2,opacity:booking.teacher_deleted_at?0.6:1,filter:booking.teacher_deleted_at?'grayscale(100%)':'none'}}),/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{color:booking.teacher_deleted_at?'text.secondary':'text.primary',textDecoration:booking.teacher_deleted_at?'line-through':'none'},children:booking.teacher_name}),booking.teacher_deleted_at&&/*#__PURE__*/_jsx(Chip,{label:t('bookings.teacherDeleted','Teacher Account Deleted'),size:\"small\",color:\"error\",variant:\"outlined\",sx:{mt:0.5,fontSize:'0.7rem'}})]})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(TimeIcon,{sx:{mr:1,color:'text.secondary'}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:formatBookingTime(booking.datetime)})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(TimeIcon,{sx:{mr:1,color:'text.secondary'}}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[t('bookings.duration'),\": \",booking.duration||50,\" \",t('bookings.minutes'),booking.duration==='25'||booking.duration===25?` (${t('booking.halfLesson')||'نصف درس'})`:` (${t('booking.fullLesson')||'درس كامل'})`]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(EventIcon,{sx:{mr:1,color:'text.secondary'}}),/*#__PURE__*/_jsx(Chip,{label:getStatusText(booking.status),color:getStatusColor(booking.status),size:\"small\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{mt:2,display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontWeight:\"bold\",children:[t('bookings.price'),\": \",(()=>{if(booking.price_paid!==null&&!isNaN(booking.price_paid)){return booking.price_paid.toFixed(2);}const basePrice=parseFloat(booking.price_per_lesson||0);const isDurationHalf=booking.duration==='25'||booking.duration===25;const finalPrice=isDurationHalf?basePrice/2:basePrice;return finalPrice.toFixed(2);})(),\" $\"]}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",size:\"small\",onClick:()=>{setSelectedBooking(booking);setDetailsDialogOpen(true);},children:t('bookings.details')})]})]})]})},booking.id))});};// Details dialog\nconst renderDetailsDialog=()=>/*#__PURE__*/_jsxs(Dialog,{open:detailsDialogOpen,onClose:()=>setDetailsDialogOpen(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsxs(DialogTitle,{children:[t('bookings.bookingDetails'),/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:()=>setDetailsDialogOpen(false),sx:{position:'absolute',right:8,top:8},children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsx(DialogContent,{children:selectedBooking&&/*#__PURE__*/_jsxs(Box,{sx:{py:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Avatar,{src:selectedBooking.teacher_picture?selectedBooking.teacher_picture.startsWith('http')?selectedBooking.teacher_picture:`https://allemnionline.com${selectedBooking.teacher_picture}`:'',alt:selectedBooking.teacher_name,sx:{width:120,height:120,border:`3px solid ${selectedBooking.teacher_deleted_at?theme.palette.error.main:theme.palette.primary.main}`,mb:2,opacity:selectedBooking.teacher_deleted_at?0.6:1,filter:selectedBooking.teacher_deleted_at?'grayscale(100%)':'none'}}),/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,sx:{color:selectedBooking.teacher_deleted_at?'text.secondary':'text.primary',textDecoration:selectedBooking.teacher_deleted_at?'line-through':'none'},children:selectedBooking.teacher_name}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:t('bookings.teacher')}),selectedBooking.teacher_deleted_at&&/*#__PURE__*/_jsx(Chip,{label:t('bookings.teacherDeleted','Teacher Account Deleted'),size:\"small\",color:\"error\",variant:\"filled\",sx:{mt:1}})]})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.date')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",gutterBottom:true,children:formatBookingDate(selectedBooking.datetime)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.time')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",gutterBottom:true,children:formatBookingTime(selectedBooking.datetime)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:1},children:formatBookingTimeRange(selectedBooking.datetime,parseInt(selectedBooking.duration)||50)})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.duration')}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",gutterBottom:true,children:[selectedBooking.duration||50,\" \",t('bookings.minutes'),selectedBooking.duration==='25'||selectedBooking.duration===25?` (${t('booking.halfLesson')})`:` (${t('booking.fullLesson')})`]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.status.title')}),/*#__PURE__*/_jsx(Chip,{label:getStatusText(selectedBooking.status),color:getStatusColor(selectedBooking.status),size:\"small\"})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",gutterBottom:true,children:t('bookings.price')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",color:\"primary\",children:[(()=>{if(selectedBooking.price_paid!==null&&!isNaN(selectedBooking.price_paid)){return selectedBooking.price_paid.toFixed(2);}const basePrice=parseFloat(selectedBooking.price_per_lesson||0);const isDurationHalf=selectedBooking.duration==='25'||selectedBooking.duration===25;const finalPrice=isDurationHalf?basePrice/2:basePrice;return finalPrice.toFixed(2);})(),\" \",t('common.currency')]})]})]})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDetailsDialogOpen(false),children:t('common.close')}),selectedBooking&&/*#__PURE__*/_jsx(Button,{onClick:()=>canJoinMeeting(selectedBooking)&&handleJoinMeeting(selectedBooking),color:canJoinMeeting(selectedBooking)?\"success\":\"inherit\",variant:canJoinMeeting(selectedBooking)?\"contained\":\"outlined\",startIcon:/*#__PURE__*/_jsx(VideoCallIcon,{}),disabled:!canJoinMeeting(selectedBooking),sx:{mr:1,...(canJoinMeeting(selectedBooking)?{}:{color:theme.palette.grey[500],borderColor:theme.palette.grey[300],backgroundColor:theme.palette.grey[100],'&:hover':{backgroundColor:theme.palette.grey[200],borderColor:theme.palette.grey[400]}})},children:getMeetingStatusText(selectedBooking)}),selectedBooking&&canStudentReschedule(selectedBooking)&&/*#__PURE__*/_jsx(Button,{onClick:()=>{setDetailsDialogOpen(false);handleRescheduleBookingClick(selectedBooking);},color:\"primary\",variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(RescheduleIcon,{}),sx:{mr:1},children:t('bookings.reschedule')}),(selectedBooking===null||selectedBooking===void 0?void 0:selectedBooking.status)==='scheduled'&&!(selectedBooking!==null&&selectedBooking!==void 0&&selectedBooking.teacher_deleted_at)&&/*#__PURE__*/_jsx(Button,{onClick:()=>{setDetailsDialogOpen(false);setCancelDialogOpen(true);},color:\"error\",variant:\"contained\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),children:t('bookings.cancel')}),selectedBooking&&selectedBooking.status==='scheduled'&&!selectedBooking.teacher_deleted_at&&!canStudentReschedule(selectedBooking)&&/*#__PURE__*/_jsx(Box,{sx:{mt:2,p:2,bgcolor:'warning.light',borderRadius:1},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"warning.dark\",children:[\"\\u26A0\\uFE0F \",(()=>{const rescheduleCount=parseInt(selectedBooking.reschedule_count)||0;const timeRemaining=calculateTimeRemaining(selectedBooking.datetime);if(rescheduleCount>=1){return t('bookings.rescheduleAlreadyUsed','You have already rescheduled this booking once. Students can only reschedule each booking once.');}else if(timeRemaining.hours<12&&!timeRemaining.isOverdue){return t('bookings.rescheduleTimeLimit','You can only reschedule bookings at least 12 hours before the lesson time.');}else if(timeRemaining.isOverdue){return t('bookings.rescheduleTimeExpired','This lesson time has already passed.');}return'';})()]})}),(selectedBooking===null||selectedBooking===void 0?void 0:selectedBooking.teacher_deleted_at)&&(selectedBooking===null||selectedBooking===void 0?void 0:selectedBooking.status)==='scheduled'&&/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mt:2,mx:2},children:t('bookings.teacherDeletedMessage','This booking cannot be modified because the teacher account has been deleted. The amount will be automatically refunded to your wallet.')})]})]});// Cancel confirmation dialog\nconst renderCancelDialog=()=>/*#__PURE__*/_jsxs(Dialog,{open:cancelDialogOpen,onClose:()=>setCancelDialogOpen(false),children:[/*#__PURE__*/_jsxs(DialogTitle,{children:[t('bookings.confirmCancel'),/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:()=>setCancelDialogOpen(false),sx:{position:'absolute',right:8,top:8},children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2},children:t('bookings.cancelWarning')}),/*#__PURE__*/_jsx(Alert,{severity:\"warning\",sx:{mb:2},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:t('bookings.lateCancellationPolicy')})}),selectedBooking&&/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.teacher'),\":\"]}),\" \",selectedBooking.teacher_name]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.date'),\":\"]}),\" \",formatBookingDate(selectedBooking.datetime)]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.time'),\":\"]}),\" \",formatBookingTime(selectedBooking.datetime)]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",gutterBottom:true,color:\"text.secondary\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.timeRange'),\":\"]}),\" \",formatBookingTimeRange(selectedBooking.datetime,parseInt(selectedBooking.duration)||50)]}),(()=>{const timeRemaining=calculateTimeRemaining(selectedBooking.datetime);const isLateCancel=timeRemaining.hours<12&&!timeRemaining.isOverdue;return/*#__PURE__*/_jsxs(Box,{sx:{mt:2,p:2,borderRadius:1,bgcolor:isLateCancel?'warning.light':'info.light'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{fontWeight:'bold',color:isLateCancel?'warning.dark':'info.dark'},children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('bookings.timeRemaining'),\":\"]}),\" \",' ',timeRemaining.isOverdue?t('bookings.lessonOverdue'):`${timeRemaining.hours} ${t('bookings.hours')} ${timeRemaining.minutes} ${t('bookings.minutes')}`]}),isLateCancel&&/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{display:'block',mt:1,color:'warning.dark'},children:[\"\\u26A0\\uFE0F \",t('bookings.lateCancelWarning')]})]});})()]}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,multiline:true,rows:3,label:t('bookings.cancellationReason'),placeholder:t('bookings.cancellationReasonPlaceholder'),value:cancellationReason,onChange:e=>setCancellationReason(e.target.value),sx:{mt:2}})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setCancelDialogOpen(false),children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{onClick:handleCancelBooking,color:\"error\",variant:\"contained\",disabled:cancellingBooking,children:cancellingBooking?t('bookings.cancelling'):t('bookings.confirmCancelButton')})]})]});return/*#__PURE__*/_jsxs(Layout,{children:[/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",sx:{py:4},children:/*#__PURE__*/_jsxs(ProfileCompletionAlert,{exemptPages:['/student/complete-profile','/student/dashboard'],children:[/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{p:3,mb:4,bgcolor:'primary.main',color:'white'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',flexWrap:'wrap',gap:2},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",gutterBottom:true,sx:{fontWeight:'bold'},children:t('bookings.weeklyTitle')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{opacity:0.9},children:t('bookings.weeklyDescription')})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'right'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{opacity:0.8,mb:0.5},children:t('booking.weekNavigation')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:'bold'},children:[\"\\uD83D\\uDCC5 \",format(currentWeekStart,'MMM d',{locale:isRtl?ar:enUS}),\" - \",format(addDays(currentWeekStart,6),'MMM d, yyyy',{locale:isRtl?ar:enUS})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",sx:{opacity:0.8},children:[studentProfile!==null&&studentProfile!==void 0&&studentProfile.timezone?moment(formatDateInStudentTimezone(new Date().toISOString(),studentProfile.timezone,'YYYY-MM-DD HH:mm:ss'),'YYYY-MM-DD HH:mm:ss').format('h:mm A'):format(currentTime,'p',{locale:isRtl?ar:enUS}),(studentProfile===null||studentProfile===void 0?void 0:studentProfile.timezone)&&` (${studentProfile.timezone})`]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1},children:[/*#__PURE__*/_jsx(Tooltip,{title:t('booking.previousWeek'),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:goToPreviousWeek,disabled:isPreviousWeekDisabled(),sx:{color:'white',bgcolor:'rgba(255, 255, 255, 0.1)','&:hover':{bgcolor:'rgba(255, 255, 255, 0.2)'},'&:disabled':{color:'rgba(255, 255, 255, 0.3)',bgcolor:'rgba(255, 255, 255, 0.05)'}},children:/*#__PURE__*/_jsx(ChevronLeftIcon,{})})})}),/*#__PURE__*/_jsx(Tooltip,{title:t('booking.nextWeek'),children:/*#__PURE__*/_jsx(\"span\",{children:/*#__PURE__*/_jsx(IconButton,{onClick:goToNextWeek,disabled:isNextWeekDisabled(),sx:{color:'white',bgcolor:'rgba(255, 255, 255, 0.1)','&:hover':{bgcolor:'rgba(255, 255, 255, 0.2)'},'&:disabled':{color:'rgba(255, 255, 255, 0.3)',bgcolor:'rgba(255, 255, 255, 0.05)'}},children:/*#__PURE__*/_jsx(ChevronRightIcon,{})})})})]})]})]})}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',py:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}):error?/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:4},children:error}):/*#__PURE__*/_jsx(WeeklyBookingsTable,{bookings:bookings,loading:loading,currentWeekStart:currentWeekStart,daysOfWeek:daysOfWeek,onViewDetails:handleViewDetails,onCancelBooking:handleCancelBookingClick,studentProfile:studentProfile,formatBookingTime:formatBookingTime,getStatusColor:getStatusColor}),renderDetailsDialog(),renderCancelDialog()]})}),/*#__PURE__*/_jsx(Dialog,{fullScreen:true,open:openMeeting,onClose:handleCloseMeeting,children:currentMeeting&&/*#__PURE__*/_jsx(VideoSDKMeeting,{roomId:currentMeeting.room_name,meetingId:currentMeeting.meeting_id,meetingData:{...currentMeeting,meeting_date:currentMeeting.datetime,duration:parseInt(currentMeeting.duration)||50},onClose:handleCloseMeeting})}),/*#__PURE__*/_jsx(RescheduleDialog,{open:rescheduleDialogOpen,onClose:()=>setRescheduleDialogOpen(false),booking:selectedBooking,availableDays:availableDays,onReschedule:handleRescheduleConfirm,loading:rescheduling,userProfile:studentProfile,isTeacherView:false}),/*#__PURE__*/_jsx(MeetingFeedbackDialog,{open:feedbackDialogOpen,meeting:feedbackMeeting,timezone:(studentProfile===null||studentProfile===void 0?void 0:studentProfile.timezone)||null,onSubmit:handleFeedbackSubmit,onClose:()=>{setFeedbackDialogOpen(false);setFeedbackMeeting(null);}})]});};export default Bookings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "Container", "Typography", "Box", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "CircularProgress", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "IconButton", "Divider", "Avatar", "useTheme", "<PERSON><PERSON><PERSON>", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "CalendarMonth", "CalendarIcon", "AccessTime", "TimeIcon", "Person", "PersonIcon", "Cancel", "CancelIcon", "Close", "CloseIcon", "Event", "EventIcon", "VideoCall", "VideoCallIcon", "ChevronLeft", "ChevronLeftIcon", "ChevronRight", "ChevronRightIcon", "Schedule", "RescheduleIcon", "format", "addDays", "startOfWeek", "addWeeks", "subWeeks", "ar", "enUS", "axios", "dayjs", "utc", "useAuth", "Layout", "WeeklyBookingsTable", "RescheduleDialog", "convertFromDatabaseTime", "formatDateInStudentTimezone", "getCurrentTimeInTimezone", "moment", "ProfileCompletionAlert", "VideoSDKMeeting", "MeetingFeedbackDialog", "toast", "jsx", "_jsx", "jsxs", "_jsxs", "extend", "Bookings", "t", "i18n", "token", "theme", "isRtl", "language", "bookings", "setBookings", "loading", "setLoading", "error", "setError", "selectedBooking", "setSelectedBooking", "cancelDialogOpen", "setCancelDialogOpen", "detailsDialogOpen", "setDetailsDialogOpen", "cancellingBooking", "setCancellingBooking", "cancellationReason", "setCancellationReason", "calculateTimeRemaining", "lessonDateTime", "now", "Date", "lessonTime", "timeDifference", "getTime", "hours", "minutes", "isOverdue", "Math", "floor", "canStudentReschedule", "booking", "status", "teacher_deleted_at", "rescheduleCount", "parseInt", "reschedule_count", "timeRemaining", "datetime", "console", "log", "bookingId", "id", "bookingDateTime", "currentTime", "toISOString", "canReschedule", "studentProfile", "setStudentProfile", "currentWeekStart", "setCurrentWeekStart", "today", "weekStartsOn", "openMeeting", "setOpenMeeting", "currentMeeting", "setCurrentMeeting", "setCurrentTime", "feedbackDialogOpen", "setFeedbackDialogOpen", "feedbackMeeting", "setFeedbackMeeting", "rescheduleDialogOpen", "setRescheduleDialogOpen", "availableDays", "setAvailableDays", "availableTimesForDay", "setAvailableTimesForDay", "selected<PERSON>ay", "setSelectedDay", "loadingDays", "setLoadingDays", "loadingTimes", "setLoadingTimes", "rescheduling", "setRescheduling", "rescheduleReason", "setRescheduleReason", "daysOfWeek", "goToPreviousWeek", "previousWeek", "goToNextWeek", "nextWeek", "oneYearAhead", "maxWeek", "isPreviousWeekDisabled", "isNextWeekDisabled", "fetchStudentProfile", "data", "get", "headers", "success", "profile", "checkPendingFeedback", "issue", "shouldOpen", "isAfter", "add", "duration", "err", "interval", "setInterval", "handleFocus", "window", "addEventListener", "clearInterval", "removeEventListener", "booking_id", "length", "targetTime", "candidates", "filter", "b", "teacher_name", "bTime", "timeDiff", "abs", "diff", "sort", "a", "aDiff", "bDiff", "bestMatch", "meeting_id", "prev", "warn", "fetchBookings", "processedBookings", "map", "price_per_lesson", "parseFloat", "price_paid", "undefined", "String", "message", "fetchWithRetry", "maxRetries", "arguments", "retries", "Promise", "resolve", "setTimeout", "timeInterval", "handleViewDetails", "handleCancelBookingClick", "handleJoinMeeting", "room_name", "response", "handleCloseMeeting", "meetingEnd", "setMinutes", "getMinutes", "correspondingBooking", "find", "post", "issue_type", "description", "catch", "getCurrentTimeInStudentTimezone", "timezone", "getMeetingDateInStudentTimezone", "getMeetingStatus", "canJoinMeeting", "currentStatus", "meetingStartTime", "meetingEndTime", "getMeetingStatusText", "canJoin", "handleCancelBooking", "put", "cancellation_reason", "prevBookings", "fullRefundToStudent", "commissionPaid<PERSON><PERSON><PERSON><PERSON><PERSON>", "icon", "style", "background", "color", "_error$response", "_error$response$data", "handleRescheduleBookingClick", "queryParams", "URLSearchParams", "append", "toString", "validSlots", "total", "note", "slotsByDate", "for<PERSON>ach", "slot", "date", "push", "displayTime", "time", "toLocaleTimeString", "hour", "minute", "hour12", "Object", "keys", "slots", "date<PERSON><PERSON>j", "day<PERSON><PERSON>", "toLocaleDateString", "weekday", "formattedDate", "month", "day", "availableCount", "availableSlots", "_error$response2", "_error$response2$data", "handleRescheduleConfirm", "timeSlot", "reason", "newDateTime", "newDuration", "reschedule_reason", "_error$response3", "_error$response3$data", "getStatusColor", "getStatusText", "defaultValue", "char<PERSON>t", "toUpperCase", "slice", "formatBookingDate", "locale", "formatBookingTime", "formattedDateTime", "formatBookingTimeRange", "startDate", "endDate", "startTimeStr", "endTimeStr", "datePart", "timePart", "split", "startMinutes", "endMinutes", "startHour", "startMin", "endHour", "endMin", "padStart", "handleFeedbackSubmit", "meetingId", "values", "newStatus", "alert", "renderBookings", "sx", "textAlign", "py", "children", "variant", "container", "spacing", "item", "xs", "sm", "md", "elevation", "height", "display", "flexDirection", "opacity", "border", "palette", "light", "transition", "bgcolor", "p", "alignItems", "gap", "flexGrow", "mb", "src", "teacher_picture", "alt", "mr", "flex", "textDecoration", "label", "size", "mt", "fontSize", "my", "justifyContent", "fontWeight", "isNaN", "toFixed", "basePrice", "isDurationHalf", "finalPrice", "onClick", "renderDetailsDialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "position", "right", "top", "startsWith", "width", "main", "primary", "gutterBottom", "startIcon", "disabled", "grey", "borderColor", "backgroundColor", "borderRadius", "severity", "mx", "renderCancelDialog", "isLateCancel", "multiline", "rows", "placeholder", "value", "onChange", "e", "target", "exemptPages", "flexWrap", "title", "onViewDetails", "onCancelBooking", "fullScreen", "roomId", "meetingData", "meeting_date", "onReschedule", "userProfile", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meeting", "onSubmit"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/student/Bookings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport {\n  Container,\n  Typography,\n  Box,\n  Paper,\n  Grid,\n  Card,\n  CardContent,\n  Button,\n  Chip,\n  CircularProgress,\n  Alert,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  IconButton,\n  Divider,\n  Avatar,\n  useTheme,\n  Tooltip,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem\n} from '@mui/material';\nimport {\n  CalendarMonth as CalendarIcon,\n  AccessTime as TimeIcon,\n  Person as PersonIcon,\n  Cancel as CancelIcon,\n  Close as CloseIcon,\n  Event as EventIcon,\n  VideoCall as VideoCallIcon,\n  ChevronLeft as ChevronLeftIcon,\n  ChevronRight as ChevronRightIcon,\n  Schedule as RescheduleIcon\n} from '@mui/icons-material';\nimport { format, addDays, startOfWeek, addWeeks, subWeeks } from 'date-fns';\nimport { ar, enUS } from 'date-fns/locale';\nimport axios from '../../utils/axios';\nimport dayjs from 'dayjs';\nimport utc from 'dayjs/plugin/utc';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Layout from '../../components/Layout';\nimport WeeklyBookingsTable from '../../components/WeeklyBookingsTable';\nimport RescheduleDialog from '../../components/RescheduleDialog';\nimport { convertFromDatabaseTime, formatDateInStudentTimezone, getCurrentTimeInTimezone } from '../../utils/timezone';\nimport moment from 'moment-timezone';\nimport ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';\nimport VideoSDKMeeting from '../../components/meeting/VideoSDKMeeting';\nimport MeetingFeedbackDialog from '../../components/MeetingFeedbackDialog';\nimport { toast } from 'react-hot-toast';\n\ndayjs.extend(utc);\n\nconst Bookings = () => {\n  const { t, i18n } = useTranslation();\n  const { token } = useAuth();\n  const theme = useTheme();\n  const isRtl = i18n.language === 'ar';\n\n  const [bookings, setBookings] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedBooking, setSelectedBooking] = useState(null);\n  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);\n  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);\n  const [cancellingBooking, setCancellingBooking] = useState(false);\n  const [cancellationReason, setCancellationReason] = useState('');\n\n  // Function to calculate time remaining until lesson\n  const calculateTimeRemaining = (lessonDateTime) => {\n    const now = new Date();\n    const lessonTime = new Date(lessonDateTime);\n    const timeDifference = lessonTime.getTime() - now.getTime();\n\n    if (timeDifference <= 0) {\n      return { hours: 0, minutes: 0, isOverdue: true };\n    }\n\n    const hours = Math.floor(timeDifference / (1000 * 60 * 60));\n    const minutes = Math.floor((timeDifference % (1000 * 60 * 60)) / (1000 * 60));\n\n    return { hours, minutes, isOverdue: false };\n  };\n\n  // Function to check if booking can be rescheduled by student\n  const canStudentReschedule = (booking) => {\n    // Check if booking exists\n    if (!booking) return false;\n\n    // Check if booking is scheduled\n    if (booking.status !== 'scheduled') return false;\n\n    // Check if teacher is not deleted\n    if (booking.teacher_deleted_at) return false;\n\n    // Check if student has already rescheduled this booking\n    const rescheduleCount = parseInt(booking.reschedule_count) || 0;\n    if (rescheduleCount >= 1) return false;\n\n    // Check if reschedule is at least 12 hours before booking time\n    const timeRemaining = calculateTimeRemaining(booking.datetime);\n\n    // Debug log\n    console.log('🔍 Reschedule check:', {\n      bookingId: booking.id,\n      bookingDateTime: booking.datetime,\n      currentTime: new Date().toISOString(),\n      timeRemaining: timeRemaining,\n      canReschedule: !timeRemaining.isOverdue && timeRemaining.hours >= 12\n    });\n\n    if (timeRemaining.isOverdue || timeRemaining.hours < 12) return false;\n\n    return true;\n  };\n  const [studentProfile, setStudentProfile] = useState(null);\n  const [currentWeekStart, setCurrentWeekStart] = useState(() => {\n    const today = new Date();\n    return startOfWeek(today, { weekStartsOn: 1 }); // Start from current week\n  });\n  const [openMeeting, setOpenMeeting] = useState(false);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);\n  const [feedbackMeeting, setFeedbackMeeting] = useState(null);\n\n  // Reschedule states\n  const [rescheduleDialogOpen, setRescheduleDialogOpen] = useState(false);\n  const [availableDays, setAvailableDays] = useState([]);\n  const [availableTimesForDay, setAvailableTimesForDay] = useState([]);\n  const [selectedDay, setSelectedDay] = useState(null);\n  const [loadingDays, setLoadingDays] = useState(false);\n  const [loadingTimes, setLoadingTimes] = useState(false);\n  const [rescheduling, setRescheduling] = useState(false);\n  const [rescheduleReason, setRescheduleReason] = useState('');\n\n  // Days of the week\n  const daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];\n\n  // Week navigation functions\n  const goToPreviousWeek = () => {\n    const previousWeek = subWeeks(currentWeekStart, 1);\n    setCurrentWeekStart(previousWeek);\n  };\n\n  const goToNextWeek = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n\n    // Don't allow going beyond one year ahead\n    if (nextWeek <= maxWeek) {\n      setCurrentWeekStart(nextWeek);\n    }\n  };\n\n  // Check if navigation buttons should be disabled\n  const isPreviousWeekDisabled = () => false;\n\n  const isNextWeekDisabled = () => {\n    const nextWeek = addWeeks(currentWeekStart, 1);\n    const today = new Date();\n    const oneYearAhead = addWeeks(today, 52); // One year ahead from today\n    const maxWeek = startOfWeek(oneYearAhead, { weekStartsOn: 1 });\n    return nextWeek > maxWeek;\n  };\n\n  // Fetch student profile\n  useEffect(() => {\n    const fetchStudentProfile = async () => {\n      if (!token) return;\n\n      try {\n        const { data } = await axios.get('/api/students/profile', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success && data.profile) {\n          setStudentProfile(data.profile);\n        }\n      } catch (error) {\n        console.error('Error fetching student profile:', error);\n      }\n    };\n\n    fetchStudentProfile();\n  }, [token]);\n\n  // Helper function to check for pending feedback\n  const checkPendingFeedback = async () => {\n    try {\n      console.log('Checking for pending feedback in Bookings page...');\n      const { data } = await axios.get('/meeting-issues/pending');\n      if (data.success && data.data) {\n        const issue = data.data;\n        const shouldOpen = dayjs.utc().isAfter(dayjs.utc(issue.datetime).add(issue.duration || 50, 'minute'));\n        console.log('Found pending feedback:', issue, 'Should open:', shouldOpen);\n        if (shouldOpen) {\n          setFeedbackMeeting(issue);\n          setFeedbackDialogOpen(true);\n          return true; // Found and opened feedback\n        }\n      } else {\n        console.log('No pending feedback found in Bookings page');\n      }\n      return false; // No feedback found or not ready to open\n    } catch (err) {\n      console.error('Error checking pending feedback', err);\n      return false;\n    }\n  };\n\n  // Check for pending feedback on mount and periodically\n  useEffect(() => {\n    // Check immediately on mount\n    checkPendingFeedback();\n\n    // Set up periodic checking every 30 seconds while on bookings page\n    const interval = setInterval(checkPendingFeedback, 30000);\n\n    // Also check when window gains focus (user returns to tab)\n    const handleFocus = () => {\n      console.log('Window focused, checking for pending feedback...');\n      checkPendingFeedback();\n    };\n\n    window.addEventListener('focus', handleFocus);\n\n    // Cleanup interval and event listener on unmount\n    return () => {\n      clearInterval(interval);\n      window.removeEventListener('focus', handleFocus);\n    };\n  }, []);\n\n  // Attach booking_id to feedback meeting if missing\n  useEffect(() => {\n    if (!feedbackMeeting || feedbackMeeting.booking_id) return;\n    if (!bookings.length) return;\n\n    const targetTime = moment(feedbackMeeting.datetime);\n    // find booking with same teacher and date (same day) within 2 hours window\n    const candidates = bookings.filter(b => {\n      if (b.teacher_name !== feedbackMeeting.teacher_name) return false;\n      const bTime = moment(b.datetime);\n      const timeDiff = Math.abs(bTime.diff(targetTime, 'minutes'));\n      return timeDiff <= 120;\n    });\n\n    // Sort by closest time match\n    candidates.sort((a, b) => {\n      const aDiff = Math.abs(moment(a.datetime).diff(targetTime, 'minutes'));\n      const bDiff = Math.abs(moment(b.datetime).diff(targetTime, 'minutes'));\n      return aDiff - bDiff;\n    });\n\n    if (candidates.length > 0) {\n      const bestMatch = candidates[0];\n      console.log(`Linking feedback to booking: Meeting ${feedbackMeeting.meeting_id} -> Booking ${bestMatch.id}`);\n      setFeedbackMeeting(prev => ({ ...prev, booking_id: bestMatch.id }));\n    } else {\n      console.warn(`No matching booking found for meeting ${feedbackMeeting.meeting_id} with teacher ${feedbackMeeting.teacher_name}`);\n    }\n  }, [bookings, feedbackMeeting]);\n\n  // Cleanup feedback state when leaving the page\n  useEffect(() => {\n    return () => {\n      // Clear any pending feedback when leaving bookings page\n      // This allows global feedback system to take over on other pages\n      console.log('Cleaning up bookings page feedback state');\n      setFeedbackDialogOpen(false);\n      setFeedbackMeeting(null);\n    };\n  }, []);\n\n  // Fetch bookings with retry logic\n  useEffect(() => {\n    const fetchBookings = async () => {\n      try {\n        setLoading(true);\n        const { data } = await axios.get('/bookings/student', {\n          headers: {\n            'Authorization': `Bearer ${token}`\n          }\n        });\n\n        if (data.success) {\n          console.log('Bookings data:', data.data);\n\n          // Make sure all bookings have the correct data types\n          const processedBookings = data.data.map(booking => ({\n            ...booking,\n            price_per_lesson: parseFloat(booking.price_per_lesson || 0),\n            price_paid: booking.price_paid !== undefined ? parseFloat(booking.price_paid) : null,\n            duration: booking.duration ? String(booking.duration) : '50'\n          }));\n\n          console.log('Processed bookings:', processedBookings);\n          setBookings(processedBookings);\n        } else {\n          setError(data.message || t('bookings.fetchError'));\n        }\n      } catch (error) {\n        console.error('Error fetching bookings:', error);\n        setError(t('bookings.fetchError'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    const fetchWithRetry = async (maxRetries = 3) => {\n      let retries = 0;\n      while (retries < maxRetries) {\n        try {\n          await fetchBookings();\n          break;\n        } catch (error) {\n          retries++;\n          if (retries === maxRetries) {\n            throw error;\n          }\n          await new Promise(resolve => setTimeout(resolve, 1000)); // Wait before retry\n        }\n      }\n    };\n\n    if (token) {\n      fetchWithRetry();\n    }\n\n    // Update current time every second\n    const timeInterval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n\n    return () => clearInterval(timeInterval);\n  }, [token, t]);\n\n  // Handle view details\n  const handleViewDetails = (booking) => {\n    setSelectedBooking(booking);\n    setDetailsDialogOpen(true);\n  };\n\n  // Handle cancel booking\n  const handleCancelBookingClick = (booking) => {\n    setSelectedBooking(booking);\n    setCancelDialogOpen(true);\n  };\n\n  // Handle join meeting\n  const handleJoinMeeting = async (booking) => {\n    try {\n      // Check if room_name exists from the booking data\n      if (!booking.room_name) {\n        console.error('No room_name found for booking:', booking);\n        toast.error(t('meetings.noRoomError') || 'Meeting room not found');\n        return;\n      }\n\n      // Check if meeting_id exists\n      if (!booking.meeting_id) {\n        console.error('No meeting_id found for booking:', booking);\n        toast.error(t('meetings.noMeetingError') || 'Meeting ID not found');\n        return;\n      }\n\n      console.log('Joining meeting with data:', {\n        room_name: booking.room_name,\n        meeting_id: booking.meeting_id,\n        datetime: booking.datetime,\n        duration: booking.duration\n      });\n\n      // التحقق من صلاحية الغرفة\n      const response = await axios.get(`/meetings/${booking.room_name}/validate`);\n      setCurrentMeeting({ ...booking, room_name: booking.room_name });\n      setOpenMeeting(true);\n    } catch (error) {\n      console.error('Error joining meeting:', error);\n      toast.error(t('meetings.joinError'));\n    }\n  };\n\n  const handleCloseMeeting = () => {\n    // After meeting dialog closes, prompt for feedback\n    if (currentMeeting) {\n      const meetingEnd = new Date(currentMeeting.datetime);\n      meetingEnd.setMinutes(meetingEnd.getMinutes() + (currentMeeting.duration || 50));\n      const now = new Date();\n      // Only prompt if meeting time has actually ended\n      if (now >= meetingEnd) {\n        setFeedbackMeeting(currentMeeting);\n\n        // Find the corresponding booking for this meeting\n        const targetTime = moment(currentMeeting.datetime);\n        const correspondingBooking = bookings.find(b => {\n          if (b.teacher_name !== currentMeeting.teacher_name) return false;\n          const bTime = moment(b.datetime);\n          return Math.abs(bTime.diff(targetTime, 'minutes')) <= 120;\n        });\n\n        // Send pending status immediately with correct booking_id\n        axios.post('/meeting-issues', {\n          booking_id: correspondingBooking ? correspondingBooking.id : null,\n          meeting_id: currentMeeting.meeting_id || currentMeeting.id,\n          issue_type: 'pending',\n          description: ''\n        }).catch(err => console.error('Failed to create pending issue', err));\n        setFeedbackDialogOpen(true);\n      }\n    }\n    setOpenMeeting(false);\n    setCurrentMeeting(null);\n  };\n\n  // Get current time in student's timezone (same method as meetings page)\n  const getCurrentTimeInStudentTimezone = () => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return new Date();\n    }\n    return getCurrentTimeInTimezone(studentProfile.timezone);\n  };\n\n  // Get meeting date in student timezone (same method as meetings page)\n  const getMeetingDateInStudentTimezone = (datetime) => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return new Date(datetime);\n    }\n    return convertFromDatabaseTime(datetime, studentProfile.timezone);\n  };\n\n  // Get meeting status from database directly\n  const getMeetingStatus = (booking) => {\n    return booking.status || 'scheduled';\n  };\n\n  // Check if user can join meeting (same method as meetings page)\n  const canJoinMeeting = (booking) => {\n    if (!booking || !studentProfile) return false;\n\n    // Cannot join if teacher is deleted\n    if (booking.teacher_deleted_at) return false;\n\n    const currentStatus = getMeetingStatus(booking);\n    if (currentStatus === 'cancelled' || currentStatus === 'cancelled_teacher_deleted' || currentStatus === 'completed') {\n      return false;\n    }\n\n    const meetingStartTime = getMeetingDateInStudentTimezone(booking.datetime);\n    const meetingEndTime = new Date(meetingStartTime);\n    meetingEndTime.setMinutes(meetingEndTime.getMinutes() + (booking.duration || 50));\n    const now = getCurrentTimeInStudentTimezone();\n\n    return now >= meetingStartTime && now < meetingEndTime;\n  };\n\n  // Get meeting status text (same method as meetings page)\n  const getMeetingStatusText = (booking) => {\n    if (!booking || !studentProfile) return t('meetings.notStarted');\n\n    // Special message for deleted teachers\n    if (booking.teacher_deleted_at) {\n      return t('meetings.teacherDeleted', 'Teacher Unavailable');\n    }\n\n    const currentStatus = getMeetingStatus(booking);\n    const canJoin = canJoinMeeting(booking);\n\n    if (canJoin) {\n      return t('meetings.join');\n    }\n\n    switch (currentStatus) {\n      case 'cancelled':\n        return t('meetings.status.cancelled');\n      case 'completed':\n        return t('meetings.status.completed');\n      case 'ongoing':\n        return t('meetings.join');\n      case 'scheduled':\n      default:\n        return t('meetings.notStarted');\n    }\n  };\n\n  // Handle booking cancellation\n  const handleCancelBooking = async () => {\n    if (!selectedBooking) return;\n\n    try {\n      setCancellingBooking(true);\n      const { data } = await axios.put(`/bookings/${selectedBooking.id}/cancel`, {\n        cancellation_reason: cancellationReason\n      }, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        // Update the booking status in the local state\n        setBookings(prevBookings =>\n          prevBookings.map(booking =>\n            booking.id === selectedBooking.id\n              ? { ...booking, status: 'cancelled' }\n              : booking\n          )\n        );\n\n        // Show appropriate success message based on the response\n        if (data.fullRefundToStudent) {\n          toast.success(t('bookings.cancelSuccess'));\n        } else if (data.commissionPaidToTeacher) {\n          toast(t('bookings.commissionPaidToTeacher'), {\n            icon: '⚠️',\n            style: {\n              background: '#ff9800',\n              color: 'white',\n            },\n          });\n        } else {\n          toast.success(data.message || t('bookings.cancelSuccess'));\n        }\n      } else {\n        toast.error(data.message || t('bookings.cancelError'));\n      }\n    } catch (error) {\n      console.error('Error cancelling booking:', error);\n      toast.error(error.response?.data?.message || t('bookings.cancelError'));\n    } finally {\n      setCancellingBooking(false);\n      setCancelDialogOpen(false);\n      setDetailsDialogOpen(false);\n      setSelectedBooking(null);\n      setCancellationReason('');\n    }\n  };\n\n  // Handle reschedule booking\n  const handleRescheduleBookingClick = async (booking) => {\n    setSelectedBooking(booking);\n    setSelectedDay(null);\n    setAvailableTimesForDay([]);\n    setRescheduleReason('');\n    setLoadingDays(true);\n    setRescheduleDialogOpen(true);\n\n    try {\n      // Get available slots for this booking using same API as booking page\n      const queryParams = new URLSearchParams();\n      if (studentProfile?.timezone) {\n        queryParams.append('student_timezone', studentProfile.timezone);\n      }\n\n      const { data } = await axios.get(`/bookings/${booking.id}/available-slots?${queryParams.toString()}`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        console.log('🔍 Student reschedule - API Response:', data);\n\n        // لا نقوم بتصفية الأوقات هنا - سنتركها لنافذة RescheduleDialog\n        // لتجنب التصفية المضاعفة\n        const validSlots = data.data;\n\n        console.log('🔍 All slots from API (no filtering here):', {\n          total: data.data.length,\n          note: 'Filtering will be done in RescheduleDialog'\n        });\n\n        // Group slots by date\n        const slotsByDate = {};\n        validSlots.forEach(slot => {\n          const date = slot.date;\n          if (!slotsByDate[date]) {\n            slotsByDate[date] = [];\n          }\n          slotsByDate[date].push({\n            ...slot,\n            // Ensure we have the display time\n            displayTime: slot.time || new Date(slot.datetime).toLocaleTimeString('en-US', {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: true\n            })\n          });\n        });\n\n        // Convert to the format expected by the UI\n        const availableDays = Object.keys(slotsByDate)\n          .filter(date => slotsByDate[date].length > 0) // Only include dates with valid slots\n          .map(date => {\n            const slots = slotsByDate[date];\n            const dateObj = new Date(date);\n\n            return {\n              date: date,\n              dayName: dateObj.toLocaleDateString('en-US', { weekday: 'long' }),\n              formattedDate: dateObj.toLocaleDateString('en-US', {\n                month: 'short',\n                day: 'numeric'\n              }),\n              slots: slots,\n              availableCount: slots.length,\n              availableSlots: slots.length\n            };\n          });\n\n        console.log('🔍 Available days for student:', availableDays);\n        setAvailableDays(availableDays);\n      } else {\n        console.error('❌ API returned error:', data);\n        toast.error(data.message || t('bookings.fetchDaysError'));\n      }\n    } catch (error) {\n      console.error('Error fetching available slots:', error);\n      toast.error(error.response?.data?.message || t('bookings.fetchDaysError'));\n    } finally {\n      setLoadingDays(false);\n    }\n  };\n\n\n\n  // Handle reschedule confirmation\n  const handleRescheduleConfirm = async (timeSlot, reason = '') => {\n    if (!timeSlot) {\n      toast.error('Please select a time slot');\n      return;\n    }\n\n    try {\n      setRescheduling(true);\n\n      // Use the slot's datetime directly\n      const newDateTime = timeSlot.datetime;\n\n      const { data } = await axios.put(`/bookings/${selectedBooking.id}/reschedule`, {\n        newDateTime: newDateTime,\n        newDuration: selectedBooking.duration,\n        reschedule_reason: reason || rescheduleReason\n      }, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n\n      if (data.success) {\n        // Update the booking in the list\n        setBookings(prevBookings =>\n          prevBookings.map(booking =>\n            booking.id === selectedBooking.id\n              ? { ...booking, datetime: newDateTime }\n              : booking\n          )\n        );\n        toast.success(t('bookings.rescheduleSuccess'));\n      } else {\n        toast.error(data.message || t('bookings.rescheduleError'));\n      }\n    } catch (error) {\n      console.error('Error rescheduling booking:', error);\n      toast.error(error.response?.data?.message || t('bookings.rescheduleError'));\n    } finally {\n      setRescheduling(false);\n      setRescheduleDialogOpen(false);\n      setDetailsDialogOpen(false);\n      setSelectedDay(null);\n      setAvailableTimesForDay([]);\n      setRescheduleReason('');\n    }\n  };\n\n  // Get status chip color\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'scheduled':\n        return 'primary';\n      case 'completed':\n        return 'success';\n      case 'cancelled':\n        return 'error';\n      case 'issue_reported':\n        return 'warning';\n      case 'ongoing':\n        return 'info';\n      default:\n        return 'default';\n    }\n  };\n\n  // Get translated status text\n  const getStatusText = (status) => {\n    return t(`bookings.statusValues.${status}`, { \n      defaultValue: status.charAt(0).toUpperCase() + status.slice(1) \n    });\n  };\n\n  // Format booking date in student's timezone\n  const formatBookingDate = (datetime) => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return format(new Date(datetime), 'PPP', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDate = formatDateInStudentTimezone(datetime, studentProfile.timezone, 'YYYY-MM-DD');\n    return moment(formattedDate, 'YYYY-MM-DD').format('MMMM D, YYYY');\n  };\n\n  // Format booking time in student's timezone\n  const formatBookingTime = (datetime) => {\n    if (!studentProfile || !studentProfile.timezone) {\n      return format(new Date(datetime), 'p', { locale: isRtl ? ar : enUS });\n    }\n\n    const formattedDateTime = formatDateInStudentTimezone(datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    return moment(formattedDateTime, 'YYYY-MM-DD HH:mm:ss').format('h:mm A');\n  };\n\n  // Format booking time range (start - end) in student's timezone\n  const formatBookingTimeRange = (datetime, duration) => {\n    if (!studentProfile || !studentProfile.timezone) {\n      const startDate = new Date(datetime);\n      const endDate = new Date(startDate.getTime() + duration * 60000);\n\n      const startTimeStr = startDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n      const endTimeStr = endDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n\n      return `${startTimeStr} - ${endTimeStr}`;\n    }\n\n    // Use student timezone for accurate time calculation\n    const formattedDateTime = formatDateInStudentTimezone(datetime, studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss');\n    const [datePart, timePart] = formattedDateTime.split(' ');\n    const [hours, minutes] = timePart.split(':');\n\n    // Calculate start and end times\n    const startMinutes = parseInt(hours) * 60 + parseInt(minutes);\n    const endMinutes = startMinutes + duration;\n\n    const startHour = Math.floor(startMinutes / 60);\n    const startMin = startMinutes % 60;\n    const endHour = Math.floor(endMinutes / 60);\n    const endMin = endMinutes % 60;\n\n    const startTimeStr = `${String(startHour).padStart(2, '0')}:${String(startMin).padStart(2, '0')}`;\n    const endTimeStr = `${String(endHour).padStart(2, '0')}:${String(endMin).padStart(2, '0')}`;\n\n    return `${startTimeStr} - ${endTimeStr}`;\n  };\n\n  // Submit feedback after meeting\n  const handleFeedbackSubmit = async (meetingId, values) => {\n    try {\n      console.log('Submitting feedback:', { meetingId, values });\n\n      const response = await axios.post('/meeting-issues', {\n        meeting_id: meetingId,\n        booking_id: values.booking_id,\n        issue_type: values.issue_type,\n        description: values.description,\n      });\n\n      console.log('Feedback submitted successfully:', response.data);\n\n      // Update booking status locally for immediate UI feedback\n      if (values.booking_id) {\n        const newStatus = values.issue_type === 'no_issue' ? 'completed' : 'issue_reported';\n        setBookings(prev => prev.map(b => (\n          b.id === values.booking_id ? { ...b, status: newStatus } : b\n        )));\n        console.log(`Updated booking ${values.booking_id} status to ${newStatus}`);\n      }\n\n      // Close current feedback dialog\n      setFeedbackDialogOpen(false);\n      setFeedbackMeeting(null);\n\n      // Check for next pending feedback after a short delay\n      setTimeout(() => {\n        console.log('Checking for next pending feedback...');\n        checkPendingFeedback();\n      }, 1000); // Wait 1 second before checking for next feedback\n\n    } catch (err) {\n      console.error('Failed to submit feedback', err);\n      // Show error message to user\n      alert('Failed to submit feedback. Please try again.');\n      return; // Don't close dialog on error\n    }\n  };\n\n  // Render booking cards\n  const renderBookings = () => {\n    if (bookings.length === 0) {\n      return (\n        <Box sx={{ textAlign: 'center', py: 4 }}>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            {t('bookings.noBookings')}\n          </Typography>\n        </Box>\n      );\n    }\n\n    return (\n      <Grid container spacing={3}>\n        {bookings.map((booking) => (\n          <Grid item xs={12} sm={6} md={4} key={booking.id}>\n            <Card\n              elevation={3}\n              sx={{\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                opacity: booking.teacher_deleted_at ? 0.8 : 1,\n                border: booking.teacher_deleted_at ? `2px solid ${theme.palette.error.light}` : 'none',\n                transition: 'all 0.2s ease'\n              }}\n            >\n              <Box sx={{\n                bgcolor: 'primary.main',\n                color: 'white',\n                p: 2,\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              }}>\n                <CalendarIcon />\n                <Typography variant=\"h6\">\n                  {formatBookingDate(booking.datetime)}\n                </Typography>\n              </Box>\n              <CardContent sx={{ flexGrow: 1 }}>\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                  <Avatar\n                    src={booking.teacher_picture}\n                    alt={booking.teacher_name}\n                    sx={{\n                      mr: 2,\n                      opacity: booking.teacher_deleted_at ? 0.6 : 1,\n                      filter: booking.teacher_deleted_at ? 'grayscale(100%)' : 'none'\n                    }}\n                  />\n                  <Box sx={{ flex: 1 }}>\n                    <Typography\n                      variant=\"subtitle1\"\n                      sx={{\n                        color: booking.teacher_deleted_at ? 'text.secondary' : 'text.primary',\n                        textDecoration: booking.teacher_deleted_at ? 'line-through' : 'none'\n                      }}\n                    >\n                      {booking.teacher_name}\n                    </Typography>\n                    {booking.teacher_deleted_at && (\n                      <Chip\n                        label={t('bookings.teacherDeleted', 'Teacher Account Deleted')}\n                        size=\"small\"\n                        color=\"error\"\n                        variant=\"outlined\"\n                        sx={{ mt: 0.5, fontSize: '0.7rem' }}\n                      />\n                    )}\n                  </Box>\n                </Box>\n\n                <Divider sx={{ my: 2 }} />\n\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                  <Typography variant=\"body2\">\n                    {formatBookingTime(booking.datetime)}\n                  </Typography>\n                </Box>\n\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <TimeIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                  <Typography variant=\"body2\">\n                    {t('bookings.duration')}: {booking.duration || 50} {t('bookings.minutes')}\n                    {booking.duration === '25' || booking.duration === 25 ?\n                      ` (${t('booking.halfLesson') || 'نصف درس'})` :\n                      ` (${t('booking.fullLesson') || 'درس كامل'})`}\n                  </Typography>\n                </Box>\n\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  <EventIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                  <Chip\n                    label={getStatusText(booking.status)}\n                    color={getStatusColor(booking.status)}\n                    size=\"small\"\n                  />\n                </Box>\n\n                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {t('bookings.price')}: {(() => {\n                      if (booking.price_paid !== null && !isNaN(booking.price_paid)) {\n                        return booking.price_paid.toFixed(2);\n                      }\n                      const basePrice = parseFloat(booking.price_per_lesson || 0);\n                      const isDurationHalf = booking.duration === '25' || booking.duration === 25;\n                      const finalPrice = isDurationHalf ? basePrice / 2 : basePrice;\n                      return finalPrice.toFixed(2);\n                    })()} $\n                  </Typography>\n\n                  <Button\n                    variant=\"outlined\"\n                    size=\"small\"\n                    onClick={() => {\n                      setSelectedBooking(booking);\n                      setDetailsDialogOpen(true);\n                    }}\n                  >\n                    {t('bookings.details')}\n                  </Button>\n                </Box>\n              </CardContent>\n            </Card>\n          </Grid>\n        ))}\n      </Grid>\n    );\n  };\n\n  // Details dialog\n  const renderDetailsDialog = () => (\n    <Dialog open={detailsDialogOpen} onClose={() => setDetailsDialogOpen(false)} maxWidth=\"sm\" fullWidth>\n      <DialogTitle>\n        {t('bookings.bookingDetails')}\n        <IconButton\n          aria-label=\"close\"\n          onClick={() => setDetailsDialogOpen(false)}\n          sx={{ position: 'absolute', right: 8, top: 8 }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n      <DialogContent>\n        {selectedBooking && (\n          <Box sx={{ py: 2 }}>\n            {/* Teacher Info */}\n            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>\n              <Avatar\n                src={selectedBooking.teacher_picture ? (\n                  selectedBooking.teacher_picture.startsWith('http')\n                    ? selectedBooking.teacher_picture\n                    : `https://allemnionline.com${selectedBooking.teacher_picture}`\n                ) : ''}\n                alt={selectedBooking.teacher_name}\n                sx={{\n                  width: 120,\n                  height: 120,\n                  border: `3px solid ${selectedBooking.teacher_deleted_at ? theme.palette.error.main : theme.palette.primary.main}`,\n                  mb: 2,\n                  opacity: selectedBooking.teacher_deleted_at ? 0.6 : 1,\n                  filter: selectedBooking.teacher_deleted_at ? 'grayscale(100%)' : 'none'\n                }}\n              />\n              <Box sx={{ textAlign: 'center' }}>\n                <Typography\n                  variant=\"h6\"\n                  gutterBottom\n                  sx={{\n                    color: selectedBooking.teacher_deleted_at ? 'text.secondary' : 'text.primary',\n                    textDecoration: selectedBooking.teacher_deleted_at ? 'line-through' : 'none'\n                  }}\n                >\n                  {selectedBooking.teacher_name}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {t('bookings.teacher')}\n                </Typography>\n                {selectedBooking.teacher_deleted_at && (\n                  <Chip\n                    label={t('bookings.teacherDeleted', 'Teacher Account Deleted')}\n                    size=\"small\"\n                    color=\"error\"\n                    variant=\"filled\"\n                    sx={{ mt: 1 }}\n                  />\n                )}\n              </Box>\n            </Box>\n\n            <Divider sx={{ my: 2 }} />\n\n            {/* Booking Details */}\n            <Grid container spacing={2}>\n              <Grid item xs={12} sm={6}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.date')}\n                </Typography>\n                <Typography variant=\"body1\" gutterBottom>\n                  {formatBookingDate(selectedBooking.datetime)}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.time')}\n                </Typography>\n                <Typography variant=\"body1\" gutterBottom>\n                  {formatBookingTime(selectedBooking.datetime)}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n                  {formatBookingTimeRange(selectedBooking.datetime, parseInt(selectedBooking.duration) || 50)}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.duration')}\n                </Typography>\n                <Typography variant=\"body1\" gutterBottom>\n                  {selectedBooking.duration || 50} {t('bookings.minutes')}\n                  {selectedBooking.duration === '25' || selectedBooking.duration === 25 ?\n                    ` (${t('booking.halfLesson')})` :\n                    ` (${t('booking.fullLesson')})`}\n                </Typography>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.status.title')}\n                </Typography>\n                <Chip\n                  label={getStatusText(selectedBooking.status)}\n                  color={getStatusColor(selectedBooking.status)}\n                  size=\"small\"\n                />\n              </Grid>\n              <Grid item xs={12}>\n                <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\n                  {t('bookings.price')}\n                </Typography>\n                <Typography variant=\"h6\" color=\"primary\">\n                  {(() => {\n                    if (selectedBooking.price_paid !== null && !isNaN(selectedBooking.price_paid)) {\n                      return selectedBooking.price_paid.toFixed(2);\n                    }\n                    const basePrice = parseFloat(selectedBooking.price_per_lesson || 0);\n                    const isDurationHalf = selectedBooking.duration === '25' || selectedBooking.duration === 25;\n                    const finalPrice = isDurationHalf ? basePrice / 2 : basePrice;\n                    return finalPrice.toFixed(2);\n                  })()} {t('common.currency')}\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n        )}\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={() => setDetailsDialogOpen(false)}>\n          {t('common.close')}\n        </Button>\n\n        {/* Join Meeting Button */}\n        {selectedBooking && (\n          <Button\n            onClick={() => canJoinMeeting(selectedBooking) && handleJoinMeeting(selectedBooking)}\n            color={canJoinMeeting(selectedBooking) ? \"success\" : \"inherit\"}\n            variant={canJoinMeeting(selectedBooking) ? \"contained\" : \"outlined\"}\n            startIcon={<VideoCallIcon />}\n            disabled={!canJoinMeeting(selectedBooking)}\n            sx={{\n              mr: 1,\n              ...(canJoinMeeting(selectedBooking) ? {} : {\n                color: theme.palette.grey[500],\n                borderColor: theme.palette.grey[300],\n                backgroundColor: theme.palette.grey[100],\n                '&:hover': {\n                  backgroundColor: theme.palette.grey[200],\n                  borderColor: theme.palette.grey[400]\n                }\n              })\n            }}\n          >\n            {getMeetingStatusText(selectedBooking)}\n          </Button>\n        )}\n\n        {/* Reschedule button - only if can reschedule */}\n        {selectedBooking && canStudentReschedule(selectedBooking) && (\n          <Button\n            onClick={() => {\n              setDetailsDialogOpen(false);\n              handleRescheduleBookingClick(selectedBooking);\n            }}\n            color=\"primary\"\n            variant=\"outlined\"\n            startIcon={<RescheduleIcon />}\n            sx={{ mr: 1 }}\n          >\n            {t('bookings.reschedule')}\n          </Button>\n        )}\n\n        {/* Cancel button - always show for scheduled bookings with non-deleted teachers */}\n        {selectedBooking?.status === 'scheduled' && !selectedBooking?.teacher_deleted_at && (\n          <Button\n            onClick={() => {\n              setDetailsDialogOpen(false);\n              setCancelDialogOpen(true);\n            }}\n            color=\"error\"\n            variant=\"contained\"\n            startIcon={<CancelIcon />}\n          >\n            {t('bookings.cancel')}\n          </Button>\n        )}\n\n        {/* Show reschedule restriction message if applicable */}\n        {selectedBooking && selectedBooking.status === 'scheduled' && !selectedBooking.teacher_deleted_at && !canStudentReschedule(selectedBooking) && (\n          <Box sx={{ mt: 2, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>\n            <Typography variant=\"body2\" color=\"warning.dark\">\n              ⚠️ {(() => {\n                const rescheduleCount = parseInt(selectedBooking.reschedule_count) || 0;\n                const timeRemaining = calculateTimeRemaining(selectedBooking.datetime);\n\n                if (rescheduleCount >= 1) {\n                  return t('bookings.rescheduleAlreadyUsed', 'You have already rescheduled this booking once. Students can only reschedule each booking once.');\n                } else if (timeRemaining.hours < 12 && !timeRemaining.isOverdue) {\n                  return t('bookings.rescheduleTimeLimit', 'You can only reschedule bookings at least 12 hours before the lesson time.');\n                } else if (timeRemaining.isOverdue) {\n                  return t('bookings.rescheduleTimeExpired', 'This lesson time has already passed.');\n                }\n                return '';\n              })()}\n            </Typography>\n          </Box>\n        )}\n\n        {/* Message for deleted teacher bookings */}\n        {selectedBooking?.teacher_deleted_at && selectedBooking?.status === 'scheduled' && (\n          <Alert severity=\"info\" sx={{ mt: 2, mx: 2 }}>\n            {t('bookings.teacherDeletedMessage', 'This booking cannot be modified because the teacher account has been deleted. The amount will be automatically refunded to your wallet.')}\n          </Alert>\n        )}\n      </DialogActions>\n    </Dialog>\n  );\n\n  // Cancel confirmation dialog\n  const renderCancelDialog = () => (\n    <Dialog open={cancelDialogOpen} onClose={() => setCancelDialogOpen(false)}>\n      <DialogTitle>\n        {t('bookings.confirmCancel')}\n        <IconButton\n          aria-label=\"close\"\n          onClick={() => setCancelDialogOpen(false)}\n          sx={{ position: 'absolute', right: 8, top: 8 }}\n        >\n          <CloseIcon />\n        </IconButton>\n      </DialogTitle>\n      <DialogContent>\n        <Typography variant=\"body1\" sx={{ mb: 2 }}>\n          {t('bookings.cancelWarning')}\n        </Typography>\n\n        {/* Late cancellation policy warning */}\n        <Alert severity=\"warning\" sx={{ mb: 2 }}>\n          <Typography variant=\"body2\">\n            {t('bookings.lateCancellationPolicy')}\n          </Typography>\n        </Alert>\n        {selectedBooking && (\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.teacher')}:</strong> {selectedBooking.teacher_name}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.date')}:</strong> {formatBookingDate(selectedBooking.datetime)}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom>\n              <strong>{t('bookings.time')}:</strong> {formatBookingTime(selectedBooking.datetime)}\n            </Typography>\n            <Typography variant=\"body2\" gutterBottom color=\"text.secondary\">\n              <strong>{t('bookings.timeRange')}:</strong> {formatBookingTimeRange(selectedBooking.datetime, parseInt(selectedBooking.duration) || 50)}\n            </Typography>\n\n            {/* Time remaining until lesson */}\n            {(() => {\n              const timeRemaining = calculateTimeRemaining(selectedBooking.datetime);\n              const isLateCancel = timeRemaining.hours < 12 && !timeRemaining.isOverdue;\n\n              return (\n                <Box sx={{ mt: 2, p: 2, borderRadius: 1, bgcolor: isLateCancel ? 'warning.light' : 'info.light' }}>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 'bold', color: isLateCancel ? 'warning.dark' : 'info.dark' }}>\n                    <strong>{t('bookings.timeRemaining')}:</strong> {' '}\n                    {timeRemaining.isOverdue\n                      ? t('bookings.lessonOverdue')\n                      : `${timeRemaining.hours} ${t('bookings.hours')} ${timeRemaining.minutes} ${t('bookings.minutes')}`\n                    }\n                  </Typography>\n                  {isLateCancel && (\n                    <Typography variant=\"caption\" sx={{ display: 'block', mt: 1, color: 'warning.dark' }}>\n                      ⚠️ {t('bookings.lateCancelWarning')}\n                    </Typography>\n                  )}\n                </Box>\n              );\n            })()}\n          </Box>\n        )}\n        <TextField\n          fullWidth\n          multiline\n          rows={3}\n          label={t('bookings.cancellationReason')}\n          placeholder={t('bookings.cancellationReasonPlaceholder')}\n          value={cancellationReason}\n          onChange={(e) => setCancellationReason(e.target.value)}\n          sx={{ mt: 2 }}\n        />\n      </DialogContent>\n      <DialogActions>\n        <Button onClick={() => setCancelDialogOpen(false)}>\n          {t('common.cancel')}\n        </Button>\n        <Button\n          onClick={handleCancelBooking}\n          color=\"error\"\n          variant=\"contained\"\n          disabled={cancellingBooking}\n        >\n          {cancellingBooking ? t('bookings.cancelling') : t('bookings.confirmCancelButton')}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n\n  return (\n    <Layout>\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\n        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>\n          <Paper elevation={3} sx={{ p: 3, mb: 4, bgcolor: 'primary.main', color: 'white' }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>\n              <Box>\n                <Typography variant=\"h4\" gutterBottom sx={{ fontWeight: 'bold' }}>\n                  {t('bookings.weeklyTitle')}\n                </Typography>\n                <Typography variant=\"body1\" sx={{ opacity: 0.9 }}>\n                  {t('bookings.weeklyDescription')}\n                </Typography>\n              </Box>\n              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n                <Box sx={{ textAlign: 'right' }}>\n                  <Typography variant=\"body2\" sx={{ opacity: 0.8, mb: 0.5 }}>\n                    {t('booking.weekNavigation')}\n                  </Typography>\n                  <Typography variant=\"h6\" sx={{ fontWeight: 'bold' }}>\n                    📅 {format(currentWeekStart, 'MMM d', { locale: isRtl ? ar : enUS })} - {format(addDays(currentWeekStart, 6), 'MMM d, yyyy', { locale: isRtl ? ar : enUS })}\n                  </Typography>\n                  <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n                    {studentProfile?.timezone ? (\n                      moment(formatDateInStudentTimezone(new Date().toISOString(), studentProfile.timezone, 'YYYY-MM-DD HH:mm:ss'), 'YYYY-MM-DD HH:mm:ss').format('h:mm A')\n                    ) : (\n                      format(currentTime, 'p', {\n                        locale: isRtl ? ar : enUS\n                      })\n                    )}\n                    {studentProfile?.timezone && ` (${studentProfile.timezone})`}\n                  </Typography>\n                </Box>\n                <Box sx={{ display: 'flex', gap: 1 }}>\n                  <Tooltip title={t('booking.previousWeek')}>\n                    <span>\n                      <IconButton\n                        onClick={goToPreviousWeek}\n                        disabled={isPreviousWeekDisabled()}\n                        sx={{\n                          color: 'white',\n                          bgcolor: 'rgba(255, 255, 255, 0.1)',\n                          '&:hover': {\n                            bgcolor: 'rgba(255, 255, 255, 0.2)',\n                          },\n                          '&:disabled': {\n                            color: 'rgba(255, 255, 255, 0.3)',\n                            bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          }\n                        }}\n                      >\n                        <ChevronLeftIcon />\n                      </IconButton>\n                    </span>\n                  </Tooltip>\n                  <Tooltip title={t('booking.nextWeek')}>\n                    <span>\n                      <IconButton\n                        onClick={goToNextWeek}\n                        disabled={isNextWeekDisabled()}\n                        sx={{\n                          color: 'white',\n                          bgcolor: 'rgba(255, 255, 255, 0.1)',\n                          '&:hover': {\n                            bgcolor: 'rgba(255, 255, 255, 0.2)',\n                          },\n                          '&:disabled': {\n                            color: 'rgba(255, 255, 255, 0.3)',\n                            bgcolor: 'rgba(255, 255, 255, 0.05)',\n                          }\n                        }}\n                      >\n                        <ChevronRightIcon />\n                      </IconButton>\n                    </span>\n                  </Tooltip>\n                </Box>\n              </Box>\n            </Box>\n          </Paper>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>\n            <CircularProgress />\n          </Box>\n        ) : error ? (\n          <Alert severity=\"error\" sx={{ mb: 4 }}>\n            {error}\n          </Alert>\n        ) : (\n          <WeeklyBookingsTable\n            bookings={bookings}\n            loading={loading}\n            currentWeekStart={currentWeekStart}\n            daysOfWeek={daysOfWeek}\n            onViewDetails={handleViewDetails}\n            onCancelBooking={handleCancelBookingClick}\n            studentProfile={studentProfile}\n            formatBookingTime={formatBookingTime}\n            getStatusColor={getStatusColor}\n          />\n        )}\n\n        {renderDetailsDialog()}\n        {renderCancelDialog()}\n        </ProfileCompletionAlert>\n      </Container>\n\n      {/* Meeting Dialog */}\n      <Dialog\n        fullScreen\n        open={openMeeting}\n        onClose={handleCloseMeeting}\n      >\n        {currentMeeting && (\n          <VideoSDKMeeting\n            roomId={currentMeeting.room_name}\n            meetingId={currentMeeting.meeting_id}\n            meetingData={{\n              ...currentMeeting,\n              meeting_date: currentMeeting.datetime,\n              duration: parseInt(currentMeeting.duration) || 50\n            }}\n            onClose={handleCloseMeeting}\n          />\n        )}\n      </Dialog>\n\n      {/* New Reschedule Dialog */}\n      <RescheduleDialog\n        open={rescheduleDialogOpen}\n        onClose={() => setRescheduleDialogOpen(false)}\n        booking={selectedBooking}\n        availableDays={availableDays}\n        onReschedule={handleRescheduleConfirm}\n        loading={rescheduling}\n        userProfile={studentProfile}\n        isTeacherView={false}\n      />\n\n      {/* Feedback Dialog */}\n      <MeetingFeedbackDialog\n        open={feedbackDialogOpen}\n        meeting={feedbackMeeting}\n        timezone={studentProfile?.timezone || null}\n        onSubmit={handleFeedbackSubmit}\n        onClose={() => {\n          setFeedbackDialogOpen(false);\n          setFeedbackMeeting(null);\n        }}\n      />\n    </Layout>\n  );\n};\n\nexport default Bookings;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,MAAM,CACNC,IAAI,CACJC,gBAAgB,CAChBC,KAAK,CACLC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,UAAU,CACVC,OAAO,CACPC,MAAM,CACNC,QAAQ,CACRC,OAAO,CACPC,SAAS,CACTC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,KACH,eAAe,CACtB,OACEC,aAAa,GAAI,CAAAC,YAAY,CAC7BC,UAAU,GAAI,CAAAC,QAAQ,CACtBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,SAAS,GAAI,CAAAC,aAAa,CAC1BC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,YAAY,GAAI,CAAAC,gBAAgB,CAChCC,QAAQ,GAAI,CAAAC,cAAc,KACrB,qBAAqB,CAC5B,OAASC,MAAM,CAAEC,OAAO,CAAEC,WAAW,CAAEC,QAAQ,CAAEC,QAAQ,KAAQ,UAAU,CAC3E,OAASC,EAAE,CAAEC,IAAI,KAAQ,iBAAiB,CAC1C,MAAO,CAAAC,KAAK,KAAM,mBAAmB,CACrC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,GAAG,KAAM,kBAAkB,CAClC,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,mBAAmB,KAAM,sCAAsC,CACtE,MAAO,CAAAC,gBAAgB,KAAM,mCAAmC,CAChE,OAASC,uBAAuB,CAAEC,2BAA2B,CAAEC,wBAAwB,KAAQ,sBAAsB,CACrH,MAAO,CAAAC,MAAM,KAAM,iBAAiB,CACpC,MAAO,CAAAC,sBAAsB,KAAM,iDAAiD,CACpF,MAAO,CAAAC,eAAe,KAAM,0CAA0C,CACtE,MAAO,CAAAC,qBAAqB,KAAM,wCAAwC,CAC1E,OAASC,KAAK,KAAQ,iBAAiB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExCjB,KAAK,CAACkB,MAAM,CAACjB,GAAG,CAAC,CAEjB,KAAM,CAAAkB,QAAQ,CAAGA,CAAA,GAAM,CACrB,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG3E,cAAc,CAAC,CAAC,CACpC,KAAM,CAAE4E,KAAM,CAAC,CAAGpB,OAAO,CAAC,CAAC,CAC3B,KAAM,CAAAqB,KAAK,CAAG1D,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA2D,KAAK,CAAGH,IAAI,CAACI,QAAQ,GAAK,IAAI,CAEpC,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGnF,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACoF,OAAO,CAAEC,UAAU,CAAC,CAAGrF,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsF,KAAK,CAAEC,QAAQ,CAAC,CAAGvF,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACwF,eAAe,CAAEC,kBAAkB,CAAC,CAAGzF,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAAC0F,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3F,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC4F,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG7F,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC8F,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/F,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACgG,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAAkG,sBAAsB,CAAIC,cAAc,EAAK,CACjD,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAD,IAAI,CAACF,cAAc,CAAC,CAC3C,KAAM,CAAAI,cAAc,CAAGD,UAAU,CAACE,OAAO,CAAC,CAAC,CAAGJ,GAAG,CAACI,OAAO,CAAC,CAAC,CAE3D,GAAID,cAAc,EAAI,CAAC,CAAE,CACvB,MAAO,CAAEE,KAAK,CAAE,CAAC,CAAEC,OAAO,CAAE,CAAC,CAAEC,SAAS,CAAE,IAAK,CAAC,CAClD,CAEA,KAAM,CAAAF,KAAK,CAAGG,IAAI,CAACC,KAAK,CAACN,cAAc,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CAC3D,KAAM,CAAAG,OAAO,CAAGE,IAAI,CAACC,KAAK,CAAEN,cAAc,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,EAAK,IAAI,CAAG,EAAE,CAAC,CAAC,CAE7E,MAAO,CAAEE,KAAK,CAAEC,OAAO,CAAEC,SAAS,CAAE,KAAM,CAAC,CAC7C,CAAC,CAED;AACA,KAAM,CAAAG,oBAAoB,CAAIC,OAAO,EAAK,CACxC;AACA,GAAI,CAACA,OAAO,CAAE,MAAO,MAAK,CAE1B;AACA,GAAIA,OAAO,CAACC,MAAM,GAAK,WAAW,CAAE,MAAO,MAAK,CAEhD;AACA,GAAID,OAAO,CAACE,kBAAkB,CAAE,MAAO,MAAK,CAE5C;AACA,KAAM,CAAAC,eAAe,CAAGC,QAAQ,CAACJ,OAAO,CAACK,gBAAgB,CAAC,EAAI,CAAC,CAC/D,GAAIF,eAAe,EAAI,CAAC,CAAE,MAAO,MAAK,CAEtC;AACA,KAAM,CAAAG,aAAa,CAAGnB,sBAAsB,CAACa,OAAO,CAACO,QAAQ,CAAC,CAE9D;AACAC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAE,CAClCC,SAAS,CAAEV,OAAO,CAACW,EAAE,CACrBC,eAAe,CAAEZ,OAAO,CAACO,QAAQ,CACjCM,WAAW,CAAE,GAAI,CAAAvB,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC,CACrCR,aAAa,CAAEA,aAAa,CAC5BS,aAAa,CAAE,CAACT,aAAa,CAACV,SAAS,EAAIU,aAAa,CAACZ,KAAK,EAAI,EACpE,CAAC,CAAC,CAEF,GAAIY,aAAa,CAACV,SAAS,EAAIU,aAAa,CAACZ,KAAK,CAAG,EAAE,CAAE,MAAO,MAAK,CAErE,MAAO,KAAI,CACb,CAAC,CACD,KAAM,CAACsB,cAAc,CAAEC,iBAAiB,CAAC,CAAGhI,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACiI,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGlI,QAAQ,CAAC,IAAM,CAC7D,KAAM,CAAAmI,KAAK,CAAG,GAAI,CAAA9B,IAAI,CAAC,CAAC,CACxB,MAAO,CAAAnD,WAAW,CAACiF,KAAK,CAAE,CAAEC,YAAY,CAAE,CAAE,CAAC,CAAC,CAAE;AAClD,CAAC,CAAC,CACF,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGtI,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACuI,cAAc,CAAEC,iBAAiB,CAAC,CAAGxI,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC4H,WAAW,CAAEa,cAAc,CAAC,CAAGzI,QAAQ,CAAC,GAAI,CAAAqG,IAAI,CAAC,CAAC,CAAC,CAC1D,KAAM,CAACqC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3I,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC4I,eAAe,CAAEC,kBAAkB,CAAC,CAAG7I,QAAQ,CAAC,IAAI,CAAC,CAE5D;AACA,KAAM,CAAC8I,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG/I,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAACgJ,aAAa,CAAEC,gBAAgB,CAAC,CAAGjJ,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACkJ,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnJ,QAAQ,CAAC,EAAE,CAAC,CACpE,KAAM,CAACoJ,WAAW,CAAEC,cAAc,CAAC,CAAGrJ,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACsJ,WAAW,CAAEC,cAAc,CAAC,CAAGvJ,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACwJ,YAAY,CAAEC,eAAe,CAAC,CAAGzJ,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC0J,YAAY,CAAEC,eAAe,CAAC,CAAG3J,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC4J,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7J,QAAQ,CAAC,EAAE,CAAC,CAE5D;AACA,KAAM,CAAA8J,UAAU,CAAG,CAAC,QAAQ,CAAE,SAAS,CAAE,WAAW,CAAE,UAAU,CAAE,QAAQ,CAAE,UAAU,CAAE,QAAQ,CAAC,CAEjG;AACA,KAAM,CAAAC,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAAC,YAAY,CAAG5G,QAAQ,CAAC6E,gBAAgB,CAAE,CAAC,CAAC,CAClDC,mBAAmB,CAAC8B,YAAY,CAAC,CACnC,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,QAAQ,CAAG/G,QAAQ,CAAC8E,gBAAgB,CAAE,CAAC,CAAC,CAC9C,KAAM,CAAAE,KAAK,CAAG,GAAI,CAAA9B,IAAI,CAAC,CAAC,CACxB,KAAM,CAAA8D,YAAY,CAAGhH,QAAQ,CAACgF,KAAK,CAAE,EAAE,CAAC,CAAE;AAC1C,KAAM,CAAAiC,OAAO,CAAGlH,WAAW,CAACiH,YAAY,CAAE,CAAE/B,YAAY,CAAE,CAAE,CAAC,CAAC,CAE9D;AACA,GAAI8B,QAAQ,EAAIE,OAAO,CAAE,CACvBlC,mBAAmB,CAACgC,QAAQ,CAAC,CAC/B,CACF,CAAC,CAED;AACA,KAAM,CAAAG,sBAAsB,CAAGA,CAAA,GAAM,KAAK,CAE1C,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAJ,QAAQ,CAAG/G,QAAQ,CAAC8E,gBAAgB,CAAE,CAAC,CAAC,CAC9C,KAAM,CAAAE,KAAK,CAAG,GAAI,CAAA9B,IAAI,CAAC,CAAC,CACxB,KAAM,CAAA8D,YAAY,CAAGhH,QAAQ,CAACgF,KAAK,CAAE,EAAE,CAAC,CAAE;AAC1C,KAAM,CAAAiC,OAAO,CAAGlH,WAAW,CAACiH,YAAY,CAAE,CAAE/B,YAAY,CAAE,CAAE,CAAC,CAAC,CAC9D,MAAO,CAAA8B,QAAQ,CAAGE,OAAO,CAC3B,CAAC,CAED;AACAnK,SAAS,CAAC,IAAM,CACd,KAAM,CAAAsK,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAACzF,KAAK,CAAE,OAEZ,GAAI,CACF,KAAM,CAAE0F,IAAK,CAAC,CAAG,KAAM,CAAAjH,KAAK,CAACkH,GAAG,CAAC,uBAAuB,CAAE,CACxDC,OAAO,CAAE,CACP,eAAe,CAAE,UAAU5F,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI0F,IAAI,CAACG,OAAO,EAAIH,IAAI,CAACI,OAAO,CAAE,CAChC5C,iBAAiB,CAACwC,IAAI,CAACI,OAAO,CAAC,CACjC,CACF,CAAE,MAAOtF,KAAK,CAAE,CACdiC,OAAO,CAACjC,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACzD,CACF,CAAC,CAEDiF,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACzF,KAAK,CAAC,CAAC,CAEX;AACA,KAAM,CAAA+F,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACFtD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAChE,KAAM,CAAEgD,IAAK,CAAC,CAAG,KAAM,CAAAjH,KAAK,CAACkH,GAAG,CAAC,yBAAyB,CAAC,CAC3D,GAAID,IAAI,CAACG,OAAO,EAAIH,IAAI,CAACA,IAAI,CAAE,CAC7B,KAAM,CAAAM,KAAK,CAAGN,IAAI,CAACA,IAAI,CACvB,KAAM,CAAAO,UAAU,CAAGvH,KAAK,CAACC,GAAG,CAAC,CAAC,CAACuH,OAAO,CAACxH,KAAK,CAACC,GAAG,CAACqH,KAAK,CAACxD,QAAQ,CAAC,CAAC2D,GAAG,CAACH,KAAK,CAACI,QAAQ,EAAI,EAAE,CAAE,QAAQ,CAAC,CAAC,CACrG3D,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEsD,KAAK,CAAE,cAAc,CAAEC,UAAU,CAAC,CACzE,GAAIA,UAAU,CAAE,CACdlC,kBAAkB,CAACiC,KAAK,CAAC,CACzBnC,qBAAqB,CAAC,IAAI,CAAC,CAC3B,MAAO,KAAI,CAAE;AACf,CACF,CAAC,IAAM,CACLpB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC,CAC3D,CACA,MAAO,MAAK,CAAE;AAChB,CAAE,MAAO2D,GAAG,CAAE,CACZ5D,OAAO,CAACjC,KAAK,CAAC,iCAAiC,CAAE6F,GAAG,CAAC,CACrD,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACAlL,SAAS,CAAC,IAAM,CACd;AACA4K,oBAAoB,CAAC,CAAC,CAEtB;AACA,KAAM,CAAAO,QAAQ,CAAGC,WAAW,CAACR,oBAAoB,CAAE,KAAK,CAAC,CAEzD;AACA,KAAM,CAAAS,WAAW,CAAGA,CAAA,GAAM,CACxB/D,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC,CAC/DqD,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAEDU,MAAM,CAACC,gBAAgB,CAAC,OAAO,CAAEF,WAAW,CAAC,CAE7C;AACA,MAAO,IAAM,CACXG,aAAa,CAACL,QAAQ,CAAC,CACvBG,MAAM,CAACG,mBAAmB,CAAC,OAAO,CAAEJ,WAAW,CAAC,CAClD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACArL,SAAS,CAAC,IAAM,CACd,GAAI,CAAC2I,eAAe,EAAIA,eAAe,CAAC+C,UAAU,CAAE,OACpD,GAAI,CAACzG,QAAQ,CAAC0G,MAAM,CAAE,OAEtB,KAAM,CAAAC,UAAU,CAAG5H,MAAM,CAAC2E,eAAe,CAACtB,QAAQ,CAAC,CACnD;AACA,KAAM,CAAAwE,UAAU,CAAG5G,QAAQ,CAAC6G,MAAM,CAACC,CAAC,EAAI,CACtC,GAAIA,CAAC,CAACC,YAAY,GAAKrD,eAAe,CAACqD,YAAY,CAAE,MAAO,MAAK,CACjE,KAAM,CAAAC,KAAK,CAAGjI,MAAM,CAAC+H,CAAC,CAAC1E,QAAQ,CAAC,CAChC,KAAM,CAAA6E,QAAQ,CAAGvF,IAAI,CAACwF,GAAG,CAACF,KAAK,CAACG,IAAI,CAACR,UAAU,CAAE,SAAS,CAAC,CAAC,CAC5D,MAAO,CAAAM,QAAQ,EAAI,GAAG,CACxB,CAAC,CAAC,CAEF;AACAL,UAAU,CAACQ,IAAI,CAAC,CAACC,CAAC,CAAEP,CAAC,GAAK,CACxB,KAAM,CAAAQ,KAAK,CAAG5F,IAAI,CAACwF,GAAG,CAACnI,MAAM,CAACsI,CAAC,CAACjF,QAAQ,CAAC,CAAC+E,IAAI,CAACR,UAAU,CAAE,SAAS,CAAC,CAAC,CACtE,KAAM,CAAAY,KAAK,CAAG7F,IAAI,CAACwF,GAAG,CAACnI,MAAM,CAAC+H,CAAC,CAAC1E,QAAQ,CAAC,CAAC+E,IAAI,CAACR,UAAU,CAAE,SAAS,CAAC,CAAC,CACtE,MAAO,CAAAW,KAAK,CAAGC,KAAK,CACtB,CAAC,CAAC,CAEF,GAAIX,UAAU,CAACF,MAAM,CAAG,CAAC,CAAE,CACzB,KAAM,CAAAc,SAAS,CAAGZ,UAAU,CAAC,CAAC,CAAC,CAC/BvE,OAAO,CAACC,GAAG,CAAC,wCAAwCoB,eAAe,CAAC+D,UAAU,eAAeD,SAAS,CAAChF,EAAE,EAAE,CAAC,CAC5GmB,kBAAkB,CAAC+D,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAEjB,UAAU,CAAEe,SAAS,CAAChF,EAAG,CAAC,CAAC,CAAC,CACrE,CAAC,IAAM,CACLH,OAAO,CAACsF,IAAI,CAAC,yCAAyCjE,eAAe,CAAC+D,UAAU,iBAAiB/D,eAAe,CAACqD,YAAY,EAAE,CAAC,CAClI,CACF,CAAC,CAAE,CAAC/G,QAAQ,CAAE0D,eAAe,CAAC,CAAC,CAE/B;AACA3I,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACX;AACA;AACAsH,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC,CACvDmB,qBAAqB,CAAC,KAAK,CAAC,CAC5BE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA5I,SAAS,CAAC,IAAM,CACd,KAAM,CAAA6M,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFzH,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAEmF,IAAK,CAAC,CAAG,KAAM,CAAAjH,KAAK,CAACkH,GAAG,CAAC,mBAAmB,CAAE,CACpDC,OAAO,CAAE,CACP,eAAe,CAAE,UAAU5F,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI0F,IAAI,CAACG,OAAO,CAAE,CAChBpD,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAEgD,IAAI,CAACA,IAAI,CAAC,CAExC;AACA,KAAM,CAAAuC,iBAAiB,CAAGvC,IAAI,CAACA,IAAI,CAACwC,GAAG,CAACjG,OAAO,GAAK,CAClD,GAAGA,OAAO,CACVkG,gBAAgB,CAAEC,UAAU,CAACnG,OAAO,CAACkG,gBAAgB,EAAI,CAAC,CAAC,CAC3DE,UAAU,CAAEpG,OAAO,CAACoG,UAAU,GAAKC,SAAS,CAAGF,UAAU,CAACnG,OAAO,CAACoG,UAAU,CAAC,CAAG,IAAI,CACpFjC,QAAQ,CAAEnE,OAAO,CAACmE,QAAQ,CAAGmC,MAAM,CAACtG,OAAO,CAACmE,QAAQ,CAAC,CAAG,IAC1D,CAAC,CAAC,CAAC,CAEH3D,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAEuF,iBAAiB,CAAC,CACrD5H,WAAW,CAAC4H,iBAAiB,CAAC,CAChC,CAAC,IAAM,CACLxH,QAAQ,CAACiF,IAAI,CAAC8C,OAAO,EAAI1I,CAAC,CAAC,qBAAqB,CAAC,CAAC,CACpD,CACF,CAAE,MAAOU,KAAK,CAAE,CACdiC,OAAO,CAACjC,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChDC,QAAQ,CAACX,CAAC,CAAC,qBAAqB,CAAC,CAAC,CACpC,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAkI,cAAc,CAAG,cAAAA,CAAA,CAA0B,IAAnB,CAAAC,UAAU,CAAAC,SAAA,CAAA7B,MAAA,IAAA6B,SAAA,MAAAL,SAAA,CAAAK,SAAA,IAAG,CAAC,CAC1C,GAAI,CAAAC,OAAO,CAAG,CAAC,CACf,MAAOA,OAAO,CAAGF,UAAU,CAAE,CAC3B,GAAI,CACF,KAAM,CAAAV,aAAa,CAAC,CAAC,CACrB,MACF,CAAE,MAAOxH,KAAK,CAAE,CACdoI,OAAO,EAAE,CACT,GAAIA,OAAO,GAAKF,UAAU,CAAE,CAC1B,KAAM,CAAAlI,KAAK,CACb,CACA,KAAM,IAAI,CAAAqI,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAAE;AAC3D,CACF,CACF,CAAC,CAED,GAAI9I,KAAK,CAAE,CACTyI,cAAc,CAAC,CAAC,CAClB,CAEA;AACA,KAAM,CAAAO,YAAY,CAAGzC,WAAW,CAAC,IAAM,CACrC5C,cAAc,CAAC,GAAI,CAAApC,IAAI,CAAC,CAAC,CAAC,CAC5B,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMoF,aAAa,CAACqC,YAAY,CAAC,CAC1C,CAAC,CAAE,CAAChJ,KAAK,CAAEF,CAAC,CAAC,CAAC,CAEd;AACA,KAAM,CAAAmJ,iBAAiB,CAAIhH,OAAO,EAAK,CACrCtB,kBAAkB,CAACsB,OAAO,CAAC,CAC3BlB,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAmI,wBAAwB,CAAIjH,OAAO,EAAK,CAC5CtB,kBAAkB,CAACsB,OAAO,CAAC,CAC3BpB,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAsI,iBAAiB,CAAG,KAAO,CAAAlH,OAAO,EAAK,CAC3C,GAAI,CACF;AACA,GAAI,CAACA,OAAO,CAACmH,SAAS,CAAE,CACtB3G,OAAO,CAACjC,KAAK,CAAC,iCAAiC,CAAEyB,OAAO,CAAC,CACzD1C,KAAK,CAACiB,KAAK,CAACV,CAAC,CAAC,sBAAsB,CAAC,EAAI,wBAAwB,CAAC,CAClE,OACF,CAEA;AACA,GAAI,CAACmC,OAAO,CAAC4F,UAAU,CAAE,CACvBpF,OAAO,CAACjC,KAAK,CAAC,kCAAkC,CAAEyB,OAAO,CAAC,CAC1D1C,KAAK,CAACiB,KAAK,CAACV,CAAC,CAAC,yBAAyB,CAAC,EAAI,sBAAsB,CAAC,CACnE,OACF,CAEA2C,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAE,CACxC0G,SAAS,CAAEnH,OAAO,CAACmH,SAAS,CAC5BvB,UAAU,CAAE5F,OAAO,CAAC4F,UAAU,CAC9BrF,QAAQ,CAAEP,OAAO,CAACO,QAAQ,CAC1B4D,QAAQ,CAAEnE,OAAO,CAACmE,QACpB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAiD,QAAQ,CAAG,KAAM,CAAA5K,KAAK,CAACkH,GAAG,CAAC,aAAa1D,OAAO,CAACmH,SAAS,WAAW,CAAC,CAC3E1F,iBAAiB,CAAC,CAAE,GAAGzB,OAAO,CAAEmH,SAAS,CAAEnH,OAAO,CAACmH,SAAU,CAAC,CAAC,CAC/D5F,cAAc,CAAC,IAAI,CAAC,CACtB,CAAE,MAAOhD,KAAK,CAAE,CACdiC,OAAO,CAACjC,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CjB,KAAK,CAACiB,KAAK,CAACV,CAAC,CAAC,oBAAoB,CAAC,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAAwJ,kBAAkB,CAAGA,CAAA,GAAM,CAC/B;AACA,GAAI7F,cAAc,CAAE,CAClB,KAAM,CAAA8F,UAAU,CAAG,GAAI,CAAAhI,IAAI,CAACkC,cAAc,CAACjB,QAAQ,CAAC,CACpD+G,UAAU,CAACC,UAAU,CAACD,UAAU,CAACE,UAAU,CAAC,CAAC,EAAIhG,cAAc,CAAC2C,QAAQ,EAAI,EAAE,CAAC,CAAC,CAChF,KAAM,CAAA9E,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB;AACA,GAAID,GAAG,EAAIiI,UAAU,CAAE,CACrBxF,kBAAkB,CAACN,cAAc,CAAC,CAElC;AACA,KAAM,CAAAsD,UAAU,CAAG5H,MAAM,CAACsE,cAAc,CAACjB,QAAQ,CAAC,CAClD,KAAM,CAAAkH,oBAAoB,CAAGtJ,QAAQ,CAACuJ,IAAI,CAACzC,CAAC,EAAI,CAC9C,GAAIA,CAAC,CAACC,YAAY,GAAK1D,cAAc,CAAC0D,YAAY,CAAE,MAAO,MAAK,CAChE,KAAM,CAAAC,KAAK,CAAGjI,MAAM,CAAC+H,CAAC,CAAC1E,QAAQ,CAAC,CAChC,MAAO,CAAAV,IAAI,CAACwF,GAAG,CAACF,KAAK,CAACG,IAAI,CAACR,UAAU,CAAE,SAAS,CAAC,CAAC,EAAI,GAAG,CAC3D,CAAC,CAAC,CAEF;AACAtI,KAAK,CAACmL,IAAI,CAAC,iBAAiB,CAAE,CAC5B/C,UAAU,CAAE6C,oBAAoB,CAAGA,oBAAoB,CAAC9G,EAAE,CAAG,IAAI,CACjEiF,UAAU,CAAEpE,cAAc,CAACoE,UAAU,EAAIpE,cAAc,CAACb,EAAE,CAC1DiH,UAAU,CAAE,SAAS,CACrBC,WAAW,CAAE,EACf,CAAC,CAAC,CAACC,KAAK,CAAC1D,GAAG,EAAI5D,OAAO,CAACjC,KAAK,CAAC,gCAAgC,CAAE6F,GAAG,CAAC,CAAC,CACrExC,qBAAqB,CAAC,IAAI,CAAC,CAC7B,CACF,CACAL,cAAc,CAAC,KAAK,CAAC,CACrBE,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED;AACA,KAAM,CAAAsG,+BAA+B,CAAGA,CAAA,GAAM,CAC5C,GAAI,CAAC/G,cAAc,EAAI,CAACA,cAAc,CAACgH,QAAQ,CAAE,CAC/C,MAAO,IAAI,CAAA1I,IAAI,CAAC,CAAC,CACnB,CACA,MAAO,CAAArC,wBAAwB,CAAC+D,cAAc,CAACgH,QAAQ,CAAC,CAC1D,CAAC,CAED;AACA,KAAM,CAAAC,+BAA+B,CAAI1H,QAAQ,EAAK,CACpD,GAAI,CAACS,cAAc,EAAI,CAACA,cAAc,CAACgH,QAAQ,CAAE,CAC/C,MAAO,IAAI,CAAA1I,IAAI,CAACiB,QAAQ,CAAC,CAC3B,CACA,MAAO,CAAAxD,uBAAuB,CAACwD,QAAQ,CAAES,cAAc,CAACgH,QAAQ,CAAC,CACnE,CAAC,CAED;AACA,KAAM,CAAAE,gBAAgB,CAAIlI,OAAO,EAAK,CACpC,MAAO,CAAAA,OAAO,CAACC,MAAM,EAAI,WAAW,CACtC,CAAC,CAED;AACA,KAAM,CAAAkI,cAAc,CAAInI,OAAO,EAAK,CAClC,GAAI,CAACA,OAAO,EAAI,CAACgB,cAAc,CAAE,MAAO,MAAK,CAE7C;AACA,GAAIhB,OAAO,CAACE,kBAAkB,CAAE,MAAO,MAAK,CAE5C,KAAM,CAAAkI,aAAa,CAAGF,gBAAgB,CAAClI,OAAO,CAAC,CAC/C,GAAIoI,aAAa,GAAK,WAAW,EAAIA,aAAa,GAAK,2BAA2B,EAAIA,aAAa,GAAK,WAAW,CAAE,CACnH,MAAO,MAAK,CACd,CAEA,KAAM,CAAAC,gBAAgB,CAAGJ,+BAA+B,CAACjI,OAAO,CAACO,QAAQ,CAAC,CAC1E,KAAM,CAAA+H,cAAc,CAAG,GAAI,CAAAhJ,IAAI,CAAC+I,gBAAgB,CAAC,CACjDC,cAAc,CAACf,UAAU,CAACe,cAAc,CAACd,UAAU,CAAC,CAAC,EAAIxH,OAAO,CAACmE,QAAQ,EAAI,EAAE,CAAC,CAAC,CACjF,KAAM,CAAA9E,GAAG,CAAG0I,+BAA+B,CAAC,CAAC,CAE7C,MAAO,CAAA1I,GAAG,EAAIgJ,gBAAgB,EAAIhJ,GAAG,CAAGiJ,cAAc,CACxD,CAAC,CAED;AACA,KAAM,CAAAC,oBAAoB,CAAIvI,OAAO,EAAK,CACxC,GAAI,CAACA,OAAO,EAAI,CAACgB,cAAc,CAAE,MAAO,CAAAnD,CAAC,CAAC,qBAAqB,CAAC,CAEhE;AACA,GAAImC,OAAO,CAACE,kBAAkB,CAAE,CAC9B,MAAO,CAAArC,CAAC,CAAC,yBAAyB,CAAE,qBAAqB,CAAC,CAC5D,CAEA,KAAM,CAAAuK,aAAa,CAAGF,gBAAgB,CAAClI,OAAO,CAAC,CAC/C,KAAM,CAAAwI,OAAO,CAAGL,cAAc,CAACnI,OAAO,CAAC,CAEvC,GAAIwI,OAAO,CAAE,CACX,MAAO,CAAA3K,CAAC,CAAC,eAAe,CAAC,CAC3B,CAEA,OAAQuK,aAAa,EACnB,IAAK,WAAW,CACd,MAAO,CAAAvK,CAAC,CAAC,2BAA2B,CAAC,CACvC,IAAK,WAAW,CACd,MAAO,CAAAA,CAAC,CAAC,2BAA2B,CAAC,CACvC,IAAK,SAAS,CACZ,MAAO,CAAAA,CAAC,CAAC,eAAe,CAAC,CAC3B,IAAK,WAAW,CAChB,QACE,MAAO,CAAAA,CAAC,CAAC,qBAAqB,CAAC,CACnC,CACF,CAAC,CAED;AACA,KAAM,CAAA4K,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAAChK,eAAe,CAAE,OAEtB,GAAI,CACFO,oBAAoB,CAAC,IAAI,CAAC,CAC1B,KAAM,CAAEyE,IAAK,CAAC,CAAG,KAAM,CAAAjH,KAAK,CAACkM,GAAG,CAAC,aAAajK,eAAe,CAACkC,EAAE,SAAS,CAAE,CACzEgI,mBAAmB,CAAE1J,kBACvB,CAAC,CAAE,CACD0E,OAAO,CAAE,CACP,eAAe,CAAE,UAAU5F,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI0F,IAAI,CAACG,OAAO,CAAE,CAChB;AACAxF,WAAW,CAACwK,YAAY,EACtBA,YAAY,CAAC3C,GAAG,CAACjG,OAAO,EACtBA,OAAO,CAACW,EAAE,GAAKlC,eAAe,CAACkC,EAAE,CAC7B,CAAE,GAAGX,OAAO,CAAEC,MAAM,CAAE,WAAY,CAAC,CACnCD,OACN,CACF,CAAC,CAED;AACA,GAAIyD,IAAI,CAACoF,mBAAmB,CAAE,CAC5BvL,KAAK,CAACsG,OAAO,CAAC/F,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAC5C,CAAC,IAAM,IAAI4F,IAAI,CAACqF,uBAAuB,CAAE,CACvCxL,KAAK,CAACO,CAAC,CAAC,kCAAkC,CAAC,CAAE,CAC3CkL,IAAI,CAAE,IAAI,CACVC,KAAK,CAAE,CACLC,UAAU,CAAE,SAAS,CACrBC,KAAK,CAAE,OACT,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACL5L,KAAK,CAACsG,OAAO,CAACH,IAAI,CAAC8C,OAAO,EAAI1I,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAC5D,CACF,CAAC,IAAM,CACLP,KAAK,CAACiB,KAAK,CAACkF,IAAI,CAAC8C,OAAO,EAAI1I,CAAC,CAAC,sBAAsB,CAAC,CAAC,CACxD,CACF,CAAE,MAAOU,KAAK,CAAE,KAAA4K,eAAA,CAAAC,oBAAA,CACd5I,OAAO,CAACjC,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjDjB,KAAK,CAACiB,KAAK,CAAC,EAAA4K,eAAA,CAAA5K,KAAK,CAAC6I,QAAQ,UAAA+B,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgB1F,IAAI,UAAA2F,oBAAA,iBAApBA,oBAAA,CAAsB7C,OAAO,GAAI1I,CAAC,CAAC,sBAAsB,CAAC,CAAC,CACzE,CAAC,OAAS,CACRmB,oBAAoB,CAAC,KAAK,CAAC,CAC3BJ,mBAAmB,CAAC,KAAK,CAAC,CAC1BE,oBAAoB,CAAC,KAAK,CAAC,CAC3BJ,kBAAkB,CAAC,IAAI,CAAC,CACxBQ,qBAAqB,CAAC,EAAE,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAmK,4BAA4B,CAAG,KAAO,CAAArJ,OAAO,EAAK,CACtDtB,kBAAkB,CAACsB,OAAO,CAAC,CAC3BsC,cAAc,CAAC,IAAI,CAAC,CACpBF,uBAAuB,CAAC,EAAE,CAAC,CAC3BU,mBAAmB,CAAC,EAAE,CAAC,CACvBN,cAAc,CAAC,IAAI,CAAC,CACpBR,uBAAuB,CAAC,IAAI,CAAC,CAE7B,GAAI,CACF;AACA,KAAM,CAAAsH,WAAW,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACzC,GAAIvI,cAAc,SAAdA,cAAc,WAAdA,cAAc,CAAEgH,QAAQ,CAAE,CAC5BsB,WAAW,CAACE,MAAM,CAAC,kBAAkB,CAAExI,cAAc,CAACgH,QAAQ,CAAC,CACjE,CAEA,KAAM,CAAEvE,IAAK,CAAC,CAAG,KAAM,CAAAjH,KAAK,CAACkH,GAAG,CAAC,aAAa1D,OAAO,CAACW,EAAE,oBAAoB2I,WAAW,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAE,CACpG9F,OAAO,CAAE,CACP,eAAe,CAAE,UAAU5F,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI0F,IAAI,CAACG,OAAO,CAAE,CAChBpD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEgD,IAAI,CAAC,CAE1D;AACA;AACA,KAAM,CAAAiG,UAAU,CAAGjG,IAAI,CAACA,IAAI,CAE5BjD,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAE,CACxDkJ,KAAK,CAAElG,IAAI,CAACA,IAAI,CAACoB,MAAM,CACvB+E,IAAI,CAAE,4CACR,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,WAAW,CAAG,CAAC,CAAC,CACtBH,UAAU,CAACI,OAAO,CAACC,IAAI,EAAI,CACzB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACC,IAAI,CACtB,GAAI,CAACH,WAAW,CAACG,IAAI,CAAC,CAAE,CACtBH,WAAW,CAACG,IAAI,CAAC,CAAG,EAAE,CACxB,CACAH,WAAW,CAACG,IAAI,CAAC,CAACC,IAAI,CAAC,CACrB,GAAGF,IAAI,CACP;AACAG,WAAW,CAAEH,IAAI,CAACI,IAAI,EAAI,GAAI,CAAA7K,IAAI,CAACyK,IAAI,CAACxJ,QAAQ,CAAC,CAAC6J,kBAAkB,CAAC,OAAO,CAAE,CAC5EC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,IACV,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAtI,aAAa,CAAGuI,MAAM,CAACC,IAAI,CAACZ,WAAW,CAAC,CAC3C7E,MAAM,CAACgF,IAAI,EAAIH,WAAW,CAACG,IAAI,CAAC,CAACnF,MAAM,CAAG,CAAC,CAAE;AAAA,CAC7CoB,GAAG,CAAC+D,IAAI,EAAI,CACX,KAAM,CAAAU,KAAK,CAAGb,WAAW,CAACG,IAAI,CAAC,CAC/B,KAAM,CAAAW,OAAO,CAAG,GAAI,CAAArL,IAAI,CAAC0K,IAAI,CAAC,CAE9B,MAAO,CACLA,IAAI,CAAEA,IAAI,CACVY,OAAO,CAAED,OAAO,CAACE,kBAAkB,CAAC,OAAO,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAC,CAAC,CACjEC,aAAa,CAAEJ,OAAO,CAACE,kBAAkB,CAAC,OAAO,CAAE,CACjDG,KAAK,CAAE,OAAO,CACdC,GAAG,CAAE,SACP,CAAC,CAAC,CACFP,KAAK,CAAEA,KAAK,CACZQ,cAAc,CAAER,KAAK,CAAC7F,MAAM,CAC5BsG,cAAc,CAAET,KAAK,CAAC7F,MACxB,CAAC,CACH,CAAC,CAAC,CAEJrE,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAEwB,aAAa,CAAC,CAC5DC,gBAAgB,CAACD,aAAa,CAAC,CACjC,CAAC,IAAM,CACLzB,OAAO,CAACjC,KAAK,CAAC,uBAAuB,CAAEkF,IAAI,CAAC,CAC5CnG,KAAK,CAACiB,KAAK,CAACkF,IAAI,CAAC8C,OAAO,EAAI1I,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAC3D,CACF,CAAE,MAAOU,KAAK,CAAE,KAAA6M,gBAAA,CAAAC,qBAAA,CACd7K,OAAO,CAACjC,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDjB,KAAK,CAACiB,KAAK,CAAC,EAAA6M,gBAAA,CAAA7M,KAAK,CAAC6I,QAAQ,UAAAgE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgB3H,IAAI,UAAA4H,qBAAA,iBAApBA,qBAAA,CAAsB9E,OAAO,GAAI1I,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAC5E,CAAC,OAAS,CACR2E,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAID;AACA,KAAM,CAAA8I,uBAAuB,CAAG,cAAAA,CAAOC,QAAQ,CAAkB,IAAhB,CAAAC,MAAM,CAAA9E,SAAA,CAAA7B,MAAA,IAAA6B,SAAA,MAAAL,SAAA,CAAAK,SAAA,IAAG,EAAE,CAC1D,GAAI,CAAC6E,QAAQ,CAAE,CACbjO,KAAK,CAACiB,KAAK,CAAC,2BAA2B,CAAC,CACxC,OACF,CAEA,GAAI,CACFqE,eAAe,CAAC,IAAI,CAAC,CAErB;AACA,KAAM,CAAA6I,WAAW,CAAGF,QAAQ,CAAChL,QAAQ,CAErC,KAAM,CAAEkD,IAAK,CAAC,CAAG,KAAM,CAAAjH,KAAK,CAACkM,GAAG,CAAC,aAAajK,eAAe,CAACkC,EAAE,aAAa,CAAE,CAC7E8K,WAAW,CAAEA,WAAW,CACxBC,WAAW,CAAEjN,eAAe,CAAC0F,QAAQ,CACrCwH,iBAAiB,CAAEH,MAAM,EAAI3I,gBAC/B,CAAC,CAAE,CACDc,OAAO,CAAE,CACP,eAAe,CAAE,UAAU5F,KAAK,EAClC,CACF,CAAC,CAAC,CAEF,GAAI0F,IAAI,CAACG,OAAO,CAAE,CAChB;AACAxF,WAAW,CAACwK,YAAY,EACtBA,YAAY,CAAC3C,GAAG,CAACjG,OAAO,EACtBA,OAAO,CAACW,EAAE,GAAKlC,eAAe,CAACkC,EAAE,CAC7B,CAAE,GAAGX,OAAO,CAAEO,QAAQ,CAAEkL,WAAY,CAAC,CACrCzL,OACN,CACF,CAAC,CACD1C,KAAK,CAACsG,OAAO,CAAC/F,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAChD,CAAC,IAAM,CACLP,KAAK,CAACiB,KAAK,CAACkF,IAAI,CAAC8C,OAAO,EAAI1I,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAC5D,CACF,CAAE,MAAOU,KAAK,CAAE,KAAAqN,gBAAA,CAAAC,qBAAA,CACdrL,OAAO,CAACjC,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDjB,KAAK,CAACiB,KAAK,CAAC,EAAAqN,gBAAA,CAAArN,KAAK,CAAC6I,QAAQ,UAAAwE,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBnI,IAAI,UAAAoI,qBAAA,iBAApBA,qBAAA,CAAsBtF,OAAO,GAAI1I,CAAC,CAAC,0BAA0B,CAAC,CAAC,CAC7E,CAAC,OAAS,CACR+E,eAAe,CAAC,KAAK,CAAC,CACtBZ,uBAAuB,CAAC,KAAK,CAAC,CAC9BlD,oBAAoB,CAAC,KAAK,CAAC,CAC3BwD,cAAc,CAAC,IAAI,CAAC,CACpBF,uBAAuB,CAAC,EAAE,CAAC,CAC3BU,mBAAmB,CAAC,EAAE,CAAC,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAgJ,cAAc,CAAI7L,MAAM,EAAK,CACjC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,WAAW,CACd,MAAO,OAAO,CAChB,IAAK,gBAAgB,CACnB,MAAO,SAAS,CAClB,IAAK,SAAS,CACZ,MAAO,MAAM,CACf,QACE,MAAO,SAAS,CACpB,CACF,CAAC,CAED;AACA,KAAM,CAAA8L,aAAa,CAAI9L,MAAM,EAAK,CAChC,MAAO,CAAApC,CAAC,CAAC,yBAAyBoC,MAAM,EAAE,CAAE,CAC1C+L,YAAY,CAAE/L,MAAM,CAACgM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGjM,MAAM,CAACkM,KAAK,CAAC,CAAC,CAC/D,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAI7L,QAAQ,EAAK,CACtC,GAAI,CAACS,cAAc,EAAI,CAACA,cAAc,CAACgH,QAAQ,CAAE,CAC/C,MAAO,CAAA/L,MAAM,CAAC,GAAI,CAAAqD,IAAI,CAACiB,QAAQ,CAAC,CAAE,KAAK,CAAE,CAAE8L,MAAM,CAAEpO,KAAK,CAAG3B,EAAE,CAAGC,IAAK,CAAC,CAAC,CACzE,CAEA,KAAM,CAAAwO,aAAa,CAAG/N,2BAA2B,CAACuD,QAAQ,CAAES,cAAc,CAACgH,QAAQ,CAAE,YAAY,CAAC,CAClG,MAAO,CAAA9K,MAAM,CAAC6N,aAAa,CAAE,YAAY,CAAC,CAAC9O,MAAM,CAAC,cAAc,CAAC,CACnE,CAAC,CAED;AACA,KAAM,CAAAqQ,iBAAiB,CAAI/L,QAAQ,EAAK,CACtC,GAAI,CAACS,cAAc,EAAI,CAACA,cAAc,CAACgH,QAAQ,CAAE,CAC/C,MAAO,CAAA/L,MAAM,CAAC,GAAI,CAAAqD,IAAI,CAACiB,QAAQ,CAAC,CAAE,GAAG,CAAE,CAAE8L,MAAM,CAAEpO,KAAK,CAAG3B,EAAE,CAAGC,IAAK,CAAC,CAAC,CACvE,CAEA,KAAM,CAAAgQ,iBAAiB,CAAGvP,2BAA2B,CAACuD,QAAQ,CAAES,cAAc,CAACgH,QAAQ,CAAE,qBAAqB,CAAC,CAC/G,MAAO,CAAA9K,MAAM,CAACqP,iBAAiB,CAAE,qBAAqB,CAAC,CAACtQ,MAAM,CAAC,QAAQ,CAAC,CAC1E,CAAC,CAED;AACA,KAAM,CAAAuQ,sBAAsB,CAAGA,CAACjM,QAAQ,CAAE4D,QAAQ,GAAK,CACrD,GAAI,CAACnD,cAAc,EAAI,CAACA,cAAc,CAACgH,QAAQ,CAAE,CAC/C,KAAM,CAAAyE,SAAS,CAAG,GAAI,CAAAnN,IAAI,CAACiB,QAAQ,CAAC,CACpC,KAAM,CAAAmM,OAAO,CAAG,GAAI,CAAApN,IAAI,CAACmN,SAAS,CAAChN,OAAO,CAAC,CAAC,CAAG0E,QAAQ,CAAG,KAAK,CAAC,CAEhE,KAAM,CAAAwI,YAAY,CAAGF,SAAS,CAACrC,kBAAkB,CAAC,EAAE,CAAE,CACpDC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,KACV,CAAC,CAAC,CACF,KAAM,CAAAqC,UAAU,CAAGF,OAAO,CAACtC,kBAAkB,CAAC,EAAE,CAAE,CAChDC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,KACV,CAAC,CAAC,CAEF,MAAO,GAAGoC,YAAY,MAAMC,UAAU,EAAE,CAC1C,CAEA;AACA,KAAM,CAAAL,iBAAiB,CAAGvP,2BAA2B,CAACuD,QAAQ,CAAES,cAAc,CAACgH,QAAQ,CAAE,qBAAqB,CAAC,CAC/G,KAAM,CAAC6E,QAAQ,CAAEC,QAAQ,CAAC,CAAGP,iBAAiB,CAACQ,KAAK,CAAC,GAAG,CAAC,CACzD,KAAM,CAACrN,KAAK,CAAEC,OAAO,CAAC,CAAGmN,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAE5C;AACA,KAAM,CAAAC,YAAY,CAAG5M,QAAQ,CAACV,KAAK,CAAC,CAAG,EAAE,CAAGU,QAAQ,CAACT,OAAO,CAAC,CAC7D,KAAM,CAAAsN,UAAU,CAAGD,YAAY,CAAG7I,QAAQ,CAE1C,KAAM,CAAA+I,SAAS,CAAGrN,IAAI,CAACC,KAAK,CAACkN,YAAY,CAAG,EAAE,CAAC,CAC/C,KAAM,CAAAG,QAAQ,CAAGH,YAAY,CAAG,EAAE,CAClC,KAAM,CAAAI,OAAO,CAAGvN,IAAI,CAACC,KAAK,CAACmN,UAAU,CAAG,EAAE,CAAC,CAC3C,KAAM,CAAAI,MAAM,CAAGJ,UAAU,CAAG,EAAE,CAE9B,KAAM,CAAAN,YAAY,CAAG,GAAGrG,MAAM,CAAC4G,SAAS,CAAC,CAACI,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIhH,MAAM,CAAC6G,QAAQ,CAAC,CAACG,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACjG,KAAM,CAAAV,UAAU,CAAG,GAAGtG,MAAM,CAAC8G,OAAO,CAAC,CAACE,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAIhH,MAAM,CAAC+G,MAAM,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAE3F,MAAO,GAAGX,YAAY,MAAMC,UAAU,EAAE,CAC1C,CAAC,CAED;AACA,KAAM,CAAAW,oBAAoB,CAAG,KAAAA,CAAOC,SAAS,CAAEC,MAAM,GAAK,CACxD,GAAI,CACFjN,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAE,CAAE+M,SAAS,CAAEC,MAAO,CAAC,CAAC,CAE1D,KAAM,CAAArG,QAAQ,CAAG,KAAM,CAAA5K,KAAK,CAACmL,IAAI,CAAC,iBAAiB,CAAE,CACnD/B,UAAU,CAAE4H,SAAS,CACrB5I,UAAU,CAAE6I,MAAM,CAAC7I,UAAU,CAC7BgD,UAAU,CAAE6F,MAAM,CAAC7F,UAAU,CAC7BC,WAAW,CAAE4F,MAAM,CAAC5F,WACtB,CAAC,CAAC,CAEFrH,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAE2G,QAAQ,CAAC3D,IAAI,CAAC,CAE9D;AACA,GAAIgK,MAAM,CAAC7I,UAAU,CAAE,CACrB,KAAM,CAAA8I,SAAS,CAAGD,MAAM,CAAC7F,UAAU,GAAK,UAAU,CAAG,WAAW,CAAG,gBAAgB,CACnFxJ,WAAW,CAACyH,IAAI,EAAIA,IAAI,CAACI,GAAG,CAAChB,CAAC,EAC5BA,CAAC,CAACtE,EAAE,GAAK8M,MAAM,CAAC7I,UAAU,CAAG,CAAE,GAAGK,CAAC,CAAEhF,MAAM,CAAEyN,SAAU,CAAC,CAAGzI,CAC5D,CAAC,CAAC,CACHzE,OAAO,CAACC,GAAG,CAAC,mBAAmBgN,MAAM,CAAC7I,UAAU,cAAc8I,SAAS,EAAE,CAAC,CAC5E,CAEA;AACA9L,qBAAqB,CAAC,KAAK,CAAC,CAC5BE,kBAAkB,CAAC,IAAI,CAAC,CAExB;AACAgF,UAAU,CAAC,IAAM,CACftG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC,CACpDqD,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,IAAI,CAAC,CAAE;AAEZ,CAAE,MAAOM,GAAG,CAAE,CACZ5D,OAAO,CAACjC,KAAK,CAAC,2BAA2B,CAAE6F,GAAG,CAAC,CAC/C;AACAuJ,KAAK,CAAC,8CAA8C,CAAC,CACrD,OAAQ;AACV,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAIzP,QAAQ,CAAC0G,MAAM,GAAK,CAAC,CAAE,CACzB,mBACErH,IAAA,CAAClE,GAAG,EAACuU,EAAE,CAAE,CAAEC,SAAS,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cACtCxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAC/E,KAAK,CAAC,gBAAgB,CAAA8E,QAAA,CAC/CnQ,CAAC,CAAC,qBAAqB,CAAC,CACf,CAAC,CACV,CAAC,CAEV,CAEA,mBACEL,IAAA,CAAChE,IAAI,EAAC0U,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAH,QAAA,CACxB7P,QAAQ,CAAC8H,GAAG,CAAEjG,OAAO,eACpBxC,IAAA,CAAChE,IAAI,EAAC4U,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAP,QAAA,cAC9BtQ,KAAA,CAACjE,IAAI,EACH+U,SAAS,CAAE,CAAE,CACbX,EAAE,CAAE,CACFY,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,OAAO,CAAE5O,OAAO,CAACE,kBAAkB,CAAG,GAAG,CAAG,CAAC,CAC7C2O,MAAM,CAAE7O,OAAO,CAACE,kBAAkB,CAAG,aAAalC,KAAK,CAAC8Q,OAAO,CAACvQ,KAAK,CAACwQ,KAAK,EAAE,CAAG,MAAM,CACtFC,UAAU,CAAE,eACd,CAAE,CAAAhB,QAAA,eAEFtQ,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CACPoB,OAAO,CAAE,cAAc,CACvB/F,KAAK,CAAE,OAAO,CACdgG,CAAC,CAAE,CAAC,CACJR,OAAO,CAAE,MAAM,CACfS,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CACP,CAAE,CAAApB,QAAA,eACAxQ,IAAA,CAAC1C,YAAY,GAAE,CAAC,cAChB0C,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,IAAI,CAAAD,QAAA,CACrB5B,iBAAiB,CAACpM,OAAO,CAACO,QAAQ,CAAC,CAC1B,CAAC,EACV,CAAC,cACN7C,KAAA,CAAChE,WAAW,EAACmU,EAAE,CAAE,CAAEwB,QAAQ,CAAE,CAAE,CAAE,CAAArB,QAAA,eAC/BtQ,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAES,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACxDxQ,IAAA,CAACnD,MAAM,EACLkV,GAAG,CAAEvP,OAAO,CAACwP,eAAgB,CAC7BC,GAAG,CAAEzP,OAAO,CAACkF,YAAa,CAC1B2I,EAAE,CAAE,CACF6B,EAAE,CAAE,CAAC,CACLd,OAAO,CAAE5O,OAAO,CAACE,kBAAkB,CAAG,GAAG,CAAG,CAAC,CAC7C8E,MAAM,CAAEhF,OAAO,CAACE,kBAAkB,CAAG,iBAAiB,CAAG,MAC3D,CAAE,CACH,CAAC,cACFxC,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAE8B,IAAI,CAAE,CAAE,CAAE,CAAA3B,QAAA,eACnBxQ,IAAA,CAACnE,UAAU,EACT4U,OAAO,CAAC,WAAW,CACnBJ,EAAE,CAAE,CACF3E,KAAK,CAAElJ,OAAO,CAACE,kBAAkB,CAAG,gBAAgB,CAAG,cAAc,CACrE0P,cAAc,CAAE5P,OAAO,CAACE,kBAAkB,CAAG,cAAc,CAAG,MAChE,CAAE,CAAA8N,QAAA,CAEDhO,OAAO,CAACkF,YAAY,CACX,CAAC,CACZlF,OAAO,CAACE,kBAAkB,eACzB1C,IAAA,CAAC5D,IAAI,EACHiW,KAAK,CAAEhS,CAAC,CAAC,yBAAyB,CAAE,yBAAyB,CAAE,CAC/DiS,IAAI,CAAC,OAAO,CACZ5G,KAAK,CAAC,OAAO,CACb+E,OAAO,CAAC,UAAU,CAClBJ,EAAE,CAAE,CAAEkC,EAAE,CAAE,GAAG,CAAEC,QAAQ,CAAE,QAAS,CAAE,CACrC,CACF,EACE,CAAC,EACH,CAAC,cAENxS,IAAA,CAACpD,OAAO,EAACyT,EAAE,CAAE,CAAEoC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1BvS,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAES,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACxDxQ,IAAA,CAACxC,QAAQ,EAAC6S,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAC,CAAExG,KAAK,CAAE,gBAAiB,CAAE,CAAE,CAAC,cACpD1L,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAAD,QAAA,CACxB1B,iBAAiB,CAACtM,OAAO,CAACO,QAAQ,CAAC,CAC1B,CAAC,EACV,CAAC,cAEN7C,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAES,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACxDxQ,IAAA,CAACxC,QAAQ,EAAC6S,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAC,CAAExG,KAAK,CAAE,gBAAiB,CAAE,CAAE,CAAC,cACpDxL,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAAD,QAAA,EACxBnQ,CAAC,CAAC,mBAAmB,CAAC,CAAC,IAAE,CAACmC,OAAO,CAACmE,QAAQ,EAAI,EAAE,CAAC,GAAC,CAACtG,CAAC,CAAC,kBAAkB,CAAC,CACxEmC,OAAO,CAACmE,QAAQ,GAAK,IAAI,EAAInE,OAAO,CAACmE,QAAQ,GAAK,EAAE,CACnD,KAAKtG,CAAC,CAAC,oBAAoB,CAAC,EAAI,SAAS,GAAG,CAC5C,KAAKA,CAAC,CAAC,oBAAoB,CAAC,EAAI,UAAU,GAAG,EACrC,CAAC,EACV,CAAC,cAENH,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAES,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACxDxQ,IAAA,CAAChC,SAAS,EAACqS,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAC,CAAExG,KAAK,CAAE,gBAAiB,CAAE,CAAE,CAAC,cACrD1L,IAAA,CAAC5D,IAAI,EACHiW,KAAK,CAAE9D,aAAa,CAAC/L,OAAO,CAACC,MAAM,CAAE,CACrCiJ,KAAK,CAAE4C,cAAc,CAAC9L,OAAO,CAACC,MAAM,CAAE,CACtC6P,IAAI,CAAC,OAAO,CACb,CAAC,EACC,CAAC,cAENpS,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAC,CAAErB,OAAO,CAAE,MAAM,CAAEwB,cAAc,CAAE,eAAe,CAAEf,UAAU,CAAE,QAAS,CAAE,CAAAnB,QAAA,eACzFtQ,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACkC,UAAU,CAAC,MAAM,CAAAnC,QAAA,EAC1CnQ,CAAC,CAAC,gBAAgB,CAAC,CAAC,IAAE,CAAC,CAAC,IAAM,CAC7B,GAAImC,OAAO,CAACoG,UAAU,GAAK,IAAI,EAAI,CAACgK,KAAK,CAACpQ,OAAO,CAACoG,UAAU,CAAC,CAAE,CAC7D,MAAO,CAAApG,OAAO,CAACoG,UAAU,CAACiK,OAAO,CAAC,CAAC,CAAC,CACtC,CACA,KAAM,CAAAC,SAAS,CAAGnK,UAAU,CAACnG,OAAO,CAACkG,gBAAgB,EAAI,CAAC,CAAC,CAC3D,KAAM,CAAAqK,cAAc,CAAGvQ,OAAO,CAACmE,QAAQ,GAAK,IAAI,EAAInE,OAAO,CAACmE,QAAQ,GAAK,EAAE,CAC3E,KAAM,CAAAqM,UAAU,CAAGD,cAAc,CAAGD,SAAS,CAAG,CAAC,CAAGA,SAAS,CAC7D,MAAO,CAAAE,UAAU,CAACH,OAAO,CAAC,CAAC,CAAC,CAC9B,CAAC,EAAE,CAAC,CAAC,IACP,EAAY,CAAC,cAEb7S,IAAA,CAAC7D,MAAM,EACLsU,OAAO,CAAC,UAAU,CAClB6B,IAAI,CAAC,OAAO,CACZW,OAAO,CAAEA,CAAA,GAAM,CACb/R,kBAAkB,CAACsB,OAAO,CAAC,CAC3BlB,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAE,CAAAkP,QAAA,CAEDnQ,CAAC,CAAC,kBAAkB,CAAC,CAChB,CAAC,EACN,CAAC,EACK,CAAC,EACV,CAAC,EA/G6BmC,OAAO,CAACW,EAgHxC,CACP,CAAC,CACE,CAAC,CAEX,CAAC,CAED;AACA,KAAM,CAAA+P,mBAAmB,CAAGA,CAAA,gBAC1BhT,KAAA,CAAC3D,MAAM,EAAC4W,IAAI,CAAE9R,iBAAkB,CAAC+R,OAAO,CAAEA,CAAA,GAAM9R,oBAAoB,CAAC,KAAK,CAAE,CAAC+R,QAAQ,CAAC,IAAI,CAACC,SAAS,MAAA9C,QAAA,eAClGtQ,KAAA,CAAC1D,WAAW,EAAAgU,QAAA,EACTnQ,CAAC,CAAC,yBAAyB,CAAC,cAC7BL,IAAA,CAACrD,UAAU,EACT,aAAW,OAAO,CAClBsW,OAAO,CAAEA,CAAA,GAAM3R,oBAAoB,CAAC,KAAK,CAAE,CAC3C+O,EAAE,CAAE,CAAEkD,QAAQ,CAAE,UAAU,CAAEC,KAAK,CAAE,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAjD,QAAA,cAE/CxQ,IAAA,CAAClC,SAAS,GAAE,CAAC,CACH,CAAC,EACF,CAAC,cACdkC,IAAA,CAACvD,aAAa,EAAA+T,QAAA,CACXvP,eAAe,eACdf,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,eAEjBtQ,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEQ,UAAU,CAAE,QAAQ,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACjFxQ,IAAA,CAACnD,MAAM,EACLkV,GAAG,CAAE9Q,eAAe,CAAC+Q,eAAe,CAClC/Q,eAAe,CAAC+Q,eAAe,CAAC0B,UAAU,CAAC,MAAM,CAAC,CAC9CzS,eAAe,CAAC+Q,eAAe,CAC/B,4BAA4B/Q,eAAe,CAAC+Q,eAAe,EAAE,CAC/D,EAAG,CACPC,GAAG,CAAEhR,eAAe,CAACyG,YAAa,CAClC2I,EAAE,CAAE,CACFsD,KAAK,CAAE,GAAG,CACV1C,MAAM,CAAE,GAAG,CACXI,MAAM,CAAE,aAAapQ,eAAe,CAACyB,kBAAkB,CAAGlC,KAAK,CAAC8Q,OAAO,CAACvQ,KAAK,CAAC6S,IAAI,CAAGpT,KAAK,CAAC8Q,OAAO,CAACuC,OAAO,CAACD,IAAI,EAAE,CACjH9B,EAAE,CAAE,CAAC,CACLV,OAAO,CAAEnQ,eAAe,CAACyB,kBAAkB,CAAG,GAAG,CAAG,CAAC,CACrD8E,MAAM,CAAEvG,eAAe,CAACyB,kBAAkB,CAAG,iBAAiB,CAAG,MACnE,CAAE,CACH,CAAC,cACFxC,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEC,SAAS,CAAE,QAAS,CAAE,CAAAE,QAAA,eAC/BxQ,IAAA,CAACnE,UAAU,EACT4U,OAAO,CAAC,IAAI,CACZqD,YAAY,MACZzD,EAAE,CAAE,CACF3E,KAAK,CAAEzK,eAAe,CAACyB,kBAAkB,CAAG,gBAAgB,CAAG,cAAc,CAC7E0P,cAAc,CAAEnR,eAAe,CAACyB,kBAAkB,CAAG,cAAc,CAAG,MACxE,CAAE,CAAA8N,QAAA,CAEDvP,eAAe,CAACyG,YAAY,CACnB,CAAC,cACb1H,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAC/E,KAAK,CAAC,gBAAgB,CAAA8E,QAAA,CAC/CnQ,CAAC,CAAC,kBAAkB,CAAC,CACZ,CAAC,CACZY,eAAe,CAACyB,kBAAkB,eACjC1C,IAAA,CAAC5D,IAAI,EACHiW,KAAK,CAAEhS,CAAC,CAAC,yBAAyB,CAAE,yBAAyB,CAAE,CAC/DiS,IAAI,CAAC,OAAO,CACZ5G,KAAK,CAAC,OAAO,CACb+E,OAAO,CAAC,QAAQ,CAChBJ,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAE,CAAE,CACf,CACF,EACE,CAAC,EACH,CAAC,cAENvS,IAAA,CAACpD,OAAO,EAACyT,EAAE,CAAE,CAAEoC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BvS,KAAA,CAAClE,IAAI,EAAC0U,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAH,QAAA,eACzBtQ,KAAA,CAAClE,IAAI,EAAC4U,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACvBxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAC/E,KAAK,CAAC,gBAAgB,CAACoI,YAAY,MAAAtD,QAAA,CAC5DnQ,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbL,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACqD,YAAY,MAAAtD,QAAA,CACrC5B,iBAAiB,CAAC3N,eAAe,CAAC8B,QAAQ,CAAC,CAClC,CAAC,EACT,CAAC,cACP7C,KAAA,CAAClE,IAAI,EAAC4U,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACvBxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAC/E,KAAK,CAAC,gBAAgB,CAACoI,YAAY,MAAAtD,QAAA,CAC5DnQ,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbL,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACqD,YAAY,MAAAtD,QAAA,CACrC1B,iBAAiB,CAAC7N,eAAe,CAAC8B,QAAQ,CAAC,CAClC,CAAC,cACb/C,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAC/E,KAAK,CAAC,gBAAgB,CAAC2E,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAE,CAAE,CAAA/B,QAAA,CAC9DxB,sBAAsB,CAAC/N,eAAe,CAAC8B,QAAQ,CAAEH,QAAQ,CAAC3B,eAAe,CAAC0F,QAAQ,CAAC,EAAI,EAAE,CAAC,CACjF,CAAC,EACT,CAAC,cACPzG,KAAA,CAAClE,IAAI,EAAC4U,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACvBxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAC/E,KAAK,CAAC,gBAAgB,CAACoI,YAAY,MAAAtD,QAAA,CAC5DnQ,CAAC,CAAC,mBAAmB,CAAC,CACb,CAAC,cACbH,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACqD,YAAY,MAAAtD,QAAA,EACrCvP,eAAe,CAAC0F,QAAQ,EAAI,EAAE,CAAC,GAAC,CAACtG,CAAC,CAAC,kBAAkB,CAAC,CACtDY,eAAe,CAAC0F,QAAQ,GAAK,IAAI,EAAI1F,eAAe,CAAC0F,QAAQ,GAAK,EAAE,CACnE,KAAKtG,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAC/B,KAAKA,CAAC,CAAC,oBAAoB,CAAC,GAAG,EACvB,CAAC,EACT,CAAC,cACPH,KAAA,CAAClE,IAAI,EAAC4U,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,eACvBxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAC/E,KAAK,CAAC,gBAAgB,CAACoI,YAAY,MAAAtD,QAAA,CAC5DnQ,CAAC,CAAC,uBAAuB,CAAC,CACjB,CAAC,cACbL,IAAA,CAAC5D,IAAI,EACHiW,KAAK,CAAE9D,aAAa,CAACtN,eAAe,CAACwB,MAAM,CAAE,CAC7CiJ,KAAK,CAAE4C,cAAc,CAACrN,eAAe,CAACwB,MAAM,CAAE,CAC9C6P,IAAI,CAAC,OAAO,CACb,CAAC,EACE,CAAC,cACPpS,KAAA,CAAClE,IAAI,EAAC4U,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAL,QAAA,eAChBxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAC/E,KAAK,CAAC,gBAAgB,CAACoI,YAAY,MAAAtD,QAAA,CAC5DnQ,CAAC,CAAC,gBAAgB,CAAC,CACV,CAAC,cACbH,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,IAAI,CAAC/E,KAAK,CAAC,SAAS,CAAA8E,QAAA,EACrC,CAAC,IAAM,CACN,GAAIvP,eAAe,CAAC2H,UAAU,GAAK,IAAI,EAAI,CAACgK,KAAK,CAAC3R,eAAe,CAAC2H,UAAU,CAAC,CAAE,CAC7E,MAAO,CAAA3H,eAAe,CAAC2H,UAAU,CAACiK,OAAO,CAAC,CAAC,CAAC,CAC9C,CACA,KAAM,CAAAC,SAAS,CAAGnK,UAAU,CAAC1H,eAAe,CAACyH,gBAAgB,EAAI,CAAC,CAAC,CACnE,KAAM,CAAAqK,cAAc,CAAG9R,eAAe,CAAC0F,QAAQ,GAAK,IAAI,EAAI1F,eAAe,CAAC0F,QAAQ,GAAK,EAAE,CAC3F,KAAM,CAAAqM,UAAU,CAAGD,cAAc,CAAGD,SAAS,CAAG,CAAC,CAAGA,SAAS,CAC7D,MAAO,CAAAE,UAAU,CAACH,OAAO,CAAC,CAAC,CAAC,CAC9B,CAAC,EAAE,CAAC,CAAC,GAAC,CAACxS,CAAC,CAAC,iBAAiB,CAAC,EACjB,CAAC,EACT,CAAC,EACH,CAAC,EACJ,CACN,CACY,CAAC,cAChBH,KAAA,CAACxD,aAAa,EAAA8T,QAAA,eACZxQ,IAAA,CAAC7D,MAAM,EAAC8W,OAAO,CAAEA,CAAA,GAAM3R,oBAAoB,CAAC,KAAK,CAAE,CAAAkP,QAAA,CAChDnQ,CAAC,CAAC,cAAc,CAAC,CACZ,CAAC,CAGRY,eAAe,eACdjB,IAAA,CAAC7D,MAAM,EACL8W,OAAO,CAAEA,CAAA,GAAMtI,cAAc,CAAC1J,eAAe,CAAC,EAAIyI,iBAAiB,CAACzI,eAAe,CAAE,CACrFyK,KAAK,CAAEf,cAAc,CAAC1J,eAAe,CAAC,CAAG,SAAS,CAAG,SAAU,CAC/DwP,OAAO,CAAE9F,cAAc,CAAC1J,eAAe,CAAC,CAAG,WAAW,CAAG,UAAW,CACpE8S,SAAS,cAAE/T,IAAA,CAAC9B,aAAa,GAAE,CAAE,CAC7B8V,QAAQ,CAAE,CAACrJ,cAAc,CAAC1J,eAAe,CAAE,CAC3CoP,EAAE,CAAE,CACF6B,EAAE,CAAE,CAAC,CACL,IAAIvH,cAAc,CAAC1J,eAAe,CAAC,CAAG,CAAC,CAAC,CAAG,CACzCyK,KAAK,CAAElL,KAAK,CAAC8Q,OAAO,CAAC2C,IAAI,CAAC,GAAG,CAAC,CAC9BC,WAAW,CAAE1T,KAAK,CAAC8Q,OAAO,CAAC2C,IAAI,CAAC,GAAG,CAAC,CACpCE,eAAe,CAAE3T,KAAK,CAAC8Q,OAAO,CAAC2C,IAAI,CAAC,GAAG,CAAC,CACxC,SAAS,CAAE,CACTE,eAAe,CAAE3T,KAAK,CAAC8Q,OAAO,CAAC2C,IAAI,CAAC,GAAG,CAAC,CACxCC,WAAW,CAAE1T,KAAK,CAAC8Q,OAAO,CAAC2C,IAAI,CAAC,GAAG,CACrC,CACF,CAAC,CACH,CAAE,CAAAzD,QAAA,CAEDzF,oBAAoB,CAAC9J,eAAe,CAAC,CAChC,CACT,CAGAA,eAAe,EAAIsB,oBAAoB,CAACtB,eAAe,CAAC,eACvDjB,IAAA,CAAC7D,MAAM,EACL8W,OAAO,CAAEA,CAAA,GAAM,CACb3R,oBAAoB,CAAC,KAAK,CAAC,CAC3BuK,4BAA4B,CAAC5K,eAAe,CAAC,CAC/C,CAAE,CACFyK,KAAK,CAAC,SAAS,CACf+E,OAAO,CAAC,UAAU,CAClBsD,SAAS,cAAE/T,IAAA,CAACxB,cAAc,GAAE,CAAE,CAC9B6R,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAEbnQ,CAAC,CAAC,qBAAqB,CAAC,CACnB,CACT,CAGA,CAAAY,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwB,MAAM,IAAK,WAAW,EAAI,EAACxB,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEyB,kBAAkB,gBAC9E1C,IAAA,CAAC7D,MAAM,EACL8W,OAAO,CAAEA,CAAA,GAAM,CACb3R,oBAAoB,CAAC,KAAK,CAAC,CAC3BF,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAE,CACFsK,KAAK,CAAC,OAAO,CACb+E,OAAO,CAAC,WAAW,CACnBsD,SAAS,cAAE/T,IAAA,CAACpC,UAAU,GAAE,CAAE,CAAA4S,QAAA,CAEzBnQ,CAAC,CAAC,iBAAiB,CAAC,CACf,CACT,CAGAY,eAAe,EAAIA,eAAe,CAACwB,MAAM,GAAK,WAAW,EAAI,CAACxB,eAAe,CAACyB,kBAAkB,EAAI,CAACH,oBAAoB,CAACtB,eAAe,CAAC,eACzIjB,IAAA,CAAClE,GAAG,EAACuU,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAC,CAAEb,CAAC,CAAE,CAAC,CAAED,OAAO,CAAE,eAAe,CAAE2C,YAAY,CAAE,CAAE,CAAE,CAAA5D,QAAA,cAClEtQ,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAC/E,KAAK,CAAC,cAAc,CAAA8E,QAAA,EAAC,eAC5C,CAAC,CAAC,IAAM,CACT,KAAM,CAAA7N,eAAe,CAAGC,QAAQ,CAAC3B,eAAe,CAAC4B,gBAAgB,CAAC,EAAI,CAAC,CACvE,KAAM,CAAAC,aAAa,CAAGnB,sBAAsB,CAACV,eAAe,CAAC8B,QAAQ,CAAC,CAEtE,GAAIJ,eAAe,EAAI,CAAC,CAAE,CACxB,MAAO,CAAAtC,CAAC,CAAC,gCAAgC,CAAE,iGAAiG,CAAC,CAC/I,CAAC,IAAM,IAAIyC,aAAa,CAACZ,KAAK,CAAG,EAAE,EAAI,CAACY,aAAa,CAACV,SAAS,CAAE,CAC/D,MAAO,CAAA/B,CAAC,CAAC,8BAA8B,CAAE,4EAA4E,CAAC,CACxH,CAAC,IAAM,IAAIyC,aAAa,CAACV,SAAS,CAAE,CAClC,MAAO,CAAA/B,CAAC,CAAC,gCAAgC,CAAE,sCAAsC,CAAC,CACpF,CACA,MAAO,EAAE,CACX,CAAC,EAAE,CAAC,EACM,CAAC,CACV,CACN,CAGA,CAAAY,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEyB,kBAAkB,GAAI,CAAAzB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwB,MAAM,IAAK,WAAW,eAC7EzC,IAAA,CAAC1D,KAAK,EAAC+X,QAAQ,CAAC,MAAM,CAAChE,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAC,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAA9D,QAAA,CACzCnQ,CAAC,CAAC,gCAAgC,CAAE,yIAAyI,CAAC,CAC1K,CACR,EACY,CAAC,EACV,CACT,CAED;AACA,KAAM,CAAAkU,kBAAkB,CAAGA,CAAA,gBACzBrU,KAAA,CAAC3D,MAAM,EAAC4W,IAAI,CAAEhS,gBAAiB,CAACiS,OAAO,CAAEA,CAAA,GAAMhS,mBAAmB,CAAC,KAAK,CAAE,CAAAoP,QAAA,eACxEtQ,KAAA,CAAC1D,WAAW,EAAAgU,QAAA,EACTnQ,CAAC,CAAC,wBAAwB,CAAC,cAC5BL,IAAA,CAACrD,UAAU,EACT,aAAW,OAAO,CAClBsW,OAAO,CAAEA,CAAA,GAAM7R,mBAAmB,CAAC,KAAK,CAAE,CAC1CiP,EAAE,CAAE,CAAEkD,QAAQ,CAAE,UAAU,CAAEC,KAAK,CAAE,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAjD,QAAA,cAE/CxQ,IAAA,CAAClC,SAAS,GAAE,CAAC,CACH,CAAC,EACF,CAAC,cACdoC,KAAA,CAACzD,aAAa,EAAA+T,QAAA,eACZxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACJ,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,CACvCnQ,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,cAGbL,IAAA,CAAC1D,KAAK,EAAC+X,QAAQ,CAAC,SAAS,CAAChE,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,cACtCxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAAAD,QAAA,CACxBnQ,CAAC,CAAC,iCAAiC,CAAC,CAC3B,CAAC,CACR,CAAC,CACPY,eAAe,eACdf,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACjBtQ,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACqD,YAAY,MAAAtD,QAAA,eACtCtQ,KAAA,WAAAsQ,QAAA,EAASnQ,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACY,eAAe,CAACyG,YAAY,EAC7D,CAAC,cACbxH,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACqD,YAAY,MAAAtD,QAAA,eACtCtQ,KAAA,WAAAsQ,QAAA,EAASnQ,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACuO,iBAAiB,CAAC3N,eAAe,CAAC8B,QAAQ,CAAC,EACzE,CAAC,cACb7C,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACqD,YAAY,MAAAtD,QAAA,eACtCtQ,KAAA,WAAAsQ,QAAA,EAASnQ,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACyO,iBAAiB,CAAC7N,eAAe,CAAC8B,QAAQ,CAAC,EACzE,CAAC,cACb7C,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACqD,YAAY,MAACpI,KAAK,CAAC,gBAAgB,CAAA8E,QAAA,eAC7DtQ,KAAA,WAAAsQ,QAAA,EAASnQ,CAAC,CAAC,oBAAoB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC2O,sBAAsB,CAAC/N,eAAe,CAAC8B,QAAQ,CAAEH,QAAQ,CAAC3B,eAAe,CAAC0F,QAAQ,CAAC,EAAI,EAAE,CAAC,EAC7H,CAAC,CAGZ,CAAC,IAAM,CACN,KAAM,CAAA7D,aAAa,CAAGnB,sBAAsB,CAACV,eAAe,CAAC8B,QAAQ,CAAC,CACtE,KAAM,CAAAyR,YAAY,CAAG1R,aAAa,CAACZ,KAAK,CAAG,EAAE,EAAI,CAACY,aAAa,CAACV,SAAS,CAEzE,mBACElC,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAC,CAAEb,CAAC,CAAE,CAAC,CAAE0C,YAAY,CAAE,CAAC,CAAE3C,OAAO,CAAE+C,YAAY,CAAG,eAAe,CAAG,YAAa,CAAE,CAAAhE,QAAA,eAChGtQ,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACJ,EAAE,CAAE,CAAEsC,UAAU,CAAE,MAAM,CAAEjH,KAAK,CAAE8I,YAAY,CAAG,cAAc,CAAG,WAAY,CAAE,CAAAhE,QAAA,eACzGtQ,KAAA,WAAAsQ,QAAA,EAASnQ,CAAC,CAAC,wBAAwB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC,GAAG,CACnDyC,aAAa,CAACV,SAAS,CACpB/B,CAAC,CAAC,wBAAwB,CAAC,CAC3B,GAAGyC,aAAa,CAACZ,KAAK,IAAI7B,CAAC,CAAC,gBAAgB,CAAC,IAAIyC,aAAa,CAACX,OAAO,IAAI9B,CAAC,CAAC,kBAAkB,CAAC,EAAE,EAE3F,CAAC,CACZmU,YAAY,eACXtU,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,SAAS,CAACJ,EAAE,CAAE,CAAEa,OAAO,CAAE,OAAO,CAAEqB,EAAE,CAAE,CAAC,CAAE7G,KAAK,CAAE,cAAe,CAAE,CAAA8E,QAAA,EAAC,eACjF,CAACnQ,CAAC,CAAC,4BAA4B,CAAC,EACzB,CACb,EACE,CAAC,CAEV,CAAC,EAAE,CAAC,EACD,CACN,cACDL,IAAA,CAAChD,SAAS,EACRsW,SAAS,MACTmB,SAAS,MACTC,IAAI,CAAE,CAAE,CACRrC,KAAK,CAAEhS,CAAC,CAAC,6BAA6B,CAAE,CACxCsU,WAAW,CAAEtU,CAAC,CAAC,wCAAwC,CAAE,CACzDuU,KAAK,CAAEnT,kBAAmB,CAC1BoT,QAAQ,CAAGC,CAAC,EAAKpT,qBAAqB,CAACoT,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACvDvE,EAAE,CAAE,CAAEkC,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,EACW,CAAC,cAChBrS,KAAA,CAACxD,aAAa,EAAA8T,QAAA,eACZxQ,IAAA,CAAC7D,MAAM,EAAC8W,OAAO,CAAEA,CAAA,GAAM7R,mBAAmB,CAAC,KAAK,CAAE,CAAAoP,QAAA,CAC/CnQ,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,cACTL,IAAA,CAAC7D,MAAM,EACL8W,OAAO,CAAEhI,mBAAoB,CAC7BS,KAAK,CAAC,OAAO,CACb+E,OAAO,CAAC,WAAW,CACnBuD,QAAQ,CAAEzS,iBAAkB,CAAAiP,QAAA,CAE3BjP,iBAAiB,CAAGlB,CAAC,CAAC,qBAAqB,CAAC,CAAGA,CAAC,CAAC,8BAA8B,CAAC,CAC3E,CAAC,EACI,CAAC,EACV,CACT,CAED,mBACEH,KAAA,CAACd,MAAM,EAAAoR,QAAA,eACLxQ,IAAA,CAACpE,SAAS,EAACyX,QAAQ,CAAC,IAAI,CAAChD,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cACrCtQ,KAAA,CAACP,sBAAsB,EAACqV,WAAW,CAAE,CAAC,2BAA2B,CAAE,oBAAoB,CAAE,CAAAxE,QAAA,eACvFxQ,IAAA,CAACjE,KAAK,EAACiV,SAAS,CAAE,CAAE,CAACX,EAAE,CAAE,CAAEqB,CAAC,CAAE,CAAC,CAAEI,EAAE,CAAE,CAAC,CAAEL,OAAO,CAAE,cAAc,CAAE/F,KAAK,CAAE,OAAQ,CAAE,CAAA8E,QAAA,cAChFtQ,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAEwB,cAAc,CAAE,eAAe,CAAEf,UAAU,CAAE,QAAQ,CAAEsD,QAAQ,CAAE,MAAM,CAAErD,GAAG,CAAE,CAAE,CAAE,CAAApB,QAAA,eAC5GtQ,KAAA,CAACpE,GAAG,EAAA0U,QAAA,eACFxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,IAAI,CAACqD,YAAY,MAACzD,EAAE,CAAE,CAAEsC,UAAU,CAAE,MAAO,CAAE,CAAAnC,QAAA,CAC9DnQ,CAAC,CAAC,sBAAsB,CAAC,CAChB,CAAC,cACbL,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACJ,EAAE,CAAE,CAAEe,OAAO,CAAE,GAAI,CAAE,CAAAZ,QAAA,CAC9CnQ,CAAC,CAAC,4BAA4B,CAAC,CACtB,CAAC,EACV,CAAC,cACNH,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAES,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAApB,QAAA,eACzDtQ,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEC,SAAS,CAAE,OAAQ,CAAE,CAAAE,QAAA,eAC9BxQ,IAAA,CAACnE,UAAU,EAAC4U,OAAO,CAAC,OAAO,CAACJ,EAAE,CAAE,CAAEe,OAAO,CAAE,GAAG,CAAEU,EAAE,CAAE,GAAI,CAAE,CAAAtB,QAAA,CACvDnQ,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,cACbH,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,IAAI,CAACJ,EAAE,CAAE,CAAEsC,UAAU,CAAE,MAAO,CAAE,CAAAnC,QAAA,EAAC,eAChD,CAAC/R,MAAM,CAACiF,gBAAgB,CAAE,OAAO,CAAE,CAAEmL,MAAM,CAAEpO,KAAK,CAAG3B,EAAE,CAAGC,IAAK,CAAC,CAAC,CAAC,KAAG,CAACN,MAAM,CAACC,OAAO,CAACgF,gBAAgB,CAAE,CAAC,CAAC,CAAE,aAAa,CAAE,CAAEmL,MAAM,CAAEpO,KAAK,CAAG3B,EAAE,CAAGC,IAAK,CAAC,CAAC,EACjJ,CAAC,cACbmB,KAAA,CAACrE,UAAU,EAAC4U,OAAO,CAAC,SAAS,CAACJ,EAAE,CAAE,CAAEe,OAAO,CAAE,GAAI,CAAE,CAAAZ,QAAA,EAChDhN,cAAc,SAAdA,cAAc,WAAdA,cAAc,CAAEgH,QAAQ,CACvB9K,MAAM,CAACF,2BAA2B,CAAC,GAAI,CAAAsC,IAAI,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC,CAAEE,cAAc,CAACgH,QAAQ,CAAE,qBAAqB,CAAC,CAAE,qBAAqB,CAAC,CAAC/L,MAAM,CAAC,QAAQ,CAAC,CAErJA,MAAM,CAAC4E,WAAW,CAAE,GAAG,CAAE,CACvBwL,MAAM,CAAEpO,KAAK,CAAG3B,EAAE,CAAGC,IACvB,CAAC,CACF,CACA,CAAAyE,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEgH,QAAQ,GAAI,KAAKhH,cAAc,CAACgH,QAAQ,GAAG,EAClD,CAAC,EACV,CAAC,cACNtK,KAAA,CAACpE,GAAG,EAACuU,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAEU,GAAG,CAAE,CAAE,CAAE,CAAApB,QAAA,eACnCxQ,IAAA,CAACjD,OAAO,EAACmY,KAAK,CAAE7U,CAAC,CAAC,sBAAsB,CAAE,CAAAmQ,QAAA,cACxCxQ,IAAA,SAAAwQ,QAAA,cACExQ,IAAA,CAACrD,UAAU,EACTsW,OAAO,CAAEzN,gBAAiB,CAC1BwO,QAAQ,CAAElO,sBAAsB,CAAC,CAAE,CACnCuK,EAAE,CAAE,CACF3E,KAAK,CAAE,OAAO,CACd+F,OAAO,CAAE,0BAA0B,CACnC,SAAS,CAAE,CACTA,OAAO,CAAE,0BACX,CAAC,CACD,YAAY,CAAE,CACZ/F,KAAK,CAAE,0BAA0B,CACjC+F,OAAO,CAAE,2BACX,CACF,CAAE,CAAAjB,QAAA,cAEFxQ,IAAA,CAAC5B,eAAe,GAAE,CAAC,CACT,CAAC,CACT,CAAC,CACA,CAAC,cACV4B,IAAA,CAACjD,OAAO,EAACmY,KAAK,CAAE7U,CAAC,CAAC,kBAAkB,CAAE,CAAAmQ,QAAA,cACpCxQ,IAAA,SAAAwQ,QAAA,cACExQ,IAAA,CAACrD,UAAU,EACTsW,OAAO,CAAEvN,YAAa,CACtBsO,QAAQ,CAAEjO,kBAAkB,CAAC,CAAE,CAC/BsK,EAAE,CAAE,CACF3E,KAAK,CAAE,OAAO,CACd+F,OAAO,CAAE,0BAA0B,CACnC,SAAS,CAAE,CACTA,OAAO,CAAE,0BACX,CAAC,CACD,YAAY,CAAE,CACZ/F,KAAK,CAAE,0BAA0B,CACjC+F,OAAO,CAAE,2BACX,CACF,CAAE,CAAAjB,QAAA,cAEFxQ,IAAA,CAAC1B,gBAAgB,GAAE,CAAC,CACV,CAAC,CACT,CAAC,CACA,CAAC,EACP,CAAC,EACH,CAAC,EACH,CAAC,CACD,CAAC,CAETuC,OAAO,cACNb,IAAA,CAAClE,GAAG,EAACuU,EAAE,CAAE,CAAEa,OAAO,CAAE,MAAM,CAAEwB,cAAc,CAAE,QAAQ,CAAEnC,EAAE,CAAE,CAAE,CAAE,CAAAC,QAAA,cAC5DxQ,IAAA,CAAC3D,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACJ0E,KAAK,cACPf,IAAA,CAAC1D,KAAK,EAAC+X,QAAQ,CAAC,OAAO,CAAChE,EAAE,CAAE,CAAEyB,EAAE,CAAE,CAAE,CAAE,CAAAtB,QAAA,CACnCzP,KAAK,CACD,CAAC,cAERf,IAAA,CAACX,mBAAmB,EAClBsB,QAAQ,CAAEA,QAAS,CACnBE,OAAO,CAAEA,OAAQ,CACjB6C,gBAAgB,CAAEA,gBAAiB,CACnC6B,UAAU,CAAEA,UAAW,CACvB4P,aAAa,CAAE3L,iBAAkB,CACjC4L,eAAe,CAAE3L,wBAAyB,CAC1CjG,cAAc,CAAEA,cAAe,CAC/BsL,iBAAiB,CAAEA,iBAAkB,CACrCR,cAAc,CAAEA,cAAe,CAChC,CACF,CAEA4E,mBAAmB,CAAC,CAAC,CACrBqB,kBAAkB,CAAC,CAAC,EACG,CAAC,CAChB,CAAC,cAGZvU,IAAA,CAACzD,MAAM,EACL8Y,UAAU,MACVlC,IAAI,CAAErP,WAAY,CAClBsP,OAAO,CAAEvJ,kBAAmB,CAAA2G,QAAA,CAE3BxM,cAAc,eACbhE,IAAA,CAACJ,eAAe,EACd0V,MAAM,CAAEtR,cAAc,CAAC2F,SAAU,CACjCqG,SAAS,CAAEhM,cAAc,CAACoE,UAAW,CACrCmN,WAAW,CAAE,CACX,GAAGvR,cAAc,CACjBwR,YAAY,CAAExR,cAAc,CAACjB,QAAQ,CACrC4D,QAAQ,CAAE/D,QAAQ,CAACoB,cAAc,CAAC2C,QAAQ,CAAC,EAAI,EACjD,CAAE,CACFyM,OAAO,CAAEvJ,kBAAmB,CAC7B,CACF,CACK,CAAC,cAGT7J,IAAA,CAACV,gBAAgB,EACf6T,IAAI,CAAE5O,oBAAqB,CAC3B6O,OAAO,CAAEA,CAAA,GAAM5O,uBAAuB,CAAC,KAAK,CAAE,CAC9ChC,OAAO,CAAEvB,eAAgB,CACzBwD,aAAa,CAAEA,aAAc,CAC7BgR,YAAY,CAAE3H,uBAAwB,CACtCjN,OAAO,CAAEsE,YAAa,CACtBuQ,WAAW,CAAElS,cAAe,CAC5BmS,aAAa,CAAE,KAAM,CACtB,CAAC,cAGF3V,IAAA,CAACH,qBAAqB,EACpBsT,IAAI,CAAEhP,kBAAmB,CACzByR,OAAO,CAAEvR,eAAgB,CACzBmG,QAAQ,CAAE,CAAAhH,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEgH,QAAQ,GAAI,IAAK,CAC3CqL,QAAQ,CAAE9F,oBAAqB,CAC/BqD,OAAO,CAAEA,CAAA,GAAM,CACbhP,qBAAqB,CAAC,KAAK,CAAC,CAC5BE,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAE,CACH,CAAC,EACI,CAAC,CAEb,CAAC,CAED,cAAe,CAAAlE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}