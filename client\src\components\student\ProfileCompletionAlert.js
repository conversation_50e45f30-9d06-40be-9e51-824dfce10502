import React, { useState, useEffect } from 'react';
import { Paper, Typography, Button, Box, CircularProgress } from '@mui/material';
import { ArrowForward as ArrowForwardIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const ProfileCompletionAlert = ({ children, exemptPages = [] }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [isProfileComplete, setIsProfileComplete] = useState(true);
  const [error, setError] = useState('');

  // Check if current page is exempt from profile completion check
  const isExemptPage = () => {
    const currentPath = window.location.pathname;
    return exemptPages.some(page => currentPath.includes(page));
  };

  useEffect(() => {
    // Skip check if on exempt page
    if (isExemptPage()) {
      setLoading(false);
      return;
    }

    const checkProfileCompletion = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('/api/students/profile', {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data.success && response.data.profile) {
          setIsProfileComplete(response.data.profile.is_completed);
        } else {
          throw new Error('Invalid profile data structure');
        }
      } catch (err) {
        console.error('Error checking profile completion:', err);
        setError(t('dashboard.errorLoadingProfile'));
      } finally {
        setLoading(false);
      }
    };

    checkProfileCompletion();
  }, [t, exemptPages]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // If on exempt page or there's an error, show the children
  if (error || isExemptPage()) {
    return <>{children}</>;
  }

  // If profile is complete, show the children
  if (isProfileComplete) {
    return <>{children}</>;
  }

  // If profile is not complete, show the alert
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '70vh' }}>
      <Paper sx={{ p: 4, maxWidth: 600, width: '100%', textAlign: 'center', bgcolor: 'warning.light' }}>
        <Typography variant="h5" gutterBottom fontWeight="bold">
          {t('dashboard.incompleteProfile')}
        </Typography>
        <Typography paragraph fontSize="1.1rem" sx={{ mb: 3 }}>
          {t('dashboard.completeProfileMessage')}
        </Typography>
        <Typography paragraph color="text.secondary" sx={{ mb: 4 }}>
          {t('dashboard.completeProfileToAccess')}
        </Typography>
        <Button
          variant="contained"
          color="warning"
          size="large"
          onClick={() => navigate('/student/complete-profile')}
          endIcon={<ArrowForwardIcon />}
          sx={{ px: 4, py: 1.5 }}
        >
          {t('dashboard.completeProfileNow')}
        </Button>
      </Paper>
    </Box>
  );
};

export default ProfileCompletionAlert;
