import React, { useState } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';
import {
  Container,
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Grid,
  Alert,
  FormControl,
  FormLabel,
  RadioGroup,
  Radio,
  FormControlLabel,
  Divider,
  Fade,
  useTheme,
  alpha,
  Checkbox,
  Link as MuiLink,
} from '@mui/material';
import {
  School as SchoolIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import GenderDialog from '../../components/auth/GenderDialog';

const StudentRegister = () => {
  const { t, i18n } = useTranslation();
  const { register, googleRegister } = useAuth();
  const navigate = useNavigate();
  const theme = useTheme();
  const [isRtl] = useState(i18n.language === 'ar');
  const [showGenderDialog, setShowGenderDialog] = useState(false);
  const [googleCredential, setGoogleCredential] = useState(null);

  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    password: '',
    confirmPassword: '',
    gender: 'male',
    privacyPolicy: false,
  });

  const [errors, setErrors] = useState({
    full_name: '',
    email: '',
    password: '',
    confirmPassword: '',
    privacyPolicy: '',
    general: '',
  });

  const [loading, setLoading] = useState(false);

  const handleGoogleSuccess = (credentialResponse) => {
    setGoogleCredential(credentialResponse.credential);
    setShowGenderDialog(true);
  };

  const handleGoogleError = () => {
    setErrors({ ...errors, general: t('auth.googleSignInError') });
  };

  const handleGenderSubmit = async (selectedGender) => {
    setErrors({ ...errors, general: '' });
    setLoading(true);
    try {
      const result = await googleRegister({
        credential: googleCredential,
        role: 'student',
        gender: selectedGender
      });

      if (result.success) {
        navigate('/verify-email', { state: { email: result.user?.email || result.email } });
      } else {
        setErrors({
          ...errors,
          general: result.error || t('auth.registrationError')
        });
      }
    } catch (err) {
      setErrors({
        ...errors,
        general: t('auth.registrationError')
      });
    } finally {
      setLoading(false);
      setShowGenderDialog(false);
      setGoogleCredential(null);
    }
  };

  const handleGenderDialogClose = () => {
    setShowGenderDialog(false);
    setGoogleCredential(null);
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.full_name.trim()) {
      newErrors.full_name = t('validation.required');
    }

    if (!formData.email.trim()) {
      newErrors.email = t('validation.required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('validation.invalidEmail');
    }

    if (!formData.password) {
      newErrors.password = t('validation.required');
    } else if (formData.password.length < 6) {
      newErrors.password = t('validation.passwordLength');
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = t('validation.required');
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = t('validation.passwordMatch');
    }

    if (!formData.privacyPolicy) {
      newErrors.privacyPolicy = t('privacyPolicy.agreement.required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setErrors({ ...errors, general: '' });

    try {
      const result = await register({
        full_name: formData.full_name,
        email: formData.email,
        password: formData.password,
        role: 'student',
        gender: formData.gender
      });

      if (result.success) {
        navigate('/verify-email', { state: { email: formData.email } });
      } else {
        setErrors({
          ...errors,
          general: result.error || t('auth.registrationError')
        });
      }
    } catch (err) {
      setErrors({
        ...errors,
        general: t('auth.registrationError')
      });
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <GoogleOAuthProvider clientId="52320482193-ig6u5a5r3hi0gu65g683c34t5efc2b6s.apps.googleusercontent.com">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          py: 8,
          background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.1)}, ${alpha(theme.palette.primary.light, 0.1)})`
        }}
      >
        <Container maxWidth="sm" sx={{ position: 'relative', zIndex: 1 }}>
          <Fade in timeout={800}>
            <Paper
              elevation={24}
              sx={{
                p: { xs: 3, sm: 6 },
                background: alpha('#fff', 0.98),
                backdropFilter: 'blur(20px)',
                borderRadius: 4,
                position: 'relative',
                overflow: 'hidden',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              }}
            >
              <Box sx={{ mb: 4, display: 'flex', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <SchoolIcon sx={{ fontSize: 40, color: theme.palette.primary.main }} />
                  <Box>
                    <Typography
                      component="h1"
                      variant="h4"
                      sx={{
                        fontWeight: 700,
                        color: 'text.primary',
                        mb: 0.5
                      }}
                    >
                      {t('auth.studentRegistration')}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        color: 'text.secondary',
                        fontWeight: 500
                      }}
                    >
                      {t('auth.joinOurCommunity')}
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {errors.general && (
                <Alert
                  severity="error"
                  sx={{
                    mb: 3,
                    borderRadius: 2,
                    boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.15)}`
                  }}
                >
                  {errors.general}
                </Alert>
              )}

              <Box component="form" onSubmit={handleSubmit}>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      name="full_name"
                      label={t('common.fullName')}
                      value={formData.full_name}
                      onChange={handleChange}
                      error={!!errors.full_name}
                      helperText={errors.full_name}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.background.paper, 0.8),
                          '&:hover': {
                            bgcolor: alpha(theme.palette.background.paper, 0.95),
                          }
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      name="email"
                      label={t('common.email')}
                      value={formData.email}
                      onChange={handleChange}
                      error={!!errors.email}
                      helperText={errors.email}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.background.paper, 0.8),
                          '&:hover': {
                            bgcolor: alpha(theme.palette.background.paper, 0.95),
                          }
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      name="password"
                      label={t('common.password')}
                      type="password"
                      value={formData.password}
                      onChange={handleChange}
                      error={!!errors.password}
                      helperText={errors.password}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.background.paper, 0.8),
                          '&:hover': {
                            bgcolor: alpha(theme.palette.background.paper, 0.95),
                          }
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      name="confirmPassword"
                      label={t('common.confirmPassword')}
                      type="password"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      error={!!errors.confirmPassword}
                      helperText={errors.confirmPassword}
                      variant="outlined"
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.background.paper, 0.8),
                          '&:hover': {
                            bgcolor: alpha(theme.palette.background.paper, 0.95),
                          }
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <FormControl component="fieldset">
                      <FormLabel component="legend" sx={{ mb: 1, color: 'text.primary', fontWeight: 500 }}>
                        {t('common.gender')}
                      </FormLabel>
                      <RadioGroup
                        row
                        name="gender"
                        value={formData.gender}
                        onChange={handleChange}
                      >
                        <FormControlLabel
                          value="male"
                          control={<Radio color="primary" />}
                          label={t('common.male')}
                          sx={{ mr: 4 }}
                        />
                        <FormControlLabel
                          value="female"
                          control={<Radio color="primary" />}
                          label={t('common.female')}
                        />
                      </RadioGroup>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={formData.privacyPolicy}
                          onChange={(e) => setFormData({ ...formData, privacyPolicy: e.target.checked })}
                          name="privacyPolicy"
                          color="primary"
                        />
                      }
                      label={
                        <Typography variant="body2">
                          {t('privacyPolicy.agreement.checkbox')}{' '}
                          <MuiLink
                            component={Link}
                            to="/privacy-policy"
                            target="_blank"
                            rel="noopener"
                            color="primary"
                            sx={{ fontWeight: 600 }}
                          >
                            {t('footer.privacy')}
                          </MuiLink>
                        </Typography>
                      }
                    />
                    {errors.privacyPolicy && (
                      <Typography color="error" variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                        {errors.privacyPolicy}
                      </Typography>
                    )}
                  </Grid>
                  <Grid item xs={12}>
                    <Button
                      type="submit"
                      fullWidth
                      variant="contained"
                      size="large"
                      disabled={loading}
                      sx={{
                        py: 1.8,
                        fontSize: '1.1rem',
                        fontWeight: 600,
                        borderRadius: 3,
                        textTransform: 'none',
                        boxShadow: `0 8px 24px ${alpha(theme.palette.primary.main, 0.3)}`,
                        '&:hover': {
                          boxShadow: `0 12px 32px ${alpha(theme.palette.primary.main, 0.4)}`,
                          transform: 'translateY(-2px)'
                        },
                        transition: 'all 0.3s ease-in-out'
                      }}
                    >
                      {t('auth.createAccount')}
                    </Button>
                  </Grid>
                </Grid>
              </Box>

              <Box sx={{ mt: 4, textAlign: 'center' }}>
                <Divider sx={{ mb: 3 }}>
                  <Typography
                    variant="body2"
                    sx={{
                      px: 2,
                      color: 'text.secondary',
                      fontWeight: 500
                    }}
                  >
                    {t('auth.or')}
                  </Typography>
                </Divider>

                <GoogleLogin
                  onSuccess={handleGoogleSuccess}
                  onError={handleGoogleError}
                  size="large"
                  width="400"
                  text="continue_with"
                  shape="rectangular"
                  theme="outline"
                />
              </Box>

              <Box sx={{ mt: 4, textAlign: 'center' }}>
                <Typography
                  variant="body1"
                  sx={{
                    color: 'text.secondary',
                    fontWeight: 500
                  }}
                >
                  {t('auth.alreadyHaveAccount')}{' '}
                  <Link
                    to="/login"
                    style={{
                      color: theme.palette.primary.main,
                      textDecoration: 'none',
                      fontWeight: 600,
                      transition: 'color 0.2s'
                    }}
                  >
                    {t('auth.signIn')}
                  </Link>
                </Typography>
              </Box>
            </Paper>
          </Fade>
        </Container>
      </Box>

      <GenderDialog
        open={showGenderDialog}
        onClose={handleGenderDialogClose}
        onSubmit={handleGenderSubmit}
      />
    </GoogleOAuthProvider>
  );
};

export default StudentRegister;
