import axios from 'axios';

/**
 * إعداد axios للتعامل مع المستخدمين المحذوفين
 */

// إضافة interceptor للطلبات للتحقق من حالة المستخدم
axios.interceptors.request.use(
  (config) => {
    // التحقق من وجود رسالة حساب محذوف في localStorage
    const accountStatusMessage = localStorage.getItem('accountStatusMessage');
    if (accountStatusMessage) {
      try {
        const messageData = JSON.parse(accountStatusMessage);
        if (messageData.accountStatus === 'deleted') {
          // منع الطلب للمستخدمين المحذوفين
          if (process.env.NODE_ENV === 'development') {
            console.warn('Blocking API request for deleted user:', config.url);
          }
          return Promise.reject(new Error('User account is deleted'));
        }
      } catch (e) {
        // تجاهل الأخطاء في parsing
      }
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// إضافة interceptor للاستجابات للتعامل مع أخطاء المستخدمين المحذوفين
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    // التحقق من أخطاء حالة المستخدم
    if (error.response && error.response.data && error.response.data.accountStatus) {
      const { accountStatus, message, message_en } = error.response.data;
      
      if (accountStatus === 'deleted' || accountStatus === 'pending_deletion') {
        // حفظ رسالة الحالة
        localStorage.setItem('accountStatusMessage', JSON.stringify({
          message: message,
          message_en: message_en,
          accountStatus: accountStatus
        }));
        
        // إعادة توجيه لصفحة تسجيل الدخول
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
      }
    }
    
    return Promise.reject(error);
  }
);

export default axios;
