const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'https://allemnionline.com',
      changeOrigin: true,
    })
  );
  
  app.use(
    '/socket.io',
    createProxyMiddleware({
      target: 'https://allemnionline.com',
      changeOrigin: true,
      ws: true,
    })
  );
  
  app.use(
    '/uploads',
    createProxyMiddleware({
      target: 'https://allemnionline.com',
      changeOrigin: true,
    })
  );
};
