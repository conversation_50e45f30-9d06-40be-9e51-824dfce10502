// Timezone options with city/region names
export const timezones = [
  { value: 'UTC-12:00', label: 'UTC-12:00 (Baker Island)' },
  { value: 'UTC-11:00', label: 'UTC-11:00 (American Samoa, Niue)' },
  { value: 'UTC-10:00', label: 'UTC-10:00 (Hawaii, Tahiti)' },
  { value: 'UTC-09:30', label: 'UTC-09:30 (Marquesas Islands)' },
  { value: 'UTC-09:00', label: 'UTC-09:00 (Alaska, Gambier Islands)' },
  { value: 'UTC-08:00', label: 'UTC-08:00 (Los Angeles, Vancouver)' },
  { value: 'UTC-07:00', label: 'UTC-07:00 (Denver, Phoenix)' },
  { value: 'UTC-06:00', label: 'UTC-06:00 (Chicago, Mexico City)' },
  { value: 'UTC-05:00', label: 'UTC-05:00 (New York, Toronto)' },
  { value: 'UTC-04:00', label: 'UTC-04:00 (Santiago, Caracas)' },
  { value: 'UTC-03:30', label: 'UTC-03:30 (Newfoundland)' },
  { value: 'UTC-03:00', label: 'UTC-03:00 (São Paulo, Buenos Aires)' },
  { value: 'UTC-02:00', label: 'UTC-02:00 (South Georgia)' },
  { value: 'UTC-01:00', label: 'UTC-01:00 (Azores, Cape Verde)' },
  { value: 'UTC+00:00', label: 'UTC+00:00 (London, Dublin, Casablanca)' },
  { value: 'UTC+01:00', label: 'UTC+01:00 (Paris, Berlin, Rome)' },
  { value: 'UTC+02:00', label: 'UTC+02:00 (Cairo, Athens, Helsinki)' },
  { value: 'UTC+03:00', label: 'UTC+03:00 (Riyadh, Kuwait, Baghdad)' },
  { value: 'UTC+03:30', label: 'UTC+03:30 (Tehran)' },
  { value: 'UTC+04:00', label: 'UTC+04:00 (Dubai, Muscat, Baku)' },
  { value: 'UTC+04:30', label: 'UTC+04:30 (Kabul)' },
  { value: 'UTC+05:00', label: 'UTC+05:00 (Karachi, Tashkent)' },
  { value: 'UTC+05:30', label: 'UTC+05:30 (New Delhi, Mumbai, Colombo)' },
  { value: 'UTC+05:45', label: 'UTC+05:45 (Kathmandu)' },
  { value: 'UTC+06:00', label: 'UTC+06:00 (Dhaka, Almaty)' },
  { value: 'UTC+06:30', label: 'UTC+06:30 (Yangon, Cocos Islands)' },
  { value: 'UTC+07:00', label: 'UTC+07:00 (Bangkok, Jakarta, Ho Chi Minh)' },
  { value: 'UTC+08:00', label: 'UTC+08:00 (Beijing, Singapore, Manila)' },
  { value: 'UTC+08:45', label: 'UTC+08:45 (Eucla)' },
  { value: 'UTC+09:00', label: 'UTC+09:00 (Tokyo, Seoul, Pyongyang)' },
  { value: 'UTC+09:30', label: 'UTC+09:30 (Adelaide, Darwin)' },
  { value: 'UTC+10:00', label: 'UTC+10:00 (Sydney, Melbourne, Port Moresby)' },
  { value: 'UTC+10:30', label: 'UTC+10:30 (Lord Howe Island)' },
  { value: 'UTC+11:00', label: 'UTC+11:00 (Solomon Islands, New Caledonia)' },
  { value: 'UTC+12:00', label: 'UTC+12:00 (Auckland, Fiji, Marshall Islands)' },
  { value: 'UTC+12:45', label: 'UTC+12:45 (Chatham Islands)' },
  { value: 'UTC+13:00', label: 'UTC+13:00 (Nuku\'alofa, Samoa)' },
  { value: 'UTC+14:00', label: 'UTC+14:00 (Kiritimati)' }
];

// Legacy timezone array for backward compatibility
export const timezoneValues = timezones.map(tz => tz.value);

// Regions and Countries data
export const regionsAndCountries = {
  'middle_east': {
    nameKey: 'regions.middle_east',
    countries: [
      'Saudi Arabia', 'UAE', 'Qatar', 'Kuwait', 'Bahrain', 'Oman',
      'Iraq', 'Jordan', 'Lebanon', 'Syria', 'Palestine', 'Yemen',
      'Egypt', 'Sudan', 'Libya', 'Tunisia', 'Algeria', 'Morocco',
      'Turkey', 'Iran', 'Israel'
    ]
  },
  'north_africa': {
    nameKey: 'regions.north_africa',
    countries: [
      'Egypt', 'Libya', 'Tunisia', 'Algeria', 'Morocco', 'Sudan'
    ]
  },
  'sub_saharan_africa': {
    nameKey: 'regions.sub_saharan_africa',
    countries: [
      'Nigeria', 'Kenya', 'Ethiopia', 'South Africa', 'Ghana', 'Uganda',
      'Tanzania', 'Mali', 'Senegal', 'Chad', 'Niger', 'Somalia',
      'Mauritania', 'Burkina Faso', 'Guinea', 'Sierra Leone',
      'Ivory Coast', 'Cameroon', 'Madagascar', 'Mozambique'
    ]
  },
  'south_asia': {
    nameKey: 'regions.south_asia',
    countries: [
      'Pakistan', 'India', 'Bangladesh', 'Afghanistan', 'Sri Lanka',
      'Nepal', 'Bhutan', 'Maldives'
    ]
  },
  'southeast_asia': {
    nameKey: 'regions.southeast_asia',
    countries: [
      'Indonesia', 'Malaysia', 'Brunei', 'Singapore', 'Thailand',
      'Philippines', 'Vietnam', 'Cambodia', 'Laos', 'Myanmar'
    ]
  },
  'central_asia': {
    nameKey: 'regions.central_asia',
    countries: [
      'Kazakhstan', 'Uzbekistan', 'Turkmenistan', 'Kyrgyzstan',
      'Tajikistan'
    ]
  },
  'europe': {
    nameKey: 'regions.europe',
    countries: [
      'United Kingdom', 'France', 'Germany', 'Italy', 'Spain',
      'Netherlands', 'Belgium', 'Switzerland', 'Austria', 'Sweden',
      'Norway', 'Denmark', 'Finland', 'Poland', 'Czech Republic',
      'Hungary', 'Romania', 'Bulgaria', 'Greece', 'Portugal',
      'Ireland', 'Croatia', 'Serbia', 'Bosnia and Herzegovina',
      'Albania', 'North Macedonia', 'Montenegro', 'Slovenia',
      'Slovakia', 'Lithuania', 'Latvia', 'Estonia', 'Russia'
    ]
  },
  'north_america': {
    nameKey: 'regions.north_america',
    countries: [
      'United States', 'Canada', 'Mexico'
    ]
  },
  'south_america': {
    nameKey: 'regions.south_america',
    countries: [
      'Brazil', 'Argentina', 'Colombia', 'Venezuela', 'Peru',
      'Chile', 'Ecuador', 'Bolivia', 'Paraguay', 'Uruguay',
      'Guyana', 'Suriname'
    ]
  },
  'east_asia': {
    nameKey: 'regions.east_asia',
    countries: [
      'China', 'Japan', 'South Korea', 'North Korea', 'Mongolia',
      'Taiwan', 'Hong Kong', 'Macau'
    ]
  },
  'oceania': {
    nameKey: 'regions.oceania',
    countries: [
      'Australia', 'New Zealand', 'Papua New Guinea', 'Fiji',
      'Solomon Islands', 'Vanuatu', 'Samoa', 'Tonga', 'Palau',
      'Marshall Islands', 'Micronesia', 'Kiribati', 'Tuvalu', 'Nauru'
    ]
  }
};
