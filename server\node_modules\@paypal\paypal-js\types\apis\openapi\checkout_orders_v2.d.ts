/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

/** WithRequired type helpers */
type WithRequired<T, K extends keyof T> = T & { [P in K]-?: T[P] };

export interface paths {
    "/v2/checkout/orders": {
        /**
         * Create order
         * @description Creates an order. Merchants and partners can add Level 2 and 3 data to payments to reduce risk and payment processing costs. For more information about processing payments, see <a href="https://developer.paypal.com/docs/checkout/advanced/processing/">checkout</a> or <a href="https://developer.paypal.com/docs/multiparty/checkout/advanced/processing/">multiparty checkout</a>.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href="/api/rest/reference/orders/v2/errors/#create-order">Orders v2 errors</a>.</blockquote>
         */
        post: operations["orders.create"];
    };
    "/v2/checkout/orders/{id}": {
        /**
         * Show order details
         * @description Shows details for an order, by ID.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href="/api/rest/reference/orders/v2/errors/#get-order">Orders v2 errors</a>.</blockquote>
         */
        get: operations["orders.get"];
        /**
         * Update order
         * @description Updates an order with a `CREATED` or `APPROVED` status. You cannot update an order with the `COMPLETED` status.<br/><br/>To make an update, you must provide a `reference_id`. If you omit this value with an order that contains only one purchase unit, PayPal sets the value to `default` which enables you to use the path: <code>\"/purchase_units/@reference_id=='default'/{attribute-or-object}\"</code>. Merchants and partners can add Level 2 and 3 data to payments to reduce risk and payment processing costs. For more information about processing payments, see <a href="https://developer.paypal.com/docs/checkout/advanced/processing/">checkout</a> or <a href="https://developer.paypal.com/docs/multiparty/checkout/advanced/processing/">multiparty checkout</a>.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href=\"/api/rest/reference/orders/v2/errors/#patch-order\">Orders v2 errors</a>.</blockquote>Patchable attributes or objects:<br/><br/><table><thead><th>Attribute</th><th>Op</th><th>Notes</th></thead><tbody><tr><td><code>intent</code></td><td>replace</td><td></td></tr><tr><td><code>payer</code></td><td>replace, add</td><td>Using replace op for <code>payer</code> will replace the whole <code>payer</code> object with the value sent in request.</td></tr><tr><td><code>purchase_units</code></td><td>replace, add</td><td></td></tr><tr><td><code>purchase_units[].custom_id</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].description</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].payee.email</code></td><td>replace</td><td></td></tr><tr><td><code>purchase_units[].shipping.name</code></td><td>replace, add</td><td></td></tr><tr><td><code>purchase_units[].shipping.address</code></td><td>replace, add</td><td></td></tr><tr><td><code>purchase_units[].shipping.type</code></td><td>replace, add</td><td></td></tr><tr><td><code>purchase_units[].soft_descriptor</code></td><td>replace, remove</td><td></td></tr><tr><td><code>purchase_units[].amount</code></td><td>replace</td><td></td></tr><tr><td><code>purchase_units[].items</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].invoice_id</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].payment_instruction</code></td><td>replace</td><td></td></tr><tr><td><code>purchase_units[].payment_instruction.disbursement_mode</code></td><td>replace</td><td>By default, <code>disbursement_mode</code> is <code>INSTANT</code>.</td></tr><tr><td><code>purchase_units[].payment_instruction.platform_fees</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].supplementary_data.airline</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].supplementary_data.card</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>application_context.client_configuration</code></td><td>replace, add</td><td></td></tr></tbody></table>
         */
        patch: operations["orders.patch"];
    };
    "/v2/checkout/orders/{id}/confirm-payment-source": {
        /**
         * Confirm the Order
         * @description Payer confirms their intent to pay for the the Order with the given payment source.
         */
        post: operations["orders.confirm"];
    };
    "/v2/checkout/orders/{id}/authorize": {
        /**
         * Authorize payment for order
         * @description Authorizes payment for an order. To successfully authorize payment for an order, the buyer must first approve the order or a valid payment_source must be provided in the request. A buyer can approve the order upon being redirected to the rel:approve URL that was returned in the HATEOAS links in the create order response.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href="/api/rest/reference/orders/v2/errors/#authorize-order">Orders v2 errors</a>.</blockquote>
         */
        post: operations["orders.authorize"];
    };
    "/v2/checkout/orders/{id}/capture": {
        /**
         * Capture payment for order
         * @description Captures payment for an order. To successfully capture payment for an order, the buyer must first approve the order or a valid payment_source must be provided in the request. A buyer can approve the order upon being redirected to the rel:approve URL that was returned in the HATEOAS links in the create order response.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href="/api/rest/reference/orders/v2/errors/#capture-order">Orders v2 errors</a>.</blockquote>
         */
        post: operations["orders.capture"];
    };
    "/v2/checkout/orders/{id}/track": {
        /**
         * Add tracking information for an Order.
         * @description Adds tracking information for an Order.
         */
        post: operations["orders.track.create"];
    };
    "/v2/checkout/orders/{id}/trackers/{tracker_id}": {
        /**
         * Update or cancel tracking information for a PayPal order
         * @description Updates or cancels the tracking information for a PayPal order, by ID. Updatable attributes or objects:<br/><br/><table><thead><th>Attribute</th><th>Op</th><th>Notes</th></thead><tbody></tr><tr><td><code>items</code></td><td>replace</td><td>Using replace op for <code>items</code> will replace the entire <code>items</code> object with the value sent in request.</td></tr><tr><td><code>notify_payer</code></td><td>replace, add</td><td></td></tr><tr><td><code>status</code></td><td>replace</td><td>Only patching status to CANCELLED is currently supported.</td></tr></tbody></table>
         */
        patch: operations["orders.trackers.patch"];
    };
}

export type webhooks = Record<string, never>;

export interface components {
    schemas: {
        400: {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_ARRAY_MAX_ITEMS";
                      /** @enum {string} */
                      description?: "The number of items in an array parameter is too large.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_ARRAY_MIN_ITEMS";
                      /** @enum {string} */
                      description?: "The number of items in an array parameter is too small.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_COUNTRY_CODE";
                      /** @enum {string} */
                      description?: "Country code is invalid. Please refer to https://developer.paypal.com/api/rest/reference/country-codes/ for a list of supported country codes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is either too short or too long";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "A parameter value is not valid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required parameter is missing.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "This field is not currently supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYPAL_REQUEST_ID_REQUIRED";
                      /** @enum {string} */
                      description?: "A PayPal-Request-Id is required if you are trying to process payment for an Order. Please specify a PayPal-Request-Id or Create the Order without a 'payment_source' specified.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MALFORMED_REQUEST_JSON";
                      /** @enum {string} */
                      description?: "The request JSON is not well formed.";
                  }
            )[];
        };
        401: {
            issues?: {
                /** @enum {string} */
                issue?: "INVALID_ACCOUNT_STATUS";
                /** @enum {string} */
                description?: "Account validations failed for the user.";
            }[];
        };
        403: {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "PERMISSION_DENIED";
                      /** @enum {string} */
                      description?: "You do not have permission to access or perform operations on this resource.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ENABLED_FOR_CARD_PROCESSING";
                      /** @enum {string} */
                      description?: "The recipient for which the API call is made on behalf of is not enabled for card processing. Please contact PayPal customer support.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_ACCOUNT_NOT_VERIFIED";
                      /** @enum {string} */
                      description?: "Payee has not verified their account with PayPal. The selected payment method requires the recipient to have a verified PayPal account before transactions can be processed on their behalf.";
                  }
            )[];
        };
        404: {
            issues?: {
                /** @enum {string} */
                issue?: "INVALID_RESOURCE_ID";
                /** @enum {string} */
                description?: "Specified resource ID does not exist. Please check the resource ID and try again.";
            }[];
        };
        422: {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "AMOUNT_MISMATCH";
                      /** @enum {string} */
                      description?: "Should equal item_total + tax_total + shipping + handling + insurance - shipping_discount - discount.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CANNOT_BE_NEGATIVE";
                      /** @enum {string} */
                      description?: "Must be greater than or equal to 0. If the currency supports decimals, only two decimal place precision is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CANNOT_BE_ZERO_OR_NEGATIVE";
                      /** @enum {string} */
                      description?: "Must be greater than zero. If the currency supports decimals, only two decimal place precision is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_EXPIRED";
                      /** @enum {string} */
                      description?: "The card is expired";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PREVIOUS_REFERENCE";
                      /** @enum {string} */
                      description?: "For Merchant initiated network token transactions, either the payment_source.card.stored_credential.previous_network_transaction_reference or payment_source.card.stored_credential.previous_transaction_reference must be included in the request.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_CRYPTOGRAM";
                      /** @enum {string} */
                      description?: "Cryptogram is mandatory for any customer initiated network token transactions.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CITY_REQUIRED";
                      /** @enum {string} */
                      description?: "The specified country requires a city (address.admin_area_2).";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DECIMAL_PRECISION";
                      /** @enum {string} */
                      description?: "If the currency supports decimals, only two decimal place precision is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DONATION_ITEMS_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "If 'purchase_unit' has \"DONATION\" as the 'items.category' then the Order can at most have one purchase_unit. Multiple purchase_units are not supported if either of them have at least one items with category as \"DONATION\".";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DUPLICATE_REFERENCE_ID";
                      /** @enum {string} */
                      description?: "`reference_id` must be unique if multiple `purchase_unit` are provided.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_CURRENCY_CODE";
                      /** @enum {string} */
                      description?: "Currency code is invalid or is not currently supported. Please refer https://developer.paypal.com/api/rest/reference/currency-codes/ for list of supported currency codes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PAYER_ID";
                      /** @enum {string} */
                      description?: "The payer ID is not valid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ITEM_TOTAL_MISMATCH";
                      /** @enum {string} */
                      description?: "Should equal sum of (unit_amount * quantity) across all items for a given purchase_unit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ITEM_TOTAL_REQUIRED";
                      /** @enum {string} */
                      description?: "If item details are specified (items.unit_amount and items.quantity) corresponding amount.breakdown.item_total is required.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MAX_VALUE_EXCEEDED";
                      /** @enum {string} */
                      description?: "Should be less than or equal to 999999999999999.99.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PICKUP_ADDRESS";
                      /** @enum {string} */
                      description?: "A pickup address(`shipping.address`) is required for the provided `shipping.type`.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MULTI_CURRENCY_ORDER";
                      /** @enum {string} */
                      description?: "Multiple differing values of currency_code are not supported. Entire Order request must have the same currency_code.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MULTIPLE_ITEM_CATEGORIES";
                      /** @enum {string} */
                      description?: 'For a given \'purchase_unit\' the \'items.category\' could be either "PHYSICAL_GOODS" and/or "DIGITAL_GOODS" or just "DONATION".  \'items.category\' as "DONATION" cannot be combined with items with either "PHYSICAL_GOODS" or "DIGITAL_GOODS".';
                  }
                | {
                      /** @enum {string} */
                      issue?: "MULTIPLE_SHIPPING_ADDRESS_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Multiple shipping addresses are not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MULTIPLE_SHIPPING_TYPE_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Different `shipping.type` are not supported across purchase units.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_ACCOUNT_INVALID";
                      /** @enum {string} */
                      description?: "Payee account specified is invalid. Please check the `payee.email_address` or `payee.merchant_id` specified and try again. Ensure that either  `payee.merchant_id` or `payee.email_address` is specified.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_ACCOUNT_LOCKED_OR_CLOSED";
                      /** @enum {string} */
                      description?: "The merchant account is locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_ACCOUNT_RESTRICTED";
                      /** @enum {string} */
                      description?: "The merchant account is restricted.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_PRICING_TIER_ID_NOT_ENABLED";
                      /** @enum {string} */
                      description?: "The API Caller is not enabled to process transactions by specifying a 'payee_pricing_tier_id'. Please work with your Account Manager to enable this option for your account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PAYEE_PRICING_TIER_ID";
                      /** @enum {string} */
                      description?: "Please check the value specified or confirm with your Account Manager that the 'payee_pricing_tier_id' specified has been setup for the account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_FX_RATE_ID_EXPIRED";
                      /** @enum {string} */
                      description?: "The specified FX Rate ID has expired. Please specify a different FX Rate Id and try the request again. Alternately, remove the FX Rate ID to process the request using the default exchange rate.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_FX_RATE_ID_CURRENCY_MISMATCH";
                      /** @enum {string} */
                      description?: "The specified FX Rate ID is for a currency that does not match with the currency of this request. Please specify a different FX Rate ID and try the request again. Alternately, remove the FX Rate ID to process the request using the default exchange rate.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_FX_RATE_ID";
                      /** @enum {string} */
                      description?: "The specific FX Rate ID is not valid. This could be either because we are not able to look up the FX Rate based on this ID or it could be because the ID belongs to another API Caller.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PLATFORM_FEES_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "The API Caller is not enabled to process transactions by specifying 'platform_fees'. Please work with your PayPal Account Manager to enable this option for your account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PLATFORM_FEES_ACCOUNT";
                      /** @enum {string} */
                      description?: "The specified platform_fees payee account is either invalid or account setup is incomplete.Please work with your PayPal Account Manager to enable this option for your account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PLATFORM_FEES_AMOUNT";
                      /** @enum {string} */
                      description?: "The platform_fees amount cannot be greater than order amount.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "POSTAL_CODE_REQUIRED";
                      /** @enum {string} */
                      description?: "The specified country requires a postal code.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REFERENCE_ID_REQUIRED";
                      /** @enum {string} */
                      description?: "'reference_id' is required for each 'purchase_unit' if multiple 'purchase_unit' are provided.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_OPTIONS_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Shipping options are not supported when `shipping.type` is specified or when 'application_context.shipping_preference' is set as 'NO_SHIPPING' or 'SET_PROVIDED_ADDRESS'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TAX_TOTAL_MISMATCH";
                      /** @enum {string} */
                      description?: "Should equal sum of (tax * quantity) across all items for a given purchase_unit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TAX_TOTAL_REQUIRED";
                      /** @enum {string} */
                      description?: "If item details are specified (items.tax_total and items.quantity) corresponding amount.breakdown.tax_total is required.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_INTENT";
                      /** @enum {string} */
                      description?: "`intent=AUTHORIZE` is not supported for multiple purchase units. Only `intent=CAPTURE` is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_PAYMENT_INSTRUCTION";
                      /** @enum {string} */
                      description?: 'You must provide the payment instruction when you capture an authorized payment for `intent=AUTHORIZE`. For details, see <a href="/docs/api/payments/v2/#authorizations_capture">Capture authorization</a>. For `intent=CAPTURE`, send the payment instruction when you create the order.';
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_TYPE_NOT_SUPPORTED_FOR_CLIENT";
                      /** @enum {string} */
                      description?: 'The API Caller account is not setup to be able to support a `shipping.type`=`PICKUP_IN_PERSON`. This feature is only supported for <a href="https://www.paypal.com/us/business/platforms-and-marketplaces">PayPal Commerce Platform for Platforms and Marketplaces</a>.';
                  }
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_SHIPPING_TYPE";
                      /** @enum {string} */
                      description?: "The provided `shipping.type` is only supported for `application_context.shipping_preference`=`SET_PROVIDED_ADDRESS` or `NO_SHIPPING`.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_OPTION_NOT_SELECTED";
                      /** @enum {string} */
                      description?: "At least one of the shipping.option should be set to 'selected = true'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_OPTIONS_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Shipping options are not supported when 'application_context.shipping_preference' is set as 'NO_SHIPPING' or 'SET_PROVIDED_ADDRESS'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MULTIPLE_SHIPPING_OPTION_SELECTED";
                      /** @enum {string} */
                      description?: "Only one shipping.option can be set to 'selected = true'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREFERRED_SHIPPING_OPTION_AMOUNT_MISMATCH";
                      /** @enum {string} */
                      description?: "The amount provided in the preferred shipping option should match the amount provided in amount breakdown";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AGREEMENT_ALREADY_CANCELLED";
                      /** @enum {string} */
                      description?: "The requested agreement is already canceled.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BILLING_AGREEMENT_NOT_FOUND";
                      /** @enum {string} */
                      description?: "The requested Billing Agreement token was not found.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "COMPLIANCE_VIOLATION";
                      /** @enum {string} */
                      description?: "Transaction is declined due to compliance violation.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DOMESTIC_TRANSACTION_REQUIRED";
                      /** @enum {string} */
                      description?: "This transaction requires the payee and payer to be resident in the same country, a domestic transaction is required to create this payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DUPLICATE_INVOICE_ID";
                      /** @enum {string} */
                      description?: "Duplicate Invoice ID detected. To avoid a potential duplicate transaction your account setting requires that Invoice Id be unique for each transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INSTRUMENT_DECLINED";
                      /** @enum {string} */
                      description?: "The instrument presented  was either declined by the processor or bank, or it can't be used for this payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MAX_NUMBER_OF_PAYMENT_ATTEMPTS_EXCEEDED";
                      /** @enum {string} */
                      description?: "You have exceeded the maximum number of payment attempts.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ENABLED_FOR_CARD_PROCESSING";
                      /** @enum {string} */
                      description?: "The API Caller account is not setup to be able to process card payments. Please contact PayPal customer support.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_BLOCKED_TRANSACTION";
                      /** @enum {string} */
                      description?: "The Fraud settings for this seller are such that this payment cannot be executed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_ACCOUNT_LOCKED_OR_CLOSED";
                      /** @enum {string} */
                      description?: "The payer account cannot be used for this transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_ACCOUNT_RESTRICTED";
                      /** @enum {string} */
                      description?: "PAYER_ACCOUNT_RESTRICTED";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_CANNOT_PAY";
                      /** @enum {string} */
                      description?: "Combination of payer and payee settings mean that this buyer cannot pay this seller.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_BLOCKED_BY_PAYEE";
                      /** @enum {string} */
                      description?: "Transaction blocked by Payee’s Fraud Protection settings.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_LIMIT_EXCEEDED";
                      /** @enum {string} */
                      description?: "Total payment amount exceeded transaction limit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_RECEIVING_LIMIT_EXCEEDED";
                      /** @enum {string} */
                      description?: "The transaction exceeds the receiver's receiving limit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_REFUSED";
                      /** @enum {string} */
                      description?: "The request was refused.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AUTH_CAPTURE_NOT_ENABLED";
                      /** @enum {string} */
                      description?: "Authorization and Capture feature is not enabled for the merchant. Make sure that the recipient of the funds is a verified business account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_PROCESSING_INSTRUCTION";
                      /** @enum {string} */
                      description?: "The specified processing_instruction is not supported for the given payment_source. Please refer to https://developer.paypal.com/api/orders/v2/#definition-processing_instruction for the list of payment_source that can be specified with this value.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_COMPLETE_ON_PAYMENT_APPROVAL";
                      /** @enum {string} */
                      description?: "A processing_instruction of `ORDER_COMPLETE_ON_PAYMENT_APPROVAL` is required for the specified payment_source. Please refer to the integration guide https://developer.paypal.com/docs/limited-release/alternative-payment-methods-with-orders/ for more details";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_EXPIRY_DATE";
                      /** @enum {string} */
                      description?: "Expiry date is invalid. Expiry date should be a date in future and within the threshold for the payment source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INCOMPATIBLE_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of the field is incompatible/redundant with other fields in the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PREVIOUS_TRANSACTION_REFERENCE";
                      /** @enum {string} */
                      description?: "The authorization or capture referenced by `previous_transaction_reference` is not valid. This could be either because the previous_transaction_reference is not found or doesn't belong to the payee. Please use a valid `previous_transaction_reference`.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREVIOUS_TRANSACTION_REFERENCE_HAS_CHARGEBACK";
                      /** @enum {string} */
                      description?: "The capture referenced by `previous_transaction_reference` has a chargeback and hence cannot be used for this order. Please use a `previous_transaction_reference` which does not have a chargeback.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREVIOUS_TRANSACTION_REFERENCE_VOIDED";
                      /** @enum {string} */
                      description?: "The status of authorization referenced by `previous_transaction_reference` is `VOIDED` and hence cannot be used for this order. Please use a `previous_transaction_reference` whose status is not `VOIDED`.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_MISMATCH";
                      /** @enum {string} */
                      description?: "The `payment_source` in the request must match the `payment_source` used for the authorization or capture referenced by `previous_transaction_reference`. Please use `previous_transaction_reference` whose `payment_source` matches with the `payment_source` specified in the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MERCHANT_INITIATED_WITH_SECURITY_CODE";
                      /** @enum {string} */
                      description?: "`stored_payment_source.payment_initiator` = `MERCHANT` is not supported if `payment_source.card.security_code` is present in the order. `security_code` can be present in the order only when customer is the payment initiator. It is semantically incorrect to perform a merchant initiated payment with `security_code` is the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MERCHANT_INITIATED_WITH_AUTHENTICATION_RESULTS";
                      /** @enum {string} */
                      description?: "`stored_payment_source.payment_initiator` = `MERCHANT` is not supported if 3D-Secure authentication results are present in the order. 3D-Secure authentication results can be present in the order only when customer is the payment initiator. It is semantically incorrect to perform a merchant initiated payment with 3D-Secure authentication results is the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MERCHANT_INITIATED_WITH_MULTIPLE_PURCHASE_UNITS";
                      /** @enum {string} */
                      description?: "`stored_payment_source.payment_initiator` = `MERCHANT` is not supported if more than one purchase_unit is present in the Order. Merchant initiated payments are not supported from orders with more than one purchase_unit. Please retry the request with multiple Order requests (one for each purchase_unit).";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_INFO_CANNOT_BE_VERIFIED";
                      /** @enum {string} */
                      description?: "The combination of the payment_source name, billing address, shipping name and shipping address could not be verified. Please correct this information and try again by creating a new order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_DECLINED_BY_PROCESSOR";
                      /** @enum {string} */
                      description?: "The provided payment source is declined by the processor. Please try again with a different payment source by creating a new order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_CANNOT_BE_USED";
                      /** @enum {string} */
                      description?: "The provided payment source cannot be used to pay for the order. Please try again with a different payment source by creating a new order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ENABLED_FOR_APPLE_PAY";
                      /** @enum {string} */
                      description?: "The 'API caller' and/or 'payee' is not setup to be able to process apple pay. Please contact your Account Manager.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ENABLED_FOR_GOOGLE_PAY";
                      /** @enum {string} */
                      description?: "The 'API caller' and/or 'payee' is not setup to be able to process google pay. Please contact your Account Manager.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "APPLE_PAY_AMOUNT_MISMATCH";
                      /** @enum {string} */
                      description?: "The 'amount' specified in the Order should match the amount that was viewed and authorized by the payer/buyer on Apple Pay. If the amount has changed, please redirect the buyer to authorize the order again via Apple Pay.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BILLING_ADDRESS_INVALID";
                      /** @enum {string} */
                      description?: "Provided billing address is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_ADDRESS_INVALID";
                      /** @enum {string} */
                      description?: "Provided shipping address is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "VAULT_INSTRUCTION_DUPLICATED";
                      /** @enum {string} */
                      description?: "Only one vault instruction is allowed. Please use `vault.store_in_vault` to provide vault instruction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "VAULT_INSTRUCTION_REQUIRED";
                      /** @enum {string} */
                      description?: "Vault instruction is required. Please use `vault.store_in_vault` to provide vault instruction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISMATCHED_VAULT_ID_TO_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "The vault_id does not match the payment_source provided. Please verify that the vault_id token used refers to the matching payment_source and try again. For example, a PayPal token cannot be passed in the vault_id field in the payment_source.card object.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CRYPTOGRAM_REQUIRED";
                      /** @enum {string} */
                      description?: "Cryptogram is required if authentication method is CRYPTOGRAM 3DS.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "EMV_DATA_REQUIRED";
                      /** @enum {string} */
                      description?: "EMV Data is required if authentication method is EMV.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_PNREF_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enabled to process payments with the `pnref`. Please contact customer support to request permissions to process transactions with PNREF.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_PAYPAL_TRANSACTION_ID_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enable to process payments using `paypal_transaction_id`. Please contact customer support to request permissions to process transactions with PayPal transaction ID.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYPAL_TRANSACTION_ID_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified `paypal_transaction_id` was not found. Verify the value and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PNREF_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified `pnref` was not found. Verify the value and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_SECURITY_CODE_LENGTH";
                      /** @enum {string} */
                      description?: "The security_code length is invalid for the specified card brand.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ENABLED_TO_VAULT_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "The API caller or the merchant on whose behalf the API call is initiated is not allowed to vault the given source. Please contact PayPal customer support for assistance.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REQUIRED_PARAMETER_FOR_CUSTOMER_INITIATED_PAYMENT";
                      /** @enum {string} */
                      description?: 'This parameter is required when the customer is present. If the customer is not present, indicate so by sending payment_initiator=`MERCHANT`. For details, see <a href="https://developer.paypal.com/docs/api/orders/v2/#definition-card_stored_credential">Stored Credential</a>.';
                  }
                | {
                      /** @enum {string} */
                      issue?: "TOKEN_EXPIRED";
                      /** @enum {string} */
                      description?: "The token is expired and cannot be used for payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_GOOGLE_PAY_TOKEN";
                      /** @enum {string} */
                      description?: "The google pay token is invalid. PayPal was not able to decrypt the googlepay token or PayPal was not able to find the necessary data in the token after decryption.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "GOOGLE_PAY_GATEWAY_MERCHANT_ID_MISMATCH";
                      /** @enum {string} */
                      description?: "The gateway merchant ID in Google Pay token is not valid. This could be because the gateway merchant Id that was authorized by payer/buyer on Google Pay does not match with the API caller of the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ONE_OF_PARAMETERS_REQUIRED";
                      /** @enum {string} */
                      description?: "One or more field is required to continue with this request.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ALIAS_DECLINED_BY_PROCESSOR";
                      /** @enum {string} */
                      description?: "The provided alias was declined by the processor. Please create a new order with a different alias_key and/or alias_label and try again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BLIK_ONE_CLICK_MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "Blik's one_click flow requires one_click.auth_code and one_click.alias_label parameters for the buyer's first transaction. For all subsequent transactions,only the one_click.alias_key parameter is required.";
                  }
            )[];
        };
        /**
         * Error Details
         * @description The error details. Required for client-side `4XX` errors.
         */
        error_details: {
            /** @description The field that caused the error. If this field is in the body, set this value to the field's JSON pointer value. Required for client-side errors. */
            field?: string;
            /** @description The value of the field that caused the error. */
            value?: string;
            /**
             * @description The location of the field that caused the error. Value is `body`, `path`, or `query`.
             * @default body
             */
            location?: string;
            /** @description The unique, fine-grained application-level error code. */
            issue: string;
            /** @description The human-readable description for an issue. The description can change over the lifetime of an API, so clients must not depend on this value. */
            description?: string;
        };
        /** @description The default error response. */
        error_default:
            | components["schemas"]["error_400"]
            | components["schemas"]["error_401"]
            | components["schemas"]["error_403"]
            | components["schemas"]["error_404"]
            | components["schemas"]["error_409"]
            | components["schemas"]["error_415"]
            | components["schemas"]["error_422"]
            | components["schemas"]["error_500"]
            | components["schemas"]["error_503"];
        /**
         * 400 Error
         * @description Error response for 400
         */
        error_400: {
            /** @enum {string} */
            name?: "INVALID_REQUEST";
            /** @enum {string} */
            message?: "Request is not well-formed, syntactically incorrect, or violates schema.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 401 Error
         * @description Error response for 401
         */
        error_401: {
            /** @enum {string} */
            name?: "AUTHENTICATION_FAILURE";
            /** @enum {string} */
            message?: "Authentication failed due to missing authorization header, or invalid authentication credentials.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 403 Error
         * @description Error response for 403
         */
        error_403: {
            /** @enum {string} */
            name?: "NOT_AUTHORIZED";
            /** @enum {string} */
            message?: "Authorization failed due to insufficient permissions.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 404 Error
         * @description Error response for 404
         */
        error_404: {
            /** @enum {string} */
            name?: "RESOURCE_NOT_FOUND";
            /** @enum {string} */
            message?: "The specified resource does not exist.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 409 Error
         * @description Error response for 409
         */
        error_409: {
            /** @enum {string} */
            name?: "RESOURCE_CONFLICT";
            /** @enum {string} */
            message?: "The server has detected a conflict while processing this request.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 409 Error
         * @description Error response for 415
         */
        error_415: {
            /** @enum {string} */
            name?: "UNSUPPORTED_MEDIA_TYPE";
            /** @enum {string} */
            message?: "The server does not support the request payload's media type.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 422 Error
         * @description Error response for 422
         */
        error_422: {
            /** @enum {string} */
            name?: "UNPROCESSABLE_ENTITY";
            /** @enum {string} */
            message?: "The requested action could not be performed, semantically incorrect, or failed business validation.";
            issues?: components["schemas"]["error_details"][];
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * 500 Error
         * @description Error response for 500
         * @example {
         *   "name": "INTERNAL_SERVER_ERROR",
         *   "message": "An internal server error occurred.",
         *   "debug_id": "90957fca61718",
         *   "information_link": "https://developer.paypal.com/api/orders/v2/#error-INTERNAL_SERVER_ERROR"
         * }
         */
        error_500: {
            /** @enum {string} */
            name?: "INTERNAL_SERVER_ERROR";
            /** @enum {string} */
            message?: "An internal server error occurred.";
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /**
             * @description The information link, or URI, that shows detailed information about this error for the developer.
             * @enum {string}
             */
            information_link?: "https://developer.paypal.com/api/orders/v2/#error-INTERNAL_SERVER_ERROR";
        };
        /**
         * 503 Error
         * @description Error response for 503
         * @example {
         *   "name": "SERVICE_UNAVAILABLE",
         *   "message": "Service Unavailable.",
         *   "debug_id": "90957fca61718",
         *   "information_link": "https://developer.paypal.com/docs/api/orders/v2/#error-SERVICE_UNAVAILABLE"
         * }
         */
        error_503: {
            /** @enum {string} */
            name?: "SERVICE_UNAVAILABLE";
            /** @enum {string} */
            message?: "Service Unavailable.";
            /** @description The PayPal internal ID. Used for correlation purposes. */
            debug_id?: string;
            /** @description The information link, or URI, that shows detailed information about this error for the developer. */
            information_link?: string;
        };
        /**
         * Checkout Payment Intent
         * @description The intent to either capture payment immediately or authorize a payment for an order after order creation.
         * @enum {string}
         */
        checkout_payment_intent: "CAPTURE" | "AUTHORIZE";
        /**
         * Format: merchant_common_email_address_v2
         * @description The internationalized email address.<blockquote><strong>Note:</strong> Up to 64 characters are allowed before and 255 characters are allowed after the <code>@</code> sign. However, the generally accepted maximum length for an email address is 254 characters. The pattern verifies that an unquoted <code>@</code> sign exists.</blockquote>
         */
        email: string;
        /**
         * PayPal Account Identifier
         * Format: ppaas_payer_id_v3
         * @description The account identifier for a PayPal account.
         */
        account_id: string;
        /**
         * Payer Base
         * @description The customer who approves and pays for the order. The customer is also known as the payer.
         */
        payer_base: {
            /** @description The email address of the payer. */
            email_address?: components["schemas"]["email"];
            /** @description The PayPal-assigned ID for the payer. */
            payer_id?: components["schemas"]["account_id"];
        };
        /**
         * Name
         * @description The name of the party.
         */
        name: {
            /** @description The prefix, or title, to the party's name. */
            prefix?: string;
            /** @description When the party is a person, the party's given, or first, name. */
            given_name?: string;
            /** @description When the party is a person, the party's surname or family name. Also known as the last name. Required when the party is a person. Use also to store multiple surnames including the matronymic, or mother's, surname. */
            surname?: string;
            /** @description When the party is a person, the party's middle name. Use also to store multiple middle names including the patronymic, or father's, middle name. */
            middle_name?: string;
            /** @description The suffix for the party's name. */
            suffix?: string;
            /** @description DEPRECATED. The party's alternate name. Can be a business name, nickname, or any other name that cannot be split into first, last name. Required when the party is a business. */
            alternate_full_name?: string;
            /** @description When the party is a person, the party's full name. */
            full_name?: string;
        };
        /**
         * Phone Type
         * @description The phone type.
         * @enum {string}
         */
        phone_type: "FAX" | "HOME" | "MOBILE" | "OTHER" | "PAGER";
        /**
         * Phone
         * @description The phone number, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en).
         */
        phone: {
            /** @description The country calling code (CC), in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). The combined length of the CC and the national number must not be greater than 15 digits. The national number consists of a national destination code (NDC) and subscriber number (SN). */
            country_code: string;
            /** @description The national number, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). The combined length of the country calling code (CC) and the national number must not be greater than 15 digits. The national number consists of a national destination code (NDC) and subscriber number (SN). */
            national_number: string;
            /** @description The extension number. */
            extension_number?: string;
        };
        /**
         * Phone With Type
         * @description The phone information.
         */
        phone_with_type: {
            phone_type?: components["schemas"]["phone_type"];
            /** @description The phone number, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). Supports only the `national_number` property. */
            phone_number: components["schemas"]["phone"];
        };
        /**
         * Format: ppaas_date_notime_v2
         * @description The stand-alone date, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). To represent special legal values, such as a date of birth, you should use dates with no associated time or time-zone data. Whenever possible, use the standard `date_time` type. This regular expression does not validate all dates. For example, February 31 is valid and nothing is known about leap years.
         */
        date_no_time: string;
        /**
         * Tax Information
         * @description The tax ID of the customer. The customer is also known as the payer. Both `tax_id` and `tax_id_type` are required.
         */
        tax_info: {
            /** @description The customer's tax ID value. */
            tax_id: string;
            /**
             * @description The customer's tax ID type.
             * @enum {string}
             */
            tax_id_type: "BR_CPF" | "BR_CNPJ";
        };
        /**
         * Format: ppaas_common_country_code_v2
         * @description The [two-character ISO 3166-1 code](/api/rest/reference/country-codes/) that identifies the country or region.<blockquote><strong>Note:</strong> The country code for Great Britain is <code>GB</code> and not <code>UK</code> as used in the top-level domain names for that country. Use the `C2` country code for China worldwide for comparable uncontrolled price (CUP) method, bank card, and cross-border transactions.</blockquote>
         */
        country_code: string;
        /**
         * Portable Postal Address (Medium-Grained)
         * @description The portable international postal address. Maps to [AddressValidationMetadata](https://github.com/googlei18n/libaddressinput/wiki/AddressValidationMetadata) and HTML 5.1 [Autofilling form controls: the autocomplete attribute](https://www.w3.org/TR/html51/sec-forms.html#autofilling-form-controls-the-autocomplete-attribute).
         */
        address_portable: {
            /** @description The first line of the address. For example, number or street. For example, `173 Drury Lane`. Required for data entry and compliance and risk checks. Must contain the full address. */
            address_line_1?: string;
            /** @description The second line of the address. For example, suite or apartment number. */
            address_line_2?: string;
            /** @description The third line of the address, if needed. For example, a street complement for Brazil, direction text, such as `next to Walmart`, or a landmark in an Indian address. */
            address_line_3?: string;
            /** @description The neighborhood, ward, or district. Smaller than `admin_area_level_3` or `sub_locality`. Value is:<ul><li>The postal sorting code for Guernsey and many French territories, such as French Guiana.</li><li>The fine-grained administrative levels in China.</li></ul> */
            admin_area_4?: string;
            /** @description A sub-locality, suburb, neighborhood, or district. Smaller than `admin_area_level_2`. Value is:<ul><li>Brazil. Suburb, bairro, or neighborhood.</li><li>India. Sub-locality or district. Street name information is not always available but a sub-locality or district can be a very small area.</li></ul> */
            admin_area_3?: string;
            /** @description A city, town, or village. Smaller than `admin_area_level_1`. */
            admin_area_2?: string;
            /** @description The highest level sub-division in a country, which is usually a province, state, or ISO-3166-2 subdivision. Format for postal delivery. For example, `CA` and not `California`. Value, by country, is:<ul><li>UK. A county.</li><li>US. A state.</li><li>Canada. A province.</li><li>Japan. A prefecture.</li><li>Switzerland. A kanton.</li></ul> */
            admin_area_1?: string;
            /** @description The postal code, which is the zip code or equivalent. Typically required for countries with a postal code or an equivalent. See [postal code](https://en.wikipedia.org/wiki/Postal_code). */
            postal_code?: string;
            country_code: components["schemas"]["country_code"];
            /**
             * Address Details
             * @description The non-portable additional address details that are sometimes needed for compliance, risk, or other scenarios where fine-grain address information might be needed. Not portable with common third party and open source. Redundant with core fields.<br/>For example, `address_portable.address_line_1` is usually a combination of `address_details.street_number`, `street_name`, and `street_type`.
             */
            address_details?: {
                /** @description The street number. */
                street_number?: string;
                /** @description The street name. Just `Drury` in `Drury Lane`. */
                street_name?: string;
                /** @description The street type. For example, avenue, boulevard, road, or expressway. */
                street_type?: string;
                /** @description The delivery service. Post office box, bag number, or post office name. */
                delivery_service?: string;
                /** @description A named locations that represents the premise. Usually a building name or number or collection of buildings with a common name or number. For example, <code>Craven House</code>. */
                building_name?: string;
                /** @description The first-order entity below a named building or location that represents the sub-premises. Usually a single building within a collection of buildings with a common name. Can be a flat, story, floor, room, or apartment. */
                sub_building?: string;
            };
        };
        /**
         * Customer
         * Format: payer_v1
         * @description The customer who approves and pays for the order. The customer is also known as the payer.
         */
        payer: components["schemas"]["payer_base"] & {
            /** @description The name of the payer. Supports only the `given_name` and `surname` properties. */
            name?: components["schemas"]["name"];
            /** @description The phone number of the customer. Available only when you enable the **Contact Telephone Number** option in the <a href="https://www.paypal.com/cgi-bin/customerprofileweb?cmd=_profile-website-payments">**Profile & Settings**</a> for the merchant's PayPal account. The `phone.phone_number` supports only `national_number`. */
            phone?: components["schemas"]["phone_with_type"];
            /** @description The birth date of the payer in `YYYY-MM-DD` format. */
            birth_date?: components["schemas"]["date_no_time"];
            /** @description The tax information of the payer. Required only for Brazilian payer's. Both `tax_id` and `tax_id_type` are required. */
            tax_info?: components["schemas"]["tax_info"];
            /** @description The address of the payer. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. Also referred to as the billing address of the customer. */
            address?: components["schemas"]["address_portable"];
        };
        /**
         * Format: ppaas_common_currency_code_v2
         * @description The [three-character ISO-4217 currency code](/api/rest/reference/currency-codes/) that identifies the currency.
         */
        currency_code: string;
        /**
         * Money
         * @description The currency and amount for a financial transaction, such as a balance or payment due.
         */
        money: {
            currency_code: components["schemas"]["currency_code"];
            /** @description The value, which might be:<ul><li>An integer for currencies like `JPY` that are not typically fractional.</li><li>A decimal fraction for currencies like `TND` that are subdivided into thousandths.</li></ul>For the required number of decimal places for a currency code, see [Currency Codes](/api/rest/reference/currency-codes/). */
            value: string;
        };
        /**
         * Amount Breakdown
         * @description The breakdown of the amount. Breakdown provides details such as total item amount, total tax amount, shipping, handling, insurance, and discounts, if any.
         */
        amount_breakdown: {
            /** @description The subtotal for all items. Required if the request includes `purchase_units[].items[].unit_amount`. Must equal the sum of `(items[].unit_amount * items[].quantity)` for all items. <code>item_total.value</code> can not be a negative number. */
            item_total?: components["schemas"]["money"];
            /** @description The shipping fee for all items within a given `purchase_unit`. <code>shipping.value</code> can not be a negative number. */
            shipping?: components["schemas"]["money"];
            /** @description The handling fee for all items within a given `purchase_unit`. <code>handling.value</code> can not be a negative number. */
            handling?: components["schemas"]["money"];
            /** @description The total tax for all items. Required if the request includes `purchase_units.items.tax`. Must equal the sum of `(items[].tax * items[].quantity)` for all items. <code>tax_total.value</code> can not be a negative number. */
            tax_total?: components["schemas"]["money"];
            /** @description The insurance fee for all items within a given `purchase_unit`. <code>insurance.value</code> can not be a negative number. */
            insurance?: components["schemas"]["money"];
            /** @description The shipping discount for all items within a given `purchase_unit`. <code>shipping_discount.value</code> can not be a negative number. */
            shipping_discount?: components["schemas"]["money"];
            /** @description The discount for all items within a given `purchase_unit`. <code>discount.value</code> can not be a negative number. */
            discount?: components["schemas"]["money"];
        };
        /**
         * Amount with Breakdown
         * @description The total order amount with an optional breakdown that provides details, such as the total item amount, total tax amount, shipping, handling, insurance, and discounts, if any.<br/>If you specify `amount.breakdown`, the amount equals `item_total` plus `tax_total` plus `shipping` plus `handling` plus `insurance` minus `shipping_discount` minus discount.<br/>The amount must be a positive number. For listed of supported currencies and decimal precision, see the PayPal REST APIs <a href="/docs/integration/direct/rest/currency-codes/">Currency Codes</a>.
         */
        amount_with_breakdown: components["schemas"]["money"] & {
            breakdown?: components["schemas"]["amount_breakdown"];
        };
        /**
         * Merchant Base
         * @description The details for the merchant who receives the funds and fulfills the order. The merchant is also known as the payee.
         */
        payee_base: {
            /** @description The email address of merchant. */
            email_address?: components["schemas"]["email"];
            /** @description The encrypted PayPal account ID of the merchant. */
            merchant_id?: components["schemas"]["account_id"];
        };
        /**
         * Payee
         * @description The merchant who receives the funds and fulfills the order. The merchant is also known as the payee.
         */
        payee: components["schemas"]["payee_base"];
        /**
         * Platform Fee
         * @description The platform or partner fee, commission, or brokerage fee that is associated with the transaction. Not a separate or isolated transaction leg from the external perspective. The platform fee is limited in scope and is always associated with the original payment for the purchase unit.
         */
        platform_fee: {
            /** @description The fee for this transaction. */
            amount: components["schemas"]["money"];
            /** @description The recipient of the fee for this transaction. If you omit this value, the default is the API caller. */
            payee?: components["schemas"]["payee_base"];
        };
        /**
         * Disbursement Mode
         * @description The funds that are held on behalf of the merchant.
         * @default INSTANT
         * @enum {string}
         */
        disbursement_mode: "INSTANT" | "DELAYED";
        /**
         * Payment Instruction
         * @description Any additional payment instructions to be consider during payment processing. This processing instruction is applicable for Capturing an order or Authorizing an Order.
         */
        payment_instruction: {
            /** @description An array of various fees, commissions, tips, or donations. This field is only applicable to merchants that been enabled for PayPal Commerce Platform for Marketplaces and Platforms capability. */
            platform_fees?: components["schemas"]["platform_fee"][];
            /** @description The funds that are held payee by the marketplace/platform. This field is only applicable to merchants that been enabled for PayPal Commerce Platform for Marketplaces and Platforms capability. */
            disbursement_mode?: components["schemas"]["disbursement_mode"];
            /** @description This field is only enabled for selected merchants/partners to use and provides the ability to trigger a specific pricing rate/plan for a payment transaction. The list of eligible 'payee_pricing_tier_id' would be provided to you by your Account Manager. Specifying values other than the one provided to you by your account manager would result in an error. */
            payee_pricing_tier_id?: string;
            /** @description FX identifier generated returned by PayPal to be used for payment processing in order to honor FX rate (for eligible integrations) to be used when amount is settled/received into the payee account. */
            payee_receivable_fx_rate_id?: string;
        };
        /**
         * Item
         * @description The details for the items to be purchased.
         */
        item: {
            /** @description The item name or title. */
            name: string;
            /** @description The item price or rate per unit. If you specify <code>unit_amount</code>, <code>purchase_units[].amount.breakdown.item_total</code> is required. Must equal <code>unit_amount * quantity</code> for all items. <code>unit_amount.value</code> can not be a negative number. */
            unit_amount: components["schemas"]["money"];
            /** @description The item tax for each unit. If <code>tax</code> is specified, <code>purchase_units[].amount.breakdown.tax_total</code> is required. Must equal <code>tax * quantity</code> for all items. <code>tax.value</code> can not be a negative number. */
            tax?: components["schemas"]["money"];
            /** @description The item quantity. Must be a whole number. */
            quantity: string;
            /** @description The detailed item description. */
            description?: string;
            /** @description The stock keeping unit (SKU) for the item. */
            sku?: string;
            /**
             * @description The item category type.
             * @enum {string}
             */
            category?: "DIGITAL_GOODS" | "PHYSICAL_GOODS" | "DONATION";
        };
        /**
         * Shipping Type
         * @description The method by which the payer wants to get their items.
         * @enum {string}
         */
        shipping_type: "SHIPPING" | "PICKUP";
        /**
         * Shipping Option
         * @description The options that the payee or merchant offers to the payer to ship or pick up their items.
         */
        shipping_option: {
            /** @description A unique ID that identifies a payer-selected shipping option. */
            id: string;
            /** @description A description that the payer sees, which helps them choose an appropriate shipping option. For example, `Free Shipping`, `USPS Priority Shipping`, `Expédition prioritaire USPS`, or `USPS yōuxiān fā huò`. Localize this description to the payer's locale. */
            label: string;
            /** @description The method by which the payer wants to get their items. */
            type?: components["schemas"]["shipping_type"];
            /** @description The shipping cost for the selected option. */
            amount?: components["schemas"]["money"];
            /** @description If the API request sets `selected = true`, it represents the shipping option that the payee or merchant expects to be pre-selected for the payer when they first view the `shipping.options` in the PayPal Checkout experience. As part of the response if a `shipping.option` contains `selected=true`, it represents the shipping option that the payer selected during the course of checkout with PayPal. Only one `shipping.option` can be set to `selected=true`. */
            selected: boolean;
        };
        /**
         * Shipping Details
         * @description The shipping details.
         */
        shipping_detail: {
            /** @description The name of the person to whom to ship the items. Supports only the `full_name` property. */
            name?: components["schemas"]["name"];
            /**
             * @description The method by which the payer wants to get their items from the payee e.g shipping, in-person pickup. Either type or options but not both may be present.
             * @enum {string}
             */
            type?: "SHIPPING" | "PICKUP_IN_PERSON";
            /** @description An array of shipping options that the payee or merchant offers to the payer to ship or pick up their items. */
            options?: components["schemas"]["shipping_option"][];
            /** @description The address of the person to whom to ship the items. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. */
            address?: components["schemas"]["address_portable"];
        };
        /**
         * Level 2 Card Processing Data
         * @description The level 2 card processing data collections. If your merchant account has been configured for Level 2 processing this field will be passed to the processor on your behalf. Please contact your PayPal Technical Account Manager to define level 2 data for your business.
         */
        level_2_card_processing_data: {
            /** @description Use this field to pass a purchase identification value of up to 12 ASCII characters for AIB and 17 ASCII characters for all other processors. */
            invoice_id?: string;
            /**
             * @description Use this field to break down the amount of tax included in the total purchase amount. The value provided here will not add to the total purchase amount. The value can't be negative, and in most cases, it must be greater than zero in order to qualify for lower interchange rates.
             *  Value, by country, is:
             *
             *     UK. A county.
             *     US. A state.
             *     Canada. A province.
             *     Japan. A prefecture.
             *     Switzerland. A kanton.
             */
            tax_total?: components["schemas"]["money"];
        };
        /**
         * Lineitem
         * @description The line items for this purchase. If your merchant account has been configured for Level 3 processing this field will be passed to the processor on your behalf.
         */
        line_item: components["schemas"]["item"] & {
            /** @description Code used to classify items purchased and track the total amount spent across various categories of products and services. Different corporate purchasing organizations may use different standards, but the United Nations Standard Products and Services Code (UNSPSC) is frequently used. */
            commodity_code?: string;
            /** @description Use this field to break down the discount amount included in the total purchase amount. The value provided here will not add to the total purchase amount. The value cannot be negative. */
            discount_amount?: components["schemas"]["money"];
            /** @description The subtotal for all items. Must equal the sum of (items[].unit_amount * items[].quantity) for all items. item_total.value can not be a negative number. */
            total_amount?: components["schemas"]["money"];
            /** @description Unit of measure is a standard used to express the magnitude of a quantity in international trade. Most commonly used (but not limited to) examples are: Acre (ACR), Ampere (AMP), Centigram (CGM), Centimetre (CMT), Cubic inch (INQ), Cubic metre (MTQ), Fluid ounce (OZA), Foot (FOT), Hour (HUR), Item (ITM), Kilogram (KGM), Kilometre (KMT), Kilowatt (KWT), Liquid gallon (GLL), Liter (LTR), Pounds (LBS), Square foot (FTK). */
            unit_of_measure?: string;
        };
        /**
         * Level 3 Card Processing Data
         * @description The level 3 card processing data collections, If your merchant account has been configured for Level 3 processing this field will be passed to the processor on your behalf. Please contact your PayPal Technical Account Manager to define level 3 data for your business.
         */
        level_3_card_processing_data: {
            /** @description Use this field to break down the shipping cost included in the total purchase amount. The value provided here will not add to the total purchase amount. The value cannot be negative. */
            shipping_amount?: components["schemas"]["money"];
            /** @description Use this field to break down the duty amount included in the total purchase amount. The value provided here will not add to the total purchase amount. The value cannot be negative. */
            duty_amount?: components["schemas"]["money"];
            /** @description Use this field to break down the discount amount included in the total purchase amount. The value provided here will not add to the total purchase amount. The value cannot be negative. */
            discount_amount?: components["schemas"]["money"];
            /** @description The address of the person to whom to ship the items. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. */
            shipping_address?: components["schemas"]["address_portable"];
            /** @description Use this field to specify the postal code of the shipping location. */
            ships_from_postal_code?: string;
            /** @description A list of the items that were purchased with this payment. If your merchant account has been configured for Level 3 processing this field will be passed to the processor on your behalf. */
            line_items?: components["schemas"]["line_item"][];
        };
        /**
         * Card Supplementary Data
         * @description Merchants and partners can add Level 2 and 3 data to payments to reduce risk and payment processing costs. For more information about processing payments, see <a href="https://developer.paypal.com/docs/checkout/advanced/processing/">checkout</a> or <a href="https://developer.paypal.com/docs/multiparty/checkout/advanced/processing/">multiparty checkout</a>.
         */
        card_supplementary_data: {
            level_2?: components["schemas"]["level_2_card_processing_data"];
            level_3?: components["schemas"]["level_3_card_processing_data"];
        };
        /**
         * Supplementary Data
         * @description Supplementary data about a payment. This object passes information that can be used to improve risk assessments and processing costs, for example, by providing Level 2 and Level 3 payment data.
         */
        supplementary_data: {
            /** @description Merchants and partners can add Level 2 and 3 data to payments to reduce risk and payment processing costs. For more information about processing payments, see <a href="https://developer.paypal.com/docs/checkout/advanced/processing/">checkout</a> or <a href="https://developer.paypal.com/docs/multiparty/checkout/advanced/processing/">multiparty checkout</a>. */
            card?: components["schemas"]["card_supplementary_data"];
        };
        /**
         * Purchase Unit Request
         * @description The purchase unit request. Includes required information for the payment contract.
         */
        purchase_unit_request: {
            /** @description The API caller-provided external ID for the purchase unit. Required for multiple purchase units when you must update the order through `PATCH`. If you omit this value and the order contains only one purchase unit, PayPal sets this value to `default`. */
            reference_id?: string;
            /** @description The total order amount with an optional breakdown that provides details, such as the total item amount, total tax amount, shipping, handling, insurance, and discounts, if any.<br/>If you specify `amount.breakdown`, the amount equals `item_total` plus `tax_total` plus `shipping` plus `handling` plus `insurance` minus `shipping_discount` minus discount.<br/>The amount must be a positive number. The `amount.value` field supports up to 15 digits preceding the decimal. For a list of supported currencies, decimal precision, and maximum charge amount, see the PayPal REST APIs <a href="https://developer.paypal.com/api/rest/reference/currency-codes/">Currency Codes</a>. */
            amount: components["schemas"]["amount_with_breakdown"];
            /** @description The merchant who receives payment for this transaction. */
            payee?: components["schemas"]["payee"];
            payment_instruction?: components["schemas"]["payment_instruction"];
            /** @description The purchase description. The maximum length of the character is dependent on the type of characters used. The character length is specified assuming a US ASCII character. Depending on type of character; (e.g. accented character, Japanese characters) the number of characters that that can be specified as input might not equal the permissible max length. */
            description?: string;
            /** @description The API caller-provided external ID. Used to reconcile client transactions with PayPal transactions. Appears in transaction and settlement reports but is not visible to the payer. */
            custom_id?: string;
            /** @description The API caller-provided external invoice number for this order. Appears in both the payer's transaction history and the emails that the payer receives. */
            invoice_id?: string;
            /** @description The soft descriptor is the dynamic text used to construct the statement descriptor that appears on a payer's card statement.<br><br>If an Order is paid using the "PayPal Wallet", the statement descriptor will appear in following format on the payer's card statement: <code><var>PAYPAL_prefix</var>+(space)+<var>merchant_descriptor</var>+(space)+ <var>soft_descriptor</var></code><blockquote><strong>Note:</strong> The merchant descriptor is the descriptor of the merchant’s payment receiving preferences which can be seen by logging into the merchant account https://www.sandbox.paypal.com/businessprofile/settings/info/edit</blockquote>The <code>PAYPAL</code> prefix uses 8 characters. Only the first 22 characters will be displayed in the statement. <br>For example, if:<ul><li>The PayPal prefix toggle is <code>PAYPAL *</code>.</li><li>The merchant descriptor in the profile is <code>Janes Gift</code>.</li><li>The soft descriptor is <code>800-123-1234</code>.</li></ul>Then, the statement descriptor on the card is <code>PAYPAL * Janes Gift 80</code>. */
            soft_descriptor?: string;
            /** @description An array of items that the customer purchases from the merchant. */
            items?: components["schemas"]["item"][];
            /** @description The name and address of the person to whom to ship the items. */
            shipping?: components["schemas"]["shipping_detail"];
            /** @description Contains Supplementary Data. */
            supplementary_data?: components["schemas"]["supplementary_data"];
        };
        /** @description The identifier of the instrument. */
        instrument_id: string;
        /** @description The year and month, in ISO-8601 `YYYY-MM` date format. See [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
        date_year_month: string;
        /**
         * Card Brand
         * @description The card network or brand. Applies to credit, debit, gift, and payment cards.
         * @enum {string}
         */
        card_brand:
            | "VISA"
            | "MASTERCARD"
            | "DISCOVER"
            | "AMEX"
            | "SOLO"
            | "JCB"
            | "STAR"
            | "DELTA"
            | "SWITCH"
            | "MAESTRO"
            | "CB_NATIONALE"
            | "CONFIGOGA"
            | "CONFIDIS"
            | "ELECTRON"
            | "CETELEM"
            | "CHINA_UNION_PAY";
        /**
         * Card Type
         * @description Type of card. i.e Credit, Debit and so on.
         * @enum {string}
         */
        card_type: "CREDIT" | "DEBIT" | "PREPAID" | "STORE" | "UNKNOWN";
        /** @description The unique ID for a customer generated by PayPal. */
        merchant_partner_customer_id: string;
        /**
         * Customer information based on PayPal's system of record
         * @description The details about a customer in PayPal's system of record.
         */
        customer: {
            id?: components["schemas"]["merchant_partner_customer_id"];
            /** @description Email address of the buyer as provided to the merchant or on file with the merchant. Email Address is required if you are processing the transaction using PayPal Guest Processing which is offered to select partners and merchants. For all other use cases we do not expect partners/merchant to send email_address of their customer. */
            email_address?: components["schemas"]["email"];
            /** @description The phone number of the buyer as provided to the merchant or on file with the merchant. The `phone.phone_number` supports only `national_number`. */
            phone?: components["schemas"]["phone_with_type"];
        };
        /**
         * @description Defines how and when the payment source gets vaulted.
         * @enum {string}
         */
        store_in_vault_instruction: "ON_SUCCESS";
        /**
         * Base vault Instruction parameters
         * @description Basic vault instruction specification that can be extended by specific payment sources that supports vaulting.
         */
        vault_instruction_base: {
            store_in_vault?: components["schemas"]["store_in_vault_instruction"];
        };
        /**
         * Card Attributes
         * @description Additional attributes associated with the use of this card.
         */
        card_attributes: {
            customer?: components["schemas"]["customer"];
            /** @description Instruction to vault the card based on the specified strategy. */
            vault?: components["schemas"]["vault_instruction_base"];
        };
        /**
         * Card
         * @description The payment card to use to fund a payment. Can be a credit or debit card.
         */
        card: {
            /** @description The PayPal-generated ID for the card. */
            id?: components["schemas"]["instrument_id"];
            /** @description The card holder's name as it appears on the card. */
            name?: string;
            /** @description The primary account number (PAN) for the payment card. */
            number?: string;
            /** @description The card expiration year and month, in [Internet date format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            expiry?: components["schemas"]["date_year_month"];
            /** @description The three- or four-digit security code of the card. Also known as the CVV, CVC, CVN, CVE, or CID. This parameter cannot be present in the request when `payment_initiator=MERCHANT`. */
            security_code?: string;
            /** @description The last digits of the payment card. */
            last_digits?: string;
            /**
             * @deprecated
             * @description The card brand or network. Typically used in the response.
             */
            card_type?: components["schemas"]["card_brand"];
            /** @description The payment card type. */
            type?: components["schemas"]["card_type"];
            /** @description The card brand or network. Typically used in the response. */
            brand?: components["schemas"]["card_brand"];
            /** @description The billing address for this card. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. */
            billing_address?: components["schemas"]["address_portable"];
            /** @description Additional attributes associated with the use of this card. */
            attributes?: components["schemas"]["card_attributes"];
        };
        /** @description The PayPal-generated ID for the vaulted payment source. This ID should be stored on the merchant's server so the saved payment source can be used for future transactions. */
        vault_id: string;
        /**
         * @description The person or party who initiated or triggered the payment.
         * @enum {string}
         */
        payment_initiator: "CUSTOMER" | "MERCHANT";
        /**
         * @description Indicates the type of the stored payment_source payment.
         * @enum {string}
         */
        stored_payment_source_payment_type:
            | "ONE_TIME"
            | "RECURRING"
            | "UNSCHEDULED";
        /**
         * @description Indicates if this is a `first` or `subsequent` payment using a stored payment source (also referred to as stored credential or card on file).
         * @default DERIVED
         * @enum {string}
         */
        stored_payment_source_usage_type: "FIRST" | "SUBSEQUENT" | "DERIVED";
        /**
         * Network Transaction Reference
         * @description Reference values used by the card network to identify a transaction.
         */
        network_transaction_reference: {
            /** @description Transaction reference id returned by the scheme. For Visa and Amex, this is the "Tran id" field in response. For MasterCard, this is the "BankNet reference id" field in response. For Discover, this is the "NRID" field in response. The pattern we expect for this field from Visa/Amex/CB/Discover is numeric, Mastercard/BNPP is alphanumeric and Paysecure is alphanumeric with special character -. */
            id: string;
            /** @description The date that the transaction was authorized by the scheme. This field may not be returned for all networks. MasterCard refers to this field as "BankNet reference date. */
            date?: string;
            /** @description Name of the card network through which the transaction was routed. */
            network?: components["schemas"]["card_brand"];
            /** @description Reference ID issued for the card transaction. This ID can be used to track the transaction across processors, card brands and issuing banks. */
            acquirer_reference_number?: string;
        };
        /**
         * Card Stored Credential
         * @description Provides additional details to process a payment using a `card` that has been stored or is intended to be stored (also referred to as stored_credential or card-on-file).<br/>Parameter compatibility:<br/><ul><li>`payment_type=ONE_TIME` is compatible only with `payment_initiator=CUSTOMER`.</li><li>`usage=FIRST` is compatible only with `payment_initiator=CUSTOMER`.</li><li>`previous_transaction_reference` or `previous_network_transaction_reference` is compatible only with `payment_initiator=MERCHANT`.</li><li>Only one of the parameters - `previous_transaction_reference` and `previous_network_transaction_reference` - can be present in the request.</li></ul>
         */
        card_stored_credential: {
            payment_initiator: components["schemas"]["payment_initiator"];
            payment_type: components["schemas"]["stored_payment_source_payment_type"];
            usage?: components["schemas"]["stored_payment_source_usage_type"];
            previous_network_transaction_reference?: components["schemas"]["network_transaction_reference"];
        };
        /**
         * @description Electronic Commerce Indicator (ECI). The ECI value is part of the 2 data elements that indicate the transaction was processed electronically. This should be passed on the authorization transaction to the Gateway/Processor.
         * @enum {string}
         */
        eci_flag:
            | "MASTERCARD_NON_3D_SECURE_TRANSACTION"
            | "MASTERCARD_ATTEMPTED_AUTHENTICATION_TRANSACTION"
            | "MASTERCARD_FULLY_AUTHENTICATED_TRANSACTION"
            | "FULLY_AUTHENTICATED_TRANSACTION"
            | "ATTEMPTED_AUTHENTICATION_TRANSACTION"
            | "NON_3D_SECURE_TRANSACTION";
        /**
         * Network Token
         * @description The Third Party Network token used to fund a payment.
         */
        network_token_request: {
            /** @description Third party network token number. */
            number: string;
            /** @description The card expiration year and month, in [Internet date format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            expiry: components["schemas"]["date_year_month"];
            /** @description An Encrypted one-time use value that's sent along with Network Token. This field is not required to be present for recurring transactions. */
            cryptogram?: string;
            eci_flag?: components["schemas"]["eci_flag"];
            /** @description A TRID, or a Token Requestor ID, is an identifier used by merchants to request network tokens from card networks. A TRID is a precursor to obtaining a network token for a credit card primary account number (PAN), and will aid in enabling secure card on file (COF) payments and reducing fraud. */
            token_requestor_id?: string;
        };
        /**
         * Format: uri
         * @description Describes the URL.
         */
        url: string;
        /**
         * Card Experience Context
         * @description Customizes the payer experience during the 3DS Approval for payment.
         */
        card_experience_context: {
            /**
             * Format: uri
             * @description The URL where the customer will be redirected upon successfully completing the 3DS challenge.
             */
            return_url?: components["schemas"]["url"];
            /**
             * Format: uri
             * @description The URL where the customer will be redirected upon cancelling the 3DS challenge.
             */
            cancel_url?: components["schemas"]["url"];
        };
        /**
         * Card Request
         * @description The payment card to use to fund a payment. Can be a credit or debit card.<blockquote><strong>Note:</strong> Passing card number, cvv and expiry directly via the API requires <a href="https://www.pcisecuritystandards.org/pci_security/completing_self_assessment"> PCI SAQ D compliance</a>. <br>*PayPal offers a mechanism by which you do not have to take on the <strong>PCI SAQ D</strong> burden by using hosted fields - refer to <a href="https://developer.paypal.com/docs/checkout/advanced/integrate/">this Integration Guide</a>*.</blockquote>
         */
        card_request: components["schemas"]["card"] & {
            /** @description The PayPal-generated ID for the saved card payment source. Typically stored on the merchant's server. */
            vault_id?: components["schemas"]["vault_id"];
            stored_credential?: components["schemas"]["card_stored_credential"];
            /** @description A 3rd party network token refers to a network token that the merchant provisions from and vaults with an external TSP (Token Service Provider) other than PayPal. */
            network_token?: components["schemas"]["network_token_request"];
            experience_context?: components["schemas"]["card_experience_context"];
        };
        /**
         * Token
         * @description The tokenized payment source to fund a payment.
         */
        token: {
            /** @description The PayPal-generated ID for the token. */
            id: string;
            /**
             * @description The tokenization method that generated the ID.
             * @enum {string}
             */
            type: "BILLING_AGREEMENT";
        };
        /**
         * Name
         * @description The name of the party.
         */
        "name-2": {
            /** @description The prefix, or title, to the party's name. */
            prefix?: string;
            /** @description When the party is a person, the party's given, or first, name. */
            given_name?: string;
            /** @description When the party is a person, the party's surname or family name. Also known as the last name. Required when the party is a person. Use also to store multiple surnames including the matronymic, or mother's, surname. */
            surname?: string;
            /** @description When the party is a person, the party's middle name. Use also to store multiple middle names including the patronymic, or father's, middle name. */
            middle_name?: string;
            /** @description The suffix for the party's name. */
            suffix?: string;
            /** @description When the party is a person, the party's full name. */
            full_name?: string;
        };
        /**
         * Format: ppaas_common_country_code_v2
         * @description The [2-character ISO 3166-1 code](/api/rest/reference/country-codes/) that identifies the country or region.<blockquote><strong>Note:</strong> The country code for Great Britain is <code>GB</code> and not <code>UK</code> as used in the top-level domain names for that country. Use the `C2` country code for China worldwide for comparable uncontrolled price (CUP) method, bank card, and cross-border transactions.</blockquote>
         */
        "country_code-2": string;
        /**
         * Portable Postal Address (Medium-Grained)
         * @description The portable international postal address. Maps to [AddressValidationMetadata](https://github.com/googlei18n/libaddressinput/wiki/AddressValidationMetadata) and HTML 5.1 [Autofilling form controls: the autocomplete attribute](https://www.w3.org/TR/html51/sec-forms.html#autofilling-form-controls-the-autocomplete-attribute).
         */
        "address_portable-2": {
            /** @description The first line of the address, such as number and street, for example, `173 Drury Lane`. Needed for data entry, and Compliance and Risk checks. This field needs to pass the full address. */
            address_line_1?: string;
            /** @description The second line of the address, for example, a suite or apartment number. */
            address_line_2?: string;
            /** @description The third line of the address, if needed. Examples include a street complement for Brazil, direction text, such as `next to Walmart`, or a landmark in an Indian address. */
            address_line_3?: string;
            /** @description The neighborhood, ward, or district. This is smaller than `admin_area_level_3` or `sub_locality`. Value is:<ul><li>The postal sorting code that is used in Guernsey and many French territories, such as French Guiana.</li><li>The fine-grained administrative levels in China.</li></ul> */
            admin_area_4?: string;
            /** @description The sub-locality, suburb, neighborhood, or district. This is smaller than `admin_area_level_2`. Value is:<ul><li>Brazil. Suburb, *bairro*, or neighborhood.</li><li>India. Sub-locality or district. Street name information isn't always available, but a sub-locality or district can be a very small area.</li></ul> */
            admin_area_3?: string;
            /** @description A city, town, or village. Smaller than `admin_area_level_1`. */
            admin_area_2?: string;
            /** @description The highest-level sub-division in a country, which is usually a province, state, or ISO-3166-2 subdivision. This data is formatted for postal delivery, for example, `CA` and not `California`. Value, by country, is:<ul><li>UK. A county.</li><li>US. A state.</li><li>Canada. A province.</li><li>Japan. A prefecture.</li><li>Switzerland. A *kanton*.</li></ul> */
            admin_area_1?: string;
            /** @description The postal code, which is the ZIP code or equivalent. Typically required for countries with a postal code or an equivalent. See [postal code](https://en.wikipedia.org/wiki/Postal_code). */
            postal_code?: string;
            country_code: components["schemas"]["country_code-2"];
            /**
             * Address Details
             * @description The non-portable additional address details include fine-grain address information for Compliance, Risk, and other scenarios. This isn't portable with common third-party and open source applications. This can include data that is redundant with core fields. For example, `address_portable.address_line_1` is usually a combination of `address_details.street_number`, `street_name`, and `street_type`.
             */
            address_details?: {
                /** @description The street number. */
                street_number?: string;
                /** @description The street name. Just `Drury` in `Drury Lane`. */
                street_name?: string;
                /** @description The street type. For example, avenue, boulevard, road, or expressway. */
                street_type?: string;
                /** @description The delivery service. Post office box, bag number, or post office name. */
                delivery_service?: string;
                /** @description A named locations that represents the premise. Usually a building name or number or collection of buildings with a common name or number. For example, <code>Craven House</code>. */
                building_name?: string;
                /** @description The first-order entity below a named building or location that represents the sub-premise. Usually a single building within a collection of buildings with a common name. Can be a flat, story, floor, room, or apartment. */
                sub_building?: string;
            };
        };
        /**
         * Vaulted PayPal Wallet Common Attributes
         * @description Resource consolidating common request and response attributes for vaulting PayPal Wallet.
         */
        vault_paypal_wallet_base: WithRequired<
            components["schemas"]["vault_instruction_base"] & {
                /** @description The description displayed to PayPal consumer on the approval flow for PayPal, as well as on the PayPal payment token management experience on PayPal.com. */
                description?: string;
                /**
                 * @description Expected business/pricing model for the billing agreement.
                 * @enum {string}
                 */
                usage_pattern?:
                    | "IMMEDIATE"
                    | "DEFERRED"
                    | "RECURRING_PREPAID"
                    | "RECURRING_POSTPAID"
                    | "THRESHOLD_PREPAID"
                    | "THRESHOLD_POSTPAID";
                /** @description The shipping address for the Payer. */
                shipping?: components["schemas"]["shipping_detail"];
                /**
                 * @description The usage type associated with the PayPal payment token.
                 * @enum {string}
                 */
                usage_type?: "MERCHANT" | "PLATFORM";
                /**
                 * @description The customer type associated with the PayPal payment token. This is to indicate whether the customer acting on the merchant / platform is either a business or a consumer.
                 * @default CONSUMER
                 * @enum {string}
                 */
                customer_type?: "CONSUMER" | "BUSINESS";
                /**
                 * @description Create multiple payment tokens for the same payer, merchant/platform combination. Use this when the customer has not logged in at merchant/platform. The payment token thus generated, can then also be used to create the customer account at merchant/platform. Use this also when multiple payment tokens are required for the same payer, different customer at merchant/platform. This helps to identify customers distinctly even though they may share the same PayPal account. This only applies to PayPal payment source.
                 * @default false
                 */
                permit_multiple_payment_tokens?: boolean;
            },
            "usage_type"
        >;
        /**
         * PayPal Wallet Attributes
         * @description Additional attributes associated with the use of this PayPal Wallet.
         */
        paypal_wallet_attributes: {
            customer?: components["schemas"]["customer"];
            /** @description Attributes used to provide the instructions during vaulting of the PayPal Wallet. */
            vault?: components["schemas"]["vault_paypal_wallet_base"];
        };
        /**
         * Format: ppaas_common_language_v3
         * @description The [language tag](https://tools.ietf.org/html/bcp47#section-2) for the language in which to localize the error-related strings, such as messages, issues, and suggested actions. The tag is made up of the [ISO 639-2 language code](https://www.loc.gov/standards/iso639-2/php/code_list.php), the optional [ISO-15924 script tag](https://www.unicode.org/iso15924/codelists.html), and the [ISO-3166 alpha-2 country code](/api/rest/reference/country-codes/) or [M49 region code](https://unstats.un.org/unsd/methodology/m49/).
         */
        language: string;
        /**
         * PayPal Wallet Experience Context
         * @description Customizes the payer experience during the approval process for payment with PayPal.<blockquote><strong>Note:</strong> Partners and Marketplaces might configure <code>brand_name</code> and <code>shipping_preference</code> during partner account setup, which overrides the request values.</blockquote>
         */
        paypal_wallet_experience_context: {
            /** @description The label that overrides the business name in the PayPal account on the PayPal site. The pattern is defined by an external party and supports Unicode. */
            brand_name?: string;
            /** @description The BCP 47-formatted locale of pages that the PayPal payment experience shows. PayPal supports a five-character code. For example, `da-DK`, `he-IL`, `id-ID`, `ja-JP`, `no-NO`, `pt-BR`, `ru-RU`, `sv-SE`, `th-TH`, `zh-CN`, `zh-HK`, or `zh-TW`. */
            locale?: components["schemas"]["language"];
            /**
             * @description The location from which the shipping address is derived.
             * @default GET_FROM_FILE
             * @enum {string}
             */
            shipping_preference?:
                | "GET_FROM_FILE"
                | "NO_SHIPPING"
                | "SET_PROVIDED_ADDRESS";
            /**
             * Format: uri
             * @description The URL where the customer will be redirected upon approving a payment.
             */
            return_url?: components["schemas"]["url"];
            /**
             * Format: uri
             * @description The URL where the customer will be redirected upon cancelling the payment approval.
             */
            cancel_url?: components["schemas"]["url"];
            /**
             * @description The type of landing page to show on the PayPal site for customer checkout.
             * @default NO_PREFERENCE
             * @enum {string}
             */
            landing_page?: "LOGIN" | "GUEST_CHECKOUT" | "NO_PREFERENCE";
            /**
             * @description Configures a <strong>Continue</strong> or <strong>Pay Now</strong> checkout flow.
             * @default CONTINUE
             * @enum {string}
             */
            user_action?: "CONTINUE" | "PAY_NOW";
            /**
             * @description The merchant-preferred payment methods.
             * @default UNRESTRICTED
             * @enum {string}
             */
            payment_method_preference?:
                | "UNRESTRICTED"
                | "IMMEDIATE_PAYMENT_REQUIRED";
        };
        /** @description The PayPal billing agreement ID. References an approved recurring payment for goods or services. */
        billing_agreement_id: string;
        /**
         * PayPal Wallet
         * @description A resource that identifies a PayPal Wallet is used for payment.
         */
        paypal_wallet: {
            /** @description The PayPal-generated ID for the payment_source stored within the Vault. */
            vault_id?: components["schemas"]["vault_id"];
            /** @description The email address of the PayPal account holder. */
            email_address?: components["schemas"]["email"];
            /** @description The name of the PayPal account holder. Supports only the `given_name` and `surname` properties. */
            name?: components["schemas"]["name-2"];
            /** @description The phone number of the customer. Available only when you enable the **Contact Telephone Number** option in the <a href="https://www.paypal.com/cgi-bin/customerprofileweb?cmd=_profile-website-payments">**Profile & Settings**</a> for the merchant's PayPal account. The `phone.phone_number` supports only `national_number`. */
            phone?: components["schemas"]["phone_with_type"];
            /** @description The birth date of the PayPal account holder in `YYYY-MM-DD` format. */
            birth_date?: components["schemas"]["date_no_time"];
            /** @description The tax information of the PayPal account holder. Required only for Brazilian PayPal account holder's. Both `tax_id` and `tax_id_type` are required. */
            tax_info?: components["schemas"]["tax_info"];
            /** @description The address of the PayPal account holder. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. Also referred to as the billing address of the customer. */
            address?: components["schemas"]["address_portable-2"];
            /** @description Additional attributes associated with the use of this wallet. */
            attributes?: components["schemas"]["paypal_wallet_attributes"];
            experience_context?: components["schemas"]["paypal_wallet_experience_context"];
            billing_agreement_id?: components["schemas"]["billing_agreement_id"];
        };
        /** @description The full name representation like Mr J Smith. */
        full_name: string;
        /**
         * Experience Context
         * @description Customizes the payer experience during the approval process for the payment.
         */
        experience_context_base: {
            /** @description The label that overrides the business name in the PayPal account on the PayPal site. The pattern is defined by an external party and supports Unicode. */
            brand_name?: string;
            /** @description The BCP 47-formatted locale of pages that the PayPal payment experience shows. PayPal supports a five-character code. For example, `da-DK`, `he-IL`, `id-ID`, `ja-JP`, `no-NO`, `pt-BR`, `ru-RU`, `sv-SE`, `th-TH`, `zh-CN`, `zh-HK`, or `zh-TW`. */
            locale?: components["schemas"]["language"];
            /**
             * @description The location from which the shipping address is derived.
             * @default GET_FROM_FILE
             * @enum {string}
             */
            shipping_preference?:
                | "GET_FROM_FILE"
                | "NO_SHIPPING"
                | "SET_PROVIDED_ADDRESS";
            /**
             * Format: uri
             * @description The URL where the customer is redirected after the customer approves the payment.
             */
            return_url?: components["schemas"]["url"];
            /**
             * Format: uri
             * @description The URL where the customer is redirected after the customer cancels the payment.
             */
            cancel_url?: components["schemas"]["url"];
        };
        altpay_recurring_attributes_request: unknown;
        /**
         * Bancontact payment object
         * @description Information needed to pay using Bancontact.
         */
        bancontact_request: {
            /** @description The name of the account holder associated with this payment method. */
            name: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code: components["schemas"]["country_code"];
            /** @description Customizes the payer experience during the approval process for the payment. */
            experience_context?: components["schemas"]["experience_context_base"];
            /** @description Attributes for altpay recurring. */
            attributes?: components["schemas"]["altpay_recurring_attributes_request"];
        };
        /**
         * Format: ppaas_common_email_address_v2
         * @description The internationalized email address.<blockquote><strong>Note:</strong> Up to 64 characters are allowed before and 255 characters are allowed after the <code>@</code> sign. However, the generally accepted maximum length for an email address is 254 characters. The pattern verifies that an unquoted <code>@</code> sign exists.</blockquote>
         */
        email_address: string;
        /**
         * IP Address
         * Format: ppaas_ip_address_v1
         * @description An Internet Protocol address (IP address). This address assigns a numerical label to each device that is connected to a computer network through the Internet Protocol. Supports IPv4 and IPv6 addresses.
         */
        ip_address: string;
        /**
         * BLIK Experience Context
         * @description Customizes the payer experience during the approval process for the BLIK payment.
         */
        blik_experience_context: components["schemas"]["experience_context_base"] & {
            /** @description The IP address of the consumer. It could be either IPv4 or IPv6. */
            consumer_ip?: components["schemas"]["ip_address"];
            /** @description The payer's User Agent. For example, Mozilla/5.0 (Macintosh; Intel Mac OS X x.y; rv:42.0). */
            consumer_user_agent?: string;
        };
        /**
         * BLIK level_0 payment object
         * @description Information used to pay using BLIK level_0 flow.
         */
        blik_seamless: {
            /** @description 6-digit code used to authenticate a consumer within BLIK. */
            auth_code: string;
        };
        /**
         * BLIK one-click payment object
         * @description Information used to pay using BLIK one-click flow.
         */
        blik_one_click: {
            /** @description 6-digit code used to authenticate a consumer within BLIK. */
            auth_code?: string;
            /** @description The merchant generated, unique reference serving as a primary identifier for accounts connected between Blik and a merchant. */
            consumer_reference: string;
            /** @description A bank defined identifier used as a display name to allow the payer to differentiate between multiple registered bank accounts. */
            alias_label?: string;
            /** @description A Blik-defined identifier for a specific Blik-enabled bank account that is associated with a given merchant. Used only in conjunction with a Consumer Reference. */
            alias_key?: string;
        };
        /**
         * BLIK payment object
         * @description Information needed to pay using BLIK.
         */
        blik_request: {
            /** @description The name of the account holder associated with this payment method. */
            name: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code: components["schemas"]["country_code"];
            /** @description The email address of the account holder associated with this payment method. */
            email?: components["schemas"]["email_address"];
            /** @description Customizes the payer experience during the approval process for the payment. */
            experience_context?: components["schemas"]["blik_experience_context"];
            /** @description The level_0 integration flow object. */
            level_0?: components["schemas"]["blik_seamless"];
            /** @description The one-click integration flow object. */
            one_click?: components["schemas"]["blik_one_click"];
        };
        /**
         * An eps payment object
         * @description Information needed to pay using eps.
         */
        eps_request: {
            /** @description The name of the account holder associated with this payment method. */
            name: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code: components["schemas"]["country_code"];
            /** @description Customizes the payer experience during the approval process for the payment. */
            experience_context?: components["schemas"]["experience_context_base"];
        };
        /**
         * A giropay payment object
         * @description Information needed to pay using giropay.
         */
        giropay_request: {
            /** @description The name of the account holder associated with this payment method. */
            name: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code: components["schemas"]["country_code"];
            /** @description Customizes the payer experience during the approval process for the payment. */
            experience_context?: components["schemas"]["experience_context_base"];
        };
        /**
         * BIC
         * @description The business identification code (BIC). In payments systems, a BIC is used to identify a specific business, most commonly a bank.
         */
        bic: string;
        /**
         * The iDEAL payment object
         * @description Information needed to pay using iDEAL.
         */
        ideal_request: {
            /** @description The name of the account holder associated with this payment method. */
            name: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code: components["schemas"]["country_code"];
            /** @description The bank identification code (BIC). */
            bic?: components["schemas"]["bic"];
            /** @description Customizes the payer experience during the approval process for the payment. */
            experience_context?: components["schemas"]["experience_context_base"];
            /** @description Attributes for altpay recurring. */
            attributes?: components["schemas"]["altpay_recurring_attributes_request"];
        };
        /**
         * MyBank payment object
         * @description Information needed to pay using MyBank.
         */
        mybank_request: {
            /** @description The name of the account holder associated with this payment method. */
            name: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code: components["schemas"]["country_code"];
            /** @description Customizes the payer experience during the approval process for the payment. */
            experience_context?: components["schemas"]["experience_context_base"];
        };
        /**
         * P24 payment object
         * @description Information needed to pay using P24 (Przelewy24).
         */
        p24_request: {
            /** @description The name of the account holder associated with this payment method. */
            name: components["schemas"]["full_name"];
            /** @description The email address of the account holder associated with this payment method. */
            email: components["schemas"]["email_address"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code: components["schemas"]["country_code"];
            /** @description Customizes the payer experience during the approval process for the payment. */
            experience_context?: components["schemas"]["experience_context_base"];
        };
        /**
         * Sofort payment object
         * @description Information needed to pay using Sofort.
         */
        sofort_request: {
            /** @description The name of the account holder associated with this payment method. */
            name: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code: components["schemas"]["country_code"];
            /** @description Customizes the payer experience during the approval process for the payment. */
            experience_context?: components["schemas"]["experience_context_base"];
        };
        /**
         * Trustly payment object
         * @description Information needed to pay using Trustly.
         */
        trustly_request: {
            /** @description The name of the account holder associated with this payment method. */
            name: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code: components["schemas"]["country_code"];
            /** @description Customizes the payer experience during the approval process for the payment. */
            experience_context?: components["schemas"]["experience_context_base"];
        };
        /**
         * Format: ppaas_common_currency_code_v2
         * @description The [3-character ISO-4217 currency code](/api/rest/reference/currency-codes/) that identifies the currency.
         */
        "currency_code-2": string;
        /**
         * Money
         * @description The currency and amount for a financial transaction, such as a balance or payment due.
         */
        "money-2": {
            currency_code: components["schemas"]["currency_code-2"];
            /** @description The value, which might be:<ul><li>An integer for currencies like `JPY` that are not typically fractional.</li><li>A decimal fraction for currencies like `TND` that are subdivided into thousandths.</li></ul>For the required number of decimal places for a currency code, see [Currency Codes](/api/rest/reference/currency-codes/). */
            value: string;
        };
        /**
         * Decrypted Apple Pay Payment details data.
         * @description Information about the decrypted apple pay payment data for the token like cryptogram, eci indicator.
         */
        apple_pay_payment_data: {
            /** @description Online payment cryptogram, as defined by 3D Secure. The pattern is defined by an external party and supports Unicode. */
            cryptogram?: string;
            /** @description ECI indicator, as defined by 3- Secure. The pattern is defined by an external party and supports Unicode. */
            eci_indicator?: string;
            /** @description Encoded Apple Pay EMV Payment Structure used for payments in China. The pattern is defined by an external party and supports Unicode. */
            emv_data?: string;
            /** @description Bank Key encrypted Apple Pay PIN. The pattern is defined by an external party and supports Unicode. */
            pin?: string;
        };
        /**
         * Decrypted Apple Pay Token data.
         * @description Information about the Payment data obtained by decrypting Apple Pay token.
         */
        apple_pay_decrypted_token_data: {
            /** @description The transaction amount for the payment that the payer has approved on apple platform. */
            transaction_amount?: components["schemas"]["money-2"];
            /** @description Apple Pay tokenized credit card used to pay. */
            tokenized_card: components["schemas"]["card"];
            /** @description Apple Pay Hex-encoded device manufacturer identifier. The pattern is defined by an external party and supports Unicode. */
            device_manufacturer_id?: string;
            /**
             * @description Indicates the type of payment data passed, in case of Non China the payment data is 3DSECURE and for China it is EMV.
             * @enum {string}
             */
            payment_data_type?: "3DSECURE" | "EMV";
            /** @description Apple Pay payment data object which contains the cryptogram, eci_indicator and other data. */
            payment_data?: components["schemas"]["apple_pay_payment_data"];
        };
        apple_pay_attributes: unknown;
        /**
         * ApplePay payment request object
         * @description Information needed to pay using ApplePay.
         */
        apple_pay_request: {
            /** @description ApplePay transaction identifier, this will be the unique identifier for this transaction provided by Apple. The pattern is defined by an external party and supports Unicode. */
            id?: string;
            /** @description Name on the account holder associated with apple pay. */
            name?: components["schemas"]["full_name"];
            /** @description The email address of the account holder associated with apple pay. */
            email_address?: components["schemas"]["email_address"];
            /** @description The phone number, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). Supports only the `national_number` property. */
            phone_number?: components["schemas"]["phone"];
            /** @description The decrypted payload details for the apple pay token. */
            decrypted_token?: components["schemas"]["apple_pay_decrypted_token_data"];
            stored_credential?: components["schemas"]["card_stored_credential"];
            /** @description The PayPal-generated ID for the saved apple pay payment_source. This ID should be stored on the merchant's server so the saved payment source can be used for future transactions. */
            vault_id?: components["schemas"]["vault_id"];
            attributes?: components["schemas"]["apple_pay_attributes"];
        };
        google_pay_request: unknown;
        /**
         * Venmo Wallet Experience Context
         * @description Customizes the buyer experience during the approval process for payment with Venmo.<blockquote><strong>Note:</strong> Partners and Marketplaces might configure <code>shipping_preference</code> during partner account setup, which overrides the request values.</blockquote>
         */
        venmo_wallet_experience_context: {
            /** @description The business name of the merchant. The pattern is defined by an external party and supports Unicode. */
            brand_name?: string;
            /**
             * @description The location from which the shipping address is derived.
             * @default GET_FROM_FILE
             * @enum {string}
             */
            shipping_preference?:
                | "GET_FROM_FILE"
                | "NO_SHIPPING"
                | "SET_PROVIDED_ADDRESS";
        };
        /**
         * Base Vault Instruction Parameters
         * @description Base vaulting specification. The object can be extended for specific use cases within each payment_source that supports vaulting.
         */
        v3_vault_instruction_base: {
            store_in_vault: components["schemas"]["store_in_vault_instruction"];
        };
        /**
         * Vaulted Venmo Wallet Common Attributes
         * @description Resource consolidating common request and response attirbutes for vaulting Venmo Wallet.
         */
        vault_venmo_wallet_base: WithRequired<
            components["schemas"]["v3_vault_instruction_base"] & {
                /** @description The description displayed to Venmo consumer on the approval flow for Venmo, as well as on the Venmo payment token management experience on Venmo.com. */
                description?: string;
                /**
                 * @description Expected business/pricing model for the billing agreement.
                 * @enum {string}
                 */
                usage_pattern?:
                    | "IMMEDIATE"
                    | "DEFERRED"
                    | "RECURRING_PREPAID"
                    | "RECURRING_POSTPAID"
                    | "THRESHOLD_PREPAID"
                    | "THRESHOLD_POSTPAID";
                /**
                 * @description The usage type associated with the Venmo payment token.
                 * @enum {string}
                 */
                usage_type?: "MERCHANT" | "PLATFORM";
                /**
                 * @description The customer type associated with the Venmo payment token. This is to indicate whether the customer acting on the merchant / platform is either a business or a consumer.
                 * @default CONSUMER
                 * @enum {string}
                 */
                customer_type?: "CONSUMER" | "BUSINESS";
                /**
                 * @description Create multiple payment tokens for the same payer, merchant/platform combination. Use this when the customer has not logged in at merchant/platform. The payment token thus generated, can then also be used to create the customer account at merchant/platform. Use this also when multiple payment tokens are required for the same payer, different customer at merchant/platform. This helps to identify customers distinctly even though they may share the same Venmo account.
                 * @default false
                 */
                permit_multiple_payment_tokens?: boolean;
            },
            "usage_type"
        >;
        /**
         * Venmo Wallet Attributes
         * @description Additional attributes associated with the use of this Venmo Wallet.
         */
        venmo_wallet_attributes: {
            customer?: components["schemas"]["customer"];
            /** @description Attributes used to provide the instructions during vaulting of the Venmo Wallet. */
            vault?: components["schemas"]["vault_venmo_wallet_base"];
        };
        /**
         * Venmo payment request object
         * @description Information needed to pay using Venmo.
         */
        venmo_wallet_request: {
            /** @description The PayPal-generated ID for the saved Venmo wallet payment_source. This ID should be stored on the merchant's server so the saved payment source can be used for future transactions. */
            vault_id?: components["schemas"]["vault_id"];
            /** @description The email address of the payer. */
            email_address?: components["schemas"]["email"];
            experience_context?: components["schemas"]["venmo_wallet_experience_context"];
            /** @description Additional attributes associated with the use of this wallet. */
            attributes?: components["schemas"]["venmo_wallet_attributes"];
        };
        /**
         * Payment Source
         * @description The payment source definition.
         */
        payment_source: {
            card?: components["schemas"]["card_request"];
            token?: components["schemas"]["token"];
            /** @description Indicates that PayPal Wallet is the payment source. Main use of this selection is to provide additional instructions associated with this choice like vaulting. */
            paypal?: components["schemas"]["paypal_wallet"];
            /** @description Bancontact is the most popular online payment in Belgium. [More Details](https://www.bancontact.com/). */
            bancontact?: components["schemas"]["bancontact_request"];
            /** @description BLIK is a mobile payment system, created by Polish Payment Standard in order to allow millions of users to pay in shops, payout cash in ATMs and make online purchases and payments. [More Details](https://blikmobile.pl/). */
            blik?: components["schemas"]["blik_request"];
            /** @description The eps transfer is an online payment method developed by many Austrian banks. [More Details](https://www.eps-ueberweisung.at/). */
            eps?: components["schemas"]["eps_request"];
            /** @description Giropay is an Internet payment System in Germany, based on online banking. [More Details](https://giropay.de/). */
            giropay?: components["schemas"]["giropay_request"];
            /** @description The Dutch payment method iDEAL is an online payment method that enables consumers to pay online through their own bank. [More Details](https://www.ideal.nl/). */
            ideal?: components["schemas"]["ideal_request"];
            /** @description MyBank is an e-authorisation solution which enables safe digital payments and identity authentication through a consumer’s own online banking portal or mobile application. [More Details](https://www.mybank.eu/). */
            mybank?: components["schemas"]["mybank_request"];
            /** @description P24 (Przelewy24) is a secure and fast online bank transfer service linked to all the major banks in Poland. [More Details](https://www.przelewy24.pl/). */
            p24?: components["schemas"]["p24_request"];
            /** @description SOFORT Banking is a real-time bank transfer payment method that buyers use to transfer funds directly to merchants from their bank accounts. [More Details](https://www.klarna.com/sofort/). */
            sofort?: components["schemas"]["sofort_request"];
            /** @description Trustly is a payment method that allows customers to shop and pay from their bank account. [More Details](https://www.trustly.net/). */
            trustly?: components["schemas"]["trustly_request"];
            /** @description ApplePay payment source, allows buyer to pay using ApplePay, both on Web as well as on Native. */
            apple_pay?: components["schemas"]["apple_pay_request"];
            /** @description Google Pay payment source, allows buyer to pay using Google Pay. */
            google_pay?: components["schemas"]["google_pay_request"];
            /** @description Information needed to indicate that Venmo is being used to fund the payment. */
            venmo?: components["schemas"]["venmo_wallet_request"];
        };
        /**
         * @description The merchant-preferred payment methods.
         * @default UNRESTRICTED
         * @enum {string}
         */
        payee_payment_method_preference:
            | "UNRESTRICTED"
            | "IMMEDIATE_PAYMENT_REQUIRED";
        /**
         * Payment Method
         * @description The customer and merchant payment preferences.
         */
        payment_method: {
            payee_preferred?: components["schemas"]["payee_payment_method_preference"];
            /**
             * @description NACHA (the regulatory body governing the ACH network) requires that API callers (merchants, partners) obtain the consumer’s explicit authorization before initiating a transaction. To stay compliant, you’ll need to make sure that you retain a compliant authorization for each transaction that you originate to the ACH Network using this API. ACH transactions are categorized (using SEC codes) by how you capture authorization from the Receiver (the person whose bank account is being debited or credited). PayPal supports the following SEC codes.
             * @default WEB
             * @enum {string}
             */
            standard_entry_class_code?: "TEL" | "WEB" | "CCD" | "PPD";
        };
        /**
         * Stored Payment Source
         * @description Provides additional details to process a payment using a `payment_source` that has been stored or is intended to be stored (also referred to as stored_credential or card-on-file).<br/>Parameter compatibility:<br/><ul><li>`payment_type=ONE_TIME` is compatible only with `payment_initiator=CUSTOMER`.</li><li>`usage=FIRST` is compatible only with `payment_initiator=CUSTOMER`.</li><li>`previous_transaction_reference` or `previous_network_transaction_reference` is compatible only with `payment_initiator=MERCHANT`.</li><li>Only one of the parameters - `previous_transaction_reference` and `previous_network_transaction_reference` - can be present in the request.</li></ul>
         */
        stored_payment_source: {
            payment_initiator: components["schemas"]["payment_initiator"];
            payment_type: components["schemas"]["stored_payment_source_payment_type"];
            usage?: components["schemas"]["stored_payment_source_usage_type"];
            previous_network_transaction_reference?: components["schemas"]["network_transaction_reference"];
        };
        /**
         * Application Context
         * @description Customizes the payer experience during the approval process for the payment with PayPal.<blockquote><strong>Note:</strong> Partners and Marketplaces might configure <code>brand_name</code> and <code>shipping_preference</code> during partner account setup, which overrides the request values.</blockquote>
         */
        order_application_context: {
            /** @description DEPRECATED. The label that overrides the business name in the PayPal account on the PayPal site. The fields in `application_context` are now available in the `experience_context` object under the `payment_source` which supports them (eg. `payment_source.paypal.experience_context.brand_name`). Please specify this field in the `experience_context` object instead of the `application_context` object. */
            brand_name?: string;
            /** @description DEPRECATED. The BCP 47-formatted locale of pages that the PayPal payment experience shows. PayPal supports a five-character code. For example, `da-DK`, `he-IL`, `id-ID`, `ja-JP`, `no-NO`, `pt-BR`, `ru-RU`, `sv-SE`, `th-TH`, `zh-CN`, `zh-HK`, or `zh-TW`.  The fields in `application_context` are now available in the `experience_context` object under the `payment_source` which supports them (eg. `payment_source.paypal.experience_context.locale`). Please specify this field in the `experience_context` object instead of the `application_context` object. */
            locale?: components["schemas"]["language"];
            /**
             * @deprecated
             * @description DEPRECATED. DEPRECATED. The type of landing page to show on the PayPal site for customer checkout.  The fields in `application_context` are now available in the `experience_context` object under the `payment_source` which supports them (eg. `payment_source.paypal.experience_context.landing_page`). Please specify this field in the `experience_context` object instead of the `application_context` object.
             * @default NO_PREFERENCE
             * @enum {string}
             */
            landing_page?: "LOGIN" | "BILLING" | "NO_PREFERENCE";
            /**
             * @deprecated
             * @description DEPRECATED. DEPRECATED. The shipping preference:<ul><li>Displays the shipping address to the customer.</li><li>Enables the customer to choose an address on the PayPal site.</li><li>Restricts the customer from changing the address during the payment-approval process.</li></ul>.  The fields in `application_context` are now available in the `experience_context` object under the `payment_source` which supports them (eg. `payment_source.paypal.experience_context.shipping_preference`). Please specify this field in the `experience_context` object instead of the `application_context` object.
             * @default GET_FROM_FILE
             * @enum {string}
             */
            shipping_preference?:
                | "GET_FROM_FILE"
                | "NO_SHIPPING"
                | "SET_PROVIDED_ADDRESS";
            /**
             * @description DEPRECATED. Configures a <strong>Continue</strong> or <strong>Pay Now</strong> checkout flow.  The fields in `application_context` are now available in the `experience_context` object under the `payment_source` which supports them (eg. `payment_source.paypal.experience_context.user_action`). Please specify this field in the `experience_context` object instead of the `application_context` object.
             * @default CONTINUE
             * @enum {string}
             */
            user_action?: "CONTINUE" | "PAY_NOW";
            /** @description DEPRECATED. The customer and merchant payment preferences. The fields in `application_context` are now available in the `experience_context` object under the `payment_source` which supports them (eg. `payment_source.paypal.experience_context.payment_method_selected`). Please specify this field in the `experience_context` object instead of the `application_context` object.. */
            payment_method?: components["schemas"]["payment_method"];
            /**
             * Format: uri
             * @description DEPRECATED. The URL where the customer is redirected after the customer approves the payment. The fields in `application_context` are now available in the `experience_context` object under the `payment_source` which supports them (eg. `payment_source.paypal.experience_context.return_url`). Please specify this field in the `experience_context` object instead of the `application_context` object.
             */
            return_url?: string;
            /**
             * Format: uri
             * @description DEPRECATED. The URL where the customer is redirected after the customer cancels the payment. The fields in `application_context` are now available in the `experience_context` object under the `payment_source` which supports them (eg. `payment_source.paypal.experience_context.cancel_url`). Please specify this field in the `experience_context` object instead of the `application_context` object.
             */
            cancel_url?: string;
            /**
             * @deprecated
             * @description DEPRECATED. Provides additional details to process a payment using a `payment_source` that has been stored or is intended to be stored (also referred to as stored_credential or card-on-file).<br/>Parameter compatibility:<br/><ul><li>`payment_type=ONE_TIME` is compatible only with `payment_initiator=CUSTOMER`.</li><li>`usage=FIRST` is compatible only with `payment_initiator=CUSTOMER`.</li><li>`previous_transaction_reference` or `previous_network_transaction_reference` is compatible only with `payment_initiator=MERCHANT`.</li><li>Only one of the parameters - `previous_transaction_reference` and `previous_network_transaction_reference` - can be present in the request.</li></ul>.  The fields in `stored_payment_source` are now available in the `stored_credential` object under the `payment_source` which supports them (eg. `payment_source.card.stored_credential.payment_initiator`). Please specify this field in the `payment_source` object instead of the `application_context` object.
             */
            stored_payment_source?: components["schemas"]["stored_payment_source"];
        };
        /**
         * Order Request
         * @description The order request details.
         */
        order_request: {
            intent: components["schemas"]["checkout_payment_intent"];
            /**
             * @deprecated
             * @description DEPRECATED. The customer is also known as the payer. The Payer object was intended to only be used with the `payment_source.paypal` object. In order to make this design more clear, the details in the `payer` object are now available under `payment_source.paypal`. Please use `payment_source.paypal`.
             */
            payer?: components["schemas"]["payer"];
            /** @description An array of purchase units. Each purchase unit establishes a contract between a payer and the payee. Each purchase unit represents either a full or partial order that the payer intends to purchase from the payee. */
            purchase_units: components["schemas"]["purchase_unit_request"][];
            payment_source?: components["schemas"]["payment_source"];
            /** @description Customize the payer experience during the approval process for the payment with PayPal. */
            application_context?: components["schemas"]["order_application_context"];
        };
        /**
         * Format: ppaas_date_time_v3
         * @description The date and time, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). Seconds are required while fractional seconds are optional.<blockquote><strong>Note:</strong> The regular expression provides guidance but does not reject all invalid dates.</blockquote>
         */
        date_time: string;
        /**
         * Transaction Date and Time Stamps
         * @description The date and time stamps that are common to authorized payment, captured payment, and refund transactions.
         */
        activity_timestamps: {
            /** @description The date and time when the transaction occurred, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            create_time?: components["schemas"]["date_time"];
            /** @description The date and time when the transaction was last updated, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            update_time?: components["schemas"]["date_time"];
        };
        /**
         * @description Liability shift indicator. The outcome of the issuer's authentication.
         * @enum {string}
         */
        liability_shift: "NO" | "POSSIBLE" | "UNKNOWN";
        /**
         * @description Transactions status result identifier. The outcome of the issuer's authentication.
         * @enum {string}
         */
        pares_status: "Y" | "N" | "U" | "A" | "C" | "R" | "D" | "I";
        /**
         * @description Status of Authentication eligibility.
         * @enum {string}
         */
        enrolled: "Y" | "N" | "U" | "B";
        /**
         * The 3D Secure Authentication Response
         * @description Results of 3D Secure Authentication.
         */
        three_d_secure_authentication_response: {
            /** @description The outcome of the issuer's authentication. */
            authentication_status?: components["schemas"]["pares_status"];
            /** @description Status of authentication eligibility. */
            enrollment_status?: components["schemas"]["enrolled"];
        };
        authentication_flow: unknown;
        exemption_details: unknown;
        /**
         * Authentication Response
         * @description Results of Authentication such as 3D Secure.
         */
        authentication_response: {
            liability_shift?: components["schemas"]["liability_shift"];
            three_d_secure?: components["schemas"]["three_d_secure_authentication_response"];
            authentication_flow?: components["schemas"]["authentication_flow"];
            /** @description Exemption details of 3D Secure Authentication. */
            exemption_details?: components["schemas"]["exemption_details"];
        };
        /**
         * Link Description
         * @description The request-related [HATEOAS link](/api/rest/responses/#hateoas-links) information.
         */
        link_description: {
            /** @description The complete target URL. To make the related call, combine the method with this [URI Template-formatted](https://tools.ietf.org/html/rfc6570) link. For pre-processing, include the `$`, `(`, and `)` characters. The `href` is the key HATEOAS component that links a completed call with a subsequent call. */
            href: string;
            /** @description The [link relation type](https://tools.ietf.org/html/rfc5988#section-4), which serves as an ID for a link that unambiguously describes the semantics of the link. See [Link Relations](https://www.iana.org/assignments/link-relations/link-relations.xhtml). */
            rel: string;
            /**
             * @description The HTTP method required to make the related call.
             * @enum {string}
             */
            method?:
                | "GET"
                | "POST"
                | "PUT"
                | "DELETE"
                | "HEAD"
                | "CONNECT"
                | "OPTIONS"
                | "PATCH";
        };
        /**
         * Saved Payment Source Response
         * @description The details about a saved payment source.
         */
        vault_response: {
            /** @description The PayPal-generated ID for the saved payment source. */
            id?: string;
            /**
             * @deprecated
             * @description The vault status.
             * @enum {string}
             */
            status?: "VAULTED" | "CREATED" | "APPROVED";
            customer?: components["schemas"]["customer"];
            /** @description An array of request-related HATEOAS links. */
            links?: readonly components["schemas"]["link_description"][];
        };
        /**
         * Card Attributes Response
         * @description Additional attributes associated with the use of this card.
         */
        card_attributes_response: {
            vault?: components["schemas"]["vault_response"];
        };
        /**
         * Response of Card from Request
         * @description Representation of card details as received in the request.
         */
        card_from_request: {
            /** @description The card expiration year and month, in [Internet date format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            expiry?: components["schemas"]["date_year_month"];
            /** @description The last digits of the payment card. */
            last_digits?: string;
        };
        /**
         * Bin Details
         * @description Bank Identification Number (BIN) details used to fund a payment.
         */
        bin_details: {
            /** @description The Bank Identification Number (BIN) signifies the number that is being used to identify the granular level details (except the PII information) of the card. */
            bin?: string;
            /** @description The issuer of the card instrument. */
            issuing_bank?: string;
            /** @description The [two-character ISO-3166-1 country code](/docs/integration/direct/rest/country-codes/) of the bank. */
            bin_country_code?: components["schemas"]["country_code"];
            /** @description The type of card product assigned to the BIN by the issuer. These values are defined by the issuer and may change over time. Some examples include: PREPAID_GIFT, CONSUMER, CORPORATE. */
            products?: string[];
        };
        /**
         * Card Response
         * @description The payment card to use to fund a payment. Card can be a credit or debit card.
         */
        card_response: {
            /** @description The card holder's name as it appears on the card. */
            name?: string;
            /** @description The last digits of the payment card. */
            last_digits?: string;
            /** @description The card brand or network. Typically used in the response. */
            brand?: components["schemas"]["card_brand"];
            /** @description Array of brands or networks associated with the card. */
            available_networks?: readonly components["schemas"]["card_brand"][];
            /**
             * @description The payment card type.
             * @enum {string}
             */
            type?: "CREDIT" | "DEBIT" | "PREPAID" | "UNKNOWN";
            authentication_result?: components["schemas"]["authentication_response"];
            attributes?: components["schemas"]["card_attributes_response"];
            from_request?: components["schemas"]["card_from_request"];
            /** @description The card expiration year and month, in [Internet date format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            expiry?: components["schemas"]["date_year_month"];
            /** @description Bank Identification Number (BIN) details used to fund a payment. */
            bin_details?: components["schemas"]["bin_details"];
        };
        /**
         * Format: ppaas_payer_id_v3
         * @description The PayPal payer ID, which is a masked version of the PayPal account number intended for use with third parties. The account number is reversibly encrypted and a proprietary variant of Base32 is used to encode the result.
         */
        "account_id-2": string;
        /**
         * Phone Type
         * @description The phone type.
         * @enum {string}
         */
        "phone_type-2": "FAX" | "HOME" | "MOBILE" | "OTHER" | "PAGER" | "WORK";
        /**
         * Phone
         * @description The phone number in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en).
         */
        "phone-2": {
            /** @description The national number, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). The combined length of the country calling code (CC) and the national number must not be greater than 15 digits. The national number consists of a national destination code (NDC) and subscriber number (SN). */
            national_number: string;
        };
        /**
         * cobranded card object
         * @description Details about the merchant cobranded card used for order purchase.
         */
        cobranded_card: {
            /** @description Array of labels for the cobranded card. */
            labels?: string[];
            /** @description Merchant associated with the purchase. */
            payee?: components["schemas"]["payee_base"];
            /** @description Amount that was charged to the cobranded card. */
            amount?: components["schemas"]["money"];
        };
        /**
         * PayPal Wallet Attributes Response
         * @description Additional attributes associated with the use of a PayPal Wallet.
         */
        paypal_wallet_attributes_response: {
            vault?: components["schemas"]["vault_response"];
            /** @description An array of merchant cobranded cards used by buyer to complete an order. This array will be present if a merchant has onboarded their cobranded card with PayPal and provided corresponding label(s). */
            cobranded_cards?: components["schemas"]["cobranded_card"][];
        };
        /**
         * PayPal Wallet Response
         * @description The PayPal Wallet response.
         */
        paypal_wallet_response: {
            /** @description The email address of the PayPal account holder. */
            email_address?: components["schemas"]["email"];
            /** @description The PayPal-assigned ID for the PayPal account holder. */
            account_id?: components["schemas"]["account_id-2"];
            /** @description The name of the PayPal account holder. Supports only the `given_name` and `surname` properties. */
            name?: components["schemas"]["name-2"];
            phone_type?: components["schemas"]["phone_type-2"];
            /** @description The phone number, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). Available only when you enable the **Contact Telephone Number** option in the <a href="https://www.paypal.com/cgi-bin/customerprofileweb?cmd=_profile-website-payments">**Profile & Settings**</a> for the merchant's PayPal account. Supports only the `national_number` property. */
            phone_number?: components["schemas"]["phone-2"];
            /** @description The birth date of the PayPal account holder in `YYYY-MM-DD` format. */
            birth_date?: components["schemas"]["date_no_time"];
            /** @description The tax information of the PayPal account holder. Required only for Brazilian PayPal account holder's. Both `tax_id` and `tax_id_type` are required. */
            tax_info?: components["schemas"]["tax_info"];
            /** @description The address of the PayPal account holder. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. Also referred to as the billing address of the customer. */
            address?: components["schemas"]["address_portable-2"];
            attributes?: components["schemas"]["paypal_wallet_attributes_response"];
        };
        /** @description The last characters of the IBAN used to pay. */
        iban_last_chars: string;
        altpay_recurring_attributes: unknown;
        /**
         * Bancontact payment object
         * @description Information used to pay Bancontact.
         */
        bancontact: {
            /** @description The name of the account holder associated with this payment method. */
            name?: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code?: components["schemas"]["country_code"];
            /** @description The bank identification code (BIC). */
            bic?: components["schemas"]["bic"];
            iban_last_chars?: components["schemas"]["iban_last_chars"];
            /** @description The last digits of the card used to fund the Bancontact payment. */
            card_last_digits?: string;
            /** @description Attributes for SEPA direct debit object. */
            attributes?: components["schemas"]["altpay_recurring_attributes"];
        };
        /**
         * BLIK one-click payment object
         * @description Information used to pay using BLIK one-click flow.
         */
        blik_one_click_response: {
            /** @description The merchant generated, unique reference serving as a primary identifier for accounts connected between Blik and a merchant. */
            consumer_reference?: string;
        };
        /**
         * BLIK payment object
         * @description Information used to pay using BLIK.
         */
        blik: {
            /** @description The name of the account holder associated with this payment method. */
            name?: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code?: components["schemas"]["country_code"];
            /** @description The email address of the account holder associated with this payment method. */
            email?: components["schemas"]["email_address"];
            /** @description The one-click integration flow object. */
            one_click?: components["schemas"]["blik_one_click_response"];
        };
        /**
         * An eps payment object
         * @description Information used to pay using eps.
         */
        eps: {
            /** @description The name of the account holder associated with this payment method. */
            name?: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code?: components["schemas"]["country_code"];
            /** @description The bank identification code (BIC). */
            bic?: components["schemas"]["bic"];
        };
        /**
         * A giropay payment object
         * @description Information needed to pay using giropay.
         */
        giropay: {
            /** @description The name of the account holder associated with this payment method. */
            name?: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code?: components["schemas"]["country_code"];
            /** @description The bank identification code (BIC). */
            bic?: components["schemas"]["bic"];
        };
        /**
         * The iDEAL payment object
         * @description Information used to pay using iDEAL.
         */
        ideal: {
            /** @description The name of the account holder associated with this payment method. */
            name?: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code?: components["schemas"]["country_code"];
            /** @description The bank identification code (BIC). */
            bic?: components["schemas"]["bic"];
            iban_last_chars?: components["schemas"]["iban_last_chars"];
            /** @description Attributes for SEPA direct debit object. */
            attributes?: components["schemas"]["altpay_recurring_attributes"];
        };
        /**
         * MyBank payment object
         * @description Information used to pay using MyBank.
         */
        mybank: {
            /** @description The name of the account holder associated with this payment method. */
            name?: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code?: components["schemas"]["country_code"];
            /** @description The bank identification code (BIC). */
            bic?: components["schemas"]["bic"];
            iban_last_chars?: components["schemas"]["iban_last_chars"];
        };
        /**
         * P24 payment object
         * @description Information used to pay using P24(Przelewy24).
         */
        p24: {
            /** @description The name of the account holder associated with this payment method. */
            name?: components["schemas"]["full_name"];
            /** @description The email address of the account holder associated with this payment method. */
            email?: components["schemas"]["email_address"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code?: components["schemas"]["country_code"];
            /** @description P24 generated payment description. */
            payment_descriptor?: string;
            /** @description Numeric identifier of the payment scheme or bank used for the payment. */
            method_id?: string;
            /** @description Friendly name of the payment scheme or bank used for the payment. */
            method_description?: string;
        };
        /**
         * Sofort payment object
         * @description Information used to pay using Sofort.
         */
        sofort: {
            /** @description The name of the account holder associated with this payment method. */
            name?: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code?: components["schemas"]["country_code"];
            /** @description The bank identification code (BIC). */
            bic?: components["schemas"]["bic"];
            iban_last_chars?: components["schemas"]["iban_last_chars"];
        };
        /**
         * Trustly payment object
         * @description Information needed to pay using Trustly.
         */
        trustly: {
            /** @description The name of the account holder associated with this payment method. */
            name?: components["schemas"]["full_name"];
            /** @description The two-character ISO 3166-1 country code. */
            country_code?: components["schemas"]["country_code"];
            /** @description The bank identification code (BIC). */
            bic?: components["schemas"]["bic"];
            iban_last_chars?: components["schemas"]["iban_last_chars"];
        };
        /**
         * Venmo Wallet Attributes Response
         * @description Additional attributes associated with the use of a Venmo Wallet.
         */
        venmo_wallet_attributes_response: {
            vault?: components["schemas"]["vault_response"];
        };
        /**
         * Venmo Wallet Response Object
         * @description Venmo wallet response.
         */
        venmo_wallet_response: {
            /** @description The email address of the payer. */
            email_address?: components["schemas"]["email"];
            /** @description This is an immutable system-generated id for a user's Venmo account. */
            account_id?: components["schemas"]["account_id-2"];
            /** @description The Venmo user name chosen by the user, also know as a Venmo handle. */
            user_name?: string;
            /** @description The name associated with the Venmo account. Supports only the `given_name` and `surname` properties. */
            name?: components["schemas"]["name-2"];
            /** @description The phone number associated with the Venmo account, in its canonical international [E.164 numbering plan format](https://www.itu.int/rec/T-REC-E.164/en). Supports only the `national_number` property. */
            phone_number?: components["schemas"]["phone-2"];
            /** @description The address of the payer. Supports only the `address_line_1`, `address_line_2`, `admin_area_1`, `admin_area_2`, `postal_code`, and `country_code` properties. Also referred to as the billing address of the customer. */
            address?: components["schemas"]["address_portable-2"];
            attributes?: components["schemas"]["venmo_wallet_attributes_response"];
        };
        /**
         * Payment Source
         * @description The payment source used to fund the payment.
         */
        payment_source_response: {
            card?: components["schemas"]["card_response"];
            paypal?: components["schemas"]["paypal_wallet_response"];
            bancontact?: components["schemas"]["bancontact"];
            blik?: components["schemas"]["blik"];
            eps?: components["schemas"]["eps"];
            giropay?: components["schemas"]["giropay"];
            ideal?: components["schemas"]["ideal"];
            mybank?: components["schemas"]["mybank"];
            p24?: components["schemas"]["p24"];
            sofort?: components["schemas"]["sofort"];
            trustly?: components["schemas"]["trustly"];
            venmo?: components["schemas"]["venmo_wallet_response"];
        };
        /**
         * Processing Instruction
         * @description The instruction to process an order.
         * @default NO_INSTRUCTION
         * @enum {string}
         */
        processing_instruction:
            | "ORDER_COMPLETE_ON_PAYMENT_APPROVAL"
            | "NO_INSTRUCTION";
        tracker_status: unknown;
        universal_product_code: unknown;
        /**
         * Tracker Item
         * @description The details of the items in the shipment.
         */
        tracker_item: {
            /** @description The item name or title. */
            name?: string;
            /** @description The item quantity. Must be a whole number. */
            quantity?: string;
            /** @description The stock keeping unit (SKU) for the item. This can contain unicode characters. */
            sku?: string;
            /**
             * Format: uri
             * @description The URL of the item's image. File type and size restrictions apply. An image that violates these restrictions will not be honored.
             */
            image_url?: string;
            /** @description The Universal Product Code of the item. */
            upc?: components["schemas"]["universal_product_code"];
        };
        /**
         * Order Tracker Response.
         * @description The tracking response on creation of tracker.
         */
        tracker: {
            /** @description The tracker id. */
            id?: string;
            status?: components["schemas"]["tracker_status"];
            /** @description An array of details of items in the shipment. */
            items?: components["schemas"]["tracker_item"][];
            /** @description An array of request-related HATEOAS links. */
            links?: readonly components["schemas"]["link_description"][];
        } & components["schemas"]["activity_timestamps"];
        /**
         * Order Shipping Details
         * @description The order shipping details.
         */
        shipping_with_tracking_details: components["schemas"]["shipping_detail"] & {
            /** @description An array of trackers for a transaction. */
            trackers?: components["schemas"]["tracker"][];
        };
        /**
         * Auhorization Status Details
         * @description The details of the authorized payment status.
         */
        authorization_status_details: {
            /**
             * @description The reason why the authorized status is `PENDING`.
             * @enum {string}
             */
            reason?: "PENDING_REVIEW";
        };
        /**
         * Authorization Status
         * @description The status fields for an authorized payment.
         */
        authorization_status: {
            /**
             * @description The status for the authorized payment.
             * @enum {string}
             */
            status?:
                | "CREATED"
                | "CAPTURED"
                | "DENIED"
                | "PARTIALLY_CAPTURED"
                | "VOIDED"
                | "PENDING";
            /** @description The details of the authorized order pending status. */
            status_details?: components["schemas"]["authorization_status_details"];
        };
        /**
         * Seller Protection
         * @description The level of protection offered as defined by [PayPal Seller Protection for Merchants](https://www.paypal.com/us/webapps/mpp/security/seller-protection).
         */
        seller_protection: {
            /**
             * @description Indicates whether the transaction is eligible for seller protection. For information, see [PayPal Seller Protection for Merchants](https://www.paypal.com/us/webapps/mpp/security/seller-protection).
             * @enum {string}
             */
            status?: "ELIGIBLE" | "PARTIALLY_ELIGIBLE" | "NOT_ELIGIBLE";
            /** @description An array of conditions that are covered for the transaction. */
            dispute_categories?: readonly (
                | "ITEM_NOT_RECEIVED"
                | "UNAUTHORIZED_TRANSACTION"
            )[];
        };
        /**
         * Authorization
         * @description The authorized payment transaction.
         */
        authorization: components["schemas"]["authorization_status"] & {
            /** @description The PayPal-generated ID for the authorized payment. */
            id?: string;
            /** @description The amount for this authorized payment. */
            amount?: components["schemas"]["money"];
            /** @description The API caller-provided external invoice number for this order. Appears in both the payer's transaction history and the emails that the payer receives. */
            invoice_id?: string;
            /** @description The API caller-provided external ID. Used to reconcile API caller-initiated transactions with PayPal transactions. Appears in transaction and settlement reports. */
            custom_id?: string;
            network_transaction_reference?: components["schemas"]["network_transaction_reference"];
            seller_protection?: components["schemas"]["seller_protection"];
            /** @description The date and time when the authorized payment expires, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            expiration_time?: components["schemas"]["date_time"];
            /** @description An array of related [HATEOAS links](/docs/api/reference/api-responses/#hateoas-links). */
            links?: readonly components["schemas"]["link_description"][];
        } & components["schemas"]["activity_timestamps"];
        /**
         * Processor Response
         * @description The processor response information for payment requests, such as direct credit card transactions.
         */
        processor_response: {
            /**
             * @description The address verification code for Visa, Discover, Mastercard, or American Express transactions.
             * @enum {string}
             */
            avs_code?:
                | "A"
                | "B"
                | "C"
                | "D"
                | "E"
                | "F"
                | "G"
                | "I"
                | "M"
                | "N"
                | "P"
                | "R"
                | "S"
                | "U"
                | "W"
                | "X"
                | "Y"
                | "Z"
                | "Null"
                | "0"
                | "1"
                | "2"
                | "3"
                | "4";
            /**
             * @description The card verification value code for for Visa, Discover, Mastercard, or American Express.
             * @enum {string}
             */
            cvv_code?:
                | "E"
                | "I"
                | "M"
                | "N"
                | "P"
                | "S"
                | "U"
                | "X"
                | "All others"
                | "0"
                | "1"
                | "2"
                | "3"
                | "4";
            /**
             * @description Processor response code for the non-PayPal payment processor errors.
             * @enum {string}
             */
            response_code?:
                | "0000"
                | "00N7"
                | "0100"
                | "0390"
                | "0500"
                | "0580"
                | "0800"
                | "0880"
                | "0890"
                | "0960"
                | "0R00"
                | "1000"
                | "10BR"
                | "1300"
                | "1310"
                | "1312"
                | "1317"
                | "1320"
                | "1330"
                | "1335"
                | "1340"
                | "1350"
                | "1352"
                | "1360"
                | "1370"
                | "1380"
                | "1382"
                | "1384"
                | "1390"
                | "1393"
                | "5100"
                | "5110"
                | "5120"
                | "5130"
                | "5135"
                | "5140"
                | "5150"
                | "5160"
                | "5170"
                | "5180"
                | "5190"
                | "5200"
                | "5210"
                | "5400"
                | "5500"
                | "5650"
                | "5700"
                | "5710"
                | "5800"
                | "5900"
                | "5910"
                | "5920"
                | "5930"
                | "5950"
                | "6300"
                | "7600"
                | "7700"
                | "7710"
                | "7800"
                | "7900"
                | "8000"
                | "8010"
                | "8020"
                | "8030"
                | "8100"
                | "8110"
                | "8220"
                | "9100"
                | "9500"
                | "9510"
                | "9520"
                | "9530"
                | "9540"
                | "9600"
                | "PCNR"
                | "PCVV"
                | "PP06"
                | "PPRN"
                | "PPAD"
                | "PPAB"
                | "PPAE"
                | "PPAG"
                | "PPAI"
                | "PPAR"
                | "PPAU"
                | "PPAV"
                | "PPAX"
                | "PPBG"
                | "PPC2"
                | "PPCE"
                | "PPCO"
                | "PPCR"
                | "PPCT"
                | "PPCU"
                | "PPD3"
                | "PPDC"
                | "PPDI"
                | "PPDV"
                | "PPDT"
                | "PPEF"
                | "PPEL"
                | "PPER"
                | "PPEX"
                | "PPFE"
                | "PPFI"
                | "PPFR"
                | "PPFV"
                | "PPGR"
                | "PPH1"
                | "PPIF"
                | "PPII"
                | "PPIM"
                | "PPIT"
                | "PPLR"
                | "PPLS"
                | "PPMB"
                | "PPMC"
                | "PPMD"
                | "PPNC"
                | "PPNL"
                | "PPNM"
                | "PPNT"
                | "PPPH"
                | "PPPI"
                | "PPPM"
                | "PPQC"
                | "PPRE"
                | "PPRF"
                | "PPRR"
                | "PPS0"
                | "PPS1"
                | "PPS2"
                | "PPS3"
                | "PPS4"
                | "PPS5"
                | "PPS6"
                | "PPSC"
                | "PPSD"
                | "PPSE"
                | "PPTE"
                | "PPTF"
                | "PPTI"
                | "PPTR"
                | "PPTT"
                | "PPTV"
                | "PPUA"
                | "PPUC"
                | "PPUE"
                | "PPUI"
                | "PPUP"
                | "PPUR"
                | "PPVC"
                | "PPVE"
                | "PPVT";
            /**
             * @description The declined payment transactions might have payment advice codes. The card networks, like Visa and Mastercard, return payment advice codes.
             * @enum {string}
             */
            payment_advice_code?: "01" | "02" | "03" | "21";
        };
        /**
         * Authorization with Additional Data
         * @description The authorization with additional payment details, such as risk assessment and processor response. These details are populated only for certain payment methods.
         */
        authorization_with_additional_data: components["schemas"]["authorization"] & {
            /** @description The processor response for card transactions. */
            processor_response?: components["schemas"]["processor_response"];
        };
        /**
         * Capture Status Details
         * @description The details of the captured payment status.
         */
        capture_status_details: {
            /**
             * @description The reason why the captured payment status is `PENDING` or `DENIED`.
             * @enum {string}
             */
            reason?:
                | "BUYER_COMPLAINT"
                | "CHARGEBACK"
                | "ECHECK"
                | "INTERNATIONAL_WITHDRAWAL"
                | "OTHER"
                | "PENDING_REVIEW"
                | "RECEIVING_PREFERENCE_MANDATES_MANUAL_ACTION"
                | "REFUNDED"
                | "TRANSACTION_APPROVED_AWAITING_FUNDING"
                | "UNILATERAL"
                | "VERIFICATION_REQUIRED";
        };
        /**
         * Capture Status
         * @description The status of a captured payment.
         */
        capture_status: {
            /**
             * @description The status of the captured payment.
             * @enum {string}
             */
            status?:
                | "COMPLETED"
                | "DECLINED"
                | "PARTIALLY_REFUNDED"
                | "PENDING"
                | "REFUNDED"
                | "FAILED";
            /** @description The details of the captured payment status. */
            status_details?: components["schemas"]["capture_status_details"];
        };
        /**
         * Exchange Rate
         * @description The exchange rate that determines the amount to convert from one currency to another currency.
         */
        readonly exchange_rate: {
            /** @description The source currency from which to convert an amount. */
            readonly source_currency?: components["schemas"]["currency_code"];
            /** @description The target currency to which to convert an amount. */
            readonly target_currency?: components["schemas"]["currency_code"];
            /** @description The target currency amount. Equivalent to one unit of the source currency. Formatted as integer or decimal value with one to 15 digits to the right of the decimal point. */
            readonly value?: string;
        };
        /**
         * Seller Receivable Breakdown
         * @description The detailed breakdown of the capture activity. This is not available for transactions that are in pending state.
         */
        seller_receivable_breakdown: {
            /** @description The amount for this captured payment in the currency of the transaction. */
            gross_amount: components["schemas"]["money"];
            /** @description The applicable fee for this captured payment in the currency of the transaction. */
            paypal_fee?: components["schemas"]["money"];
            /** @description The applicable fee for this captured payment in the receivable currency. Returned only in cases the fee is charged in the receivable currency. Example 'CNY'. */
            paypal_fee_in_receivable_currency?: components["schemas"]["money"];
            /** @description The net amount that the payee receives for this captured payment in their PayPal account. The net amount is computed as <code>gross_amount</code> minus the <code>paypal_fee</code> minus the <code>platform_fees</code>. */
            net_amount?: components["schemas"]["money"];
            /** @description The net amount that is credited to the payee's PayPal account. Returned only when the currency of the captured payment is different from the currency of the PayPal account where the payee wants to credit the funds. The amount is computed as <code>net_amount</code> times <code>exchange_rate</code>. */
            receivable_amount?: components["schemas"]["money"];
            /** @description The exchange rate that determines the amount that is credited to the payee's PayPal account. Returned when the currency of the captured payment is different from the currency of the PayPal account where the payee wants to credit the funds. */
            exchange_rate?: components["schemas"]["exchange_rate"];
            /** @description An array of platform or partner fees, commissions, or brokerage fees that associated with the captured payment. */
            platform_fees?: components["schemas"]["platform_fee"][];
        };
        /**
         * Capture
         * @description A captured payment.
         */
        capture: components["schemas"]["capture_status"] & {
            /** @description The PayPal-generated ID for the captured payment. */
            id?: string;
            /** @description The amount for this captured payment. */
            amount?: components["schemas"]["money"];
            /** @description The API caller-provided external invoice number for this order. Appears in both the payer's transaction history and the emails that the payer receives. */
            invoice_id?: string;
            /** @description The API caller-provided external ID. Used to reconcile API caller-initiated transactions with PayPal transactions. Appears in transaction and settlement reports. */
            custom_id?: string;
            network_transaction_reference?: components["schemas"]["network_transaction_reference"];
            seller_protection?: components["schemas"]["seller_protection"];
            /**
             * @description Indicates whether you can make additional captures against the authorized payment. Set to `true` if you do not intend to capture additional payments against the authorization. Set to `false` if you intend to capture additional payments against the authorization.
             * @default false
             */
            final_capture?: boolean;
            seller_receivable_breakdown?: components["schemas"]["seller_receivable_breakdown"];
            disbursement_mode?: components["schemas"]["disbursement_mode"];
            /** @description An array of related [HATEOAS links](/docs/api/reference/api-responses/#hateoas-links). */
            links?: readonly components["schemas"]["link_description"][];
            /** @description An object that provides additional processor information for a direct credit card transaction. */
            processor_response?: components["schemas"]["processor_response"];
        } & components["schemas"]["activity_timestamps"];
        /**
         * Refund Status Details
         * @description The details of the refund status.
         */
        refund_status_details: {
            /**
             * @description The reason why the refund has the `PENDING` or `FAILED` status.
             * @enum {string}
             */
            reason?: "ECHECK";
        };
        /**
         * Refund Status
         * @description The refund status.
         */
        refund_status: {
            /**
             * @description The status of the refund.
             * @enum {string}
             */
            status?: "CANCELLED" | "FAILED" | "PENDING" | "COMPLETED";
            /** @description The details of the refund status. */
            status_details?: components["schemas"]["refund_status_details"];
        };
        /**
         * Net Amount Breakdown Item
         * @description The net amount. Returned when the currency of the refund is different from the currency of the PayPal account where the merchant holds their funds.
         */
        net_amount_breakdown_item: {
            /** @description The net amount debited from the merchant's PayPal account. */
            payable_amount?: components["schemas"]["money"];
            /** @description The converted payable amount. */
            converted_amount?: components["schemas"]["money"];
            /** @description The exchange rate that determines the amount that was debited from the merchant's PayPal account. */
            exchange_rate?: components["schemas"]["exchange_rate"];
        };
        /**
         * Refund
         * @description The refund information.
         */
        refund: components["schemas"]["refund_status"] & {
            /** @description The PayPal-generated ID for the refund. */
            id?: string;
            /** @description The amount that the payee refunded to the payer. */
            amount?: components["schemas"]["money"];
            /** @description The API caller-provided external invoice number for this order. Appears in both the payer's transaction history and the emails that the payer receives. */
            invoice_id?: string;
            /** @description The API caller-provided external ID. Used to reconcile API caller-initiated transactions with PayPal transactions. Appears in transaction and settlement reports. */
            custom_id?: string;
            /** @description Reference ID issued for the card transaction. This ID can be used to track the transaction across processors, card brands and issuing banks. */
            acquirer_reference_number?: string;
            /** @description The reason for the refund. Appears in both the payer's transaction history and the emails that the payer receives. */
            note_to_payer?: string;
            /**
             * Merchant Payable Breakdown
             * @description The breakdown of the refund.
             */
            seller_payable_breakdown?: {
                /** @description The amount that the payee refunded to the payer. */
                readonly gross_amount?: components["schemas"]["money"];
                /** @description The PayPal fee that was refunded to the payer in the currency of the transaction. This fee might not match the PayPal fee that the payee paid when the payment was captured. */
                readonly paypal_fee?: components["schemas"]["money"];
                /** @description The PayPal fee that was refunded to the payer in the receivable currency. Returned only in cases when the receivable currency is different from transaction currency. Example 'CNY'. */
                readonly paypal_fee_in_receivable_currency?: components["schemas"]["money"];
                /** @description The net amount that the payee's account is debited in the transaction currency. The net amount is calculated as <code>gross_amount</code> minus <code>paypal_fee</code> minus <code>platform_fees</code>. */
                readonly net_amount?: components["schemas"]["money"];
                /** @description The net amount that the payee's account is debited in the receivable currency. Returned only in cases when the receivable currency is different from transaction currency. Example 'CNY'. */
                readonly net_amount_in_receivable_currency?: components["schemas"]["money"];
                /** @description An array of platform or partner fees, commissions, or brokerage fees for the refund. */
                readonly platform_fees?: components["schemas"]["platform_fee"][];
                /** @description An array of breakdown values for the net amount. Returned when the currency of the refund is different from the currency of the PayPal account where the payee holds their funds. */
                readonly net_amount_breakdown?: readonly components["schemas"]["net_amount_breakdown_item"][];
                /** @description The total amount refunded from the original capture to date. For example, if a payer makes a $100 purchase and was refunded $20 a week ago and was refunded $30 in this refund, the `gross_amount` is $30 for this refund and the `total_refunded_amount` is $50. */
                readonly total_refunded_amount?: components["schemas"]["money"];
            };
            /** @description The details associated with the merchant for this transaction. */
            payer?: components["schemas"]["payee_base"];
            /** @description An array of related [HATEOAS links](/docs/api/reference/api-responses/#hateoas-links). */
            links?: readonly components["schemas"]["link_description"][];
        } & components["schemas"]["activity_timestamps"];
        /**
         * Payment Collection
         * @description The collection of payments, or transactions, for a purchase unit in an order. For example, authorized payments, captured payments, and refunds.
         */
        payment_collection: {
            /** @description An array of authorized payments for a purchase unit. A purchase unit can have zero or more authorized payments. */
            authorizations?: components["schemas"]["authorization_with_additional_data"][];
            /** @description An array of captured payments for a purchase unit. A purchase unit can have zero or more captured payments. */
            captures?: components["schemas"]["capture"][];
            /** @description An array of refunds for a purchase unit. A purchase unit can have zero or more refunds. */
            refunds?: components["schemas"]["refund"][];
        };
        /**
         * Purchase Unit
         * @description The purchase unit details. Used to capture required information for the payment contract.
         */
        purchase_unit: {
            /** @description The API caller-provided external ID for the purchase unit. Required for multiple purchase units when you must update the order through `PATCH`. If you omit this value and the order contains only one purchase unit, PayPal sets this value to `default`. <blockquote><strong>Note:</strong> If there are multiple purchase units, <code>reference_id</code> is required for each purchase unit.</blockquote> */
            reference_id?: string;
            amount?: components["schemas"]["amount_with_breakdown"];
            /** @description The merchant who receives payment for this transaction. */
            payee?: components["schemas"]["payee"];
            payment_instruction?: components["schemas"]["payment_instruction"];
            /** @description The purchase description. */
            description?: string;
            /** @description The API caller-provided external ID. Used to reconcile API caller-initiated transactions with PayPal transactions. Appears in transaction and settlement reports. */
            custom_id?: string;
            /** @description The API caller-provided external invoice ID for this order. */
            invoice_id?: string;
            /** @description The PayPal-generated ID for the purchase unit. This ID appears in both the payer's transaction history and the emails that the payer receives. In addition, this ID is available in transaction and settlement reports that merchants and API callers can use to reconcile transactions. This ID is only available when an order is saved by calling <code>v2/checkout/orders/id/save</code>. */
            id?: string;
            /** @description The payment descriptor on account transactions on the customer's credit card statement, that PayPal sends to processors. The maximum length of the soft descriptor information that you can pass in the API field is 22 characters, in the following format:<code>22 - len(PAYPAL * (8)) - len(<var>Descriptor in Payment Receiving Preferences of Merchant account</var> + 1)</code>The PAYPAL prefix uses 8 characters.<br/><br/>The soft descriptor supports the following ASCII characters:<ul><li>Alphanumeric characters</li><li>Dashes</li><li>Asterisks</li><li>Periods (.)</li><li>Spaces</li></ul>For Wallet payments marketplace integrations:<ul><li>The merchant descriptor in the Payment Receiving Preferences must be the marketplace name.</li><li>You can't use the remaining space to show the customer service number.</li><li>The remaining spaces can be a combination of seller name and country.</li></ul><br/>For unbranded payments (Direct Card) marketplace integrations, use a combination of the seller name and phone number. */
            soft_descriptor?: string;
            /** @description An array of items that the customer purchases from the merchant. */
            items?: components["schemas"]["item"][];
            /** @description The shipping address and method. */
            shipping?: components["schemas"]["shipping_with_tracking_details"];
            /** @description Supplementary data about this payment. Merchants and partners can add Level 2 and 3 data to payments to reduce risk and payment processing costs. For more information about processing payments, see <a href="https://developer.paypal.com/docs/checkout/advanced/processing/">checkout</a> or <a href="https://developer.paypal.com/docs/multiparty/checkout/advanced/processing/">multiparty checkout</a>. */
            supplementary_data?: components["schemas"]["supplementary_data"];
            /** @description The comprehensive history of payments for the purchase unit. */
            payments?: components["schemas"]["payment_collection"];
        };
        /**
         * Order Status
         * @description The order status.
         * @enum {string}
         */
        order_status:
            | "CREATED"
            | "SAVED"
            | "APPROVED"
            | "VOIDED"
            | "COMPLETED"
            | "PAYER_ACTION_REQUIRED";
        /**
         * Order
         * @description The order details.
         */
        order: components["schemas"]["activity_timestamps"] & {
            /** @description The ID of the order. */
            id?: string;
            payment_source?: components["schemas"]["payment_source_response"];
            intent?: components["schemas"]["checkout_payment_intent"];
            processing_instruction?: components["schemas"]["processing_instruction"];
            /**
             * @deprecated
             * @description DEPRECATED. The customer is also known as the payer. The Payer object was intended to only be used with the `payment_source.paypal` object. In order to make this design more clear, the details in the `payer` object are now available under `payment_source.paypal`. Please use `payment_source.paypal`.
             */
            payer?: components["schemas"]["payer"];
            /** @description An array of purchase units. Each purchase unit establishes a contract between a customer and merchant. Each purchase unit represents either a full or partial order that the customer intends to purchase from the merchant. */
            purchase_units?: components["schemas"]["purchase_unit"][];
            status?: components["schemas"]["order_status"];
            /** @description An array of request-related HATEOAS links. To complete payer approval, use the `approve` link to redirect the payer. The API caller has 3 hours (default setting, this which can be changed by your account manager to 24/48/72 hours to accommodate your use case) from the time the order is created, to redirect your payer. Once redirected, the API caller has 3 hours for the payer to approve the order and either authorize or capture the order. If you are not using the PayPal JavaScript SDK to initiate PayPal Checkout (in context) ensure that you include `application_context.return_url` is specified or you will get "We're sorry, Things don't appear to be working at the moment" after the payer approves the payment. */
            links?: readonly components["schemas"]["link_description"][];
        };
        /**
         * Patch
         * @description The JSON patch object to apply partial updates to resources.
         */
        patch: {
            /**
             * @description The operation.
             * @enum {string}
             */
            op: "add" | "remove" | "replace" | "move" | "copy" | "test";
            /** @description The <a href="https://tools.ietf.org/html/rfc6901">JSON Pointer</a> to the target document location at which to complete the operation. */
            path?: string;
            /**
             * Patch Value
             * @description The value to apply. The <code>remove</code>, <code>copy</code>, and <code>move</code> operations do not require a value. Since <a href="https://www.rfc-editor.org/rfc/rfc69021">JSON Patch</a> allows any type for <code>value</code>, the <code>type</code> property is not specified.
             */
            value?: unknown;
            /** @description The <a href="https://tools.ietf.org/html/rfc6901">JSON Pointer</a> to the target document location from which to move the value. Required for the <code>move</code> operation. */
            from?: string;
        };
        /**
         * Patch Request
         * @description An array of JSON patch objects to apply partial updates to resources.
         */
        patch_request: components["schemas"]["patch"][];
        "orders.patch-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "FIELD_NOT_PATCHABLE";
                      /** @enum {string} */
                      description?: "Field cannot be patched.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_ARRAY_MAX_ITEMS";
                      /** @enum {string} */
                      description?: "The number of items in an array parameter is too large.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is either too short or too long";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field / parameter is missing.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AMOUNT_NOT_PATCHABLE";
                      /** @enum {string} */
                      description?: "The amount cannot be updated as the 'payer' has chosen and approved a specific financing offer for a given amount. Please Create a new Order with the updated Order amount and have the 'payer' approve the new payment terms.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PATCH_OPERATION";
                      /** @enum {string} */
                      description?: "The operation cannot be honored. Cannot add a property that's already present, use replace. Cannot remove a property thats not present, use add. Cannot replace a property thats not present, use add.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MALFORMED_REQUEST_JSON";
                      /** @enum {string} */
                      description?: "The request JSON is not well formed.";
                  }
            )[];
        };
        "orders.patch-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "AMOUNT_MISMATCH";
                      /** @enum {string} */
                      description?: "Should equal item_total + tax_total + shipping + handling + insurance - shipping_discount - discount.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CANNOT_BE_NEGATIVE";
                      /** @enum {string} */
                      description?: "Must be greater than or equal to 0. If the currency supports decimals, only two decimal place precision is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CANNOT_BE_ZERO_OR_NEGATIVE";
                      /** @enum {string} */
                      description?: "Must be greater than zero. If the currency supports decimals, only two decimal place precision is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CITY_REQUIRED";
                      /** @enum {string} */
                      description?: "The specified country requires a city (address.admin_area_2).";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DECIMAL_PRECISION";
                      /** @enum {string} */
                      description?: "If the currency supports decimals, only two decimal place precision is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DONATION_ITEMS_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "If 'purchase_unit' has \"DONATION\" as the 'items.category' then the Order can at most have one purchase_unit. Multiple purchase_units are not supported if either of them have at least one items with category as \"DONATION\".";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DUPLICATE_REFERENCE_ID";
                      /** @enum {string} */
                      description?: "`reference_id` must be unique if multiple `purchase_unit` are provided.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_CURRENCY_CODE";
                      /** @enum {string} */
                      description?: "Currency code is invalid or is not currently supported. Please refer https://developer.paypal.com/api/rest/reference/currency-codes/ for list of supported currency codes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ITEM_TOTAL_MISMATCH";
                      /** @enum {string} */
                      description?: "Should equal sum of (unit_amount * quantity) across all items for a given purchase_unit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ITEM_TOTAL_REQUIRED";
                      /** @enum {string} */
                      description?: "If item details are specified (items.unit_amount and items.quantity) corresponding amount.breakdown.item_total is required.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MAX_VALUE_EXCEEDED";
                      /** @enum {string} */
                      description?: "Should be less than or equal to 999999999999999.99.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_JSON_POINTER_FORMAT";
                      /** @enum {string} */
                      description?: "Path should be a valid JSON Pointer https://tools.ietf.org/html/rfc6901 that references a location within the request where the operation is performed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER";
                      /** @enum {string} */
                      description?: "Cannot be specified as part of the request.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_PATCHABLE";
                      /** @enum {string} */
                      description?: "Cannot be patched.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TAX_TOTAL_MISMATCH";
                      /** @enum {string} */
                      description?: "Should equal sum of (tax * quantity) across all items for a given purchase_unit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TAX_TOTAL_REQUIRED";
                      /** @enum {string} */
                      description?: "If item details are specified (items.tax_total and items.quantity) corresponding amount.breakdown.tax_total is required.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_INTENT";
                      /** @enum {string} */
                      description?: "`intent=AUTHORIZE` is not supported for multiple purchase units. Only `intent=CAPTURE` is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_PATCH_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value specified for this field is not currently supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PATCH_VALUE_REQUIRED";
                      /** @enum {string} */
                      description?: "Please specify a 'value' to for the field that is being patched.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PATCH_PATH_REQUIRED";
                      /** @enum {string} */
                      description?: "Please specify a 'path' for the field for which the operation needs to be performed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_ACCOUNT_LOCKED_OR_CLOSED";
                      /** @enum {string} */
                      description?: "The merchant account is locked or closed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_ACCOUNT_RESTRICTED";
                      /** @enum {string} */
                      description?: "The merchant account is restricted.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_FX_RATE_ID_EXPIRED";
                      /** @enum {string} */
                      description?: "The specified FX Rate ID has expired. Please specify a different FX Rate Id and try the request again. Alternately, remove the FX Rate ID to process the request using the default exchange rate.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_FX_RATE_ID_CURRENCY_MISMATCH";
                      /** @enum {string} */
                      description?: "The specified FX Rate ID is for a currency that does not match with the currency of this request. Please specify a different FX Rate ID and try the request again. Alternately, remove the FX Rate ID to process the request using the default exchange rate.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_FX_RATE_ID";
                      /** @enum {string} */
                      description?: "The specific FX Rate ID is not valid. This could be either because we are not able to look up the FX Rate based on this ID or it could be because the ID belongs to another API Caller.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PLATFORM_FEES_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "The API Caller is not enabled to process transactions by specifying 'platform_fees'. Please work with your PayPal Account Manager to enable this option for your account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PLATFORM_FEES_ACCOUNT";
                      /** @enum {string} */
                      description?: "The specified platform_fees payee account is either invalid or account setup is incomplete.Please work with your PayPal Account Manager to enable this option for your account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PLATFORM_FEES_AMOUNT";
                      /** @enum {string} */
                      description?: "The platform_fees amount cannot be greater than order amount.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "POSTAL_CODE_REQUIRED";
                      /** @enum {string} */
                      description?: "The specified country requires a postal code.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REFERENCE_ID_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Filter expression value is incorrect. Please check the value of the reference_id and try again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REFERENCE_ID_REQUIRED";
                      /** @enum {string} */
                      description?: "'reference_id' is required for each 'purchase_unit' if multiple 'purchase_unit' are provided.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MULTI_CURRENCY_ORDER";
                      /** @enum {string} */
                      description?: "Multiple differing values of currency_code are not supported. Entire Order request must have the same currency_code.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_OPTION_NOT_SELECTED";
                      /** @enum {string} */
                      description?: "At least one of the shipping.option should be set to 'selected = true'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_OPTIONS_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Shipping options are not supported when 'application_context.shipping_preference' is set as 'NO_SHIPPING' or 'SET_PROVIDED_ADDRESS'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MULTIPLE_SHIPPING_OPTION_SELECTED";
                      /** @enum {string} */
                      description?: "Only one shipping.option can be set to 'selected = true'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_ALREADY_COMPLETED";
                      /** @enum {string} */
                      description?: "The order cannot be patched after it is completed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREFERRED_SHIPPING_OPTION_AMOUNT_MISMATCH";
                      /** @enum {string} */
                      description?: "The amount provided in the preferred shipping option should match the amount provided in amount breakdown";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AMOUNT_CHANGE_NOT_ALLOWED";
                      /** @enum {string} */
                      description?: "The amount specified is different from the amount authorized by payer.";
                  }
            )[];
        };
        /**
         * Confirm Application Context
         * @description Customizes the payer confirmation experience.
         */
        order_confirm_application_context: {
            /** @description Label to present to your payer as part of the PayPal hosted web experience. */
            brand_name?: string;
            /** @description The BCP 47-formatted locale of pages that the PayPal payment experience shows. PayPal supports a five-character code. For example, `da-DK`, `he-IL`, `id-ID`, `ja-JP`, `no-NO`, `pt-BR`, `ru-RU`, `sv-SE`, `th-TH`, `zh-CN`, `zh-HK`, or `zh-TW`. */
            locale?: components["schemas"]["language"];
            /**
             * Format: uri
             * @description The URL where the customer is redirected after the customer approves the payment.
             */
            return_url?: string;
            /**
             * Format: uri
             * @description The URL where the customer is redirected after the customer cancels the payment.
             */
            cancel_url?: string;
            stored_payment_source?: components["schemas"]["stored_payment_source"];
        };
        /**
         * Confirm Order Request
         * @description Payer confirms the intent to pay for the Order using the provided payment source.
         */
        confirm_order_request: {
            payment_source: components["schemas"]["payment_source"];
            processing_instruction?: components["schemas"]["processing_instruction"];
            application_context?: components["schemas"]["order_confirm_application_context"];
        };
        "orders.confirm-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of the field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "A parameter value is not valid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field / parameter is missing";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is either too short or too long";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_MAX_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is too long.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MALFORMED_REQUEST_JSON";
                      /** @enum {string} */
                      description?: "The request JSON is not well formed.";
                  }
            )[];
        };
        "orders.confirm-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "ORDER_ALREADY_CAPTURED";
                      /** @enum {string} */
                      description?: "Order already captured. If 'intent=CAPTURE' only one capture per order is allowed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_ALREADY_AUTHORIZED";
                      /** @enum {string} */
                      description?: "Order already captured. If 'intent=CAPTURE' only one capture per order is allowed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_CANNOT_BE_CONFIRMED";
                      /** @enum {string} */
                      description?: "An order with status = 'COMPLETED' cannot be confirmed again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PREVIOUS_REFERENCE";
                      /** @enum {string} */
                      description?: "For Merchant initiated network token transactions, either the payment_source.card.stored_credential.previous_network_transaction_reference or payment_source.card.stored_credential.previous_transaction_reference must be included in the request.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_CRYPTOGRAM";
                      /** @enum {string} */
                      description?: "Cryptogram is mandatory for any customer initiated network token transactions.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_NOT_SUPPORTED_FOR_COUNTRY";
                      /** @enum {string} */
                      description?: " For the payment_source specified, the currency of the Order is restricted by the country in which the payee account is based. Please refer https://developer.paypal.com/api/rest/reference/currency-codes/ for list of supported currency codes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_EXPIRED";
                      /** @enum {string} */
                      description?: "The card is expired";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_TYPE_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Processing of this card type is not supported. Use another card type.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_NOT_SUPPORTED_FOR_CARD_TYPE";
                      /** @enum {string} */
                      description?: "The issued currency code of this card is not supported for direct card payments. Please refer https://developer.paypal.com/api/rest/reference/currency-codes/ for list of supported currency codes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ONLY_ONE_PAYMENT_SOURCE_ALLOWED";
                      /** @enum {string} */
                      description?: "More than one payment method within the payment source is not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NO_PAYMENT_SOURCE_PROVIDED";
                      /** @enum {string} */
                      description?: "At least one payment method is required within the payment source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_ALREADY_APPROVED";
                      /** @enum {string} */
                      description?: "The payment has already been approved.  Please capture the order, or create and confirm a new order with this payment source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_PROCESSING_INSTRUCTION";
                      /** @enum {string} */
                      description?: "The specified processing_instruction is not supported for the given payment_source. Please refer to https://developer.paypal.com/api/orders/v2/#definition-processing_instruction for the list of payment_source that can be specified with this value.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_COMPLETE_ON_PAYMENT_APPROVAL";
                      /** @enum {string} */
                      description?: "A processing_instruction of `ORDER_COMPLETE_ON_PAYMENT_APPROVAL` is required for the specified payment_source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_EXPIRY_DATE";
                      /** @enum {string} */
                      description?: "Expiry date is invalid. Expiry date should be a date in future and within the threshold for the payment source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TOKEN_EXPIRED";
                      /** @enum {string} */
                      description?: "The token is expired and cannot be used for payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_GOOGLE_PAY_TOKEN";
                      /** @enum {string} */
                      description?: "The google pay token is invalid. PayPal was not able to decrypt the googlepay token or PayPal was not able to find the necessary data in the token after decryption.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "GOOGLE_PAY_GATEWAY_MERCHANT_ID_MISMATCH";
                      /** @enum {string} */
                      description?: "The gateway merchant ID in Google Pay token is not valid. This could be because the gateway merchant Id that was authorized by payer/buyer on Google Pay does not match with the API caller of the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CRYPTOGRAM_REQUIRED";
                      /** @enum {string} */
                      description?: "Cryptogram is required if authentication method is CRYPTOGRAM 3DS.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ONE_OF_PARAMETERS_REQUIRED";
                      /** @enum {string} */
                      description?: "One or more field is required to continue with this request.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "RETURN_URL_REQUIRED";
                      /** @enum {string} */
                      description?: "The return url is required when attempting to vault this source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CANCEL_URL_REQUIRED";
                      /** @enum {string} */
                      description?: "The cancel url is required when attempting to vault this source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "COUNTRY_NOT_SUPPORTED_BY_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "Country code provided is not supported by the provided payment source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REQUIRED_PARAMETER_FOR_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "The parameter is required for provided payment source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REQUIRED_PARAMETER_FOR_CUSTOMER_INITIATED_PAYMENT";
                      /** @enum {string} */
                      description?: 'This parameter is required when the customer is present. If the customer is not present, indicate so by sending payment_initiator=`MERCHANT`. For details, see <a href="https://developer.paypal.com/docs/api/orders/v2/#definition-card_stored_credential">Stored Credential</a>.';
                  }
                | {
                      /** @enum {string} */
                      issue?: "ITEM_CATEGORY_NOT_SUPPORTED_BY_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "The provided payment source does not support provided item category.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_INFO_CANNOT_BE_VERIFIED";
                      /** @enum {string} */
                      description?: "The combination of the payment_source name, billing address, shipping name and shipping address could not be verified. Please correct this information and try again by creating a new order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_DECLINED_BY_PROCESSOR";
                      /** @enum {string} */
                      description?: "The provided payment source is declined by the processor. Please try again with a different payment source by creating a new order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_CANNOT_BE_USED";
                      /** @enum {string} */
                      description?: "The provided payment source cannot be used to pay for the order. Please try again with a different payment source by creating a new order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SETUP_ERROR_FOR_BANK";
                      /** @enum {string} */
                      description?: "The API Caller account setup, for bank payments, is incomplete or incorrect. Please contact your PayPal account manager.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BANK_NOT_SUPPORTED_FOR_VERIFICATION";
                      /** @enum {string} */
                      description?: "Verification for this bank account is not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "APPLE_PAY_AMOUNT_MISMATCH";
                      /** @enum {string} */
                      description?: "The 'amount' specified in the Order should match the amount that was viewed and authorized by the payer/buyer on Apple Pay. If the amount has changed, please redirect the buyer to authorize the order again via Apple Pay.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ONE_OF_THE_PARAMETERS_REQUIRED";
                      /** @enum {string} */
                      description?: "One or more field is required to continue with this request.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BILLING_ADDRESS_INVALID";
                      /** @enum {string} */
                      description?: "Provided billing address is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_ADDRESS_INVALID";
                      /** @enum {string} */
                      description?: "Provided shipping address is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_IS_PENDING_APPROVAL";
                      /** @enum {string} */
                      description?: "The order was confirmed and payer action completed but order approval processing from PayPal is pending. No action is needed from Payee or Payer. Please wait until order status changes to 'APPROVED'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DEVICE_DATA_NOT_AVAILABLE";
                      /** @enum {string} */
                      description?: "Device Data is not available for processing this order. The PayPal-Client-Metadata-Id header value sent during `Create Order` api call is either missing or incorrect or there was an error in collecting required data. Please verify if appropriate value for PayPal-Client-Metadata-Id header is being sent during 'Create Order' api call. Please note this error only applies to payment_source.pay_upon_invoice at the moment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_NOT_SUPPORTED_FOR_BANK";
                      /** @enum {string} */
                      description?: "The payment_source does not support the currency of the Order. For ACH debit, only USD is supported and for SEPA debit, only EUR is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ONLY_ONE_BANK_SOURCE_ALLOWED";
                      /** @enum {string} */
                      description?: "More than one payment method within the bank payment object is not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_IBAN";
                      /** @enum {string} */
                      description?: "IBAN provided is not a valid bank account number.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "IBAN_COUNTRY_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Country code of issuer bank for the provided IBAN is not supported for SEPA debit payments.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_COUNTRY_NOT_SUPPORTED_FOR_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "Payee country code is not supported by the provided payment source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_NUMBER_REQUIRED";
                      /** @enum {string} */
                      description?: "The card number is required when attempting to process payment with card.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_EXPIRY_REQUIRED";
                      /** @enum {string} */
                      description?: "The card expiry is required when attempting to process payment with card.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INCOMPATIBLE_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of the field is incompatible/redundant with other fields in the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "VAULT_INSTRUCTION_DUPLICATED";
                      /** @enum {string} */
                      description?: "Only one vault instruction is allowed. Please use `vault.store_in_vault` to provide vault instruction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "VAULT_INSTRUCTION_REQUIRED";
                      /** @enum {string} */
                      description?: "Vault instruction is required. Please use `vault.store_in_vault` to provide vault instruction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISMATCHED_VAULT_ID_TO_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "The vault_id does not match the payment_source provided. Please verify that the vault_id token used refers to the matching payment_source and try again. For example, a PayPal token cannot be passed in the vault_id field in the payment_source.card object.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_PNREF_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enabled to process payments with the `pnref`. Please contact customer support to request permissions to process transactions with PNREF.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_PAYPAL_TRANSACTION_ID_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enable to process payments using `paypal_transaction_id`. Please contact customer support to request permissions to process transactions with PayPal transaction ID.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYPAL_TRANSACTION_ID_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified `paypal_transaction_id` was not found. Verify the value and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PNREF_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified `pnref` was not found. Verify the value and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_SECURITY_CODE_LENGTH";
                      /** @enum {string} */
                      description?: "The security_code length is invalid for the specified card brand.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ENABLED_TO_VAULT_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "The API caller or the merchant on whose behalf the API call is initiated is not allowed to vault the given source. Please contact PayPal customer support for assistance.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "EMV_DATA_REQUIRED";
                      /** @enum {string} */
                      description?: "EMV Data is required if authentication method is EMV.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ALIAS_DECLINED_BY_PROCESSOR";
                      /** @enum {string} */
                      description?: "The provided alias was declined by the processor. Please create a new order with a different alias_key and/or alias_label and try again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BLIK_ONE_CLICK_MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "Blik's one_click flow requires one_click.auth_code and one_click.alias_label parameters for the buyer's first transaction. For all subsequent transactions,only the one_click.alias_key parameter is required.";
                  }
            )[];
        };
        /**
         * Authorize Request
         * @description The authorization of an order request.
         */
        order_authorize_request: {
            /** @description The source of payment for the order, which can be a token or a card. Use this object only if you have not redirected the user after order creation to approve the payment. In such cases, the user-selected payment method in the PayPal flow is implicitly used. */
            payment_source?: components["schemas"]["payment_source"];
        };
        /**
         * Order
         * @description The order authorize response.
         */
        order_authorize_response: components["schemas"]["activity_timestamps"] & {
            /** @description The ID of the order. */
            id?: string;
            payment_source?: components["schemas"]["payment_source_response"];
            intent?: components["schemas"]["checkout_payment_intent"];
            processing_instruction?: components["schemas"]["processing_instruction"];
            payer?: components["schemas"]["payer"];
            /** @description An array of purchase units. Each purchase unit establishes a contract between a customer and merchant. Each purchase unit represents either a full or partial order that the customer intends to purchase from the merchant. */
            purchase_units?: components["schemas"]["purchase_unit"][];
            status?: components["schemas"]["order_status"];
            /** @description An array of request-related HATEOAS links. To complete payer approval, use the `approve` link to redirect the payer. The API caller has 3 hours (default setting, this which can be changed by your account manager to 24/48/72 hours to accommodate your use case) from the time the order is created, to redirect your payer. Once redirected, the API caller has 3 hours for the payer to approve the order and either authorize or capture the order. If you are not using the PayPal JavaScript SDK to initiate PayPal Checkout (in context) ensure that you include `application_context.return_url` is specified or you will get "We're sorry, Things don't appear to be working at the moment" after the payer approves the payment. */
            links?: readonly components["schemas"]["link_description"][];
        };
        "orders.authorize-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_COUNTRY_CODE";
                      /** @enum {string} */
                      description?: "Country code is invalid. Please refer to https://developer.paypal.com/api/rest/reference/country-codes/ for a list of supported country codes.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "A parameter value is not valid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field / parameter is missing";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is either too short or too long";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MALFORMED_REQUEST_JSON";
                      /** @enum {string} */
                      description?: "The request JSON is not well formed.";
                  }
            )[];
        };
        "orders.authorize-403": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_TOKEN_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enabled to process payments with the specified type of token. Please contact customer support to request permissions to process transactions with this type of token.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PERMISSION_DENIED";
                      /** @enum {string} */
                      description?: "You do not have permission to access or perform operations on this resource.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PERMISSION_DENIED_FOR_DONATION_ITEMS";
                      /** @enum {string} */
                      description?: "The API Caller or Payee have not been granted appropriate permissions to send 'items.category' as 'DONATION'. Please speak to your account manager if you want to process these type of items.";
                  }
            )[];
        };
        "orders.authorize-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "ACTION_DOES_NOT_MATCH_INTENT";
                      /** @enum {string} */
                      description?: "Order was created with an intent to 'CAPTURE'. Please use v2/checkout/orders/order_id/capture to complete the transaction or alternately Create an order with an intent of 'AUTHORIZE'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AGREEMENT_ALREADY_CANCELLED";
                      /** @enum {string} */
                      description?: "The requested agreement is already canceled.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BILLING_AGREEMENT_NOT_FOUND";
                      /** @enum {string} */
                      description?: "The requested Billing Agreement token was not found.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PREVIOUS_REFERENCE";
                      /** @enum {string} */
                      description?: "For Merchant initiated network token transactions, either the payment_source.card.stored_credential.previous_network_transaction_reference or payment_source.card.stored_credential.previous_transaction_reference must be included in the request.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_CRYPTOGRAM";
                      /** @enum {string} */
                      description?: "Cryptogram is mandatory for any customer initiated network token transactions.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_BRAND_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Processing of this card brand is not supported. Please use another card to continue with this transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DECLINED_DUE_TO_RELATED_TXN";
                      /** @enum {string} */
                      description?: "One or more transactions in this Order did not succeed. Since this Order is being processed as an All or None Order, if one or more transactions in this Order do not succeed, then all purchase units are marked declined and will not be processed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DOMESTIC_TRANSACTION_REQUIRED";
                      /** @enum {string} */
                      description?: "This transaction requires the payee and payer to be resident in the same country, a domestic transaction is required to create this payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DUPLICATE_INVOICE_ID";
                      /** @enum {string} */
                      description?: "Duplicate Invoice ID detected. To avoid a potential duplicate transaction your account setting requires that Invoice Id be unique for each transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_NOT_APPROVED";
                      /** @enum {string} */
                      description?: "Payer has not yet approved the Order for payment. Please redirect the payer to the 'rel':'approve' url returned as part of the HATEOAS links within the Create Order call.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MAX_NUMBER_OF_PAYMENT_ATTEMPTS_EXCEEDED";
                      /** @enum {string} */
                      description?: "You have exceeded the maximum number of payment attempts.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_BLOCKED_TRANSACTION";
                      /** @enum {string} */
                      description?: "The Fraud settings for this seller are such that this payment cannot be executed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_FX_RATE_ID_EXPIRED";
                      /** @enum {string} */
                      description?: "The specified FX Rate ID has expired. Please specify a different FX Rate Id and try the request again. Alternately, remove the FX Rate ID to process the request using the default exchange rate.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "UNSUPPORTED_INTENT_FOR_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "`intent=AUTHORIZE` is not supported for the specified payment_source. Only `intent=CAPTURE` is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_ACCOUNT_LOCKED_OR_CLOSED";
                      /** @enum {string} */
                      description?: "The payer account cannot be used for this transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_ACCOUNT_RESTRICTED";
                      /** @enum {string} */
                      description?: "PAYER_ACCOUNT_RESTRICTED";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_CANNOT_PAY";
                      /** @enum {string} */
                      description?: "Payer cannot pay for this transaction. Please contact the payer to find other ways to pay for this transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYPAL_TRANSACTION_ID_EXPIRED";
                      /** @enum {string} */
                      description?: "Specified `paypal_transaction_id` has expired. PayPal transaction ID expires 4 years after the date of the initial transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PNREF_EXPIRED";
                      /** @enum {string} */
                      description?: "Specified `pnref` has expired. PNREF expires 15 months after the date of the initial transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REFERENCED_CARD_EXPIRED";
                      /** @enum {string} */
                      description?: "The card underlying the token has expired and hence cannot be used to process a payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TOKEN_EXPIRED";
                      /** @enum {string} */
                      description?: "The token is expired and cannot be used for payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TOKEN_ID_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified token was not found. Verify the token and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_LIMIT_EXCEEDED";
                      /** @enum {string} */
                      description?: "Total payment amount exceeded transaction limit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_RECEIVING_LIMIT_EXCEEDED";
                      /** @enum {string} */
                      description?: "The transaction exceeds the receiver's receiving limit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_REFUSED";
                      /** @enum {string} */
                      description?: "The request was refused.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_ALREADY_AUTHORIZED";
                      /** @enum {string} */
                      description?: "Order already authorized.If 'intent=AUTHORIZE' only one authorization per order is allowed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AUTH_CAPTURE_NOT_ENABLED";
                      /** @enum {string} */
                      description?: "Authorization and Capture feature is not enabled for the merchant. Make sure that the recipient of the funds is a verified business account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AMOUNT_CANNOT_BE_SPECIFIED";
                      /** @enum {string} */
                      description?: "An authorization amount can only be specified if an Order has been saved by calling /v2/checkout/orders/{order_id}/save.  Please save the order and try again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AUTHORIZATION_AMOUNT_EXCEEDED";
                      /** @enum {string} */
                      description?: "Authorization amount specified exceeded allowable limit. Specify a different amount and try the request again. Alternately, contact Customer Support to increase your limits. Local regulations (e.g. in PSD2 countries) prohibit overages above the amount authorized by the payer.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AUTHORIZATION_CURRENCY_MISMATCH";
                      /** @enum {string} */
                      description?: "The currency of the authorization should be same as that in which the Order was created and approved by the Payer. Please check the 'currency_code' and try again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MAX_AUTHORIZATION_COUNT_EXCEEDED";
                      /** @enum {string} */
                      description?: "Maximum number of authorization allowed for the order is reached. Please contact Customer Support if you need to increase your limit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_COMPLETED_OR_VOIDED";
                      /** @enum {string} */
                      description?: "Order is voided or completed and hence cannot be authorized.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_EXPIRED";
                      /** @enum {string} */
                      description?: "Order is expired and hence cannot be authorized. Please contact Customer Support if you need to increase your order validity period.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PICKUP_ADDRESS";
                      /** @enum {string} */
                      description?: "If the 'shipping_option.type' is set as 'PICKUP' then the 'shipping_detail.name.full_name' should start with 'S2S' meaning Ship To Store. Example: 'S2S My Store'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_ADDRESS_INVALID";
                      /** @enum {string} */
                      description?: "Provided shipping address is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_TYPE_NOT_SUPPORTED_FOR_INTENT";
                      /** @enum {string} */
                      description?: "Provided payment type not supported for order intent. Payment authorizations are supported only for order with `intent=AUTHORIZE` and payment captures are supported only for order with `intent=CAPTURE`.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BILLING_AGREEMENT_ID_MISMATCH";
                      /** @enum {string} */
                      description?: "Billing Agreement ID must exactly match the Billing Agreement ID that was provided during order creation.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREFERRED_PAYMENT_SOURCE_MISMATCH";
                      /** @enum {string} */
                      description?: "Payment Source must exactly match the Preferred Payment Source that was provided during order creation.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INCOMPATIBLE_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of the field is incompatible/redundant with other fields in the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PREVIOUS_TRANSACTION_REFERENCE";
                      /** @enum {string} */
                      description?: "The authorization or capture referenced by `previous_transaction_reference` is not valid. This could be either because the previous_transaction_reference is not found or doesn't belong to the payee. Please use a valid `previous_transaction_reference`.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREVIOUS_TRANSACTION_REFERENCE_HAS_CHARGEBACK";
                      /** @enum {string} */
                      description?: "The capture referenced by `previous_transaction_reference` has a chargeback and hence cannot be used for this order. Please use a `previous_transaction_reference` which does not have a chargeback.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREVIOUS_TRANSACTION_REFERENCE_VOIDED";
                      /** @enum {string} */
                      description?: "The status of authorization referenced by `previous_transaction_reference` is `VOIDED` and hence cannot be used for this order. Please use a `previous_transaction_reference` whose status is not `VOIDED`.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_MISMATCH";
                      /** @enum {string} */
                      description?: "The `payment_source` in the request must match the `payment_source` used for the authorization or capture referenced by `previous_transaction_reference`. Please use `previous_transaction_reference` whose `payment_source` matches with the `payment_source` specified in the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MERCHANT_INITIATED_WITH_SECURITY_CODE";
                      /** @enum {string} */
                      description?: "`stored_payment_source.payment_initiator` = `MERCHANT` is not supported if `payment_source.card.security_code` is present in the order. `security_code` can be present in the order only when customer is the payment initiator. It is semantically incorrect to perform a merchant initiated payment with `security_code` is the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MERCHANT_INITIATED_WITH_AUTHENTICATION_RESULTS";
                      /** @enum {string} */
                      description?: "`stored_payment_source.payment_initiator` = `MERCHANT` is not supported if 3D-Secure authentication results are present in the order. 3D-Secure authentication results can be present in the order only when customer is the payment initiator. It is semantically incorrect to perform a merchant initiated payment with 3D-Secure authentication results is the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MERCHANT_INITIATED_WITH_MULTIPLE_PURCHASE_UNITS";
                      /** @enum {string} */
                      description?: "`stored_payment_source.payment_initiator` = `MERCHANT` is not supported if more than one purchase_unit is present in the Order. Merchant initiated payments are not supported from orders with more than one purchase_unit. Please retry the request with multiple Order requests (one for each purchase_unit).";
                  }
                | {
                      /** @enum {string} */
                      issue?: "RETURN_URL_REQUIRED";
                      /** @enum {string} */
                      description?: "The return url is required when attempting to vault this source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CANCEL_URL_REQUIRED";
                      /** @enum {string} */
                      description?: "The cancel url is required when attempting to vault this source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_ACTION_REQUIRED";
                      /** @enum {string} */
                      description?: "Transaction cannot complete successfully, instruct the buyer to return to PayPal.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "APPLE_PAY_AMOUNT_MISMATCH";
                      /** @enum {string} */
                      description?: "The 'amount' specified in the Order should match the amount that was viewed and authorized by the payer/buyer on Apple Pay. If the amount has changed, please redirect the buyer to authorize the order again via Apple Pay.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_NUMBER_REQUIRED";
                      /** @enum {string} */
                      description?: "The card number is required when attempting to process payment with card.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_EXPIRY_REQUIRED";
                      /** @enum {string} */
                      description?: "The card expiry is required when attempting to process payment with card.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "VAULT_INSTRUCTION_REQUIRED";
                      /** @enum {string} */
                      description?: "Vault instruction is required. Please use `vault.store_in_vault` to provide vault instruction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISMATCHED_VAULT_ID_TO_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "The vault_id does not match the payment_source provided. Please verify that the vault_id token used refers to the matching payment_source and try again. For example, a PayPal token cannot be passed in the vault_id field in the payment_source.card object.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_CANNOT_BE_SAVED";
                      /** @enum {string} */
                      description?: "The option to save an order is only available if the `intent` is AUTHORIZE and `processing_instruction` uses one of the `ORDER_SAVED` options. For example, `intent`=AUTHORIZE, `processing_instruction`=ORDER_SAVED_EXPLICITLY. Please change the intent and/or processing_instruction` and try again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SAVE_ORDER_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "The API caller account is setup in a way that does not allow it to be used for saving the order. This functionality is not available for PayPal Commerce Platform for Platforms & Marketplaces.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_PNREF_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enabled to process payments with the `pnref`. Please contact customer support to request permissions to process transactions with PNREF.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_PAYPAL_TRANSACTION_ID_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enable to process payments using `paypal_transaction_id`. Please contact customer support to request permissions to process transactions with PayPal transaction ID.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYPAL_TRANSACTION_ID_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified `paypal_transaction_id` was not found. Verify the value and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PNREF_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified `pnref` was not found. Verify the value and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_SECURITY_CODE_LENGTH";
                      /** @enum {string} */
                      description?: "The security_code length is invalid for the specified card brand.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REQUIRED_PARAMETER_FOR_CUSTOMER_INITIATED_PAYMENT";
                      /** @enum {string} */
                      description?: 'This parameter is required when the customer is present. If the customer is not present, indicate so by sending payment_initiator=`MERCHANT`. For details, see <a href="https://developer.paypal.com/docs/api/orders/v2/#definition-card_stored_credential">Stored Credential</a>.';
                  }
            )[];
        };
        /**
         * Order Capture Request
         * @description Completes an capture payment for an order.
         */
        order_capture_request: {
            payment_source?: components["schemas"]["payment_source"];
        };
        "orders.capture-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "A parameter value is not valid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field / parameter is missing";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is either too short or too long";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MALFORMED_REQUEST_JSON";
                      /** @enum {string} */
                      description?: "The request JSON is not well formed.";
                  }
            )[];
        };
        "orders.capture-403": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "CONSENT_NEEDED";
                      /** @enum {string} */
                      description?: "CONSENT_NEEDED";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_TOKEN_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enabled to process payments with the specified type of token. Please contact customer support to request permissions to process transactions with this type of token.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PERMISSION_DENIED";
                      /** @enum {string} */
                      description?: "You do not have permission to access or perform operations on this resource.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PERMISSION_DENIED_FOR_DONATION_ITEMS";
                      /** @enum {string} */
                      description?: "The API Caller or Payee have not been granted appropriate permissions to send 'items.category' as 'DONATION'. Please speak to your account manager if you want to process these type of items.";
                  }
            )[];
        };
        "orders.capture-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "AGREEMENT_ALREADY_CANCELLED";
                      /** @enum {string} */
                      description?: "The requested agreement is already canceled.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BILLING_AGREEMENT_NOT_FOUND";
                      /** @enum {string} */
                      description?: "The requested Billing Agreement token was not found.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DECLINED_DUE_TO_RELATED_TXN";
                      /** @enum {string} */
                      description?: "One or more transactions in this Order did not succeed. Since this Order is being processed as an All or None Order, if one or more transactions in this Order do not succeed, then all purchase units are marked declined and will not be processed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_PREVIOUS_REFERENCE";
                      /** @enum {string} */
                      description?: "For Merchant initiated network token transactions, either the payment_source.card.stored_credential.previous_network_transaction_reference or payment_source.card.stored_credential.previous_transaction_reference must be included in the request.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_CRYPTOGRAM";
                      /** @enum {string} */
                      description?: "Cryptogram is mandatory for any customer initiated network token transactions.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_BRAND_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Processing of this card brand is not supported. Please use another card to continue with this transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "COMPLIANCE_VIOLATION";
                      /** @enum {string} */
                      description?: "Transaction is declined due to compliance violation.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DOMESTIC_TRANSACTION_REQUIRED";
                      /** @enum {string} */
                      description?: "This transaction requires the payee and payer to be resident in the same country, a domestic transaction is required to create this payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "DUPLICATE_INVOICE_ID";
                      /** @enum {string} */
                      description?: "Duplicate Invoice ID detected. To avoid a potential duplicate transaction your account setting requires that Invoice Id be unique for each transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INSTRUMENT_DECLINED";
                      /** @enum {string} */
                      description?: "The instrument presented  was either declined by the processor or bank, or it can't be used for this payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_NOT_APPROVED";
                      /** @enum {string} */
                      description?: "Payer has not yet approved the Order for payment. Please redirect the payer to the 'rel':'approve' url returned as part of the HATEOAS links within the Create Order call or provide a valid `payment_source` in the request.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MAX_NUMBER_OF_PAYMENT_ATTEMPTS_EXCEEDED";
                      /** @enum {string} */
                      description?: "You have exceeded the maximum number of payment attempts.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_BLOCKED_TRANSACTION";
                      /** @enum {string} */
                      description?: "The Fraud settings for this seller are such that this payment cannot be executed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_FX_RATE_ID_EXPIRED";
                      /** @enum {string} */
                      description?: "The specified FX Rate ID has expired. Please specify a different FX Rate Id and try the request again. Alternately, remove the FX Rate ID to process the request using the default exchange rate.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_ACCOUNT_LOCKED_OR_CLOSED";
                      /** @enum {string} */
                      description?: "The payer account cannot be used for this transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_ACCOUNT_RESTRICTED";
                      /** @enum {string} */
                      description?: "PAYER_ACCOUNT_RESTRICTED";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_CANNOT_PAY";
                      /** @enum {string} */
                      description?: "Payer cannot pay for this transaction. Please contact the payer to find other ways to pay for this transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYPAL_TRANSACTION_ID_EXPIRED";
                      /** @enum {string} */
                      description?: "Specified `paypal_transaction_id` has expired. PayPal transaction ID expires 4 years after the date of the initial transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PNREF_EXPIRED";
                      /** @enum {string} */
                      description?: "Specified `pnref` has expired. PNREF expires 15 months after the date of the initial transaction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REFERENCED_CARD_EXPIRED";
                      /** @enum {string} */
                      description?: "The card underlying the token has expired and hence cannot be used to process a payment.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TOKEN_ID_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified token was not found. Verify the token and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_LIMIT_EXCEEDED";
                      /** @enum {string} */
                      description?: "Total payment amount exceeded transaction limit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_RECEIVING_LIMIT_EXCEEDED";
                      /** @enum {string} */
                      description?: "The transaction exceeds the receiver's receiving limit.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_REFUSED";
                      /** @enum {string} */
                      description?: "The request was refused.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REDIRECT_PAYER_FOR_ALTERNATE_FUNDING";
                      /** @enum {string} */
                      description?: "Transaction failed. Redirect the payer to select another funding source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_ALREADY_CAPTURED";
                      /** @enum {string} */
                      description?: "Order already captured.If 'intent=CAPTURE' only one capture per order is allowed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "TRANSACTION_BLOCKED_BY_PAYEE";
                      /** @enum {string} */
                      description?: "Transaction blocked by Payee’s Fraud Protection settings.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "AUTH_CAPTURE_NOT_ENABLED";
                      /** @enum {string} */
                      description?: "Authorization and Capture feature is not enabled for the merchant. Make sure that the recipient of the funds is a verified business account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ENABLED_FOR_BANK_PROCESSING";
                      /** @enum {string} */
                      description?: "The API Caller account is not setup to be able to process bank payments. Please contact your PayPal account manager.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ENABLED_FOR_CARD_PROCESSING";
                      /** @enum {string} */
                      description?: "The API Caller account is not setup to be able to process card payments. Please contact PayPal customer support.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_NOT_ENABLED_FOR_BANK_PROCESSING";
                      /** @enum {string} */
                      description?: "Payee account is not setup to be able to process bank payments. Please contact your PayPal account manager.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYEE_NOT_ENABLED_FOR_CARD_PROCESSING";
                      /** @enum {string} */
                      description?: "Payee account is not setup to be able to process card payments. Please contact PayPal customer support.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PICKUP_ADDRESS";
                      /** @enum {string} */
                      description?: "If the 'shipping_option.type' is set as 'PICKUP' then the 'shipping_detail.name.full_name' should start with 'S2S' meaning Ship To Store. Example: 'S2S My Store'.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SHIPPING_ADDRESS_INVALID";
                      /** @enum {string} */
                      description?: "Provided shipping address is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "The payer selected method of payment is not supported when multiple purchase units are specified for an Order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ORDER_COMPLETION_IN_PROGRESS";
                      /** @enum {string} */
                      description?: "The order was created with processing_instruction of ORDER_COMPLETE_ON_PAYMENT_APPROVAL. The customer has approved the payment and PayPal is still in the process of capturing the order on your behalf as instructed. Please try your request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BILLING_AGREEMENT_ID_MISMATCH";
                      /** @enum {string} */
                      description?: "Billing Agreement ID must exactly match the Billing Agreement ID that was provided during order creation.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREFERRED_PAYMENT_SOURCE_MISMATCH";
                      /** @enum {string} */
                      description?: "Payment Source must exactly match the Preferred Payment Source that was provided during order creation.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INCOMPATIBLE_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of the field is incompatible/redundant with other fields in the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PREVIOUS_TRANSACTION_REFERENCE";
                      /** @enum {string} */
                      description?: "The authorization or capture referenced by `previous_transaction_reference` is not valid. This could be either because the previous_transaction_reference is not found or doesn't belong to the payee. Please use a valid `previous_transaction_reference`.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREVIOUS_TRANSACTION_REFERENCE_HAS_CHARGEBACK";
                      /** @enum {string} */
                      description?: "The capture referenced by `previous_transaction_reference` has a chargeback and hence cannot be used for this order. Please use a `previous_transaction_reference` which does not have a chargeback.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PREVIOUS_TRANSACTION_REFERENCE_VOIDED";
                      /** @enum {string} */
                      description?: "The status of authorization referenced by `previous_transaction_reference` is `VOIDED` and hence cannot be used for this order. Please use a `previous_transaction_reference` whose status is not `VOIDED`.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYMENT_SOURCE_MISMATCH";
                      /** @enum {string} */
                      description?: "The `payment_source` in the request must match the `payment_source` used for the authorization or capture referenced by `previous_transaction_reference`. Please use `previous_transaction_reference` whose `payment_source` matches with the `payment_source` specified in the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MERCHANT_INITIATED_WITH_SECURITY_CODE";
                      /** @enum {string} */
                      description?: "`stored_payment_source.payment_initiator` = `MERCHANT` is not supported if `payment_source.card.security_code` is present in the order. `security_code` can be present in the order only when customer is the payment initiator. It is semantically incorrect to perform a merchant initiated payment with `security_code` is the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MERCHANT_INITIATED_WITH_AUTHENTICATION_RESULTS";
                      /** @enum {string} */
                      description?: "`stored_payment_source.payment_initiator` = `MERCHANT` is not supported if 3D-Secure authentication results are present in the order. 3D-Secure authentication results can be present in the order only when customer is the payment initiator. It is semantically incorrect to perform a merchant initiated payment with 3D-Secure authentication results is the order.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MERCHANT_INITIATED_WITH_MULTIPLE_PURCHASE_UNITS";
                      /** @enum {string} */
                      description?: "`stored_payment_source.payment_initiator` = `MERCHANT` is not supported if more than one purchase_unit is present in the Order. Merchant initiated payments are not supported from orders with more than one purchase_unit. Please retry the request with multiple Order requests (one for each purchase_unit).";
                  }
                | {
                      /** @enum {string} */
                      issue?: "RETURN_URL_REQUIRED";
                      /** @enum {string} */
                      description?: "The return url is required when attempting to vault this source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CANCEL_URL_REQUIRED";
                      /** @enum {string} */
                      description?: "The cancel url is required when attempting to vault this source.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "SETUP_ERROR_FOR_BANK";
                      /** @enum {string} */
                      description?: "The API Caller account setup, for bank payments, is incomplete or incorrect. Please contact your PayPal account manager.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "BANK_NOT_SUPPORTED_FOR_VERIFICATION";
                      /** @enum {string} */
                      description?: "Verification for this bank account is not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYER_ACTION_REQUIRED";
                      /** @enum {string} */
                      description?: "Transaction cannot complete successfully, instruct the buyer to return to PayPal.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "APPLE_PAY_AMOUNT_MISMATCH";
                      /** @enum {string} */
                      description?: "The 'amount' specified in the Order should match the amount that was viewed and authorized by the payer/buyer on Apple Pay. If the amount has changed, please redirect the buyer to authorize the order again via Apple Pay.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CURRENCY_NOT_SUPPORTED_FOR_BANK";
                      /** @enum {string} */
                      description?: "The payment_source does not support the currency of the Order. For ACH debit, only USD is supported and for SEPA debit, only EUR is supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ONLY_ONE_BANK_SOURCE_ALLOWED";
                      /** @enum {string} */
                      description?: "More than one payment method within the bank payment object is not supported.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_IBAN";
                      /** @enum {string} */
                      description?: "IBAN provided is not a valid bank account number.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "IBAN_COUNTRY_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Country code of issuer bank for the provided IBAN is not supported for SEPA debit payments.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_NUMBER_REQUIRED";
                      /** @enum {string} */
                      description?: "The card number is required when attempting to process payment with card.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CARD_EXPIRY_REQUIRED";
                      /** @enum {string} */
                      description?: "The card expiry is required when attempting to process payment with card.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "VAULT_INSTRUCTION_REQUIRED";
                      /** @enum {string} */
                      description?: "Vault instruction is required. Please use `vault.store_in_vault` to provide vault instruction.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISMATCHED_VAULT_ID_TO_PAYMENT_SOURCE";
                      /** @enum {string} */
                      description?: "The vault_id does not match the payment_source provided. Please verify that the vault_id token used refers to the matching payment_source and try again. For example, a PayPal token cannot be passed in the vault_id field in the payment_source.card object.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_PNREF_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enabled to process payments with the `pnref`. Please contact customer support to request permissions to process transactions with PNREF.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_ELIGIBLE_FOR_PAYPAL_TRANSACTION_ID_PROCESSING";
                      /** @enum {string} */
                      description?: "API caller is not enable to process payments using `paypal_transaction_id`. Please contact customer support to request permissions to process transactions with PayPal transaction ID.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PAYPAL_TRANSACTION_ID_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified `paypal_transaction_id` was not found. Verify the value and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PNREF_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified `pnref` was not found. Verify the value and try the request again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_SECURITY_CODE_LENGTH";
                      /** @enum {string} */
                      description?: "The security_code length is invalid for the specified card brand.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PLATFORM_FEE_PAYEE_CANNOT_BE_SAME_AS_PAYER";
                      /** @enum {string} */
                      description?: "The payer cannot pay themselves. The recipient account of the platform fees must be different from the payer account.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "REQUIRED_PARAMETER_FOR_CUSTOMER_INITIATED_PAYMENT";
                      /** @enum {string} */
                      description?: 'This parameter is required when the customer is present. If the customer is not present, indicate so by sending payment_initiator=`MERCHANT`. For details, see <a href="https://developer.paypal.com/docs/api/orders/v2/#definition-card_stored_credential">Stored Credential</a>.';
                  }
                | {
                      /** @enum {string} */
                      issue?: "IDENTIFIER_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified identifier was not found. Please verify the correct identifier was used and try the request again.";
                  }
            )[];
        };
        /**
         * Shipment Tracking Number Type.
         * @description The tracking number type.
         * @enum {string}
         */
        shipment_tracking_number_type:
            | "CARRIER_PROVIDED"
            | "E2E_PARTNER_PROVIDED";
        /**
         * Shipment Tracking Status.
         * @description The status of the item shipment. For allowed values, see <a href="/docs/tracking/reference/shipping-status/">Shipping Statuses</a>.
         * @enum {string}
         */
        shipment_tracking_status:
            | "CANCELLED"
            | "DELIVERED"
            | "LOCAL_PICKUP"
            | "ON_HOLD"
            | "SHIPPED"
            | "SHIPMENT_CREATED"
            | "DROPPED_OFF"
            | "IN_TRANSIT"
            | "RETURNED"
            | "LABEL_PRINTED"
            | "ERROR"
            | "UNCONFIRMED"
            | "PICKUP_FAILED"
            | "DELIVERY_DELAYED"
            | "DELIVERY_SCHEDULED"
            | "DELIVERY_FAILED"
            | "INRETURN"
            | "IN_PROCESS"
            | "NEW"
            | "VOID"
            | "PROCESSED"
            | "NOT_SHIPPED"
            | "COMPLETED";
        /**
         * Carrier.
         * @description The carrier for the shipment. Some carriers have a global version as well as local subsidiaries. The subsidiaries are repeated over many countries and might also have an entry in the global list. Choose the carrier for your country. If the carrier is not available for your country, choose the global version of the carrier. If your carrier name is not in the list, set `carrier` to `OTHER` and set carrier name in `carrier_name_other`. For allowed values, see <a href="/docs/tracking/reference/carriers/">Carriers</a>.
         * @enum {string}
         */
        shipment_carrier:
            | "DPD_RU"
            | "BG_BULGARIAN_POST"
            | "KR_KOREA_POST"
            | "ZA_COURIERIT"
            | "FR_EXAPAQ"
            | "ARE_EMIRATES_POST"
            | "GAC"
            | "GEIS"
            | "SF_EX"
            | "PAGO"
            | "MYHERMES"
            | "DIAMOND_EUROGISTICS"
            | "CORPORATECOURIERS_WEBHOOK"
            | "BOND"
            | "OMNIPARCEL"
            | "SK_POSTA"
            | "PUROLATOR"
            | "FETCHR_WEBHOOK"
            | "THEDELIVERYGROUP"
            | "CELLO_SQUARE"
            | "TARRIVE"
            | "COLLIVERY"
            | "MAINFREIGHT"
            | "IND_FIRSTFLIGHT"
            | "ACSWORLDWIDE"
            | "AMSTAN"
            | "OKAYPARCEL"
            | "ENVIALIA_REFERENCE"
            | "SEUR_ES"
            | "CONTINENTAL"
            | "FDSEXPRESS"
            | "AMAZON_FBA_SWISHIP"
            | "WYNGS"
            | "DHL_ACTIVE_TRACING"
            | "ZYLLEM"
            | "RUSTON"
            | "XPOST"
            | "CORREOS_ES"
            | "DHL_FR"
            | "PAN_ASIA"
            | "BRT_IT"
            | "SRE_KOREA"
            | "SPEEDEE"
            | "TNT_UK"
            | "VENIPAK"
            | "SHREENANDANCOURIER"
            | "CROSHOT"
            | "NIPOST_NG"
            | "EPST_GLBL"
            | "NEWGISTICS"
            | "POST_SLOVENIA"
            | "JERSEY_POST"
            | "BOMBINOEXP"
            | "WMG"
            | "XQ_EXPRESS"
            | "FURDECO"
            | "LHT_EXPRESS"
            | "SOUTH_AFRICAN_POST_OFFICE"
            | "SPOTON"
            | "DIMERCO"
            | "CYPRUS_POST_CYP"
            | "ABCUSTOM"
            | "IND_DELIVREE"
            | "CN_BESTEXPRESS"
            | "DX_SFTP"
            | "PICKUPP_MYS"
            | "FMX"
            | "HELLMANN"
            | "SHIP_IT_ASIA"
            | "KERRY_ECOMMERCE"
            | "FRETERAPIDO"
            | "PITNEY_BOWES"
            | "XPRESSEN_DK"
            | "SEUR_SP_API"
            | "DELIVERYONTIME"
            | "JINSUNG"
            | "TRANS_KARGO"
            | "SWISHIP_DE"
            | "IVOY_WEBHOOK"
            | "AIRMEE_WEBHOOK"
            | "DHL_BENELUX"
            | "FIRSTMILE"
            | "FASTWAY_IR"
            | "HH_EXP"
            | "MYS_MYPOST_ONLINE"
            | "TNT_NL"
            | "TIPSA"
            | "TAQBIN_MY"
            | "KGMHUB"
            | "INTEXPRESS"
            | "OVERSE_EXP"
            | "ONECLICK"
            | "ROADRUNNER_FREIGHT"
            | "GLS_CROTIA"
            | "MRW_FTP"
            | "BLUEX"
            | "DYLT"
            | "DPD_IR"
            | "SIN_GLBL"
            | "TUFFNELLS_REFERENCE"
            | "CJPACKET"
            | "MILKMAN"
            | "ASIGNA"
            | "ONEWORLDEXPRESS"
            | "ROYAL_MAIL"
            | "VIA_EXPRESS"
            | "TIGFREIGHT"
            | "ZTO_EXPRESS"
            | "TWO_GO"
            | "IML"
            | "INTEL_VALLEY"
            | "EFS"
            | "UK_UK_MAIL"
            | "RAM"
            | "ALLIEDEXPRESS"
            | "APC_OVERNIGHT"
            | "SHIPPIT"
            | "TFM"
            | "M_XPRESS"
            | "HDB_BOX"
            | "CLEVY_LINKS"
            | "IBEONE"
            | "FIEGE_NL"
            | "KWE_GLOBAL"
            | "CTC_EXPRESS"
            | "LAO_POST"
            | "AMAZON"
            | "MORE_LINK"
            | "JX"
            | "EASY_MAIL"
            | "ADUIEPYLE"
            | "GB_PANTHER"
            | "EXPRESSSALE"
            | "SG_DETRACK"
            | "TRUNKRS_WEBHOOK"
            | "MATDESPATCH"
            | "DICOM"
            | "MBW"
            | "KHM_CAMBODIA_POST"
            | "SINOTRANS"
            | "BRT_IT_PARCELID"
            | "DHL_SUPPLY_CHAIN"
            | "DHL_PL"
            | "TOPYOU"
            | "PALEXPRESS"
            | "DHL_SG"
            | "CN_WEDO"
            | "FULFILLME"
            | "DPD_DELISTRACK"
            | "UPS_REFERENCE"
            | "CARIBOU"
            | "LOCUS_WEBHOOK"
            | "DSV"
            | "CN_GOFLY"
            | "P2P_TRC"
            | "DIRECTPARCELS"
            | "NOVA_POSHTA_INT"
            | "FEDEX_POLAND"
            | "CN_JCEX"
            | "FAR_INTERNATIONAL"
            | "IDEXPRESS"
            | "GANGBAO"
            | "NEWAY"
            | "POSTNL_INT_3_S"
            | "RPX_ID"
            | "DESIGNERTRANSPORT_WEBHOOK"
            | "GLS_SLOVEN"
            | "PARCELLED_IN"
            | "GSI_EXPRESS"
            | "CON_WAY"
            | "BROUWER_TRANSPORT"
            | "CPEX"
            | "ISRAEL_POST"
            | "DTDC_IN"
            | "PTT_POST"
            | "XDE_WEBHOOK"
            | "TOLOS"
            | "GIAO_HANG"
            | "GEODIS_ESPACE"
            | "MAGYAR_HU"
            | "DOORDASH_WEBHOOK"
            | "TIKI_ID"
            | "CJ_HK_INTERNATIONAL"
            | "STAR_TRACK_EXPRESS"
            | "HELTHJEM"
            | "SFB2C"
            | "FREIGHTQUOTE"
            | "LANDMARK_GLOBAL_REFERENCE"
            | "PARCEL2GO"
            | "DELNEXT"
            | "RCL"
            | "CGS_EXPRESS"
            | "HK_POST"
            | "SAP_EXPRESS"
            | "PARCELPOST_SG"
            | "HERMES"
            | "IND_SAFEEXPRESS"
            | "TOPHATTEREXPRESS"
            | "MGLOBAL"
            | "AVERITT"
            | "LEADER"
            | "_2EBOX"
            | "SG_SPEEDPOST"
            | "DBSCHENKER_SE"
            | "ISR_POST_DOMESTIC"
            | "BESTWAYPARCEL"
            | "ASENDIA_DE"
            | "NIGHTLINE_UK"
            | "TAQBIN_SG"
            | "TCK_EXPRESS"
            | "ENDEAVOUR_DELIVERY"
            | "NANJINGWOYUAN"
            | "HEPPNER_FR"
            | "EMPS_CN"
            | "FONSEN"
            | "PICKRR"
            | "APC_OVERNIGHT_CONNUM"
            | "STAR_TRACK_NEXT_FLIGHT"
            | "DAJIN"
            | "UPS_FREIGHT"
            | "POSTA_PLUS"
            | "CEVA"
            | "ANSERX"
            | "JS_EXPRESS"
            | "PADTF"
            | "UPS_MAIL_INNOVATIONS"
            | "EZSHIP"
            | "SYPOST"
            | "AMAZON_SHIP_MCF"
            | "YUSEN"
            | "BRING"
            | "SDA_IT"
            | "GBA"
            | "NEWEGGEXPRESS"
            | "SPEEDCOURIERS_GR"
            | "FORRUN"
            | "PICKUP"
            | "ECMS"
            | "INTELIPOST"
            | "FLASHEXPRESS"
            | "CN_STO"
            | "SEKO_SFTP"
            | "HOME_DELIVERY_SOLUTIONS"
            | "DPD_HGRY"
            | "KERRYTTC_VN"
            | "JOYING_BOX"
            | "TOTAL_EXPRESS"
            | "ZJS_EXPRESS"
            | "STARKEN"
            | "DEMANDSHIP"
            | "CN_DPEX"
            | "AUPOST_CN"
            | "LOGISTERS"
            | "GOGLOBALPOST"
            | "GLS_CZ"
            | "PAACK_WEBHOOK"
            | "GRAB_WEBHOOK"
            | "PARCELPOINT"
            | "ICUMULUS"
            | "DAIGLOBALTRACK"
            | "GLOBAL_IPARCEL"
            | "YURTICI_KARGO"
            | "CN_PAYPAL_PACKAGE"
            | "PARCEL_2_POST"
            | "GLS_IT"
            | "PIL_LOGISTICS"
            | "HEPPNER"
            | "GENERAL_OVERNIGHT"
            | "HAPPY2POINT"
            | "CHITCHATS"
            | "SMOOTH"
            | "CLE_LOGISTICS"
            | "FIEGE"
            | "MX_CARGO"
            | "ZIINGFINALMILE"
            | "DAYTON_FREIGHT"
            | "TCS"
            | "AEX"
            | "HERMES_DE"
            | "ROUTIFIC_WEBHOOK"
            | "GLOBAVEND"
            | "CJ_LOGISTICS"
            | "PALLET_NETWORK"
            | "RAF_PH"
            | "UK_XDP"
            | "PAPER_EXPRESS"
            | "LA_POSTE_SUIVI"
            | "PAQUETEXPRESS"
            | "LIEFERY"
            | "STRECK_TRANSPORT"
            | "PONY_EXPRESS"
            | "ALWAYS_EXPRESS"
            | "GBS_BROKER"
            | "CITYLINK_MY"
            | "ALLJOY"
            | "YODEL"
            | "YODEL_DIR"
            | "STONE3PL"
            | "PARCELPAL_WEBHOOK"
            | "DHL_ECOMERCE_ASA"
            | "SIMPLYPOST"
            | "KY_EXPRESS"
            | "SHENZHEN"
            | "US_LASERSHIP"
            | "UC_EXPRE"
            | "DIDADI"
            | "CJ_KR"
            | "DBSCHENKER_B2B"
            | "MXE"
            | "CAE_DELIVERS"
            | "PFCEXPRESS"
            | "WHISTL"
            | "WEPOST"
            | "DHL_PARCEL_ES"
            | "DDEXPRESS"
            | "ARAMEX_AU"
            | "BNEED"
            | "HK_TGX"
            | "LATVIJAS_PASTS"
            | "VIAEUROPE"
            | "CORREO_UY"
            | "CHRONOPOST_FR"
            | "J_NET"
            | "_6LS"
            | "BLR_BELPOST"
            | "BIRDSYSTEM"
            | "DOBROPOST"
            | "WAHANA_ID"
            | "WEASHIP"
            | "SONICTL"
            | "KWT"
            | "AFLLOG_FTP"
            | "SKYNET_WORLDWIDE"
            | "NOVA_POSHTA"
            | "SEINO"
            | "SZENDEX"
            | "BPOST_INT"
            | "DBSCHENKER_SV"
            | "AO_DEUTSCHLAND"
            | "EU_FLEET_SOLUTIONS"
            | "PCFCORP"
            | "LINKBRIDGE"
            | "PRIMAMULTICIPTA"
            | "COUREX"
            | "ZAJIL_EXPRESS"
            | "COLLECTCO"
            | "JTEXPRESS"
            | "FEDEX_UK"
            | "USHIP"
            | "PIXSELL"
            | "SHIPTOR"
            | "CDEK"
            | "VNM_VIETTELPOST"
            | "CJ_CENTURY"
            | "GSO"
            | "VIWO"
            | "SKYBOX"
            | "KERRYTJ"
            | "NTLOGISTICS_VN"
            | "SDH_SCM"
            | "ZINC"
            | "DPE_SOUTH_AFRC"
            | "CESKA_CZ"
            | "ACS_GR"
            | "DEALERSEND"
            | "JOCOM"
            | "CSE"
            | "TFORCE_FINALMILE"
            | "SHIP_GATE"
            | "SHIPTER"
            | "NATIONAL_SAMEDAY"
            | "YUNEXPRESS"
            | "CAINIAO"
            | "DMS_MATRIX"
            | "DIRECTLOG"
            | "ASENDIA_US"
            | "_3JMSLOGISTICS"
            | "LICCARDI_EXPRESS"
            | "SKY_POSTAL"
            | "CNWANGTONG"
            | "POSTNORD_LOGISTICS_DK"
            | "LOGISTIKA"
            | "CELERITAS"
            | "PRESSIODE"
            | "SHREE_MARUTI"
            | "LOGISTICSWORLDWIDE_HK"
            | "EFEX"
            | "LOTTE"
            | "LONESTAR"
            | "APRISAEXPRESS"
            | "BEL_RS"
            | "OSM_WORLDWIDE"
            | "WESTGATE_GL"
            | "FASTRACK"
            | "DTD_EXPR"
            | "ALFATREX"
            | "PROMEDDELIVERY"
            | "THABIT_LOGISTICS"
            | "HCT_LOGISTICS"
            | "CARRY_FLAP"
            | "US_OLD_DOMINION"
            | "ANICAM_BOX"
            | "WANBEXPRESS"
            | "AN_POST"
            | "DPD_LOCAL"
            | "STALLIONEXPRESS"
            | "RAIDEREX"
            | "SHOPFANS"
            | "KYUNGDONG_PARCEL"
            | "CHAMPION_LOGISTICS"
            | "PICKUPP_SGP"
            | "MORNING_EXPRESS"
            | "NACEX"
            | "THENILE_WEBHOOK"
            | "HOLISOL"
            | "LBCEXPRESS_FTP"
            | "KURASI"
            | "USF_REDDAWAY"
            | "APG"
            | "CN_BOXC"
            | "ECOSCOOTING"
            | "MAINWAY"
            | "PAPERFLY"
            | "HOUNDEXPRESS"
            | "BOX_BERRY"
            | "EP_BOX"
            | "PLUS_LOG_UK"
            | "FULFILLA"
            | "ASE"
            | "MAIL_PLUS"
            | "XPO_LOGISTICS"
            | "WNDIRECT"
            | "CLOUDWISH_ASIA"
            | "ZELERIS"
            | "GIO_EXPRESS"
            | "OCS_WORLDWIDE"
            | "ARK_LOGISTICS"
            | "AQUILINE"
            | "PILOT_FREIGHT"
            | "QWINTRY"
            | "DANSKE_FRAGT"
            | "CARRIERS"
            | "AIR_CANADA_GLOBAL"
            | "PRESIDENT_TRANS"
            | "STEPFORWARDFS"
            | "SKYNET_UK"
            | "PITTOHIO"
            | "CORREOS_EXPRESS"
            | "RL_US"
            | "MARA_XPRESS"
            | "DESTINY"
            | "UK_YODEL"
            | "COMET_TECH"
            | "DHL_PARCEL_RU"
            | "TNT_REFR"
            | "SHREE_ANJANI_COURIER"
            | "MIKROPAKKET_BE"
            | "ETS_EXPRESS"
            | "COLIS_PRIVE"
            | "CN_YUNDA"
            | "AAA_COOPER"
            | "ROCKET_PARCEL"
            | "_360LION"
            | "PANDU"
            | "PROFESSIONAL_COURIERS"
            | "FLYTEXPRESS"
            | "LOGISTICSWORLDWIDE_MY"
            | "CORREOS_DE_ESPANA"
            | "IMX"
            | "FOUR_PX_EXPRESS"
            | "XPRESSBEES"
            | "PICKUPP_VNM"
            | "STARTRACK_EXPRESS"
            | "FR_COLISSIMO"
            | "NACEX_SPAIN_REFERENCE"
            | "DHL_SUPPLY_CHAIN_AU"
            | "ESHIPPING"
            | "SHREETIRUPATI"
            | "HX_EXPRESS"
            | "INDOPAKET"
            | "CN_17POST"
            | "K1_EXPRESS"
            | "CJ_GLS"
            | "MYS_GDEX"
            | "NATIONEX"
            | "ANJUN"
            | "FARGOOD"
            | "SMG_EXPRESS"
            | "RZYEXPRESS"
            | "SEFL"
            | "TNT_CLICK_IT"
            | "HDB"
            | "HIPSHIPPER"
            | "RPXLOGISTICS"
            | "KUEHNE"
            | "IT_NEXIVE"
            | "PTS"
            | "SWISS_POST_FTP"
            | "FASTRK_SERV"
            | "_4_72"
            | "US_YRC"
            | "POSTNL_INTL_3S"
            | "ELIAN_POST"
            | "CUBYN"
            | "SAU_SAUDI_POST"
            | "ABXEXPRESS_MY"
            | "HUAHAN_EXPRESS"
            | "IND_JAYONEXPRESS"
            | "ZES_EXPRESS"
            | "ZEPTO_EXPRESS"
            | "SKYNET_ZA"
            | "ZEEK_2_DOOR"
            | "BLINKLASTMILE"
            | "POSTA_UKR"
            | "CHROBINSON"
            | "CN_POST56"
            | "COURANT_PLUS"
            | "SCUDEX_EXPRESS"
            | "SHIPENTEGRA"
            | "B_TWO_C_EUROPE"
            | "COPE"
            | "IND_GATI"
            | "CN_WISHPOST"
            | "NACEX_ES"
            | "TAQBIN_HK"
            | "GLOBALTRANZ"
            | "HKD"
            | "BJSHOMEDELIVERY"
            | "OMNIVA"
            | "SUTTON"
            | "PANTHER_REFERENCE"
            | "SFCSERVICE"
            | "LTL"
            | "PARKNPARCEL"
            | "SPRING_GDS"
            | "ECEXPRESS"
            | "INTERPARCEL_AU"
            | "AGILITY"
            | "XL_EXPRESS"
            | "ADERONLINE"
            | "DIRECTCOURIERS"
            | "PLANZER"
            | "SENDING"
            | "NINJAVAN_WB"
            | "NATIONWIDE_MY"
            | "SENDIT"
            | "GB_ARROW"
            | "IND_GOJAVAS"
            | "KPOST"
            | "DHL_FREIGHT"
            | "BLUECARE"
            | "JINDOUYUN"
            | "TRACKON"
            | "GB_TUFFNELLS"
            | "TRUMPCARD"
            | "ETOTAL"
            | "SFPLUS_WEBHOOK"
            | "SEKOLOGISTICS"
            | "HERMES_2MANN_HANDLING"
            | "DPD_LOCAL_REF"
            | "UDS"
            | "ZA_SPECIALISED_FREIGHT"
            | "THA_KERRY"
            | "PRT_INT_SEUR"
            | "BRA_CORREIOS"
            | "NZ_NZ_POST"
            | "CN_EQUICK"
            | "MYS_EMS"
            | "GB_NORSK"
            | "ESP_MRW"
            | "ESP_PACKLINK"
            | "KANGAROO_MY"
            | "RPX"
            | "XDP_UK_REFERENCE"
            | "NINJAVAN_MY"
            | "ADICIONAL"
            | "NINJAVAN_ID"
            | "ROADBULL"
            | "YAKIT"
            | "MAILAMERICAS"
            | "MIKROPAKKET"
            | "DYNALOGIC"
            | "DHL_ES"
            | "DHL_PARCEL_NL"
            | "DHL_GLOBAL_MAIL_ASIA"
            | "DAWN_WING"
            | "GENIKI_GR"
            | "HERMESWORLD_UK"
            | "ALPHAFAST"
            | "BUYLOGIC"
            | "EKART"
            | "MEX_SENDA"
            | "SFC_LOGISTICS"
            | "POST_SERBIA"
            | "IND_DELHIVERY"
            | "DE_DPD_DELISTRACK"
            | "RPD2MAN"
            | "CN_SF_EXPRESS"
            | "YANWEN"
            | "MYS_SKYNET"
            | "CORREOS_DE_MEXICO"
            | "CBL_LOGISTICA"
            | "MEX_ESTAFETA"
            | "AU_AUSTRIAN_POST"
            | "RINCOS"
            | "NLD_DHL"
            | "RUSSIAN_POST"
            | "COURIERS_PLEASE"
            | "POSTNORD_LOGISTICS"
            | "FEDEX"
            | "DPE_EXPRESS"
            | "DPD"
            | "ADSONE"
            | "IDN_JNE"
            | "THECOURIERGUY"
            | "CNEXPS"
            | "PRT_CHRONOPOST"
            | "LANDMARK_GLOBAL"
            | "IT_DHL_ECOMMERCE"
            | "ESP_NACEX"
            | "PRT_CTT"
            | "BE_KIALA"
            | "ASENDIA_UK"
            | "GLOBAL_TNT"
            | "POSTUR_IS"
            | "EPARCEL_KR"
            | "INPOST_PACZKOMATY"
            | "IT_POSTE_ITALIA"
            | "BE_BPOST"
            | "PL_POCZTA_POLSKA"
            | "MYS_MYS_POST"
            | "SG_SG_POST"
            | "THA_THAILAND_POST"
            | "LEXSHIP"
            | "FASTWAY_NZ"
            | "DHL_AU"
            | "COSTMETICSNOW"
            | "PFLOGISTICS"
            | "LOOMIS_EXPRESS"
            | "GLS_ITALY"
            | "LINE"
            | "GEL_EXPRESS"
            | "HUODULL"
            | "NINJAVAN_SG"
            | "JANIO"
            | "AO_COURIER"
            | "BRT_IT_SENDER_REF"
            | "SAILPOST"
            | "LALAMOVE"
            | "NEWZEALAND_COURIERS"
            | "ETOMARS"
            | "VIRTRANSPORT"
            | "WIZMO"
            | "PALLETWAYS"
            | "I_DIKA"
            | "CFL_LOGISTICS"
            | "GEMWORLDWIDE"
            | "GLOBAL_EXPRESS"
            | "LOGISTYX_TRANSGROUP"
            | "WESTBANK_COURIER"
            | "ARCO_SPEDIZIONI"
            | "YDH_EXPRESS"
            | "PARCELINKLOGISTICS"
            | "CNDEXPRESS"
            | "NOX_NIGHT_TIME_EXPRESS"
            | "AERONET"
            | "LTIANEXP"
            | "INTEGRA2_FTP"
            | "PARCELONE"
            | "NOX_NACHTEXPRESS"
            | "CN_CHINA_POST_EMS"
            | "CHUKOU1"
            | "GLS_SLOV"
            | "ORANGE_DS"
            | "JOOM_LOGIS"
            | "AUS_STARTRACK"
            | "DHL"
            | "GB_APC"
            | "BONDSCOURIERS"
            | "JPN_JAPAN_POST"
            | "USPS"
            | "WINIT"
            | "ARG_OCA"
            | "TW_TAIWAN_POST"
            | "DMM_NETWORK"
            | "TNT"
            | "BH_POSTA"
            | "SWE_POSTNORD"
            | "CA_CANADA_POST"
            | "WISELOADS"
            | "ASENDIA_HK"
            | "NLD_GLS"
            | "MEX_REDPACK"
            | "JET_SHIP"
            | "DE_DHL_EXPRESS"
            | "NINJAVAN_THAI"
            | "RABEN_GROUP"
            | "ESP_ASM"
            | "HRV_HRVATSKA"
            | "GLOBAL_ESTES"
            | "LTU_LIETUVOS"
            | "BEL_DHL"
            | "AU_AU_POST"
            | "SPEEDEXCOURIER"
            | "FR_COLIS"
            | "ARAMEX"
            | "DPEX"
            | "MYS_AIRPAK"
            | "CUCKOOEXPRESS"
            | "DPD_POLAND"
            | "NLD_POSTNL"
            | "NIM_EXPRESS"
            | "QUANTIUM"
            | "SENDLE"
            | "ESP_REDUR"
            | "MATKAHUOLTO"
            | "CPACKET"
            | "POSTI"
            | "HUNTER_EXPRESS"
            | "CHOIR_EXP"
            | "LEGION_EXPRESS"
            | "AUSTRIAN_POST_EXPRESS"
            | "GRUPO"
            | "POSTA_RO"
            | "INTERPARCEL_UK"
            | "GLOBAL_ABF"
            | "POSTEN_NORGE"
            | "XPERT_DELIVERY"
            | "DHL_REFR"
            | "DHL_HK"
            | "SKYNET_UAE"
            | "GOJEK"
            | "YODEL_INTNL"
            | "JANCO"
            | "YTO"
            | "WISE_EXPRESS"
            | "JTEXPRESS_VN"
            | "FEDEX_INTL_MLSERV"
            | "VAMOX"
            | "AMS_GRP"
            | "DHL_JP"
            | "HRPARCEL"
            | "GESWL"
            | "BLUESTAR"
            | "CDEK_TR"
            | "DESCARTES"
            | "DELTEC_UK"
            | "DTDC_EXPRESS"
            | "TOURLINE"
            | "BH_WORLDWIDE"
            | "OCS"
            | "YINGNUO_LOGISTICS"
            | "UPS"
            | "TOLL"
            | "PRT_SEUR"
            | "DTDC_AU"
            | "THA_DYNAMIC_LOGISTICS"
            | "UBI_LOGISTICS"
            | "FEDEX_CROSSBORDER"
            | "A1POST"
            | "TAZMANIAN_FREIGHT"
            | "CJ_INT_MY"
            | "SAIA_FREIGHT"
            | "SG_QXPRESS"
            | "NHANS_SOLUTIONS"
            | "DPD_FR"
            | "COORDINADORA"
            | "ANDREANI"
            | "DOORA"
            | "INTERPARCEL_NZ"
            | "PHL_JAMEXPRESS"
            | "BEL_BELGIUM_POST"
            | "US_APC"
            | "IDN_POS"
            | "FR_MONDIAL"
            | "DE_DHL"
            | "HK_RPX"
            | "DHL_PIECEID"
            | "VNPOST_EMS"
            | "RRDONNELLEY"
            | "DPD_DE"
            | "DELCART_IN"
            | "IMEXGLOBALSOLUTIONS"
            | "ACOMMERCE"
            | "EURODIS"
            | "CANPAR"
            | "GLS"
            | "IND_ECOM"
            | "ESP_ENVIALIA"
            | "DHL_UK"
            | "SMSA_EXPRESS"
            | "TNT_FR"
            | "DEX_I"
            | "BUDBEE_WEBHOOK"
            | "COPA_COURIER"
            | "VNM_VIETNAM_POST"
            | "DPD_HK"
            | "TOLL_NZ"
            | "ECHO"
            | "FEDEX_FR"
            | "BORDEREXPRESS"
            | "MAILPLUS_JPN"
            | "TNT_UK_REFR"
            | "KEC"
            | "DPD_RO"
            | "TNT_JP"
            | "TH_CJ"
            | "EC_CN"
            | "FASTWAY_UK"
            | "FASTWAY_US"
            | "GLS_DE"
            | "GLS_ES"
            | "GLS_FR"
            | "MONDIAL_BE"
            | "SGT_IT"
            | "TNT_CN"
            | "TNT_DE"
            | "TNT_ES"
            | "TNT_PL"
            | "PARCELFORCE"
            | "SWISS_POST"
            | "TOLL_IPEC"
            | "AIR_21"
            | "AIRSPEED"
            | "BERT"
            | "BLUEDART"
            | "COLLECTPLUS"
            | "COURIERPLUS"
            | "COURIER_POST"
            | "DHL_GLOBAL_MAIL"
            | "DPD_UK"
            | "DELTEC_DE"
            | "DEUTSCHE_DE"
            | "DOTZOT"
            | "ELTA_GR"
            | "EMS_CN"
            | "ECARGO"
            | "ENSENDA"
            | "FERCAM_IT"
            | "FASTWAY_ZA"
            | "FASTWAY_AU"
            | "FIRST_LOGISITCS"
            | "GEODIS"
            | "GLOBEGISTICS"
            | "GREYHOUND"
            | "JETSHIP_MY"
            | "LION_PARCEL"
            | "AEROFLASH"
            | "ONTRAC"
            | "SAGAWA"
            | "SIODEMKA"
            | "STARTRACK"
            | "TNT_AU"
            | "TNT_IT"
            | "TRANSMISSION"
            | "YAMATO"
            | "DHL_IT"
            | "DHL_AT"
            | "LOGISTICSWORLDWIDE_KR"
            | "GLS_SPAIN"
            | "AMAZON_UK_API"
            | "DPD_FR_REFERENCE"
            | "DHLPARCEL_UK"
            | "MEGASAVE"
            | "QUALITYPOST"
            | "IDS_LOGISTICS"
            | "JOYINGBOX"
            | "PANTHER_ORDER_NUMBER"
            | "WATKINS_SHEPARD"
            | "FASTTRACK"
            | "UP_EXPRESS"
            | "ELOGISTICA"
            | "ECOURIER"
            | "CJ_PHILIPPINES"
            | "SPEEDEX"
            | "ORANGECONNEX"
            | "TECOR"
            | "SAEE"
            | "GLS_ITALY_FTP"
            | "DELIVERE"
            | "YYCOM"
            | "ADICIONAL_PT"
            | "DKSH"
            | "NIPPON_EXPRESS_FTP"
            | "GOLS"
            | "FUJEXP"
            | "QTRACK"
            | "OMLOGISTICS_API"
            | "GDPHARM"
            | "MISUMI_CN"
            | "AIR_CANADA"
            | "CITY56_WEBHOOK"
            | "SAGAWA_API"
            | "KEDAEX"
            | "PGEON_API"
            | "WEWORLDEXPRESS"
            | "JT_LOGISTICS"
            | "TRUSK"
            | "VIAXPRESS"
            | "DHL_SUPPLYCHAIN_ID"
            | "ZUELLIGPHARMA_SFTP"
            | "MEEST"
            | "TOLL_PRIORITY"
            | "MOTHERSHIP_API"
            | "CAPITAL"
            | "EUROPAKET_API"
            | "HFD"
            | "TOURLINE_REFERENCE"
            | "GIO_ECOURIER"
            | "CN_LOGISTICS"
            | "PANDION"
            | "BPOST_API"
            | "PASSPORTSHIPPING"
            | "PAKAJO"
            | "DACHSER"
            | "YUSEN_SFTP"
            | "SHYPLITE"
            | "XYY"
            | "MWD"
            | "FAXECARGO"
            | "MAZET"
            | "FIRST_LOGISTICS_API"
            | "SPRINT_PACK"
            | "HERMES_DE_FTP"
            | "CONCISE"
            | "KERRY_EXPRESS_TW_API"
            | "EWE"
            | "FASTDESPATCH"
            | "ABCUSTOM_SFTP"
            | "CHAZKI"
            | "SHIPPIE"
            | "GEODIS_API"
            | "NAQEL_EXPRESS"
            | "PAPA_WEBHOOK"
            | "FORWARDAIR"
            | "DIALOGO_LOGISTICA_API"
            | "LALAMOVE_API"
            | "TOMYDOOR"
            | "KRONOS_WEBHOOK"
            | "JTCARGO"
            | "T_CAT"
            | "CONCISE_WEBHOOK"
            | "TELEPORT_WEBHOOK"
            | "CUSTOMCO_API"
            | "SPX_TH"
            | "BOLLORE_LOGISTICS"
            | "CLICKLINK_SFTP"
            | "M3LOGISTICS"
            | "VNPOST_API"
            | "AXLEHIRE_FTP"
            | "SHADOWFAX"
            | "MYHERMES_UK_API"
            | "DAIICHI"
            | "MENSAJEROSURBANOS_API"
            | "POLARSPEED"
            | "IDEXPRESS_ID"
            | "PAYO"
            | "WHISTL_SFTP"
            | "INTEX_DE"
            | "TRANS2U"
            | "PRODUCTCAREGROUP_SFTP"
            | "BIGSMART"
            | "EXPEDITORS_API_REF"
            | "AITWORLDWIDE_API"
            | "WORLDCOURIER"
            | "QUIQUP"
            | "AGEDISS_SFTP"
            | "ANDREANI_API"
            | "CRLEXPRESS"
            | "SMARTCAT"
            | "CROSSFLIGHT"
            | "PROCARRIER"
            | "DHL_REFERENCE_API"
            | "SEINO_API"
            | "WSPEXPRESS"
            | "KRONOS"
            | "TOTAL_EXPRESS_API"
            | "PARCLL"
            | "XPEDIGO"
            | "STAR_TRACK_WEBHOOK"
            | "GPOST"
            | "UCS"
            | "DMFGROUP"
            | "COORDINADORA_API"
            | "MARKEN"
            | "NTL"
            | "REDJEPAKKETJE"
            | "ALLIED_EXPRESS_FTP"
            | "MONDIALRELAY_ES"
            | "NAEKO_FTP"
            | "MHI"
            | "SHIPPIFY"
            | "MALCA_AMIT_API"
            | "JTEXPRESS_SG_API"
            | "DACHSER_WEB"
            | "FLIGHTLG"
            | "CAGO"
            | "COM1EXPRESS"
            | "TONAMI_FTP"
            | "PACKFLEET"
            | "PUROLATOR_INTERNATIONAL"
            | "WINESHIPPING_WEBHOOK"
            | "DHL_ES_SFTP"
            | "PCHOME_API"
            | "CESKAPOSTA_API"
            | "GORUSH"
            | "HOMERUNNER"
            | "AMAZON_ORDER"
            | "EFWNOW_API"
            | "CBL_LOGISTICA_API"
            | "NIMBUSPOST"
            | "LOGWIN_LOGISTICS"
            | "NOWLOG_API"
            | "DPD_NL"
            | "GODEPENDABLE"
            | "ESDEX"
            | "LOGISYSTEMS_SFTP"
            | "EXPEDITORS"
            | "SNTGLOBAL_API"
            | "SHIPX"
            | "QINTL_API"
            | "PACKS"
            | "POSTNL_INTERNATIONAL"
            | "AMAZON_EMAIL_PUSH"
            | "DHL_API"
            | "SPX"
            | "AXLEHIRE"
            | "ICSCOURIER"
            | "DIALOGO_LOGISTICA"
            | "SHUNBANG_EXPRESS"
            | "TCS_API"
            | "SF_EXPRESS_CN"
            | "PACKETA"
            | "SIC_TELIWAY"
            | "MONDIALRELAY_FR"
            | "INTIME_FTP"
            | "JD_EXPRESS"
            | "FASTBOX"
            | "PATHEON"
            | "INDIA_POST"
            | "TIPSA_REF"
            | "ECOFREIGHT"
            | "VOX"
            | "DIRECTFREIGHT_AU_REF"
            | "BESTTRANSPORT_SFTP"
            | "AUSTRALIA_POST_API"
            | "FRAGILEPAK_SFTP"
            | "FLIPXP"
            | "VALUE_WEBHOOK"
            | "DAESHIN"
            | "SHERPA"
            | "MWD_API"
            | "SMARTKARGO"
            | "DNJ_EXPRESS"
            | "GOPEOPLE"
            | "MYSENDLE_API"
            | "ARAMEX_API"
            | "PIDGE"
            | "THAIPARCELS"
            | "PANTHER_REFERENCE_API"
            | "POSTAPLUS"
            | "BUFFALO"
            | "U_ENVIOS"
            | "ELITE_CO"
            | "BARQEXP"
            | "ROCHE_INTERNAL_SFTP"
            | "DBSCHENKER_ICELAND"
            | "TNT_FR_REFERENCE"
            | "NEWGISTICSAPI"
            | "GLOVO"
            | "GWLOGIS_API"
            | "SPREETAIL_API"
            | "MOOVA"
            | "PLYCONGROUP"
            | "USPS_WEBHOOK"
            | "REIMAGINEDELIVERY"
            | "EDF_FTP"
            | "DAO365"
            | "BIOCAIR_FTP"
            | "RANSA_WEBHOOK"
            | "SHIPXPRES"
            | "COURANT_PLUS_API"
            | "SHIPA"
            | "HOMELOGISTICS"
            | "DX"
            | "POSTE_ITALIANE_PACCOCELERE"
            | "TOLL_WEBHOOK"
            | "LCTBR_API"
            | "DX_FREIGHT"
            | "DHL_SFTP"
            | "SHIPROCKET"
            | "UBER_WEBHOOK"
            | "STATOVERNIGHT"
            | "BURD"
            | "FASTSHIP"
            | "IBVENTURE_WEBHOOK"
            | "GATI_KWE_API"
            | "CRYOPDP_FTP"
            | "HUBBED"
            | "TIPSA_API"
            | "ARASKARGO"
            | "THIJS_NL"
            | "ATSHEALTHCARE_REFERENCE"
            | "99MINUTOS"
            | "HELLENIC_POST"
            | "HSM_GLOBAL"
            | "MNX"
            | "NMTRANSFER"
            | "LOGYSTO"
            | "INDIA_POST_INT"
            | "AMAZON_FBA_SWISHIP_IN"
            | "SRT_TRANSPORT"
            | "BOMI"
            | "DELIVERR_SFTP"
            | "HSDEXPRESS"
            | "SIMPLETIRE_WEBHOOK"
            | "HUNTER_EXPRESS_SFTP"
            | "UPS_API"
            | "WOOYOUNG_LOGISTICS_SFTP"
            | "PHSE_API"
            | "WISH_EMAIL_PUSH"
            | "NORTHLINE"
            | "MEDAFRICA"
            | "DPD_AT_SFTP"
            | "ANTERAJA"
            | "DHL_GLOBAL_FORWARDING_API"
            | "LBCEXPRESS_API"
            | "SIMSGLOBAL"
            | "CDLDELIVERS"
            | "TYP"
            | "TESTING_COURIER_WEBHOOK"
            | "PANDAGO_API"
            | "ROYAL_MAIL_FTP"
            | "THUNDEREXPRESS"
            | "SECRETLAB_WEBHOOK"
            | "SETEL"
            | "JD_WORLDWIDE"
            | "DPD_RU_API"
            | "ARGENTS_WEBHOOK"
            | "POSTONE"
            | "TUSKLOGISTICS"
            | "RHENUS_UK_API"
            | "TAQBIN_SG_API"
            | "INNTRALOG_SFTP"
            | "DAYROSS"
            | "CORREOSEXPRESS_API"
            | "INTERNATIONAL_SEUR_API"
            | "YODEL_API"
            | "HEROEXPRESS"
            | "DHL_SUPPLYCHAIN_IN"
            | "URGENT_CARGUS"
            | "FRONTDOORCORP"
            | "JTEXPRESS_PH"
            | "PARCELSTARS_WEBHOOK"
            | "DPD_SK_SFTP"
            | "MOVIANTO"
            | "OZEPARTS_SHIPPING"
            | "KARGOMKOLAY"
            | "TRUNKRS"
            | "OMNIRPS_WEBHOOK"
            | "CHILEXPRESS"
            | "TESTING_COURIER"
            | "JNE_API"
            | "BJSHOMEDELIVERY_FTP"
            | "DEXPRESS_WEBHOOK"
            | "USPS_API"
            | "TRANSVIRTUAL"
            | "SOLISTICA_API"
            | "CHIENVENTURE_WEBHOOK"
            | "DPD_UK_SFTP"
            | "INPOST_UK"
            | "JAVIT"
            | "ZTO_DOMESTIC"
            | "DHL_GT_API"
            | "CEVA_TRACKING"
            | "KOMON_EXPRESS"
            | "EASTWESTCOURIER_FTP"
            | "DANNIAO"
            | "SPECTRAN"
            | "DELIVER_IT"
            | "RELAISCOLIS"
            | "GLS_SPAIN_API"
            | "POSTPLUS"
            | "AIRTERRA"
            | "GIO_ECOURIER_API"
            | "DPD_CH_SFTP"
            | "FEDEX_API"
            | "INTERSMARTTRANS"
            | "HERMES_UK_SFTP"
            | "EXELOT_FTP"
            | "DHL_PA_API"
            | "VIRTRANSPORT_SFTP"
            | "WORLDNET"
            | "INSTABOX_WEBHOOK"
            | "KNG"
            | "FLASHEXPRESS_WEBHOOK"
            | "MAGYAR_POSTA_API"
            | "WESHIP_API"
            | "OHI_WEBHOOK"
            | "MUDITA"
            | "BLUEDART_API"
            | "T_CAT_API"
            | "ADS"
            | "HERMES_IT"
            | "FITZMARK_API"
            | "POSTI_API"
            | "SMSA_EXPRESS_WEBHOOK"
            | "TAMERGROUP_WEBHOOK"
            | "LIVRAPIDE"
            | "NIPPON_EXPRESS"
            | "BETTERTRUCKS"
            | "FAN"
            | "PB_USPSFLATS_FTP"
            | "PARCELRIGHT"
            | "ITHINKLOGISTICS"
            | "KERRY_EXPRESS_TH_WEBHOOK"
            | "ECOUTIER"
            | "SHOWL"
            | "BRT_IT_API"
            | "RIXONHK_API"
            | "DBSCHENKER_API"
            | "ILYANGLOGIS"
            | "MAIL_BOX_ETC"
            | "WESHIP"
            | "DHL_GLOBAL_MAIL_API"
            | "ACTIVOS24_API"
            | "ATSHEALTHCARE"
            | "LUWJISTIK"
            | "GW_WORLD"
            | "FAIRSENDEN_API"
            | "SERVIP_WEBHOOK"
            | "SWISHIP"
            | "TANET"
            | "HOTSIN_CARGO"
            | "DIREX"
            | "HUANTONG"
            | "IMILE_API"
            | "BDMNET"
            | "AUEXPRESS"
            | "NYTLOGISTICS"
            | "DSV_REFERENCE"
            | "NOVOFARMA_WEBHOOK"
            | "AITWORLDWIDE_SFTP"
            | "SHOPOLIVE"
            | "FNF_ZA"
            | "DHL_ECOMMERCE_GC"
            | "FETCHR"
            | "STARLINKS_API"
            | "YYEXPRESS"
            | "SERVIENTREGA"
            | "HANJIN"
            | "SPANISH_SEUR_FTP"
            | "DX_B2B_CONNUM"
            | "HELTHJEM_API"
            | "INEXPOST"
            | "A2B_BA"
            | "RHENUS_GROUP"
            | "SBERLOGISTICS_RU"
            | "MALCA_AMIT"
            | "PPL"
            | "OSM_WORLDWIDE_SFTP"
            | "ACILOGISTIX"
            | "OPTIMACOURIER"
            | "NOVA_POSHTA_API"
            | "LOGGI"
            | "YIFAN"
            | "MYDYNALOGIC"
            | "MORNINGLOBAL"
            | "CONCISE_API"
            | "FXTRAN"
            | "DELIVERYOURPARCEL_ZA"
            | "UPARCEL"
            | "MOBI_BR"
            | "LOGINEXT_WEBHOOK"
            | "EMS"
            | "SPEEDY";
        /**
         * Shipment Tracker.
         * @description The tracking information for a shipment.
         */
        shipment_tracker: {
            /** @description The PayPal transaction ID. */
            transaction_id: string;
            /** @description The tracking number for the shipment. This property supports Unicode. */
            tracking_number?: string;
            /** @description The type of tracking number. */
            tracking_number_type?: components["schemas"]["shipment_tracking_number_type"];
            status: components["schemas"]["shipment_tracking_status"];
            /** @description The date when the shipment occurred, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            shipment_date?: components["schemas"]["date_no_time"];
            carrier?: components["schemas"]["shipment_carrier"];
            /** @description The name of the carrier for the shipment. Provide this value only if the carrier parameter is OTHER. This property supports Unicode. */
            carrier_name_other?: string;
            /** @description The postage payment ID. This property supports Unicode. */
            postage_payment_id?: string;
            /**
             * @description If true, sends an email notification to the buyer of the PayPal transaction. The email contains the tracking information that was uploaded through the API.
             * @default false
             */
            notify_buyer?: boolean;
            /** @description The quantity of items shipped. */
            quantity?: number;
            /** @description Indicates whether the carrier validated the tracking number. */
            tracking_number_validated?: boolean;
            /** @description The date and time when the tracking information was last updated, in [Internet date and time format](https://tools.ietf.org/html/rfc3339#section-5.6). */
            last_updated_time?: components["schemas"]["date_time"];
            /**
             * @description To denote whether the shipment is sent forward to the receiver or returned back.
             * @enum {string}
             */
            shipment_direction?: "FORWARD" | "RETURN";
            /**
             * @description To denote which party uploaded the shipment tracking info.
             * @enum {string}
             */
            shipment_uploader?: "MERCHANT" | "CONSUMER" | "PARTNER";
        };
        /**
         * Order Tracker Request.
         * @description The tracking details of an order.
         */
        order_tracker_request: components["schemas"]["shipment_tracker"] & {
            /** @description The PayPal capture ID. */
            capture_id: string;
            /**
             * @description If true, sends an email notification to the payer of the PayPal transaction. The email contains the tracking information that was uploaded through the API.
             * @default false
             */
            notify_payer?: boolean;
            /** @description An array of details of items in the shipment. */
            items?: components["schemas"]["tracker_item"][];
        };
        "orders.track.create-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field / parameter is missing.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is either too short or too long.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "A parameter value is not valid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_SYNTAX";
                      /** @enum {string} */
                      description?: "The value of a field does not conform to the expected format.";
                  }
            )[];
        };
        "orders.track.create-403": {
            issues?: {
                /** @enum {string} */
                issue?: "PERMISSION_DENIED";
                /** @enum {string} */
                description?: "You do not have permission to access or perform operations on this resource.";
            }[];
        };
        "orders.track.create-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "CAPTURE_STATUS_NOT_VALID";
                      /** @enum {string} */
                      description?: "Invalid capture status. Tracker information can only be added to captures in `COMPLETED` state.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ITEM_SKU_MISMATCH";
                      /** @enum {string} */
                      description?: "Item sku must match one of the items sku that was provided during order creation.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "CAPTURE_ID_NOT_FOUND";
                      /** @enum {string} */
                      description?: "Specified capture ID does not exist. Check the capture ID and try again.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MSP_NOT_SUPPORTED";
                      /** @enum {string} */
                      description?: "Multiple purchase units are not supported for this operation.";
                  }
            )[];
        };
        "orders.trackers.patch-400": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "FIELD_NOT_PATCHABLE";
                      /** @enum {string} */
                      description?: "Field cannot be patched.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PARAMETER_VALUE";
                      /** @enum {string} */
                      description?: "The value of a field is invalid.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MISSING_REQUIRED_PARAMETER";
                      /** @enum {string} */
                      description?: "A required field or parameter is missing.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_STRING_LENGTH";
                      /** @enum {string} */
                      description?: "The value of a field is either too short or too long.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "INVALID_PATCH_OPERATION";
                      /** @enum {string} */
                      description?: "The operation cannot be honored. Cannot add a property that's already present, use replace. Cannot remove a property thats not present, use add. Cannot replace a property thats not present, use add.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "MALFORMED_REQUEST_JSON";
                      /** @enum {string} */
                      description?: "The request JSON is not well formed.";
                  }
            )[];
        };
        "orders.trackers.patch-403": {
            issues?: {
                /** @enum {string} */
                issue?: "PERMISSION_DENIED";
                /** @enum {string} */
                description?: "You do not have permission to access or perform operations on this resource.";
            }[];
        };
        "orders.trackers.patch-404": {
            issues?: {
                /** @enum {string} */
                issue?: "TRACKER_ID_NOT_FOUND";
                /** @enum {string} */
                description?: "Specified tracker ID does not exist. Check the tracker ID and try again.";
            }[];
        };
        "orders.trackers.patch-422": {
            issues?: (
                | {
                      /** @enum {string} */
                      issue?: "INVALID_JSON_POINTER_FORMAT";
                      /** @enum {string} */
                      description?: "Path should be a valid [JSON Pointer](https://tools.ietf.org/html/rfc6901) that references a location within the request where the operation is performed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "NOT_PATCHABLE";
                      /** @enum {string} */
                      description?: "Cannot be patched.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PATCH_VALUE_REQUIRED";
                      /** @enum {string} */
                      description?: "Specify a `value` for the field being patched.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "PATCH_PATH_REQUIRED";
                      /** @enum {string} */
                      description?: "Specify a `value` for the field in which the operation needs to be performed.";
                  }
                | {
                      /** @enum {string} */
                      issue?: "ITEM_SKU_MISMATCH";
                      /** @enum {string} */
                      description?: "Item sku must match one of the items sku that was provided during order creation.";
                  }
            )[];
        };
    };
    responses: {
        /** @description The default response. */
        default: {
            content: {
                "application/json": components["schemas"]["error_default"];
            };
        };
    };
    parameters: {
        /** @description The server stores keys for 6 hours. The API callers can request the times to up to 72 hours by speaking to their Account Manager. */
        paypal_request_id: string;
        paypal_partner_attribution_id?: string;
        paypal_client_metadata_id?: string;
        /** @description The preferred server response upon successful completion of the request. Value is:<ul><li><code>return=minimal</code>. The server returns a minimal response to optimize communication between the API caller and the server. A minimal response includes the <code>id</code>, <code>status</code> and HATEOAS links.</li><li><code>return=representation</code>. The server returns a complete resource representation, including the current state of the resource.</li></ul> */
        prefer?: string;
        /** @description The media type. Required for operations with a request body. The value is `application/<format>`, where `format` is `json`. */
        content_type: string;
        /** @description The ID of the order for which to update payment details. */
        id: string;
        /** @description A comma-separated list of fields that should be returned for the order. Valid filter field is `payment_source`. */
        fields?: string;
        /** @description An API-caller-provided JSON Web Token (JWT) assertion that identifies the merchant. For details, see <a href="/api/rest/requests/#paypal-auth-assertion">PayPal-Auth-Assertion</a>. */
        paypal_auth_assertion?: string;
        /** @description The order tracking ID. */
        tracker_id: string;
    };
    requestBodies: {
        patch_request?: {
            content: {
                "application/json": components["schemas"]["patch_request"];
            };
        };
    };
    headers: never;
    pathItems: never;
}

export type $defs = Record<string, never>;

export type external = Record<string, never>;

export interface operations {
    /**
     * Create order
     * @description Creates an order. Merchants and partners can add Level 2 and 3 data to payments to reduce risk and payment processing costs. For more information about processing payments, see <a href="https://developer.paypal.com/docs/checkout/advanced/processing/">checkout</a> or <a href="https://developer.paypal.com/docs/multiparty/checkout/advanced/processing/">multiparty checkout</a>.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href="/api/rest/reference/orders/v2/errors/#create-order">Orders v2 errors</a>.</blockquote>
     */
    "orders.create": {
        parameters: {
            header: {
                "PayPal-Request-Id": components["parameters"]["paypal_request_id"];
                "PayPal-Partner-Attribution-Id"?: components["parameters"]["paypal_partner_attribution_id"];
                "PayPal-Client-Metadata-Id"?: components["parameters"]["paypal_client_metadata_id"];
                Prefer?: components["parameters"]["prefer"];
                "Content-Type": components["parameters"]["content_type"];
            };
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["order_request"];
            };
        };
        responses: {
            /** @description A successful response to an idempotent request returns the HTTP `200 OK` status code with a JSON response body that shows order details. */
            200: {
                content: {
                    "application/json": components["schemas"]["order"];
                };
            };
            /** @description A successful request returns the HTTP `201 Created` status code and a JSON response body that includes by default a minimal response with the ID, status, and HATEOAS links. If you require the complete order resource representation, you must pass the <a href="/docs/api/orders/v2/#orders-create-header-parameters"><code>Prefer: return=representation</code> request header</a>. This header value is not the default. */
            201: {
                content: {
                    "application/json": components["schemas"]["order"];
                };
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["422"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Show order details
     * @description Shows details for an order, by ID.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href="/api/rest/reference/orders/v2/errors/#get-order">Orders v2 errors</a>.</blockquote>
     */
    "orders.get": {
        parameters: {
            query?: {
                fields?: components["parameters"]["fields"];
            };
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        responses: {
            /** @description A successful request returns the HTTP `200 OK` status code and a JSON response body that shows order details. */
            200: {
                content: {
                    "application/json": components["schemas"]["order"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Update order
     * @description Updates an order with a `CREATED` or `APPROVED` status. You cannot update an order with the `COMPLETED` status.<br/><br/>To make an update, you must provide a `reference_id`. If you omit this value with an order that contains only one purchase unit, PayPal sets the value to `default` which enables you to use the path: <code>\"/purchase_units/@reference_id=='default'/{attribute-or-object}\"</code>. Merchants and partners can add Level 2 and 3 data to payments to reduce risk and payment processing costs. For more information about processing payments, see <a href="https://developer.paypal.com/docs/checkout/advanced/processing/">checkout</a> or <a href="https://developer.paypal.com/docs/multiparty/checkout/advanced/processing/">multiparty checkout</a>.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href=\"/api/rest/reference/orders/v2/errors/#patch-order\">Orders v2 errors</a>.</blockquote>Patchable attributes or objects:<br/><br/><table><thead><th>Attribute</th><th>Op</th><th>Notes</th></thead><tbody><tr><td><code>intent</code></td><td>replace</td><td></td></tr><tr><td><code>payer</code></td><td>replace, add</td><td>Using replace op for <code>payer</code> will replace the whole <code>payer</code> object with the value sent in request.</td></tr><tr><td><code>purchase_units</code></td><td>replace, add</td><td></td></tr><tr><td><code>purchase_units[].custom_id</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].description</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].payee.email</code></td><td>replace</td><td></td></tr><tr><td><code>purchase_units[].shipping.name</code></td><td>replace, add</td><td></td></tr><tr><td><code>purchase_units[].shipping.address</code></td><td>replace, add</td><td></td></tr><tr><td><code>purchase_units[].shipping.type</code></td><td>replace, add</td><td></td></tr><tr><td><code>purchase_units[].soft_descriptor</code></td><td>replace, remove</td><td></td></tr><tr><td><code>purchase_units[].amount</code></td><td>replace</td><td></td></tr><tr><td><code>purchase_units[].items</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].invoice_id</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].payment_instruction</code></td><td>replace</td><td></td></tr><tr><td><code>purchase_units[].payment_instruction.disbursement_mode</code></td><td>replace</td><td>By default, <code>disbursement_mode</code> is <code>INSTANT</code>.</td></tr><tr><td><code>purchase_units[].payment_instruction.platform_fees</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].supplementary_data.airline</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>purchase_units[].supplementary_data.card</code></td><td>replace, add, remove</td><td></td></tr><tr><td><code>application_context.client_configuration</code></td><td>replace, add</td><td></td></tr></tbody></table>
     */
    "orders.patch": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody: components["requestBodies"]["patch_request"];
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with an empty object in the JSON response body. */
            204: {
                content: never;
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["orders.patch-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["orders.patch-422"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Confirm the Order
     * @description Payer confirms their intent to pay for the the Order with the given payment source.
     */
    "orders.confirm": {
        parameters: {
            header: {
                "PayPal-Client-Metadata-Id"?: components["parameters"]["paypal_client_metadata_id"];
                "Content-Type": components["parameters"]["content_type"];
                Prefer?: components["parameters"]["prefer"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["confirm_order_request"];
            };
        };
        responses: {
            /** @description A successful request indicates that the payment source was added to the Order. A successful request returns the HTTP `200 OK` status code with a JSON response body that shows order details. */
            200: {
                content: {
                    "application/json": components["schemas"]["order"];
                };
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["orders.confirm-400"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["403"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["orders.confirm-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Authorize payment for order
     * @description Authorizes payment for an order. To successfully authorize payment for an order, the buyer must first approve the order or a valid payment_source must be provided in the request. A buyer can approve the order upon being redirected to the rel:approve URL that was returned in the HATEOAS links in the create order response.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href="/api/rest/reference/orders/v2/errors/#authorize-order">Orders v2 errors</a>.</blockquote>
     */
    "orders.authorize": {
        parameters: {
            header: {
                "PayPal-Request-Id": components["parameters"]["paypal_request_id"];
                Prefer?: components["parameters"]["prefer"];
                "PayPal-Client-Metadata-Id"?: components["parameters"]["paypal_client_metadata_id"];
                "PayPal-Auth-Assertion"?: components["parameters"]["paypal_auth_assertion"];
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["order_authorize_request"];
            };
        };
        responses: {
            /** @description A successful response to an idempotent request returns the HTTP `200 OK` status code with a JSON response body that shows authorized payment details. */
            200: {
                content: {
                    "application/json": components["schemas"]["order_authorize_response"];
                };
            };
            /** @description A successful response to a non-idempotent request returns the HTTP `201 Created` status code with a JSON response body that shows authorized payment details. If a duplicate response is retried, returns the HTTP `200 OK` status code. By default, the response is minimal. If you need the complete resource representation, you must pass the <a href="/docs/api/orders/v2/#orders-authorize-header-parameters"><code>Prefer: return=representation</code> request header</a>. */
            201: {
                content: {
                    "application/json": components["schemas"]["order_authorize_response"];
                };
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["orders.authorize-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description The authorized payment failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["orders.authorize-403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["orders.authorize-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Capture payment for order
     * @description Captures payment for an order. To successfully capture payment for an order, the buyer must first approve the order or a valid payment_source must be provided in the request. A buyer can approve the order upon being redirected to the rel:approve URL that was returned in the HATEOAS links in the create order response.<blockquote><strong>Note:</strong> For error handling and troubleshooting, see <a href="/api/rest/reference/orders/v2/errors/#capture-order">Orders v2 errors</a>.</blockquote>
     */
    "orders.capture": {
        parameters: {
            header: {
                "PayPal-Request-Id": components["parameters"]["paypal_request_id"];
                Prefer?: components["parameters"]["prefer"];
                "PayPal-Client-Metadata-Id"?: components["parameters"]["paypal_client_metadata_id"];
                "PayPal-Auth-Assertion"?: components["parameters"]["paypal_auth_assertion"];
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody?: {
            content: {
                "application/json": components["schemas"]["order_capture_request"];
            };
        };
        responses: {
            /** @description A successful response to an idempotent request returns the HTTP `200 OK` status code with a JSON response body that shows captured payment details. */
            200: {
                content: {
                    "application/json": components["schemas"]["order"];
                };
            };
            /** @description A successful response to a non-idempotent request returns the HTTP `201 Created` status code with a JSON response body that shows captured payment details. If a duplicate response is retried, returns the HTTP `200 OK` status code. By default, the response is minimal. If you need the complete resource representation, pass the <a href="/docs/api/orders/v2/#orders-authorize-header-parameters"><code>Prefer: return=representation</code> request header</a>. */
            201: {
                content: {
                    "application/json": components["schemas"]["order"];
                };
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["orders.capture-400"];
                };
            };
            /** @description Authentication failed due to missing authorization header, or invalid authentication credentials. */
            401: {
                content: {
                    "application/json": components["schemas"]["error_401"] &
                        components["schemas"]["401"];
                };
            };
            /** @description The authorized payment failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["orders.capture-403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["orders.capture-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Add tracking information for an Order.
     * @description Adds tracking information for an Order.
     */
    "orders.track.create": {
        parameters: {
            header: {
                "PayPal-Auth-Assertion"?: components["parameters"]["paypal_auth_assertion"];
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
            };
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["order_tracker_request"];
            };
        };
        responses: {
            /** @description A successful response to an idempotent request returns the HTTP `200 OK` status code with a JSON response body that shows tracker details. */
            200: {
                content: {
                    "application/json": components["schemas"]["order"];
                };
            };
            /** @description A successful response to a non-idempotent request returns the HTTP `201 Created` status code with a JSON response body that shows tracker details. If a duplicate response is retried, returns the HTTP `200 OK` status code. */
            201: {
                content: {
                    "application/json": components["schemas"]["order"];
                };
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["orders.track.create-400"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["orders.track.create-403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["orders.track.create-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
    /**
     * Update or cancel tracking information for a PayPal order
     * @description Updates or cancels the tracking information for a PayPal order, by ID. Updatable attributes or objects:<br/><br/><table><thead><th>Attribute</th><th>Op</th><th>Notes</th></thead><tbody></tr><tr><td><code>items</code></td><td>replace</td><td>Using replace op for <code>items</code> will replace the entire <code>items</code> object with the value sent in request.</td></tr><tr><td><code>notify_payer</code></td><td>replace, add</td><td></td></tr><tr><td><code>status</code></td><td>replace</td><td>Only patching status to CANCELLED is currently supported.</td></tr></tbody></table>
     */
    "orders.trackers.patch": {
        parameters: {
            header: {
                "Content-Type": components["parameters"]["content_type"];
            };
            path: {
                id: components["parameters"]["id"];
                tracker_id: components["parameters"]["tracker_id"];
            };
        };
        requestBody: components["requestBodies"]["patch_request"];
        responses: {
            /** @description A successful request returns the HTTP `204 No Content` status code with an empty object in the JSON response body. */
            204: {
                content: never;
            };
            /** @description Request is not well-formed, syntactically incorrect, or violates schema. */
            400: {
                content: {
                    "application/json": components["schemas"]["error_400"] &
                        components["schemas"]["orders.trackers.patch-400"];
                };
            };
            /** @description Authorization failed due to insufficient permissions. */
            403: {
                content: {
                    "application/json": components["schemas"]["error_403"] &
                        components["schemas"]["orders.trackers.patch-403"];
                };
            };
            /** @description The specified resource does not exist. */
            404: {
                content: {
                    "application/json": components["schemas"]["error_404"] &
                        components["schemas"]["orders.trackers.patch-404"];
                };
            };
            /** @description The requested action could not be performed, semantically incorrect, or failed business validation. */
            422: {
                content: {
                    "application/json": components["schemas"]["error_422"] &
                        components["schemas"]["orders.trackers.patch-422"];
                };
            };
            /** @description An internal server error has occurred. */
            500: {
                content: {
                    "application/json": components["schemas"]["error_500"];
                };
            };
            default: components["responses"]["default"];
        };
    };
}
