import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import axios from '../../utils/axios';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Rating,
  Alert,
  CircularProgress,
  Grid,
  Avatar,
  Divider,
  Card,
  CardContent,
  FormHelperText
} from '@mui/material';
import {
  Star as StarIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  CalendarToday as CalendarTodayIcon
} from '@mui/icons-material';
import Layout from '../../components/Layout';

const WriteReview = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const isRtl = i18n.dir() === 'rtl';

  const [teachers, setTeachers] = useState([]);
  const [allTeachers, setAllTeachers] = useState([]); // Store all teachers for reference
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [myReviews, setMyReviews] = useState([]);
  const [editingReviewId, setEditingReviewId] = useState(null); // Track the ID of the review being edited

  const [selectedTeacher, setSelectedTeacher] = useState('');
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const [editingReview, setEditingReview] = useState(null);
  const [completedBookings, setCompletedBookings] = useState([]);

  // Fetch teachers the student has had lessons with
  useEffect(() => {
    const fetchTeachers = async () => {
      try {
        setLoading(true);
        console.log('Fetching teachers and reviews data...');

        // Get completed bookings to find teachers the student has had lessons with
        const token = localStorage.getItem('token');
        console.log('Using auth token:', token ? `${token.substring(0, 10)}...` : 'No token found');

        const bookingsResponse = await axios.get('/api/bookings/student', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        console.log('Bookings response:', bookingsResponse.data);

        if (bookingsResponse.data && (bookingsResponse.data.success || bookingsResponse.data.data)) {
          // Check different possible structures for bookings data
          let bookingsData = [];

          if (bookingsResponse.data.data) {
            bookingsData = bookingsResponse.data.data;
          } else if (bookingsResponse.data.bookings) {
            bookingsData = bookingsResponse.data.bookings;
          } else if (Array.isArray(bookingsResponse.data)) {
            bookingsData = bookingsResponse.data;
          }

          // Filter completed bookings
          const completedBookingsData = bookingsData.filter(
            booking => booking.status === 'completed'
          );

          // Log detailed information about each completed booking
          completedBookingsData.forEach((booking, index) => {
            console.log(`Completed booking ${index + 1}:`, {
              id: booking.id,
              teacher_id: booking.teacher_id,
              teacher_profile_id: booking.teacher_profile_id,
              student_id: booking.student_id,
              datetime: booking.datetime,
              status: booking.status
            });
          });

          // Log all teacher_id values from bookings for easier debugging
          console.log('All teacher_id values from bookings:',
            completedBookingsData.map(booking => String(booking.teacher_id))
          );

          // Store completed bookings in state for later use
          setCompletedBookings(completedBookingsData);

          console.log('Completed bookings:', completedBookingsData);

          // Extract unique teacher profile IDs from bookings
          const teacherProfileIds = [...new Set(completedBookingsData.map(booking => {
            // Log each booking to debug
            console.log('Processing booking:', booking);

            // Get teacher profile ID
            const teacherProfileId = booking.teacher_profile_id;

            console.log('Extracted teacher profile ID:', teacherProfileId);
            return teacherProfileId;
          }))].filter(id => id); // Filter out undefined or null values

          console.log('Teacher profile IDs extracted from bookings:', teacherProfileIds);

          if (teacherProfileIds.length === 0) {
            console.log('No teacher profile IDs found in completed bookings');
            setTeachers([]);
            setLoading(false);
            return;
          }

          // We'll use the teacher profile IDs directly
          console.log('Using teacher profile IDs directly:', teacherProfileIds);

          // Create a mapping of profile IDs to store later
          const profileIdMapping = {};
          teacherProfileIds.forEach(profileId => {
            profileIdMapping[profileId] = true;
          });

          // Fetch teacher details for each teacher profile ID
          const teacherDetailsPromises = teacherProfileIds.map(profileId => {
            console.log(`Fetching details for teacher profile ID: ${profileId}`);
            return axios.get(`/api/teachers/profile/${profileId}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });
          });

          const teacherResponses = await Promise.all(teacherDetailsPromises);

          console.log('Teacher responses:', teacherResponses.map(r => r.data));

          // Process teacher responses with more detailed logging
          const teachersList = [];

          for (const response of teacherResponses) {
            console.log('Processing teacher response:', response.data);

            try {
              let teacherData = null;

              // Check different possible structures for teacher data
              if (response.data && response.data.data) {
                console.log('Found teacher data in response.data.data');
                const data = response.data.data;

                // Log all fields for debugging
                console.log('Teacher data fields:', Object.keys(data));
                console.log('Teacher user_id:', data.user_id);
                console.log('Teacher id:', data.id);
                console.log('Teacher profile_id:', data.profile_id);

                teacherData = {
                  id: data.user_id || data.id, // User ID
                  profile_id: data.id || data.profile_id, // Teacher profile ID (this is the ID from the API response)
                  full_name: data.full_name || data.name || 'Unknown Teacher'
                };
              } else if (response.data && response.data.teacher) {
                console.log('Found teacher data in response.data.teacher');
                const data = response.data.teacher;

                // Log all fields for debugging
                console.log('Teacher data fields:', Object.keys(data));
                console.log('Teacher user_id:', data.user_id);
                console.log('Teacher id:', data.id);
                console.log('Teacher profile_id:', data.profile_id);

                teacherData = {
                  id: data.user_id || data.id, // User ID
                  profile_id: data.id || data.profile_id, // Teacher profile ID
                  full_name: data.full_name || data.name || 'Unknown Teacher'
                };
              } else if (response.data && response.data.profile) {
                console.log('Found teacher data in response.data.profile');
                const data = response.data.profile;

                // Log all fields for debugging
                console.log('Teacher data fields:', Object.keys(data));
                console.log('Teacher user_id:', data.user_id);
                console.log('Teacher id:', data.id);
                console.log('Teacher profile_id:', data.profile_id);

                teacherData = {
                  id: data.user_id || data.id, // User ID
                  profile_id: data.id || data.profile_id, // Teacher profile ID
                  full_name: data.full_name || data.name || 'Unknown Teacher'
                };
              } else if (response.data) {
                console.log('Using response.data directly');
                const data = response.data;

                // Log all fields for debugging
                console.log('Teacher data fields:', Object.keys(data));
                console.log('Teacher user_id:', data.user_id);
                console.log('Teacher id:', data.id);
                console.log('Teacher profile_id:', data.profile_id);

                teacherData = {
                  id: data.user_id || data.id, // User ID
                  profile_id: data.id || data.profile_id, // Teacher profile ID
                  full_name: data.full_name || data.name || 'Unknown Teacher'
                };
              }

              if (teacherData && teacherData.id) {
                // Fix the profile_id to match the one from the booking
                // This is crucial for correct filtering of reviewed teachers
                const bookingWithTeacher = completedBookingsData.find(booking =>
                  String(booking.teacher_id) === String(teacherData.id)
                );

                if (bookingWithTeacher) {
                  // Use the profile_id from the booking
                  teacherData.profile_id = bookingWithTeacher.teacher_profile_id;
                  console.log(`Updated teacher profile_id to match booking: ${teacherData.profile_id}`);
                } else {
                  // If we couldn't find a booking by teacher_id, try to find by profile_id
                  const bookingByProfileId = completedBookingsData.find(booking =>
                    String(booking.teacher_profile_id) === String(teacherData.profile_id)
                  );

                  if (bookingByProfileId) {
                    console.log(`Found booking by profile_id match: ${bookingByProfileId.teacher_profile_id}`);
                  } else {
                    // If we still couldn't find a booking, check if this is the teacher from the API request
                    const requestedProfileId = teacherProfileIds[0]; // The profile ID we requested from the API
                    if (requestedProfileId) {
                      console.log(`Using requested profile ID: ${requestedProfileId}`);
                      teacherData.profile_id = requestedProfileId;
                    }
                  }
                }

                console.log('Adding teacher to list:', teacherData);
                teachersList.push(teacherData);
              } else {
                console.log('Could not extract valid teacher data from response');
              }
            } catch (err) {
              console.error('Error processing teacher response:', err);
            }
          }

          console.log('Initial teachers list:', teachersList);

          // Fetch student's existing reviews
          const reviewsResponse = await axios.get('/api/reviews/student', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          console.log('Student reviews response:', reviewsResponse.data);

          let reviewsData = [];

          if (reviewsResponse.data) {
            // Check different possible structures for reviews data
            if (reviewsResponse.data.reviews) {
              reviewsData = reviewsResponse.data.reviews;
            } else if (reviewsResponse.data.data) {
              if (Array.isArray(reviewsResponse.data.data)) {
                reviewsData = reviewsResponse.data.data;
              } else if (reviewsResponse.data.data.reviews) {
                reviewsData = reviewsResponse.data.data.reviews;
              }
            } else if (Array.isArray(reviewsResponse.data)) {
              reviewsData = reviewsResponse.data;
            }

            setMyReviews(reviewsData);
          }

          // Store all teachers for reference (needed for editing reviews)
          setAllTeachers(teachersList);

          // If we're not editing a review, filter out teachers that already have reviews
          if (!editingReviewId) {
            // Get the teacher profile IDs from the completed bookings
            const bookingTeacherProfileIds = completedBookingsData.map(booking =>
              String(booking.teacher_profile_id)
            );
            console.log('Teacher profile IDs from bookings:', bookingTeacherProfileIds);

            // Get the teacher profile IDs from the reviews
            const reviewedTeacherProfileIds = reviewsData.map(review =>
              String(review.teacher_profile_id)
            );
            console.log('Already reviewed teacher profile IDs:', reviewedTeacherProfileIds);

            // Log the profile IDs of all teachers for debugging
            console.log('All teacher profile IDs:', teachersList.map(teacher => ({
              name: teacher.full_name,
              user_id: teacher.id,
              profile_id: teacher.profile_id
            })));

            // Create a mapping of user IDs to profile IDs from the teacher list
            const teacherUserIdToProfileId = {};
            teachersList.forEach(teacher => {
              if (teacher.id && teacher.profile_id) {
                teacherUserIdToProfileId[String(teacher.id)] = String(teacher.profile_id);
              }
            });
            console.log('Teacher user ID to profile ID mapping:', teacherUserIdToProfileId);

            // Include all teachers that have completed bookings with the student
            // We'll filter out reviewed teachers later
            const filteredTeachersList = teachersList.filter(teacher => {
              // We need to check if this teacher has any completed bookings
              // Either by matching profile_id directly or by checking the user_id mapping
              const teacherProfileId = String(teacher.profile_id);
              const teacherUserId = String(teacher.id);

              // Check if this teacher's profile_id is in the list of reviewed teacher profile IDs
              const isAlreadyReviewed = reviewedTeacherProfileIds.some(reviewedId => {
                // Try exact match first
                if (reviewedId === teacherProfileId) {
                  console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}) matches review profile_id: ${reviewedId}`);
                  return true;
                }

                // If we have a booking with this teacher, check if the booking's teacher_profile_id matches the review
                const bookingWithTeacher = completedBookingsData.find(booking =>
                  String(booking.teacher_id) === String(teacher.id)
                );

                if (bookingWithTeacher && String(bookingWithTeacher.teacher_profile_id) === reviewedId) {
                  console.log(`Teacher ${teacher.full_name} (user_id: ${teacher.id}) has booking with profile_id: ${bookingWithTeacher.teacher_profile_id} that matches review profile_id: ${reviewedId}`);
                  return true;
                }

                return false;
              });

              // Check if this teacher has completed bookings
              // We need to be more flexible here because the profile_id in the booking might not match exactly
              let hasCompletedBooking = false;

              // First check: Direct match with profile_id in bookings
              if (bookingTeacherProfileIds.includes(teacherProfileId)) {
                console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}) has direct profile_id match in bookings`);
                hasCompletedBooking = true;
              }

              // Second check: Match by teacher user ID in bookings
              if (!hasCompletedBooking) {
                const bookingWithTeacher = completedBookingsData.find(booking => {
                  const bookingTeacherId = String(booking.teacher_id);
                  const teacherId = String(teacher.id);
                  const isMatch = bookingTeacherId === teacherId;

                  if (isMatch) {
                    console.log(`Teacher ${teacher.full_name} (user_id: ${teacherId}) matches booking teacher_id: ${bookingTeacherId}`);
                  }

                  return isMatch;
                });

                if (bookingWithTeacher) {
                  console.log(`Found booking for teacher ${teacher.full_name} by user_id match`);
                  hasCompletedBooking = true;
                }
              }

              // Third check: Check if this teacher's profile_id matches the one we requested from the API
              if (!hasCompletedBooking) {
                const requestedProfileId = teacherProfileIds[0]; // The profile ID we requested from the API
                if (requestedProfileId && String(requestedProfileId) === String(teacher.profile_id)) {
                  console.log(`Teacher ${teacher.full_name} (profile_id: ${teacher.profile_id}) matches requested profile ID: ${requestedProfileId}`);
                  hasCompletedBooking = true;
                }
              }

              // Fourth check: For debugging, always include teachers for now
              // TODO: Remove this in production
              if (!hasCompletedBooking && process.env.NODE_ENV === 'development') {
                console.log(`No booking found for teacher ${teacher.full_name}, including anyway for debugging`);
                hasCompletedBooking = true;
              }

              if (isAlreadyReviewed) {
                console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}, user_id: ${teacherUserId}) already has a review - excluding`);
              } else if (!hasCompletedBooking) {
                console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}, user_id: ${teacherUserId}) has no completed booking - excluding`);
              } else {
                console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}, user_id: ${teacherUserId}) has no review yet - including`);
              }

              // Only include teachers that have completed bookings and don't have reviews
              return !isAlreadyReviewed && hasCompletedBooking;
            });

            console.log('Filtered teachers list (excluding already reviewed):', filteredTeachersList);
            console.log('Original teachers list length:', teachersList.length, 'Filtered list length:', filteredTeachersList.length);

            setTeachers(filteredTeachersList);
          } else {
            // If we're editing a review, we need all teachers
            console.log('Editing a review, using all teachers list:', teachersList);
            setTeachers(teachersList);
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);

        // Log detailed error information
        console.error('Error details:', {
          message: err.message,
          response: err.response?.data,
          status: err.response?.status
        });

        let errorMessage = err.response?.data?.message || 'حدث خطأ أثناء جلب البيانات';

        // تحقق مما إذا كان الخطأ بسبب عدم وجود حجوزات مكتملة
        if (err.response?.status === 404 || (err.message && err.message.includes('bookings'))) {
          errorMessage = 'لا توجد حجوزات مكتملة. يجب عليك إكمال درس مع معلم قبل أن تتمكن من كتابة مراجعة.';
        } else if (err.response?.status === 401) {
          errorMessage = 'يرجى تسجيل الدخول مرة أخرى للمتابعة.';
        } else if (!teachers || teachers.length === 0) {
          errorMessage = 'لم نتمكن من العثور على معلمين لديك دروس مكتملة معهم. يرجى التأكد من إكمال درس واحد على الأقل قبل كتابة مراجعة.';
        } else if (err.message && err.message.includes('Network Error')) {
          errorMessage = 'حدث خطأ في الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';
        } else if (!err.response) {
          errorMessage = 'لا يمكن الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';
        }

        setError(errorMessage);
        setLoading(false);

        // Set empty teachers list to show appropriate message
        setTeachers([]);

        // Set empty completed bookings to avoid errors
        setCompletedBookings([]);
      }
    };

    fetchTeachers();
  }, [success, editingReviewId]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('Review form submitted');

    // تحقق من صحة البيانات
    if (!selectedTeacher) {
      console.log('Error: No teacher selected');
      setSubmitError(t('reviews.selectTeacherRequired') || 'يرجى اختيار معلم');
      return;
    }

    if (rating === 0) {
      console.log('Error: No rating provided');
      setSubmitError(t('reviews.ratingRequired') || 'يرجى تقييم المعلم (من 1 إلى 5 نجوم)');
      return;
    }

    console.log('Submitting review with data:', {
      teacher_profile_id: selectedTeacher,
      rating,
      comment,
      editingReview: editingReview ? editingReview.id : 'new review'
    });

    try {
      setSubmitting(true);
      setSubmitError(null);

      // If we're editing a review, use the teacher profile ID from the review
      if (editingReview) {
        console.log('Editing existing review, using profile ID from review:', editingReview.teacher_profile_id);

        // We already have the teacher profile ID in the review
        const teacherProfileId = editingReview.teacher_profile_id;

        const reviewData = {
          teacher_profile_id: teacherProfileId,
          rating,
          comment
        };

        console.log('Using teacher profile ID for review update:', teacherProfileId);

        try {
          const token = localStorage.getItem('token');
          const headers = {
            'Authorization': `Bearer ${token}`
          };

          // Update existing review
          const response = await axios.put(`/api/reviews/${editingReview.id}`, reviewData, { headers });
          console.log('Review update response:', response.data);
          setSuccess(t('reviews.reviewUpdateSuccess') || 'تم تحديث المراجعة بنجاح!');

          // Reset form
          setSelectedTeacher('');
          setRating(0);
          setComment('');
          setEditingReview(null);
          setEditingReviewId(null);

          // Refresh reviews list
          const reviewsResponse = await axios.get('/api/reviews/student', { headers });
          console.log('Refresh reviews response:', reviewsResponse.data);

          if (reviewsResponse.data) {
            // Check different possible structures for reviews data
            let reviewsData = [];

            if (reviewsResponse.data.reviews) {
              reviewsData = reviewsResponse.data.reviews;
            } else if (reviewsResponse.data.data) {
              if (Array.isArray(reviewsResponse.data.data)) {
                reviewsData = reviewsResponse.data.data;
              } else if (reviewsResponse.data.data.reviews) {
                reviewsData = reviewsResponse.data.data.reviews;
              }
            } else if (Array.isArray(reviewsResponse.data)) {
              reviewsData = reviewsResponse.data;
            }

            setMyReviews(reviewsData);

            // After submitting a review successfully, we need to update the teachers list
            // to exclude the teacher we just reviewed
            if (reviewsData.length > 0 && allTeachers.length > 0) {
              // Get the updated list of reviewed teacher profile IDs
              const updatedReviewedTeacherProfileIds = reviewsData.map(review =>
                String(review.teacher_profile_id)
              );
              console.log('Updated reviewed teacher profile IDs after submission:', updatedReviewedTeacherProfileIds);

              // Log the profile IDs for debugging
              console.log('All teacher profile IDs:', allTeachers.map(teacher => ({
                name: teacher.full_name,
                user_id: teacher.id,
                profile_id: teacher.profile_id
              })));

              // Filter out teachers that already have reviews
              const updatedFilteredTeachersList = allTeachers.filter(teacher => {
                // Convert profile_id to string for comparison
                const teacherProfileId = String(teacher.profile_id);

                // Check if this teacher's profile_id is in the list of reviewed teacher profile IDs
                let isAlreadyReviewed = false;

                // First check: Direct match with profile_id in reviews
                if (updatedReviewedTeacherProfileIds.includes(teacherProfileId)) {
                  console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}) matches review profile_id directly`);
                  isAlreadyReviewed = true;
                }

                // Second check: Match by teacher user ID in bookings
                if (!isAlreadyReviewed) {
                  // Find a booking for this teacher
                  const bookingWithTeacher = completedBookings.find(booking =>
                    String(booking.teacher_id) === String(teacher.id)
                  );

                  if (bookingWithTeacher) {
                    // Check if the booking's teacher_profile_id is in the reviewed list
                    const bookingProfileId = String(bookingWithTeacher.teacher_profile_id);
                    if (updatedReviewedTeacherProfileIds.includes(bookingProfileId)) {
                      console.log(`Teacher ${teacher.full_name} (user_id: ${teacher.id}) has booking with profile_id: ${bookingProfileId} that matches a review`);
                      isAlreadyReviewed = true;
                    }
                  }
                }

                // Third check: Check if this teacher's ID matches the ID of the teacher we just reviewed
                if (!isAlreadyReviewed && selectedTeacherObj && String(selectedTeacherObj.id) === String(teacher.id)) {
                  console.log(`Teacher ${teacher.full_name} (user_id: ${teacher.id}) is the teacher we just reviewed`);
                  isAlreadyReviewed = true;
                }

                // Fourth check: Check if this teacher's profile_id matches the profile_id we just used for the review
                if (!isAlreadyReviewed && teacherProfileId && String(teacherProfileId) === String(teacher.profile_id)) {
                  console.log(`Teacher ${teacher.full_name} (profile_id: ${teacher.profile_id}) matches the profile_id we just used for the review: ${teacherProfileId}`);
                  isAlreadyReviewed = true;
                }

                // Fourth check: Check if this teacher's profile_id matches the profile_id we just used for the review
                if (!isAlreadyReviewed && teacherProfileId && String(teacherProfileId) === String(teacher.profile_id)) {
                  console.log(`Teacher ${teacher.full_name} (profile_id: ${teacher.profile_id}) matches the profile_id we just used for the review: ${teacherProfileId}`);
                  isAlreadyReviewed = true;
                }

                if (isAlreadyReviewed) {
                  console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}) already has a review - excluding`);
                } else {
                  console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}) has no review yet - including`);
                }

                // Only include teachers that don't have reviews
                return !isAlreadyReviewed;
              });

              console.log('Updated filtered teachers list after submission:', updatedFilteredTeachersList);
              setTeachers(updatedFilteredTeachersList);
            }
          }

          setSubmitting(false);
          return;
        } catch (err) {
          console.error('Error updating review:', err);

          // Log detailed error information
          console.error('Submit error details:', {
            message: err.message,
            response: err.response?.data,
            status: err.response?.status
          });

          let errorMessage = err.response?.data?.message || t('reviews.reviewError') || 'حدث خطأ أثناء تحديث المراجعة';
          setSubmitError(errorMessage);
          setSubmitting(false);
          return;
        }
      }

      // For new reviews, find the selected teacher object to get the profile_id
      const selectedTeacherObj = teachers.find(t => t.id === selectedTeacher);
      console.log('Selected teacher object:', selectedTeacherObj);

      if (!selectedTeacherObj) {
        console.error('Could not find teacher object for selected teacher:', selectedTeacher);
        setSubmitError('لم نتمكن من العثور على المعلم المحدد. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
        setSubmitting(false);
        return;
      }

      // Get the selected teacher object
      console.log('Selected teacher object:', selectedTeacherObj);

      // Log all completed bookings for debugging
      console.log('All completed bookings:', completedBookings);

      // Find a booking with the selected teacher's user ID
      const bookingWithTeacher = completedBookings.find(booking => {
        // Try to match by teacher user ID
        return String(selectedTeacherObj.id) === String(booking.teacher_id);
      });

      // Determine which teacher profile ID to use
      let teacherProfileId;

      if (bookingWithTeacher) {
        // If we found a booking with this teacher, use the teacher_profile_id from the booking
        teacherProfileId = bookingWithTeacher.teacher_profile_id;
        console.log('Found matching booking with teacher ID:', bookingWithTeacher.teacher_id);
        console.log('Using teacher profile ID from booking:', teacherProfileId);
      } else {
        // If we couldn't find a booking by teacher_id, try to find by profile_id
        const bookingByProfileId = completedBookings.find(booking =>
          String(booking.teacher_profile_id) === String(selectedTeacherObj.profile_id)
        );

        if (bookingByProfileId) {
          teacherProfileId = bookingByProfileId.teacher_profile_id;
          console.log('Found booking by profile_id match:', bookingByProfileId.teacher_profile_id);
          console.log('Using teacher profile ID from booking (profile_id match):', teacherProfileId);
        } else if (completedBookings.length > 0) {
          // If we still couldn't find a booking, use the first booking's teacher_profile_id
          teacherProfileId = completedBookings[0].teacher_profile_id;
          console.log('No matching booking found for teacher. Using teacher profile ID from first booking:', teacherProfileId);
        } else {
          // Last resort - use the profile_id from the teacher object
          teacherProfileId = selectedTeacherObj.profile_id;
          console.log('No bookings found, using teacher profile ID from teacher object:', teacherProfileId);
        }
      }

      console.log('Final teacher profile ID:', teacherProfileId);

      if (!teacherProfileId) {
        console.error('Could not find teacher profile ID for selected teacher:', selectedTeacher);
        setSubmitError('لم نتمكن من العثور على معرف الملف الشخصي للمعلم المحدد. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
        setSubmitting(false);
        return;
      }

      const reviewData = {
        teacher_profile_id: teacherProfileId,
        rating,
        comment
      };

      console.log('Using teacher profile ID for review:', teacherProfileId);

      // Create new review
      const token = localStorage.getItem('token');
      const headers = {
        'Authorization': `Bearer ${token}`
      };

      // Create new review - use the correct endpoint
      console.log('Sending review data to /api/reviews:', reviewData);
      const response = await axios.post('/api/reviews', reviewData, { headers });
      console.log('Review creation response:', response.data);
      setSuccess(t('reviews.reviewSuccess') || 'تم إرسال المراجعة بنجاح! شكرًا على مشاركة رأيك.');

      // Reset form
      setSelectedTeacher('');
      setRating(0);
      setComment('');
      setEditingReview(null);
      setEditingReviewId(null);

      // Refresh reviews list
      const reviewsResponse = await axios.get('/api/reviews/student', { headers });
      console.log('Refresh reviews response:', reviewsResponse.data);

      if (reviewsResponse.data) {
        // Check different possible structures for reviews data
        let reviewsData = [];

        if (reviewsResponse.data.reviews) {
          reviewsData = reviewsResponse.data.reviews;
        } else if (reviewsResponse.data.data) {
          if (Array.isArray(reviewsResponse.data.data)) {
            reviewsData = reviewsResponse.data.data;
          } else if (reviewsResponse.data.data.reviews) {
            reviewsData = reviewsResponse.data.data.reviews;
          }
        } else if (Array.isArray(reviewsResponse.data)) {
          reviewsData = reviewsResponse.data;
        }

        setMyReviews(reviewsData);

        // After submitting a review successfully, we need to update the teachers list
        // to exclude the teacher we just reviewed
        if (reviewsData.length > 0 && allTeachers.length > 0) {
          // Get the updated list of reviewed teacher profile IDs
          const updatedReviewedTeacherProfileIds = reviewsData.map(review =>
            String(review.teacher_profile_id)
          );
          console.log('Updated reviewed teacher profile IDs after new submission:', updatedReviewedTeacherProfileIds);

          // Log the profile IDs for debugging
          console.log('All teacher profile IDs:', allTeachers.map(teacher => ({
            name: teacher.full_name,
            user_id: teacher.id,
            profile_id: teacher.profile_id
          })));

          // Filter out teachers that already have reviews
          const updatedFilteredTeachersList = allTeachers.filter(teacher => {
            // Convert profile_id to string for comparison
            const teacherProfileId = String(teacher.profile_id);

            // Check if this teacher's profile_id is in the list of reviewed teacher profile IDs
            let isAlreadyReviewed = false;

            // First check: Direct match with profile_id in reviews
            if (updatedReviewedTeacherProfileIds.includes(teacherProfileId)) {
              console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}) matches review profile_id directly`);
              isAlreadyReviewed = true;
            }

            // Second check: Match by teacher user ID in bookings
            if (!isAlreadyReviewed) {
              // Find a booking for this teacher
              const bookingWithTeacher = completedBookings.find(booking =>
                String(booking.teacher_id) === String(teacher.id)
              );

              if (bookingWithTeacher) {
                // Check if the booking's teacher_profile_id is in the reviewed list
                const bookingProfileId = String(bookingWithTeacher.teacher_profile_id);
                if (updatedReviewedTeacherProfileIds.includes(bookingProfileId)) {
                  console.log(`Teacher ${teacher.full_name} (user_id: ${teacher.id}) has booking with profile_id: ${bookingProfileId} that matches a review`);
                  isAlreadyReviewed = true;
                }
              }
            }

            // Third check: Check if this teacher's ID matches the ID of the teacher we just reviewed
            if (!isAlreadyReviewed && selectedTeacherObj && String(selectedTeacherObj.id) === String(teacher.id)) {
              console.log(`Teacher ${teacher.full_name} (user_id: ${teacher.id}) is the teacher we just reviewed`);
              isAlreadyReviewed = true;
            }

            // Fourth check: Check if this teacher's profile_id matches the profile_id we just used for the review
            if (!isAlreadyReviewed && teacherProfileId && String(teacherProfileId) === String(teacher.profile_id)) {
              console.log(`Teacher ${teacher.full_name} (profile_id: ${teacher.profile_id}) matches the profile_id we just used for the review: ${teacherProfileId}`);
              isAlreadyReviewed = true;
            }

            if (isAlreadyReviewed) {
              console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}) already has a review - excluding`);
            } else {
              console.log(`Teacher ${teacher.full_name} (profile_id: ${teacherProfileId}) has no review yet - including`);
            }

            // Only include teachers that don't have reviews
            return !isAlreadyReviewed;
          });

          console.log('Updated filtered teachers list after new submission:', updatedFilteredTeachersList);
          setTeachers(updatedFilteredTeachersList);
        }
      }

      setSubmitting(false);
    } catch (err) {
      console.error('Error submitting review:', err);

      // Log detailed error information
      console.error('Submit error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });

      let errorMessage = err.response?.data?.message || t('reviews.reviewError') || 'حدث خطأ أثناء إرسال المراجعة';

      // تحسين رسائل الخطأ للمستخدم
      if (errorMessage.includes('only review teachers you have had lessons with')) {
        errorMessage = 'يمكنك فقط كتابة مراجعة للمعلمين الذين أخذت دروسًا معهم وتم إكمالها.';
      } else if (err.response?.status === 401) {
        errorMessage = 'يرجى تسجيل الدخول مرة أخرى للمتابعة.';
      } else if (err.response?.status === 400) {
        if (errorMessage.includes('already exists')) {
          errorMessage = 'لقد قمت بالفعل بكتابة مراجعة لهذا المعلم. يمكنك تحديث المراجعة الحالية.';
        } else if (errorMessage.includes('Teacher ID and rating are required')) {
          errorMessage = 'معرف المعلم والتقييم مطلوبان. يرجى التأكد من اختيار معلم وتقييمه.';
        } else if (errorMessage.includes('Teacher not found')) {
          errorMessage = 'لم يتم العثور على المعلم. يرجى تحديث الصفحة والمحاولة مرة أخرى.';
        } else {
          // Log the actual error message for debugging
          console.error('Unhandled 400 error:', errorMessage);
          errorMessage = 'حدث خطأ أثناء إرسال المراجعة. يرجى التأكد من اختيار معلم صحيح وتقييمه.';
        }
      } else if (err.message && err.message.includes('Network Error')) {
        errorMessage = 'حدث خطأ في الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';
      } else if (!err.response) {
        errorMessage = 'لا يمكن الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';
      }

      setSubmitError(errorMessage);
      setSubmitting(false);
    }
  };

  const handleEditReview = async (review) => {
    console.log('Editing review:', review);

    // Set the editing review ID to trigger the useEffect
    setEditingReviewId(review.id);

    // First try to find the teacher in the filtered teachers list
    let teacherWithProfileId = teachers.find(t => t.profile_id === review.teacher_profile_id);

    // If not found in the filtered list, try to find in the complete teachers list
    if (!teacherWithProfileId) {
      console.log('Teacher not found in filtered list, trying allTeachers list');
      teacherWithProfileId = allTeachers.find(t => t.profile_id === review.teacher_profile_id);
    }

    if (!teacherWithProfileId) {
      console.error('Could not find teacher with profile ID:', review.teacher_profile_id);

      // If we have the complete list of teachers but still can't find the teacher,
      // we need to fetch the teacher data directly
      if (allTeachers.length > 0) {
        // Try to get teacher info from the review itself
        if (review.teacher_name) {
          console.log('Using teacher info from review:', {
            name: review.teacher_name,
            profile_id: review.teacher_profile_id
          });

          // We need to fetch the teacher data directly to get the user ID
          try {
            const token = localStorage.getItem('token');
            const teacherResponse = await axios.get(`/api/teachers/profile/${review.teacher_profile_id}`, {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            console.log('Fetched teacher data for review:', teacherResponse.data);

            if (teacherResponse.data && teacherResponse.data.success) {
              const teacherData = teacherResponse.data.data;

              // Create a teacher object with the correct user ID
              teacherWithProfileId = {
                id: teacherData.user_id || teacherData.id,
                profile_id: review.teacher_profile_id,
                full_name: review.teacher_name || teacherData.full_name
              };

              console.log('Created teacher object with correct user ID:', teacherWithProfileId);
            } else {
              // Fallback to using the first teacher in the list
              if (allTeachers.length > 0) {
                teacherWithProfileId = allTeachers[0];
                console.log('Using first teacher as fallback:', teacherWithProfileId);
              } else {
                setError('لم نتمكن من العثور على المعلم المرتبط بهذه المراجعة. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
                return;
              }
            }
          } catch (err) {
            console.error('Error fetching teacher data:', err);
            setError('حدث خطأ أثناء محاولة تحميل بيانات المعلم. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
            return;
          }

          // Add this teacher to the allTeachers list
          setAllTeachers(prev => [...prev, teacherWithProfileId]);

          // Also add to the current teachers list for the dropdown
          setTeachers(prev => [...prev, teacherWithProfileId]);
        } else {
          setError('لم نتمكن من العثور على المعلم المرتبط بهذه المراجعة. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
          return;
        }
      } else {
        setError('لم نتمكن من العثور على المعلم المرتبط بهذه المراجعة. يرجى تحديث الصفحة والمحاولة مرة أخرى.');
        return;
      }
    }

    setEditingReview(review);
    setSelectedTeacher(teacherWithProfileId.id); // Use the user ID for the dropdown
    setRating(review.rating);
    setComment(review.comment || '');

    // Make sure we're using all teachers when editing
    setTeachers(allTeachers);

    // Scroll to form
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleDeleteReview = async (reviewId) => {
    if (window.confirm(t('reviews.confirmDelete'))) {
      try {
        setLoading(true);

        const authToken = localStorage.getItem('token');
        const response = await axios.delete(`/api/reviews/${reviewId}`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        if (response.data && response.data.success) {
          // Remove deleted review from list
          setMyReviews(myReviews.filter(review => review.id !== reviewId));
          setSuccess(t('reviews.reviewDeleteSuccess'));

          // If we were editing this review, reset the form
          if (editingReview && editingReview.id === reviewId) {
            setEditingReview(null);
            setEditingReviewId(null);
            setSelectedTeacher('');
            setRating(0);
            setComment('');
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error deleting review:', err);
        setError(err.response?.data?.message || 'Error deleting review');
        setLoading(false);
      }
    }
  };

  const cancelEdit = () => {
    setEditingReview(null);
    setEditingReviewId(null);
    setSelectedTeacher('');
    setRating(0);
    setComment('');

    // Re-filter the teachers list to exclude already reviewed teachers
    if (myReviews.length > 0 && allTeachers.length > 0 && completedBookings.length > 0) {
      // Convert profile IDs to strings to ensure consistent comparison
      const reviewedTeacherProfileIds = myReviews.map(review => String(review.teacher_profile_id));
      console.log('After cancel edit - Already reviewed teacher profile IDs:', reviewedTeacherProfileIds);

      // Filter out teachers that already have reviews
      const filteredTeachersList = allTeachers.filter(teacher => {
        // Convert profile_id to string for comparison
        const teacherProfileId = String(teacher.profile_id);

        // Only include teachers that don't have reviews
        return !reviewedTeacherProfileIds.includes(teacherProfileId);
      });

      console.log('After cancel edit - Filtered teachers list length:', filteredTeachersList.length);
      setTeachers(filteredTeachersList);
    }
  };

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          {editingReview ? t('reviews.editReview') : t('reviews.writeReview')}
        </Typography>

        {success && (
          <Alert
            severity="success"
            sx={{
              mb: 3,
              p: 2,
              fontSize: '1rem',
              '& .MuiAlert-icon': { fontSize: '1.5rem' }
            }}
          >
            {success}
          </Alert>
        )}

        {error && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              p: 2,
              fontSize: '1rem',
              '& .MuiAlert-icon': { fontSize: '1.5rem' }
            }}
          >
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            {teachers.length === 0 ? (
              <Alert
                severity="info"
                sx={{
                  mb: 3,
                  p: 2,
                  fontSize: '1rem',
                  '& .MuiAlert-icon': { fontSize: '1.5rem' }
                }}
              >
                {completedBookings.length === 0
                  ? (t('reviews.noCompletedLessons') || 'لا يمكنك كتابة مراجعة حتى تكمل درسًا مع معلم. يرجى حجز درس وإكماله أولاً.')
                  : myReviews.length > 0 && teachers.length === 0
                    ? (t('reviews.allTeachersReviewed') || 'لقد قمت بكتابة مراجعات لجميع المعلمين الذين أخذت دروسًا معهم. يمكنك تحديث المراجعات الحالية من القسم أدناه.')
                    : (t('reviews.noTeachers') || 'لم نتمكن من العثور على معلمين متاحين لكتابة مراجعات لهم. يرجى التأكد من إكمال درس واحد على الأقل قبل كتابة مراجعة.')}
              </Alert>
            ) : (
              <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
                <Box component="form" onSubmit={handleSubmit}>
                  <FormControl fullWidth sx={{ mb: 3 }}>
                    <InputLabel id="teacher-select-label">{t('reviews.selectTeacher') || 'اختر المعلم'}</InputLabel>
                    <Select
                      labelId="teacher-select-label"
                      value={selectedTeacher}
                      onChange={(e) => setSelectedTeacher(e.target.value)}
                      label={t('reviews.selectTeacher') || 'اختر المعلم'}
                      disabled={!!editingReview}
                    >
                      {teachers.map((teacher) => (
                        <MenuItem key={teacher.id} value={teacher.id}>
                          {teacher.full_name || 'معلم'}
                        </MenuItem>
                      ))}
                    </Select>
                    <FormHelperText>
                      اختر المعلم الذي تريد كتابة مراجعة له (يجب أن تكون قد أكملت درسًا معه)
                    </FormHelperText>
                  </FormControl>

                  <Box sx={{ mb: 3 }}>
                    <Typography component="legend">{t('reviews.rating') || 'التقييم'}</Typography>
                    <Rating
                      name="rating"
                      value={rating}
                      onChange={(e, newValue) => setRating(newValue)}
                      precision={0.5}
                      size="large"
                    />
                    <FormHelperText>
                      قم بتقييم المعلم من 1 إلى 5 نجوم (مطلوب)
                    </FormHelperText>
                    {submitError && rating === 0 && (
                      <FormHelperText error>{t('reviews.reviewRequired') || 'التقييم مطلوب'}</FormHelperText>
                    )}
                  </Box>

                  <TextField
                    fullWidth
                    label={t('reviews.comment') || 'التعليق'}
                    multiline
                    rows={4}
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    placeholder={t('reviews.commentPlaceholder') || 'اكتب تعليقك عن تجربتك مع هذا المعلم...'}
                    sx={{ mb: 3 }}
                    helperText="اكتب تعليقًا يصف تجربتك مع المعلم (اختياري)"
                  />

                  {submitError && (
                    <Alert severity="error" sx={{ mb: 3 }}>
                      {submitError}
                    </Alert>
                  )}

                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {editingReview && (
                      <Button
                        variant="outlined"
                        onClick={cancelEdit}
                        disabled={submitting}
                      >
                        {t('common.cancel')}
                      </Button>
                    )}

                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                      disabled={submitting}
                      size="large"
                      sx={{ py: 1, px: 3 }}
                    >
                      {submitting ? (
                        <CircularProgress size={24} />
                      ) : editingReview ? (
                        t('reviews.update') || 'تحديث المراجعة'
                      ) : (
                        t('reviews.submit') || 'إرسال المراجعة'
                      )}
                    </Button>
                  </Box>
                </Box>
              </Paper>
            )}

            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              mt: 6,
              mb: 3
            }}>
              <Typography variant="h5" component="h2" gutterBottom sx={{ mb: 0 }}>
                {t('reviews.myReviews') || 'مراجعاتي'}
                {myReviews.length > 0 && <Typography component="span" color="text.secondary" sx={{ ml: 1, fontSize: '1rem' }}>
                  ({myReviews.length})
                </Typography>}
              </Typography>

              {myReviews.length > 0 && teachers.length === 0 && (
                <Typography variant="body2" color="primary" sx={{ fontStyle: 'italic' }}>
                  {t('reviews.allTeachersReviewedShort') || 'لقد قمت بتقييم جميع المعلمين'}
                </Typography>
              )}
            </Box>

            {myReviews.length === 0 ? (
              <Alert severity="info">
                {t('reviews.noReviews')}
              </Alert>
            ) : (
              <Grid container spacing={3}>
                {myReviews.map((review) => (
                  <Grid item xs={12} key={review.id}>
                    <Card>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar
                            src={review.teacher_profile_picture}
                            alt={review.teacher_name}
                            sx={{ mr: 2, width: 56, height: 56 }}
                          />
                          <Box>
                            <Typography variant="h6" sx={{ fontWeight: 'bold' }}>{review.teacher_name}</Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Rating value={review.rating} precision={0.5} readOnly size="small" />
                              <Typography variant="body2" sx={{ ml: 1, fontWeight: 'medium' }}>
                                ({review.rating})
                              </Typography>
                            </Box>
                          </Box>
                        </Box>

                        <Divider sx={{ my: 2 }} />

                        <Typography variant="body1" paragraph>
                          {review.comment || t('reviews.noComment')}
                        </Typography>

                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                            <CalendarTodayIcon sx={{ fontSize: '0.875rem', mr: 0.5 }} />
                            {new Date(review.created_at).toLocaleDateString(undefined, {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </Typography>

                          <Box>
                            <Button
                              variant="outlined"
                              color="primary"
                              startIcon={<EditIcon />}
                              onClick={() => handleEditReview(review)}
                              sx={{ mr: 1 }}
                            >
                              {t('common.edit') || 'تعديل'}
                            </Button>
                            <Button
                              variant="outlined"
                              startIcon={<DeleteIcon />}
                              color="error"
                              onClick={() => handleDeleteReview(review.id)}
                            >
                              {t('common.delete') || 'حذف'}
                            </Button>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </>
        )}
      </Container>
    </Layout>
  );
};

export default WriteReview;
