import React, { useState, useEffect } from 'react';
import { 
  Container, Typography, Grid, Paper, Box, Avatar,
  Button, CircularProgress, Divider
} from '@mui/material';
import {
  Edit as EditIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import Layout from '../../components/Layout';

const StudentDashboard = () => {
  const { t } = useTranslation();
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [profileData, setProfileData] = useState(null);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('/api/students/profile', {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        if (response.data.success && response.data.profile) {
          setProfileData(response.data.profile);
        } else {
          throw new Error('Invalid profile data structure');
        }
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError(t('dashboard.errorLoadingProfile'));
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [t]);

  if (loading) {
    return (
      <Layout>
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '80vh' }}>
          <CircularProgress />
        </Box>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <Typography color="error">{error}</Typography>
            <Button
              variant="contained"
              onClick={() => window.location.reload()}
              sx={{ mt: 2 }}
            >
              {t('common.retry')}
            </Button>
          </Paper>
        </Container>
      </Layout>
    );
  }

  const isProfileComplete = profileData?.is_completed;

  return (
    <Layout>
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {/* Profile Overview */}
        <Paper sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={3} alignItems="center">
            <Grid item>
              <Avatar
                src={currentUser?.profile_picture_url}
                sx={{ width: 80, height: 80 }}
              >
                {currentUser?.full_name?.charAt(0)}
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h4" gutterBottom>
                {t('dashboard.welcome')}, {currentUser?.full_name}
              </Typography>
              <Typography variant="body1" color="textSecondary" gutterBottom>
                {currentUser?.email}
              </Typography>
            </Grid>
            <Grid item>
              {!isProfileComplete && (
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={() => navigate('/student/complete-profile')}
                  color="warning"
                >
                  {t('dashboard.completeProfile')}
                </Button>
              )}
            </Grid>
          </Grid>
        </Paper>

        {!isProfileComplete && (
          <Paper sx={{ p: 3, mb: 3, bgcolor: 'warning.light' }}>
            <Typography variant="h6" gutterBottom>
              {t('dashboard.incompleteProfile')}
            </Typography>
            <Typography paragraph>
              {t('dashboard.completeProfileMessage')}
            </Typography>
            <Button
              variant="contained"
              color="warning"
              onClick={() => navigate('/student/complete-profile')}
              endIcon={<ArrowForwardIcon />}
            >
              {t('dashboard.completeProfileNow')}
            </Button>
          </Paper>
        )}
      </Container>
    </Layout>
  );
};

export default StudentDashboard;
