import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  Box,
  Alert,
  Divider,
  IconButton,
  Avatar,
  CircularProgress
} from '@mui/material';
import { Language as LanguageIcon, PhotoCamera as PhotoCameraIcon } from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import Layout from '../../components/Layout';
import axios from '../../utils/axios';

const Profile = () => {
  const { t, i18n } = useTranslation();
  const { currentUser, updateUser } = useAuth();
  const [isRtl, setIsRtl] = useState(i18n.language === 'ar');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const [profileData, setProfileData] = useState({
    fullName: currentUser?.full_name || '',
    email: currentUser?.email || '',
    gender: currentUser?.gender || '',
  });

  const [previewUrl, setPreviewUrl] = useState(
    currentUser?.profile_picture_url || ''
  );

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmNewPassword: '',
  });

  // Auto-hide messages after 3 seconds
  useEffect(() => {
    let timer;
    if (success || error) {
      timer = setTimeout(() => {
        setSuccess('');
        setError('');
      }, 3000);
    }
    return () => clearTimeout(timer);
  }, [success, error]);

  // Update local data when currentUser changes
  useEffect(() => {
    if (currentUser) {
      setProfileData({
        fullName: currentUser.full_name || '',
        email: currentUser.email || '',
        gender: currentUser.gender || '',
      });

      if (currentUser.profile_picture_url) {
        console.log('Profile picture URL:', currentUser.profile_picture_url); // Debug log
        setPreviewUrl(currentUser.profile_picture_url);
      }
    }
  }, [currentUser]);

  const handleLanguageChange = () => {
    const newLang = i18n.language === 'en' ? 'ar' : 'en';
    i18n.changeLanguage(newLang);
    setIsRtl(newLang === 'ar');
    document.dir = newLang === 'ar' ? 'rtl' : 'ltr';
  };

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // File validation
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setError(t('profile.errors.fileTooLarge'));
      return;
    }
    if (!file.type.startsWith('image/')) {
      setError(t('profile.errors.invalidFileType'));
      return;
    }

    const formData = new FormData();
    formData.append('profilePicture', file);

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axios.post('/users/upload-profile-picture', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('Upload response:', response.data); // Debug log

      // Check for url in response data
      if (response.data.success && response.data.url) {
        console.log('New picture URL:', response.data.url); // Debug log
        setPreviewUrl(response.data.url);
        updateUser({ ...currentUser, profile_picture_url: response.data.url });
        setSuccess(t('profile.updateSuccess'));
      } else {
        throw new Error('Profile picture update failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      setError(error.response?.data?.message || t('profile.errors.update'));
    } finally {
      setLoading(false);
    }
  };

  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await axios.put('/auth/update-profile', {
        full_name: profileData.fullName,
        email: profileData.email
      });

      if (response.data.success) {
        setSuccess(t('admin.profile.updateSuccess'));
        // Update the local user data
        if (response.data.user) {
          setProfileData({
            fullName: response.data.user.full_name || '',
            email: response.data.user.email || ''
          });
        }
      } else {
        setError(response.data.message || t('admin.profile.updateError'));
      }
    } catch (error) {
      setError(error.response?.data?.message || t('admin.profile.updateError'));
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordUpdate = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      if (passwordData.newPassword !== passwordData.confirmNewPassword) {
        setError(t('admin.profile.passwordMismatch'));
        setLoading(false);
        return;
      }

      const response = await axios.put('/auth/change-password', {
        current_password: passwordData.currentPassword,
        new_password: passwordData.newPassword
      });

      if (response.data.success) {
        setSuccess(t('admin.profile.passwordSuccess'));
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmNewPassword: ''
        });
      } else {
        setError(response.data.message || t('admin.profile.passwordError'));
      }
    } catch (error) {
      setError(error.response?.data?.message || t('admin.profile.passwordError'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout title={t('admin.profile.title')}>
      <Container maxWidth="md">
        {(success || error) && (
          <Box mb={2}>
            {success && <Alert severity="success">{success}</Alert>}
            {error && <Alert severity="error">{error}</Alert>}
          </Box>
        )}
        <IconButton
          onClick={handleLanguageChange}
          sx={{ position: 'absolute', top: 16, right: isRtl ? 'auto' : 16, left: isRtl ? 16 : 'auto' }}
        >
          <LanguageIcon />
        </IconButton>

        <Grid container spacing={3}>
          {/* Personal Information */}
          <Grid item xs={12}>
            <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                {t('admin.profile.personalInfo')}
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <form onSubmit={handleProfileUpdate}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
                    <Box sx={{ position: 'relative' }}>
                      <Avatar
                        src={previewUrl}
                        alt={profileData.fullName}
                        sx={{
                          width: 120,
                          height: 120,
                          mb: 2,
                          border: '2px solid',
                          borderColor: 'primary.main'
                        }}
                      />
                      <input
                        accept="image/*"
                        type="file"
                        id="profile-picture-input"
                        onChange={handleImageUpload}
                        style={{ display: 'none' }}
                      />
                      <label htmlFor="profile-picture-input">
                        <IconButton
                          color="primary"
                          component="span"
                          sx={{
                            position: 'absolute',
                            bottom: 16,
                            right: -16,
                            backgroundColor: 'background.paper',
                            boxShadow: 1,
                            '&:hover': {
                              backgroundColor: 'background.paper',
                            }
                          }}
                        >
                          <PhotoCameraIcon />
                        </IconButton>
                      </label>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label={t('auth.fullName')}
                      value={profileData.fullName}
                      onChange={(e) => setProfileData({ ...profileData, fullName: e.target.value })}
                      disabled={loading}
                      dir={isRtl ? 'rtl' : 'ltr'}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label={t('auth.email')}
                      value={profileData.email}
                      onChange={(e) => setProfileData({ ...profileData, email: e.target.value })}
                      disabled={loading}
                      dir={isRtl ? 'rtl' : 'ltr'}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                      <Button
                        type="submit"
                        variant="contained"
                        color="primary"
                        disabled={loading}
                      >
                        {t('admin.profile.updateInfo')}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </form>
            </Paper>
          </Grid>

          {/* Change Password */}
          <Grid item xs={12}>
            <Paper elevation={3} sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                {t('admin.profile.changePassword')}
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <form onSubmit={handlePasswordUpdate}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      type="password"
                      label={t('admin.profile.currentPassword')}
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                      disabled={loading}
                      dir={isRtl ? 'rtl' : 'ltr'}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      type="password"
                      label={t('admin.profile.newPassword')}
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                      disabled={loading}
                      dir={isRtl ? 'rtl' : 'ltr'}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      type="password"
                      label={t('admin.profile.confirmNewPassword')}
                      value={passwordData.confirmNewPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, confirmNewPassword: e.target.value })}
                      disabled={loading}
                      dir={isRtl ? 'rtl' : 'ltr'}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                      <Button
                        type="submit"
                        variant="contained"
                        color="primary"
                        disabled={loading}
                      >
                        {t('admin.profile.changePassword')}
                      </Button>
                    </Box>
                  </Grid>
                </Grid>
              </form>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Layout>
  );
};

export default Profile;
