import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Paper,
  Button,
  TextField,
  Grid,
  Avatar,

  Card,
  CardMedia,
  CardContent,
  CardActions
} from '@mui/material';
import Layout from '../../components/Layout';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import OndemandVideoIcon from '@mui/icons-material/OndemandVideo';
import CropImageDialog from '../../components/common/CropImageDialog';

const EditApplication = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { currentUser, updateUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [fieldErrors, setFieldErrors] = useState({});
  const [profileData, setProfileData] = useState(null);
  // Crop dialog state
  const [cropDialogOpen, setCropDialogOpen] = useState(false);
  const [tempImageSrc, setTempImageSrc] = useState('');

  // Form data state
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    country: '',
    residence: '',
    profilePicture: null,
    profilePicturePreview: '',
    introVideoUrl: '',
    cv: '',
    qualifications: '',
    teachingExperience: 0
  });

  // دالة تحميل بيانات المعلم
  const fetchProfileData = async () => {
    try {
      setLoading(true);
      console.log('Fetching teacher profile data...');

      // الحصول على بيانات المعلم
      const profileResponse = await axios.get('/api/teacher/profile');

      if (profileResponse.data && profileResponse.data.profile) {
        console.log('Profile data received:', profileResponse.data.profile);
        const profile = profileResponse.data.profile;

        // استخدام بيانات المستخدم من currentUser بدلاً من API call منفصل
        const user = currentUser;

          setProfileData(profile);

          // إعداد البيانات للنموذج
          let profilePictureUrl = profile.profile_picture_url || '';



          // التحقق من وجود فيديو مؤقت في localStorage
          const savedVideoUrl = localStorage.getItem('teacherVideoUrl');
          const videoUrl = savedVideoUrl || profile.intro_video_url || '';

          // تحديث بيانات النموذج
          setFormData({
            fullName: user.fullName || user.full_name || '',
            email: user.email || '',
            country: profile.country || '',
            residence: profile.residence || '',
            profilePicture: null,
            profilePicturePreview: profilePictureUrl,
            introVideoUrl: videoUrl,
            cv: profile.cv || '',
            qualifications: profile.qualifications || '',
            teachingExperience: parseInt(profile.teaching_experience) || 0
          });
        }
      } catch (error) {
        console.error('Error fetching profile data:', error);
        setError(t('profile.errors.fetchingProfile') || 'حدث خطأ أثناء جلب بيانات الملف الشخصي');
      } finally {
        setLoading(false);
      }
    };

  // تحميل بيانات المعلم
  useEffect(() => {
    if (currentUser) {
      fetchProfileData();
    }
  }, [t, currentUser]);

  // تحديث البيانات عند العودة من صفحة تعديل الفيديو
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && currentUser) {
        // التحقق من علامة تحديث الفيديو
        const videoUpdated = localStorage.getItem('videoUpdated');
        if (videoUpdated === 'true') {
          localStorage.removeItem('videoUpdated');
          // إعادة تحميل البيانات
          fetchProfileData();
        }
      }
    };

    // إضافة مستمع لتغيير visibility
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // تنظيف المستمع عند إلغاء المكون
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [currentUser]);

  // التحقق من تحديث الفيديو عند تحميل الصفحة
  useEffect(() => {
    const videoUpdated = localStorage.getItem('videoUpdated');
    if (videoUpdated === 'true' && currentUser) {
      localStorage.removeItem('videoUpdated');
      // إعادة تحميل البيانات
      setTimeout(() => {
        fetchProfileData();
      }, 500); // تأخير قصير للتأكد من تحديث البيانات
    }
  }, [currentUser]);

  // إضافة مستمع لـ window focus لتحديث البيانات عند العودة للصفحة
  useEffect(() => {
    const handleWindowFocus = () => {
      const videoUpdated = localStorage.getItem('videoUpdated');
      if (videoUpdated === 'true' && currentUser) {
        localStorage.removeItem('videoUpdated');
        fetchProfileData();
      }
    };

    window.addEventListener('focus', handleWindowFocus);

    return () => {
      window.removeEventListener('focus', handleWindowFocus);
    };
  }, [currentUser]);

  // دوال التعامل مع النموذج
  const handleChange = (e) => {
    const { name, value } = e.target;

    // restrict teachingExperience to 0-99 (max two digits)
    if (name === 'teachingExperience') {
      let numeric = String(value).replace(/\D/g, '');
      if (numeric.length > 2) {
        numeric = numeric.slice(0, 2);
      }
      setFormData(prev => ({ ...prev, [name]: numeric }));
      // clear field error if value modified
      if (fieldErrors[name]) {
        setFieldErrors(prev => ({ ...prev, [name]: '' }));
      }
      return;
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // إزالة خطأ الحقل عند التعديل
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleProfilePictureChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setTempImageSrc(url);
      setCropDialogOpen(true);
    }

      // إزالة خطأ الصورة
      if (fieldErrors.profilePicture) {
        setFieldErrors(prev => ({
          ...prev,
          profilePicture: ''
        }));
      }
  };

  // دالة إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');
    setSuccess('');
    setFieldErrors({});

    // التحقق من صحة البيانات
    const errors = {};
    const requiredFields = ['fullName', 'email', 'country', 'residence', 'qualifications', 'teachingExperience', 'cv'];

    requiredFields.forEach(field => {
      if (!formData[field] || formData[field] === '') {
        errors[field] = t('teacher.required') || 'هذا الحقل مطلوب';
      }
    });

    // التحقق من الفيديو
    if (!formData.introVideoUrl) {
      errors.introVideoUrl = t('teacher.required') || 'هذا الحقل مطلوب';
    }

    // التحقق من الصورة (للمعلمين الذكور فقط)
    if (currentUser && currentUser.gender !== 'female' && !formData.profilePicture && !formData.profilePicturePreview) {
      errors.profilePicture = t('teacher.profilePictureRequired') || 'الصورة الشخصية مطلوبة';
    }

    if (Object.keys(errors).length > 0) {
      setFieldErrors(errors);
      setError(t('teacher.formHasErrors') || 'يرجى تصحيح الأخطاء في النموذج');
      setSubmitting(false);
      return;
    }

    try {
      const data = new FormData();

      // الحصول على URL الفيديو من localStorage أو النموذج
      const savedVideoUrl = localStorage.getItem('teacherVideoRelativePath');
      const videoUrl = savedVideoUrl || formData.introVideoUrl;

      // إضافة البيانات الأساسية (بما في ذلك الاسم والإيميل)
      data.append('fullName', formData.fullName);
      data.append('email', formData.email);
      data.append('country', formData.country);
      data.append('residence', formData.residence);
      data.append('qualifications', formData.qualifications);
      data.append('teachingExperience', formData.teachingExperience);
      data.append('cv', formData.cv);
      data.append('introVideoUrl', videoUrl);

      // إضافة الحقول المطلوبة الأخرى (حتى لو كانت فارغة)
      data.append('nativeLanguage', profileData?.native_language || '');
      data.append('teachingLanguages', JSON.stringify(profileData?.teaching_languages || []));
      data.append('courseTypes', JSON.stringify(profileData?.course_types || []));
      data.append('phone', profileData?.phone || '');
      data.append('pricePerLesson', profileData?.price_per_lesson || '');
      data.append('timezone', profileData?.timezone || '');

      data.append('availableHours', JSON.stringify(profileData?.available_hours || {}));
      data.append('commitmentAccepted', 'true');

      // إضافة الصورة إذا تم اختيارها
      if (formData.profilePicture) {
        data.append('profilePicture', formData.profilePicture);
      } else if (currentUser && currentUser.gender === 'female') {
        // للمعلمات، استخدم الصورة الافتراضية
        data.append('useDefaultFemalePicture', 'true');
      }

      // إضافة علامة التحديث
      data.append('updateApplication', 'true');

      // إرسال طلب تعديل بيانات المعلم (بما في ذلك الاسم والإيميل)
      const response = await axios.post('/api/teacher/submit-profile-update', data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response.data && response.data.success) {
        setSuccess(response.data.message || 'تم إرسال طلب تعديل البيانات بنجاح وسيتم مراجعته من قبل الإدارة');

        // تنظيف localStorage بعد الإرسال الناجح
        localStorage.removeItem('teacherVideoUrl');
        localStorage.removeItem('teacherVideoRelativePath');
        localStorage.removeItem('videoUpdated');

        // الانتقال إلى صفحة الملف الشخصي بعد 2 ثانية
        setTimeout(() => {
          navigate('/teacher/profile');
        }, 2000);
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      setError(error.response?.data?.message || t('common.errorOccurred') || 'حدث خطأ أثناء إرسال البيانات');
    } finally {
      setSubmitting(false);
    }
  };

  // دالة الإلغاء والعودة للملف الشخصي
  const handleCancel = () => {
    navigate('/teacher/profile');
  };

  if (loading) {
    return (
      <Layout>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      </Layout>
    );
  }

  return (
    <Layout>
      <Box sx={{ maxWidth: '1200px', mx: 'auto', p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={handleCancel}
          >
            {t('common.cancel')}
          </Button>
        </Box>

        <Paper elevation={3} sx={{ p: 4, mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            {t('teacher.application.editApplication.title') || 'تعديل بيانات التقديم'}
          </Typography>

          <Typography variant="body1" paragraph>
            {t('teacher.application.editApplication.description') || 'يمكنك تعديل بيانات التقديم الخاصة بك هنا. بعد التعديل، سيتم إرسال طلبك للمراجعة مرة أخرى.'}
          </Typography>

          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body1">
              {t('teacher.application.editApplication.warning') || 'ستبقى بياناتك الحالية نشطة حتى توافق الإدارة على التغييرات.'}
            </Typography>
          </Alert>
        </Paper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        <Paper elevation={3} sx={{ p: 4 }}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              {/* الاسم */}
              <Grid item xs={12} md={6}>
                <TextField
                  required
                  fullWidth
                  name="fullName"
                  label={t('profile.fullName') || 'الاسم الكامل'}
                  value={formData.fullName}
                  onChange={handleChange}
                  error={!!fieldErrors.fullName}
                  helperText={fieldErrors.fullName || ''}
                />
              </Grid>

              {/* الإيميل */}
              <Grid item xs={12} md={6}>
                <TextField
                  required
                  fullWidth
                  type="email"
                  name="email"
                  label={t('profile.email') || 'البريد الإلكتروني'}
                  value={formData.email}
                  onChange={handleChange}
                  error={!!fieldErrors.email}
                  helperText={fieldErrors.email || ''}
                />
              </Grid>

              {/* الدولة */}
              <Grid item xs={12} md={6}>
                <TextField
                  required
                  fullWidth
                  name="country"
                  label={t('teacher.country') || 'الدولة'}
                  value={formData.country}
                  onChange={handleChange}
                  error={!!fieldErrors.country}
                  helperText={fieldErrors.country || ''}
                />
              </Grid>

              {/* المنطقة */}
              <Grid item xs={12} md={6}>
                <TextField
                  required
                  fullWidth
                  name="residence"
                  label={t('teacher.residence') || 'منطقة الإقامة'}
                  value={formData.residence}
                  onChange={handleChange}
                  error={!!fieldErrors.residence}
                  helperText={fieldErrors.residence || ''}
                />
              </Grid>

              {/* الصورة الشخصية */}
              <Grid item xs={12} md={6}>
                <Box>
                  <input
                    type="file"
                    accept="image/*"
                    style={{ display: 'none' }}
                    id="profile-picture-upload"
                    onChange={handleProfilePictureChange}
                  />
                  <Typography variant="subtitle1" gutterBottom>
                    {t('teacher.profilePicture') || 'الصورة الشخصية'} *
                  </Typography>
                  <label htmlFor="profile-picture-upload">
                    <Button
                      variant="outlined"
                      component="span"
                      fullWidth
                      startIcon={<CloudUploadIcon />}
                      sx={{ mb: 1 }}
                    >
                      {formData.profilePicture ? formData.profilePicture.name : (t('teacher.profilePicturePlaceholder') || 'اختر صورة شخصية')}
                    </Button>
                  </label>
                  {formData.profilePicturePreview && (
                    <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                      <Avatar
                        src={formData.profilePicturePreview}
                        alt="Profile preview"
                        sx={{ width: 120, height: 120 }}
                      />
                      <CropImageDialog
                        open={cropDialogOpen}
                        imageSrc={tempImageSrc}
                        onClose={() => setCropDialogOpen(false)}
                        onSave={(blob) => {
                          const file = new File([blob], 'profile.jpg', { type: blob.type });
                          setFormData(prev => ({
                            ...prev,
                            profilePicture: file,
                            profilePicturePreview: URL.createObjectURL(blob)
                          }));
                          setCropDialogOpen(false);
                        }}
                      />
                    </Box>
                  )}
                  {fieldErrors.profilePicture && (
                    <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                      {fieldErrors.profilePicture}
                    </Typography>
                  )}
                </Box>
              </Grid>

              {/* الفيديو التعريفي */}
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>
                  {t('teacher.introVideo') || 'الفيديو التعريفي'} *
                </Typography>
                {formData.introVideoUrl ? (
                  <Card>
                    <CardMedia
                      component="video"
                      controls
                      src={formData.introVideoUrl}
                      sx={{ height: 200 }}
                    />
                    <CardContent>
                      <Typography variant="body2" color="text.secondary">
                        {t('teacher.videoReady') || 'الفيديو جاهز'}
                      </Typography>
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        color="primary"
                        startIcon={<OndemandVideoIcon />}
                        onClick={() => navigate('/teacher/edit-video')}
                      >
                        {t('teacher.application.changeVideo') || 'تغيير الفيديو'}
                      </Button>
                    </CardActions>
                  </Card>
                ) : (
                  <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    <Typography variant="body2" color={fieldErrors.introVideoUrl ? "error" : "textSecondary"} sx={{ mb: 2 }}>
                      {t('teacher.videoRequired') || 'الفيديو التعريفي مطلوب'}
                    </Typography>
                    <Button
                      variant="contained"
                      color="primary"
                      startIcon={<OndemandVideoIcon />}
                      onClick={() => navigate('/teacher/edit-video')}
                      fullWidth
                    >
                      {t('teacher.uploadVideoNow') || 'رفع الفيديو الآن'}
                    </Button>
                    {fieldErrors.introVideoUrl && (
                      <Typography variant="body2" color="error" sx={{ mt: 1 }}>
                        {fieldErrors.introVideoUrl}
                      </Typography>
                    )}
                  </Box>
                )}
              </Grid>

              {/* السيرة الذاتية */}
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  multiline
                  rows={6}
                  name="cv"
                  label={t('teacher.cv') || 'السيرة الذاتية'}
                  placeholder={t('teacher.cvPlaceholder') || 'اكتب سيرتك الذاتية هنا...'}
                  value={formData.cv}
                  onChange={handleChange}
                  error={!!fieldErrors.cv}
                  helperText={fieldErrors.cv || t('teacher.cvHelperText') || 'اكتب سيرتك الذاتية بالتفصيل'}
                  inputProps={{ maxLength: 2000 }}
                />
                <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
                  {formData.cv.length}/2000 {t('teacher.characters') || 'حرف'}
                </Typography>
              </Grid>

              {/* المؤهلات */}
              <Grid item xs={12}>
                <TextField
                  required
                  fullWidth
                  multiline
                  rows={4}
                  name="qualifications"
                  label={t('teacher.qualifications') || 'المؤهلات'}
                  placeholder={t('teacher.qualificationsPlaceholder') || 'اذكر مؤهلاتك العلمية والشهادات...'}
                  value={formData.qualifications}
                  onChange={handleChange}
                  error={!!fieldErrors.qualifications}
                  helperText={fieldErrors.qualifications || ''}
                />
              </Grid>

              {/* سنوات الخبرة */}
              <Grid item xs={12} md={6}>
                <TextField
                  required
                  fullWidth
                  type="number"
                  name="teachingExperience"
                  label={t('teacher.teachingExperience') || 'سنوات الخبرة في التدريس'}
                  value={formData.teachingExperience}
                  onChange={handleChange}
                  error={!!fieldErrors.teachingExperience}
                  helperText={fieldErrors.teachingExperience || ''}
                  inputProps={{ min: 0, max: 99 }}
                />
              </Grid>

              {/* أزرار الإرسال والإلغاء */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', mt: 3 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    color="primary"
                    size="large"
                    disabled={submitting}
                    sx={{ minWidth: 200 }}
                  >
                    {submitting ? <CircularProgress size={24} /> : (t('common.save') || 'حفظ التعديلات')}
                  </Button>
                  <Button
                    variant="outlined"
                    color="secondary"
                    size="large"
                    onClick={handleCancel}
                    disabled={submitting}
                    sx={{ minWidth: 200 }}
                  >
                    {t('common.cancel') || 'إلغاء'}
                  </Button>
                </Box>
              </Grid>
            </Grid>

            {/* Crop dialog for profile picture */}
            <CropImageDialog
              open={cropDialogOpen}
              imageSrc={tempImageSrc}
              onClose={() => setCropDialogOpen(false)}
              onSave={(blob) => {
                const file = new File([blob], 'profile.jpg', { type: blob.type });
                setFormData(prev => ({
                  ...prev,
                  profilePicture: file,
                  profilePicturePreview: URL.createObjectURL(blob)
                }));
                setCropDialogOpen(false);
              }}
            />
          </form>
        </Paper>
      </Box>
    </Layout>
  );
};

export default EditApplication;
