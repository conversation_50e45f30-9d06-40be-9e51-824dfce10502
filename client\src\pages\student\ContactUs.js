import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Typography,
  TextField,
  Button,
  Box,
  Paper,
  Grid,
  MenuItem,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import { Send as SendIcon } from '@mui/icons-material';
import Layout from '../../components/Layout';
import ProfileCompletionAlert from '../../components/student/ProfileCompletionAlert';
import { useAuth } from '../../contexts/AuthContext';
import axios from '../../utils/axios';

const ContactUs = () => {
  const { t } = useTranslation();
  const { currentUser, token } = useAuth();

  const [formData, setFormData] = useState({
    subject: '',
    message: '',
    type: 'question' // Default type
  });

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.subject.trim() || !formData.message.trim()) {
      setError(t('contactUs.fillAllFields'));
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await axios.post('/contact-us', {
        ...formData,
        user_id: currentUser.id,
        user_role: currentUser.role
      });

      if (response.data.success) {
        setSuccess(true);
        setFormData({
          subject: '',
          message: '',
          type: 'question'
        });
      } else {
        setError(response.data.message || t('contactUs.sendError'));
      }
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err.response?.data?.message || t('contactUs.sendError'));
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSuccess(false);
  };

  return (
    <Layout>
      <Container maxWidth="md" sx={{ py: 4 }}>
        <ProfileCompletionAlert exemptPages={['/student/complete-profile', '/student/dashboard']}>
        <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
          <Typography variant="h4" component="h1" gutterBottom align="center" sx={{ mb: 4 }}>
            {t('contactUs.title')}
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  select
                  fullWidth
                  label={t('contactUs.type')}
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  required
                >
                  <MenuItem value="question">{t('contactUs.typeQuestion')}</MenuItem>
                  <MenuItem value="problem">{t('contactUs.typeProblem')}</MenuItem>
                  <MenuItem value="suggestion">{t('contactUs.typeSuggestion')}</MenuItem>
                  <MenuItem value="other">{t('contactUs.typeOther')}</MenuItem>
                </TextField>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={t('contactUs.subject')}
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  multiline
                  rows={6}
                  label={t('contactUs.message')}
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                />
              </Grid>

              <Grid item xs={12}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                  fullWidth
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={24} color="inherit" /> : <SendIcon />}
                >
                  {loading ? t('common.sending') : t('contactUs.send')}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </Paper>

        <Snackbar
          open={success}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert onClose={handleCloseSnackbar} severity="success" sx={{ width: '100%' }}>
            {t('contactUs.messageSent')}
          </Alert>
        </Snackbar>
        </ProfileCompletionAlert>
      </Container>
    </Layout>
  );
};

export default ContactUs;
